import React from "react";
import Routes from "./Routes";
import { SidebarProvider } from "./contexts/SidebarContext";
import { AuthProvider } from "./contexts/AuthContext";
import AuthErrorBoundary from "./components/auth/AuthErrorBoundary";
import ProductionErrorBoundary from "./components/ProductionErrorBoundary";
import NotificationProvider from "./components/notifications/NotificationProvider.jsx";
import "./styles/animations.css";

function App() {
  return (
    <ProductionErrorBoundary>
      <AuthErrorBoundary>
        <AuthProvider>
          <NotificationProvider>
            <SidebarProvider>
              <Routes />
            </SidebarProvider>
          </NotificationProvider>
        </AuthProvider>
      </AuthErrorBoundary>
    </ProductionErrorBoundary>
  );
}

export default App;
