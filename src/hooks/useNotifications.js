/**
 * Additional Notification Hooks for DocForge AI
 * 
 * Provides specialized hooks for common notification patterns
 * and integration with existing services.
 */

import { useCallback, useRef, useEffect } from 'react';
import { useNotifications as useNotificationContext } from '../contexts/NotificationContext.jsx';
import { NOTIFICATION_TYPES } from '../services/userNotificationService.js';

/**
 * Hook for async operation notifications
 * Provides methods to show loading, success, and error states for async operations
 * @returns {Object} Async notification methods
 */
export const useAsyncNotifications = () => {
  const { showLoading, showSuccess, showError, removeNotification } = useNotificationContext();
  const activeLoadingNotifications = useRef(new Map());

  const startLoading = useCallback((title, message, options = {}) => {
    const id = showLoading(title, message, options);
    if (id) {
      activeLoadingNotifications.current.set(options.operationId || id, id);
    }
    return id;
  }, [showLoading]);

  const finishLoading = useCallback((operationId, successTitle, successMessage, options = {}) => {
    const loadingId = activeLoadingNotifications.current.get(operationId);
    if (loadingId) {
      removeNotification(loadingId);
      activeLoadingNotifications.current.delete(operationId);
    }
    
    if (successTitle) {
      return showSuccess(successTitle, successMessage, options);
    }
  }, [removeNotification, showSuccess]);

  const failLoading = useCallback((operationId, errorTitle, errorMessage, options = {}) => {
    const loadingId = activeLoadingNotifications.current.get(operationId);
    if (loadingId) {
      removeNotification(loadingId);
      activeLoadingNotifications.current.delete(operationId);
    }
    
    return showError(errorTitle, errorMessage, options);
  }, [removeNotification, showError]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      activeLoadingNotifications.current.forEach(id => {
        removeNotification(id);
      });
      activeLoadingNotifications.current.clear();
    };
  }, [removeNotification]);

  return {
    startLoading,
    finishLoading,
    failLoading
  };
};

/**
 * Hook for project-related notifications
 * @returns {Object} Project notification methods
 */
export const useProjectNotifications = () => {
  const { showProjectNotification } = useNotificationContext();

  const notifyProjectCreated = useCallback((projectTitle) => {
    return showProjectNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Project Created',
      `"${projectTitle}" has been created successfully.`,
      { duration: 4000 }
    );
  }, [showProjectNotification]);

  const notifyProjectUpdated = useCallback((projectTitle) => {
    return showProjectNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Project Updated',
      `"${projectTitle}" has been updated successfully.`,
      { duration: 3000 }
    );
  }, [showProjectNotification]);

  const notifyProjectDeleted = useCallback((projectTitle) => {
    return showProjectNotification(
      NOTIFICATION_TYPES.INFO,
      'Project Deleted',
      `"${projectTitle}" has been deleted.`,
      { duration: 4000 }
    );
  }, [showProjectNotification]);

  const notifyProjectError = useCallback((operation, error) => {
    return showProjectNotification(
      NOTIFICATION_TYPES.ERROR,
      `Project ${operation} Failed`,
      error || 'An error occurred while processing your project.',
      { persistent: true }
    );
  }, [showProjectNotification]);

  return {
    notifyProjectCreated,
    notifyProjectUpdated,
    notifyProjectDeleted,
    notifyProjectError
  };
};

/**
 * Hook for template-related notifications
 * @returns {Object} Template notification methods
 */
export const useTemplateNotifications = () => {
  const { showTemplateNotification } = useNotificationContext();

  const notifyTemplateSaved = useCallback((templateName) => {
    return showTemplateNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Template Saved',
      `Template "${templateName}" has been saved successfully.`,
      { duration: 3000 }
    );
  }, [showTemplateNotification]);

  const notifyTemplateLoaded = useCallback((templateName) => {
    return showTemplateNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Template Applied',
      `Template "${templateName}" has been applied to your document.`,
      { duration: 3000 }
    );
  }, [showTemplateNotification]);

  const notifyTemplateError = useCallback((operation, error) => {
    return showTemplateNotification(
      NOTIFICATION_TYPES.ERROR,
      `Template ${operation} Failed`,
      error || 'An error occurred while processing the template.',
      { persistent: true }
    );
  }, [showTemplateNotification]);

  return {
    notifyTemplateSaved,
    notifyTemplateLoaded,
    notifyTemplateError
  };
};

/**
 * Hook for export-related notifications
 * @returns {Object} Export notification methods
 */
export const useExportNotifications = () => {
  const { showExportNotification } = useNotificationContext();

  const notifyExportStarted = useCallback((format) => {
    return showExportNotification(
      NOTIFICATION_TYPES.INFO,
      'Export Started',
      `Generating your document in ${format.toUpperCase()} format...`,
      { duration: 2000 }
    );
  }, [showExportNotification]);

  const notifyExportCompleted = useCallback((format, filename) => {
    return showExportNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Export Complete',
      `Your ${format.toUpperCase()} document "${filename}" is ready for download.`,
      { duration: 5000 }
    );
  }, [showExportNotification]);

  const notifyExportError = useCallback((format, error) => {
    return showExportNotification(
      NOTIFICATION_TYPES.ERROR,
      'Export Failed',
      error || `Failed to export document in ${format.toUpperCase()} format.`,
      { persistent: true }
    );
  }, [showExportNotification]);

  return {
    notifyExportStarted,
    notifyExportCompleted,
    notifyExportError
  };
};

/**
 * Hook for upload-related notifications
 * @returns {Object} Upload notification methods
 */
export const useUploadNotifications = () => {
  const { showUploadNotification } = useNotificationContext();

  const notifyUploadStarted = useCallback((filename) => {
    return showUploadNotification(
      NOTIFICATION_TYPES.INFO,
      'Upload Started',
      `Uploading "${filename}"...`,
      { duration: 2000 }
    );
  }, [showUploadNotification]);

  const notifyUploadCompleted = useCallback((filename) => {
    return showUploadNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Upload Complete',
      `"${filename}" has been uploaded successfully.`,
      { duration: 4000 }
    );
  }, [showUploadNotification]);

  const notifyUploadError = useCallback((filename, error) => {
    return showUploadNotification(
      NOTIFICATION_TYPES.ERROR,
      'Upload Failed',
      error || `Failed to upload "${filename}".`,
      { persistent: true }
    );
  }, [showUploadNotification]);

  return {
    notifyUploadStarted,
    notifyUploadCompleted,
    notifyUploadError
  };
};

/**
 * Hook for generation-related notifications (AI content, images)
 * @returns {Object} Generation notification methods
 */
export const useGenerationNotifications = () => {
  const { showGenerationNotification } = useNotificationContext();

  const notifyGenerationStarted = useCallback((type) => {
    return showGenerationNotification(
      NOTIFICATION_TYPES.INFO,
      'Generation Started',
      `Generating ${type}...`,
      { duration: 2000 }
    );
  }, [showGenerationNotification]);

  const notifyGenerationCompleted = useCallback((type) => {
    return showGenerationNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Generation Complete',
      `${type} has been generated successfully.`,
      { duration: 4000 }
    );
  }, [showGenerationNotification]);

  const notifyGenerationError = useCallback((type, error) => {
    return showGenerationNotification(
      NOTIFICATION_TYPES.ERROR,
      'Generation Failed',
      error || `Failed to generate ${type}.`,
      { persistent: true }
    );
  }, [showGenerationNotification]);

  return {
    notifyGenerationStarted,
    notifyGenerationCompleted,
    notifyGenerationError
  };
};

/**
 * Hook for font-related notifications
 * @returns {Object} Font notification methods
 */
export const useFontNotifications = () => {
  const { showFontNotification } = useNotificationContext();

  const notifyFontLoaded = useCallback((fontName) => {
    return showFontNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Font Loaded',
      `"${fontName}" is now available for use.`,
      { duration: 3000 }
    );
  }, [showFontNotification]);

  const notifyFontError = useCallback((fontName, error) => {
    return showFontNotification(
      NOTIFICATION_TYPES.WARNING,
      'Font Load Failed',
      error || `Failed to load font "${fontName}". Using fallback font.`,
      { duration: 5000 }
    );
  }, [showFontNotification]);

  return {
    notifyFontLoaded,
    notifyFontError
  };
};

/**
 * Hook for API error notifications
 * Integrates with existing userNotificationService patterns
 * @returns {Object} API error notification methods
 */
export const useApiErrorNotifications = () => {
  const { showError, showWarning } = useNotificationContext();

  const notifyApiError = useCallback((apiErrorResponse, serviceName) => {
    const { error } = apiErrorResponse;
    return showError(
      error.title,
      error.message,
      {
        serviceName,
        actions: error.actions,
        persistent: true,
        errorType: error.type
      }
    );
  }, [showError]);

  const notifyServiceUnavailable = useCallback((serviceName, feature) => {
    return showWarning(
      `${serviceName} Service Unavailable`,
      `${feature} is temporarily unavailable. Please try again later.`,
      {
        serviceName,
        duration: 8000
      }
    );
  }, [showWarning]);

  return {
    notifyApiError,
    notifyServiceUnavailable
  };
};

// Re-export the main hook from context for convenience
export { useNotifications, useToasts, useNotificationCenter } from '../contexts/NotificationContext.jsx';
