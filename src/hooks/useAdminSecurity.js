/**
 * Admin Security Hook
 * Provides comprehensive security features for admin operations
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import adminService from '../services/adminService';
import { prodLogger } from '../utils/prodLogger';
import { sessionManager } from '../utils/sessionManager';

export const useAdminSecurity = () => {
  const { user, isAuthenticated } = useAuth();
  const [securityStatus, setSecurityStatus] = useState({
    isAdmin: false,
    adminInfo: null,
    sessionValid: true,
    suspiciousActivity: false,
    lastSecurityCheck: null,
    loading: true
  });

  // Perform comprehensive security check
  const performSecurityCheck = useCallback(async () => {
    if (!user?.id || !isAuthenticated) {
      setSecurityStatus(prev => ({
        ...prev,
        isAdmin: false,
        adminInfo: null,
        sessionValid: false,
        loading: false
      }));
      return;
    }

    try {
      // Parallel security checks
      const [adminInfo, sessionValidation, suspiciousActivity] = await Promise.all([
        adminService.getCurrentUserAdminInfo(),
        adminService.validateAdminSession(),
        adminService.checkSuspiciousActivity(user.id)
      ]);

      setSecurityStatus({
        isAdmin: adminInfo?.is_admin || false,
        adminInfo,
        sessionValid: sessionValidation.valid,
        suspiciousActivity: suspiciousActivity.detected,
        suspiciousDetails: suspiciousActivity.details,
        sessionValidationReason: sessionValidation.reason,
        requiresReauth: sessionValidation.requiresReauth,
        lastSecurityCheck: new Date(),
        loading: false
      });

      // Log security check if admin
      if (adminInfo?.is_admin) {
        await adminService.logAdminActivity(
          'security_check',
          'security',
          'session_validation',
          {
            sessionValid: sessionValidation.valid,
            suspiciousActivity: suspiciousActivity.detected,
            suspiciousDetails: suspiciousActivity.details
          }
        );
      }

    } catch (error) {
      prodLogger.error('Admin security check failed:', error);
      setSecurityStatus(prev => ({
        ...prev,
        sessionValid: false,
        loading: false
      }));
    }
  }, [user?.id, isAuthenticated]);

  // Periodic security checks for admin users
  useEffect(() => {
    if (!user?.id || !isAuthenticated) return;

    // Initial check
    performSecurityCheck();

    // Set up periodic checks every 5 minutes for admin users
    const checkInterval = setInterval(async () => {
      const isAdmin = await adminService.isUserAdmin(user.id);
      if (isAdmin) {
        performSecurityCheck();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(checkInterval);
  }, [user?.id, isAuthenticated, performSecurityCheck]);

  // Force security recheck
  const recheckSecurity = useCallback(() => {
    setSecurityStatus(prev => ({ ...prev, loading: true }));
    performSecurityCheck();
  }, [performSecurityCheck]);

  // Handle security violations
  const handleSecurityViolation = useCallback(async (violationType, details = {}) => {
    try {
      await adminService.logAdminActivity(
        'security_violation',
        'security',
        violationType,
        {
          violationType,
          ...details,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          sessionInfo: sessionManager.getSessionInfo()
        }
      );

      // Update security status
      setSecurityStatus(prev => ({
        ...prev,
        suspiciousActivity: true,
        lastViolation: {
          type: violationType,
          timestamp: new Date(),
          details
        }
      }));

    } catch (error) {
      prodLogger.error('Failed to log security violation:', error);
    }
  }, []);

  // Enhanced session monitoring for admin users
  const startEnhancedMonitoring = useCallback(() => {
    if (!securityStatus.isAdmin) return;

    // Monitor for suspicious patterns
    const monitoringInterval = setInterval(() => {
      const sessionInfo = sessionManager.getSessionInfo();
      
      // Check for unusual activity patterns
      if (sessionInfo.activityCount > 1000) { // High activity threshold
        handleSecurityViolation('high_activity', {
          activityCount: sessionInfo.activityCount,
          sessionDuration: Date.now() - sessionInfo.startTime
        });
      }

      // Check for session anomalies
      if (sessionInfo.ipChanges > 3) { // Multiple IP changes
        handleSecurityViolation('multiple_ip_changes', {
          ipChanges: sessionInfo.ipChanges,
          currentIP: sessionInfo.currentIP
        });
      }

    }, 60 * 1000); // Check every minute

    return () => clearInterval(monitoringInterval);
  }, [securityStatus.isAdmin, handleSecurityViolation]);

  // Start enhanced monitoring when user becomes admin
  useEffect(() => {
    if (securityStatus.isAdmin && !securityStatus.loading) {
      const cleanup = startEnhancedMonitoring();
      return cleanup;
    }
  }, [securityStatus.isAdmin, securityStatus.loading, startEnhancedMonitoring]);

  return {
    // Security status
    ...securityStatus,
    
    // Actions
    recheckSecurity,
    handleSecurityViolation,
    
    // Utilities
    isSecure: securityStatus.isAdmin && securityStatus.sessionValid && !securityStatus.suspiciousActivity,
    needsAttention: securityStatus.suspiciousActivity || !securityStatus.sessionValid,
    canPerformAdminActions: securityStatus.isAdmin && securityStatus.sessionValid && !securityStatus.suspiciousActivity
  };
};

/**
 * Hook for admin activity monitoring
 */
export const useAdminActivityMonitor = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch recent admin activities
  const fetchActivities = useCallback(async (options = {}) => {
    setLoading(true);
    try {
      const activities = await adminService.getAdminActivityLogs({
        limit: 50,
        ...options
      });
      setActivities(activities);
    } catch (error) {
      prodLogger.error('Failed to fetch admin activities:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Real-time activity updates (simplified - in production use WebSocket)
  useEffect(() => {
    fetchActivities();
    
    // Refresh activities every 30 seconds
    const interval = setInterval(fetchActivities, 30 * 1000);
    return () => clearInterval(interval);
  }, [fetchActivities]);

  return {
    activities,
    loading,
    refreshActivities: fetchActivities
  };
};

/**
 * Hook for admin statistics and monitoring
 */
export const useAdminStats = () => {
  const [stats, setStats] = useState({
    totalAdmins: 0,
    recentActivity: 0,
    roleDistribution: {},
    securityAlerts: 0
  });
  const [loading, setLoading] = useState(false);

  const fetchStats = useCallback(async () => {
    setLoading(true);
    try {
      const adminStats = await adminService.getAdminStats();
      
      // Get security alerts (activities with security violations)
      const securityActivities = await adminService.getAdminActivityLogs({
        action: 'security_violation',
        limit: 100
      });

      setStats({
        ...adminStats,
        securityAlerts: securityActivities.length
      });
    } catch (error) {
      prodLogger.error('Failed to fetch admin stats:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
    
    // Refresh stats every 5 minutes
    const interval = setInterval(fetchStats, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [fetchStats]);

  return {
    stats,
    loading,
    refreshStats: fetchStats
  };
};

/**
 * Hook for admin role management
 */
export const useAdminRoleManagement = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const grantRole = useCallback(async (userId, role) => {
    setLoading(true);
    setError(null);
    try {
      await adminService.grantAdminRole(userId, role);
      return true;
    } catch (err) {
      setError(err.message);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const revokeRole = useCallback(async (userId) => {
    setLoading(true);
    setError(null);
    try {
      await adminService.revokeAdminRole(userId);
      return true;
    } catch (err) {
      setError(err.message);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    grantRole,
    revokeRole,
    loading,
    error,
    clearError
  };
};

export default useAdminSecurity;
