import { useState, useCallback, useEffect, useRef } from "react";

import { prodLogger } from '../utils/prodLogger.js';
/**
 * Text Overlay Editor Hook
 * Manages state for text overlay customizations with debounced updates,
 * undo functionality, and session persistence
 */
const useTextOverlayEditor = (template, documentData, options = {}) => {
  const {
    debounceMs = 300,
    enableSessionStorage = true,
    onPreviewUpdate = null,
  } = options;

  // State management
  const [customizations, setCustomizations] = useState({});
  const [isEditing, setIsEditing] = useState(false);
  const [undoStack, setUndoStack] = useState([]);
  const [isPreviewUpdating, setIsPreviewUpdating] = useState(false);
  const [error, setError] = useState(null);

  // Refs for debouncing
  const debounceTimeoutRef = useRef(null);
  const lastCustomizationsRef = useRef({});

  // Generate storage key for session persistence
  const getStorageKey = useCallback(() => {
    if (!template?.id) return null;
    return `text-overlay-customizations-${template.id}`;
  }, [template?.id]);

  // Load customizations from session storage
  useEffect(() => {
    if (!enableSessionStorage || !template?.id) return;

    const storageKey = getStorageKey();
    if (!storageKey) return;

    try {
      const stored = sessionStorage.getItem(storageKey);
      if (stored) {
        const parsedCustomizations = JSON.parse(stored);
        setCustomizations(parsedCustomizations);
        lastCustomizationsRef.current = parsedCustomizations;
      }
    } catch (error) {
      prodLogger.warn(
        "Failed to load customizations from session storage:",
        error
      );
    }
  }, [template?.id, enableSessionStorage, getStorageKey]);

  // Save customizations to session storage
  const saveToSessionStorage = useCallback(
    (newCustomizations) => {
      if (!enableSessionStorage) return;

      const storageKey = getStorageKey();
      if (!storageKey) return;

      try {
        sessionStorage.setItem(storageKey, JSON.stringify(newCustomizations));
      } catch (error) {
        prodLogger.warn(
          "Failed to save customizations to session storage:",
          error
        );
      }
    },
    [enableSessionStorage, getStorageKey]
  );

  // Debounced preview update
  const triggerPreviewUpdate = useCallback(
    (newCustomizations) => {
      if (!onPreviewUpdate) return;

      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Set new timeout
      debounceTimeoutRef.current = setTimeout(async () => {
        setIsPreviewUpdating(true);
        setError(null);

        try {
          await onPreviewUpdate(template, documentData, newCustomizations);
        } catch (err) {
          prodLogger.error("Preview update failed:", err);
          setError(err.message || "Failed to update preview");
        } finally {
          setIsPreviewUpdating(false);
        }
      }, debounceMs);
    },
    [onPreviewUpdate, template, documentData, debounceMs]
  );

  // Handle customization changes
  const handleCustomizationChange = useCallback(
    (newCustomizations) => {
      // Add to undo stack (limit to 10 entries)
      setUndoStack((prev) => {
        const newStack = [lastCustomizationsRef.current, ...prev].slice(0, 10);
        return newStack;
      });

      // Update state
      setCustomizations(newCustomizations);
      lastCustomizationsRef.current = newCustomizations;

      // Save to session storage
      saveToSessionStorage(newCustomizations);

      // Trigger preview update
      triggerPreviewUpdate(newCustomizations);
    },
    [saveToSessionStorage, triggerPreviewUpdate]
  );

  // Undo last change
  const handleUndo = useCallback(() => {
    if (undoStack.length === 0) return;

    const [previousState, ...restStack] = undoStack;
    setUndoStack(restStack);
    setCustomizations(previousState);
    lastCustomizationsRef.current = previousState;

    // Save to session storage
    saveToSessionStorage(previousState);

    // Trigger preview update
    triggerPreviewUpdate(previousState);
  }, [undoStack, saveToSessionStorage, triggerPreviewUpdate]);

  // Reset all customizations
  const handleReset = useCallback(() => {
    // Add current state to undo stack
    setUndoStack((prev) => [customizations, ...prev].slice(0, 10));

    const emptyCustomizations = {};
    setCustomizations(emptyCustomizations);
    lastCustomizationsRef.current = emptyCustomizations;

    // Clear session storage
    const storageKey = getStorageKey();
    if (storageKey) {
      try {
        sessionStorage.removeItem(storageKey);
      } catch (error) {
        prodLogger.warn("Failed to clear session storage:", error);
      }
    }

    // Trigger preview update
    triggerPreviewUpdate(emptyCustomizations);
  }, [customizations, getStorageKey, triggerPreviewUpdate]);

  // Reset specific overlay
  const handleResetOverlay = useCallback(
    (overlayId) => {
      if (!overlayId || !customizations[overlayId]) return;

      // Add current state to undo stack
      setUndoStack((prev) => [customizations, ...prev].slice(0, 10));

      const newCustomizations = { ...customizations };
      delete newCustomizations[overlayId];

      setCustomizations(newCustomizations);
      lastCustomizationsRef.current = newCustomizations;

      // Save to session storage
      saveToSessionStorage(newCustomizations);

      // Trigger preview update
      triggerPreviewUpdate(newCustomizations);
    },
    [customizations, saveToSessionStorage, triggerPreviewUpdate]
  );

  // Toggle editing mode
  const handleToggleEditing = useCallback(() => {
    setIsEditing((prev) => !prev);
  }, []);

  // Get merged overlay configuration (original + customizations)
  const getMergedOverlay = useCallback(
    (overlayId) => {
      if (!template?.text_overlays?.overlays) return null;

      const originalOverlay = template.text_overlays.overlays.find(
        (overlay) => overlay.id === overlayId
      );

      if (!originalOverlay) return null;

      const overlayCustomizations = customizations[overlayId] || {};

      return {
        ...originalOverlay,
        styling: {
          ...originalOverlay.styling,
          ...overlayCustomizations.styling,
        },
        position: {
          ...originalOverlay.position,
          ...overlayCustomizations.position,
        },
      };
    },
    [template, customizations]
  );

  // Get all merged overlays
  const getMergedOverlays = useCallback(() => {
    if (!template?.text_overlays?.overlays) return [];

    return template.text_overlays.overlays
      .map((overlay) => getMergedOverlay(overlay.id))
      .filter(Boolean);
  }, [template, getMergedOverlay]);

  // Check if any customizations exist
  const hasCustomizations = Object.keys(customizations).length > 0;

  // Check if specific overlay has customizations
  const hasOverlayCustomizations = useCallback(
    (overlayId) => {
      return Boolean(customizations[overlayId]);
    },
    [customizations]
  );

  // Get customization summary
  const getCustomizationSummary = useCallback(() => {
    const overlayIds = Object.keys(customizations);
    const totalCustomizations = overlayIds.reduce((count, overlayId) => {
      const overlay = customizations[overlayId];
      return (
        count +
        Object.keys(overlay.styling || {}).length +
        Object.keys(overlay.position || {}).length
      );
    }, 0);

    return {
      overlayCount: overlayIds.length,
      totalCustomizations,
      overlayIds,
    };
  }, [customizations]);

  // Clear customizations for specific template
  const clearCustomizationsForTemplate = useCallback((templateId) => {
    if (!templateId) return;

    const storageKey = `text-overlay-customizations-${templateId}`;
    try {
      sessionStorage.removeItem(storageKey);
      prodLogger.debug("🧹 Cleared customizations for template:", templateId);
    } catch (error) {
      prodLogger.warn(
        "Failed to clear customizations from session storage:",
        error
      );
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    customizations,
    isEditing,
    isPreviewUpdating,
    error,
    hasCustomizations,
    undoStack,

    // Actions
    handleCustomizationChange,
    handleToggleEditing,
    handleUndo,
    handleReset,
    handleResetOverlay,
    clearCustomizationsForTemplate,

    // Getters
    getMergedOverlay,
    getMergedOverlays,
    hasOverlayCustomizations,
    getCustomizationSummary,

    // Utils
    canUndo: undoStack.length > 0,
  };
};

export default useTextOverlayEditor;
