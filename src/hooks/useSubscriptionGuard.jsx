import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import subscriptionService from '../services/subscriptionService';
import usageTrackingService from '../services/usageTrackingService';
import { prodLogger } from '../utils/prodLogger';

/**
 * Custom hook for subscription-based feature protection and usage tracking
 */
export const useSubscriptionGuard = () => {
  const { user, profile } = useAuth();
  const [subscription, setSubscription] = useState(null);
  const [usage, setUsage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load subscription and usage data
  const loadSubscriptionData = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);

      const [subscriptionData, usageData] = await Promise.all([
        subscriptionService.getUserSubscription(user.id),
        usageTrackingService.getUserUsage(user.id)
      ]);

      setSubscription(subscriptionData);
      setUsage(usageData);
    } catch (err) {
      prodLogger.error('Failed to load subscription data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Load data on mount and when user changes
  useEffect(() => {
    loadSubscriptionData();
  }, [loadSubscriptionData]);

  /**
   * Check if user has access to a specific feature
   */
  const hasFeatureAccess = useCallback(async (feature) => {
    if (!user?.id) return false;

    try {
      return await subscriptionService.hasFeatureAccess(user.id, feature);
    } catch (err) {
      prodLogger.error('Error checking feature access:', err);
      return false;
    }
  }, [user?.id]);

  /**
   * Check if user can perform an action (with usage limits)
   */
  const canPerformAction = useCallback(async (actionType, amount = 1) => {
    if (!user?.id) return false;

    try {
      return await usageTrackingService.canPerformAction(user.id, actionType, amount);
    } catch (err) {
      prodLogger.error('Error checking action permission:', err);
      return false;
    }
  }, [user?.id]);

  /**
   * Track usage for an action
   */
  const trackUsage = useCallback(async (usageType, amount = 1, metadata = {}) => {
    if (!user?.id) return false;

    try {
      await usageTrackingService.trackUsage(user.id, usageType, amount, metadata);
      // Reload usage data after tracking
      const newUsage = await usageTrackingService.getUserUsage(user.id);
      setUsage(newUsage);
      return true;
    } catch (err) {
      prodLogger.error('Error tracking usage:', err);
      throw err; // Re-throw to let caller handle
    }
  }, [user?.id]);

  /**
   * Get upgrade suggestions based on current usage
   */
  const getUpgradeSuggestions = useCallback(() => {
    if (!usage || !subscription) return [];

    const suggestions = [];
    const currentTier = subscription.subscription_tier;

    // Check if user is hitting limits
    if (usage.limits_exceeded.documents && currentTier !== 'pro') {
      suggestions.push({
        type: 'documents',
        message: 'You\'ve reached your document limit. Upgrade for more documents.',
        recommendedTier: currentTier === 'free' ? 'basic' : currentTier === 'basic' ? 'standard' : 'pro'
      });
    }

    if (usage.limits_exceeded.ai_generations && currentTier !== 'pro') {
      suggestions.push({
        type: 'ai_generations',
        message: 'You\'ve reached your AI generation limit. Upgrade for more AI requests.',
        recommendedTier: currentTier === 'free' ? 'basic' : currentTier === 'basic' ? 'standard' : 'pro'
      });
    }

    if (usage.limits_exceeded.storage && currentTier !== 'pro') {
      suggestions.push({
        type: 'storage',
        message: 'You\'ve reached your storage limit. Upgrade for more storage space.',
        recommendedTier: currentTier === 'free' ? 'basic' : currentTier === 'basic' ? 'standard' : 'pro'
      });
    }

    // Check if user is approaching limits (>80%)
    if (usage.usage_percentages.documents > 80 && !usage.limits_exceeded.documents) {
      suggestions.push({
        type: 'documents_warning',
        message: 'You\'re approaching your document limit. Consider upgrading.',
        recommendedTier: currentTier === 'free' ? 'basic' : currentTier === 'basic' ? 'standard' : 'pro'
      });
    }

    return suggestions;
  }, [usage, subscription]);

  /**
   * Start subscription flow
   */
  const startSubscription = useCallback(async (tier, billingPeriod = 'monthly') => {
    if (!user?.id) return;

    try {
      await subscriptionService.startSubscription(user.id, tier, billingPeriod);
    } catch (err) {
      prodLogger.error('Error starting subscription:', err);
      throw err;
    }
  }, [user?.id]);

  /**
   * Upgrade subscription
   */
  const upgradeSubscription = useCallback(async (newTier) => {
    if (!user?.id) return;

    try {
      await subscriptionService.upgradeSubscription(user.id, newTier);
    } catch (err) {
      prodLogger.error('Error upgrading subscription:', err);
      throw err;
    }
  }, [user?.id]);

  /**
   * Cancel subscription
   */
    const cancelSubscription = async () => {
    try {
      setLoading(true);
      await subscriptionService.cancelSubscription(user.id);
      await updateSubscriptionStatus();
      toast.success('Subscription cancelled successfully');
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast.error('Failed to cancel subscription');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Open customer portal
   */
  const openCustomerPortal = useCallback(async () => {
    if (!user?.id) {
      console.error('No user ID available for customer portal');
      return;
    }

    console.log('Opening customer portal for user:', user.id);
    
    try {
      await subscriptionService.redirectToCustomerPortal(user.id);
    } catch (err) {
      console.error('Error opening customer portal:', err);
      prodLogger.error('Error opening customer portal:', err);
      // Don't re-throw the error, let the subscriptionService handle user feedback
    }
  }, [user?.id]);

  /**
   * Refresh subscription data
   */
  const refresh = useCallback(() => {
    loadSubscriptionData();
  }, [loadSubscriptionData]);

  return {
    // Data
    subscription,
    usage,
    loading,
    error,
    
    // Computed values
    isSubscribed: subscription?.is_active || false,
    isTrial: subscription?.is_trial || false,
    currentTier: subscription?.subscription_tier || 'free',
    upgradeSuggestions: getUpgradeSuggestions(),
    
    // Actions
    hasFeatureAccess,
    canPerformAction,
    trackUsage,
    startSubscription,
    upgradeSubscription,
    cancelSubscription,
    openCustomerPortal,
    refresh,
  };
};

/**
 * Higher-order component for protecting premium features
 */
export const withSubscriptionGuard = (WrappedComponent, requiredFeature) => {
  return function SubscriptionGuardedComponent(props) {
    const { hasFeatureAccess, loading, currentTier } = useSubscriptionGuard();
    const [hasAccess, setHasAccess] = useState(false);

    useEffect(() => {
      if (requiredFeature) {
        hasFeatureAccess(requiredFeature).then(setHasAccess);
      } else {
        setHasAccess(true);
      }
    }, [hasFeatureAccess, requiredFeature]);

    if (loading) {
      return <div className="flex items-center justify-center p-8">Loading...</div>;
    }

    if (requiredFeature && !hasAccess) {
      return (
        <div className="text-center p-8 bg-surface rounded-lg border border-border">
          <h3 className="text-lg font-semibold text-text-primary mb-2">
            Premium Feature
          </h3>
          <p className="text-text-secondary mb-4">
            This feature requires a {requiredFeature} subscription.
          </p>
          <p className="text-sm text-text-secondary">
            Current plan: {currentTier}
          </p>
        </div>
      );
    }

    return <WrappedComponent {...props} />;
  };
};

export default useSubscriptionGuard;
