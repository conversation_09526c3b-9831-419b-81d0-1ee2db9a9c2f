import { useState, useCallback, useEffect, useRef } from "react";
import { templateHasLogoOverlays, getLogoOverlays } from "../services/logoOverlayService.js";
import { prodLogger } from '../utils/prodLogger.js';

/**
 * Logo Overlay Editor Hook
 * Manages state for logo overlay customizations, mirroring useTextOverlayEditor patterns
 * Handles simplified logo operations: replace/change and delete/remove only
 */
const useLogoOverlayEditor = (template, documentData, options = {}) => {
  const {
    debounceMs = 300,
    enableSessionStorage = true,
    onPreviewUpdate = null,
  } = options;

  // State management (mirrors text overlay editor)
  const [logoCustomizations, setLogoCustomizations] = useState({});
  const [isEditing, setIsEditing] = useState(false);
  const [isPreviewUpdating, setIsPreviewUpdating] = useState(false);
  const [error, setError] = useState(null);
  const [undoStack, setUndoStack] = useState([]);

  // Refs for debouncing
  const debounceTimeoutRef = useRef(null);
  const templateIdRef = useRef(null);

  // Check if template supports logo overlays
  const hasLogoOverlays = templateHasLogoOverlays(template);
  const logoOverlays = hasLogoOverlays ? getLogoOverlays(template) : [];

  // Session storage key
  const getStorageKey = useCallback((templateId) => {
    return `logo-overlay-customizations-${templateId}`;
  }, []);

  // Load customizations from session storage
  useEffect(() => {
    if (!template?.id || !enableSessionStorage) return;

    try {
      const storageKey = getStorageKey(template.id);
      const stored = sessionStorage.getItem(storageKey);
      
      if (stored) {
        const parsedCustomizations = JSON.parse(stored);
        setLogoCustomizations(parsedCustomizations);
        prodLogger.debug('📂 Loaded logo customizations from session storage', {
          templateId: template.id,
          customizations: parsedCustomizations
        });
      }
    } catch (error) {
      prodLogger.warn('⚠️ Failed to load logo customizations from session storage:', error);
    }
  }, [template?.id, enableSessionStorage, getStorageKey]);

  // Save customizations to session storage
  const saveToSessionStorage = useCallback((customizations, templateId) => {
    if (!enableSessionStorage || !templateId) return;

    try {
      const storageKey = getStorageKey(templateId);
      sessionStorage.setItem(storageKey, JSON.stringify(customizations));
      prodLogger.debug('💾 Saved logo customizations to session storage', {
        templateId,
        customizations
      });
    } catch (error) {
      prodLogger.warn('⚠️ Failed to save logo customizations to session storage:', error);
    }
  }, [enableSessionStorage, getStorageKey]);

  // Debounced preview update
  const debouncedPreviewUpdate = useCallback((customizations) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    setIsPreviewUpdating(true);

    debounceTimeoutRef.current = setTimeout(() => {
      try {
        onPreviewUpdate?.(customizations);
        setError(null);
      } catch (err) {
        setError('Failed to update preview');
        prodLogger.error('❌ Error updating logo preview:', err);
      } finally {
        setIsPreviewUpdating(false);
      }
    }, debounceMs);
  }, [debounceMs, onPreviewUpdate]);

  // Handle logo change (replace/upload new logo)
  const handleLogoChange = useCallback((overlayId, logoData) => {
    if (!hasLogoOverlays) return;

    try {
      setError(null);
      
      // Add current state to undo stack
      setUndoStack(prev => [...prev, logoCustomizations]);

      const newCustomizations = {
        ...logoCustomizations,
        [overlayId]: {
          ...logoCustomizations[overlayId],
          selectedLogoId: logoData.id,
          logoData: logoData
        }
      };

      setLogoCustomizations(newCustomizations);
      saveToSessionStorage(newCustomizations, template.id);
      debouncedPreviewUpdate(newCustomizations);

      prodLogger.debug('🎨 Logo changed for overlay', {
        overlayId,
        logoId: logoData.id,
        logoName: logoData.name
      });
    } catch (error) {
      setError('Failed to change logo');
      prodLogger.error('❌ Error changing logo:', error);
    }
  }, [hasLogoOverlays, logoCustomizations, template?.id, saveToSessionStorage, debouncedPreviewUpdate]);

  // Handle logo deletion (remove logo from overlay)
  const handleLogoDelete = useCallback((overlayId) => {
    if (!hasLogoOverlays) return;

    try {
      setError(null);
      
      // Add current state to undo stack
      setUndoStack(prev => [...prev, logoCustomizations]);

      const newCustomizations = {
        ...logoCustomizations,
        [overlayId]: {
          ...logoCustomizations[overlayId],
          selectedLogoId: null,
          logoData: null
        }
      };

      setLogoCustomizations(newCustomizations);
      saveToSessionStorage(newCustomizations, template.id);
      debouncedPreviewUpdate(newCustomizations);

      prodLogger.debug('🗑️ Logo deleted from overlay', { overlayId });
    } catch (error) {
      setError('Failed to delete logo');
      prodLogger.error('❌ Error deleting logo:', error);
    }
  }, [hasLogoOverlays, logoCustomizations, template?.id, saveToSessionStorage, debouncedPreviewUpdate]);

  // Handle undo
  const handleUndo = useCallback(() => {
    if (undoStack.length === 0) return;

    try {
      const previousState = undoStack[undoStack.length - 1];
      const newUndoStack = undoStack.slice(0, -1);

      setLogoCustomizations(previousState);
      setUndoStack(newUndoStack);
      saveToSessionStorage(previousState, template.id);
      debouncedPreviewUpdate(previousState);

      prodLogger.debug('↶ Logo customizations undone');
    } catch (error) {
      setError('Failed to undo changes');
      prodLogger.error('❌ Error undoing logo changes:', error);
    }
  }, [undoStack, template?.id, saveToSessionStorage, debouncedPreviewUpdate]);

  // Handle reset (clear all customizations)
  const handleReset = useCallback(() => {
    try {
      setError(null);
      
      // Add current state to undo stack
      setUndoStack(prev => [...prev, logoCustomizations]);

      const emptyCustomizations = {};
      setLogoCustomizations(emptyCustomizations);
      saveToSessionStorage(emptyCustomizations, template.id);
      debouncedPreviewUpdate(emptyCustomizations);

      prodLogger.debug('🔄 Logo customizations reset');
    } catch (error) {
      setError('Failed to reset customizations');
      prodLogger.error('❌ Error resetting logo customizations:', error);
    }
  }, [logoCustomizations, template?.id, saveToSessionStorage, debouncedPreviewUpdate]);

  // Handle reset for specific overlay
  const handleResetOverlay = useCallback((overlayId) => {
    if (!hasLogoOverlays) return;

    try {
      setError(null);
      
      // Add current state to undo stack
      setUndoStack(prev => [...prev, logoCustomizations]);

      const newCustomizations = {
        ...logoCustomizations
      };
      delete newCustomizations[overlayId];

      setLogoCustomizations(newCustomizations);
      saveToSessionStorage(newCustomizations, template.id);
      debouncedPreviewUpdate(newCustomizations);

      prodLogger.debug('🔄 Logo overlay reset', { overlayId });
    } catch (error) {
      setError('Failed to reset overlay');
      prodLogger.error('❌ Error resetting logo overlay:', error);
    }
  }, [hasLogoOverlays, logoCustomizations, template?.id, saveToSessionStorage, debouncedPreviewUpdate]);

  // Clear customizations for template (when switching templates)
  const clearCustomizationsForTemplate = useCallback((templateId) => {
    if (!enableSessionStorage) return;

    try {
      const storageKey = getStorageKey(templateId);
      sessionStorage.removeItem(storageKey);
      prodLogger.debug('🗑️ Cleared logo customizations for template', { templateId });
    } catch (error) {
      prodLogger.warn('⚠️ Failed to clear logo customizations:', error);
    }
  }, [enableSessionStorage, getStorageKey]);

  // Check if overlay has customizations
  const hasOverlayCustomizations = useCallback((overlayId) => {
    return logoCustomizations[overlayId] && 
           (logoCustomizations[overlayId].selectedLogoId || logoCustomizations[overlayId].logoData);
  }, [logoCustomizations]);

  // Check if any customizations exist
  const hasCustomizations = Object.keys(logoCustomizations).some(overlayId => 
    hasOverlayCustomizations(overlayId)
  );

  // Get customization summary
  const getCustomizationSummary = useCallback(() => {
    const overlayCount = Object.keys(logoCustomizations).length;
    const logoCount = Object.values(logoCustomizations).filter(c => c.selectedLogoId).length;
    
    return {
      totalOverlays: logoOverlays.length,
      customizedOverlays: overlayCount,
      logosSelected: logoCount,
      hasChanges: hasCustomizations
    };
  }, [logoCustomizations, logoOverlays.length, hasCustomizations]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    logoCustomizations,
    isEditing,
    isPreviewUpdating,
    error,
    hasCustomizations,
    hasLogoOverlays,
    logoOverlays,
    undoStack,

    // Actions
    handleLogoChange,
    handleLogoDelete,
    handleUndo,
    handleReset,
    handleResetOverlay,
    clearCustomizationsForTemplate,

    // Getters
    hasOverlayCustomizations,
    getCustomizationSummary,

    // Utils
    canUndo: undoStack.length > 0,
  };
};

export default useLogoOverlayEditor;
