import React, { useState, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import DocumentWorkflowHeader from '../document-editor/components/DocumentWorkflowHeader';
import CreationWizard from './components/CreationWizard';

const DocumentCreator = () => {
  const { contentMargin } = useSidebar();

  const [documentData, setDocumentData] = useState({
    documentType: 'ebook',
    language: 'english',
    tone: 'academic',
    title: 'Untitled Document',
    format: 'pdf',
    baseline: 'scratch'
  });
  const [currentStep, setCurrentStep] = useState(1);



  const handleDocumentDataChange = useCallback((newData) => {
    setDocumentData(prev => ({ ...prev, ...newData }));
  }, []);

  const handleStepChange = useCallback((step) => {
    setCurrentStep(step);
  }, []);



  // Always show 'Generate' phase as active during document creation
  const currentPhase = 'Generate';

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />

      {/* Reused DocumentWorkflowHeader with creator mode */}
      <DocumentWorkflowHeader
        currentPhase={currentPhase}
        mode="creator"
        headerTitle="Creation Wizard"
        showStepCounter={true}
        currentStep={currentStep}
        totalSteps={8}
        onPhaseClick={null} // Disable phase navigation in creator mode
      />

      {/* Main Content - Clean Layout with top padding for fixed header */}
      <main className={`${contentMargin} pt-16 transition-all duration-300 ease-in-out`}>
        <div className="h-screen">
          <CreationWizard
            currentStep={currentStep}
            onStepChange={handleStepChange}
            documentData={documentData}
            onDocumentDataChange={handleDocumentDataChange}
          />
        </div>
      </main>
    </div>
  );
};

export default DocumentCreator;