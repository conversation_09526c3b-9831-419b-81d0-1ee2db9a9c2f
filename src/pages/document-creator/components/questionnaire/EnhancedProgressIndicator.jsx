import React from 'react';
import Icon from '../../../../components/AppIcon';

/**
 * EnhancedProgressIndicator - Advanced progress indicator for multi-step questionnaires
 * Features completion tracking, validation states, and interactive navigation
 */
const EnhancedProgressIndicator = ({
  steps = [],
  currentStep = 1,
  completedSteps = [],
  validationErrors = {},
  onStepClick = null,
  showLabels = true,
  showDescriptions = false,
  showProgress = true,
  showValidation = true,
  allowNavigation = true,
  orientation = 'horizontal', // 'horizontal', 'vertical'
  size = 'medium', // 'small', 'medium', 'large'
  variant = 'default', // 'default', 'minimal', 'detailed'
  className = '',
  progressColor = 'primary',
  completionPercentage = null,
}) => {
  // Calculate overall progress
  const calculateProgress = () => {
    if (completionPercentage !== null) {
      return completionPercentage;
    }
    return Math.round((completedSteps.length / steps.length) * 100);
  };

  // Check if step is accessible for navigation
  const isStepAccessible = (stepIndex) => {
    if (!allowNavigation) return false;
    
    // Current step and completed steps are always accessible
    if (stepIndex === currentStep - 1 || completedSteps.includes(stepIndex + 1)) {
      return true;
    }
    
    // Allow navigation to next step if current step is completed
    if (stepIndex === currentStep && completedSteps.includes(currentStep)) {
      return true;
    }
    
    return false;
  };

  // Get step status
  const getStepStatus = (stepIndex) => {
    const stepNumber = stepIndex + 1;
    
    if (completedSteps.includes(stepNumber)) {
      return 'completed';
    }
    
    if (stepNumber === currentStep) {
      return validationErrors[`step${stepNumber}`] ? 'error' : 'current';
    }
    
    if (stepNumber < currentStep) {
      return 'visited';
    }
    
    return 'pending';
  };

  // Get step icon
  const getStepIcon = (step, status, stepIndex) => {
    const stepNumber = stepIndex + 1;
    
    switch (status) {
      case 'completed':
        return <Icon name="Check" size={size === 'large' ? 16 : size === 'small' ? 12 : 14} />;
      case 'error':
        return <Icon name="AlertCircle" size={size === 'large' ? 16 : size === 'small' ? 12 : 14} />;
      case 'current':
        return step.icon ? (
          <Icon name={step.icon} size={size === 'large' ? 16 : size === 'small' ? 12 : 14} />
        ) : (
          <span className="font-semibold">{stepNumber}</span>
        );
      default:
        return <span className="font-medium">{stepNumber}</span>;
    }
  };

  // Get step colors
  const getStepColors = (status) => {
    switch (status) {
      case 'completed':
        return {
          bg: 'bg-green-500',
          text: 'text-white',
          border: 'border-green-500',
          label: 'text-green-600'
        };
      case 'error':
        return {
          bg: 'bg-red-500',
          text: 'text-white',
          border: 'border-red-500',
          label: 'text-red-600'
        };
      case 'current':
        return {
          bg: `bg-${progressColor}`,
          text: 'text-white',
          border: `border-${progressColor}`,
          label: `text-${progressColor}`
        };
      case 'visited':
        return {
          bg: 'bg-gray-400',
          text: 'text-white',
          border: 'border-gray-400',
          label: 'text-gray-600'
        };
      default:
        return {
          bg: 'bg-gray-200',
          text: 'text-gray-500',
          border: 'border-gray-200',
          label: 'text-gray-400'
        };
    }
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          circle: 'w-6 h-6 text-xs',
          text: 'text-xs',
          spacing: orientation === 'horizontal' ? 'space-x-2' : 'space-y-2'
        };
      case 'large':
        return {
          circle: 'w-10 h-10 text-base',
          text: 'text-base',
          spacing: orientation === 'horizontal' ? 'space-x-6' : 'space-y-6'
        };
      default:
        return {
          circle: 'w-8 h-8 text-sm',
          text: 'text-sm',
          spacing: orientation === 'horizontal' ? 'space-x-4' : 'space-y-4'
        };
    }
  };

  const sizeClasses = getSizeClasses();
  const progress = calculateProgress();

  // Handle step click
  const handleStepClick = (stepIndex) => {
    if (isStepAccessible(stepIndex) && onStepClick) {
      onStepClick(stepIndex + 1);
    }
  };

  // Render step item
  const renderStep = (step, stepIndex) => {
    const status = getStepStatus(stepIndex);
    const colors = getStepColors(status);
    const isAccessible = isStepAccessible(stepIndex);
    const hasError = validationErrors[`step${stepIndex + 1}`];

    return (
      <div
        key={step.id || stepIndex}
        className={`
          flex items-center
          ${orientation === 'vertical' ? 'flex-col text-center' : 'flex-row'}
        `}
      >
        {/* Step Circle */}
        <button
          type="button"
          onClick={() => handleStepClick(stepIndex)}
          disabled={!isAccessible}
          className={`
            ${sizeClasses.circle} ${colors.bg} ${colors.border}
            border-2 rounded-full flex items-center justify-center
            transition-all duration-200 relative z-10
            ${isAccessible 
              ? 'cursor-pointer hover:shadow-lg hover:scale-105' 
              : 'cursor-not-allowed'
            }
            ${colors.text}
          `}
          title={step.title}
        >
          {getStepIcon(step, status, stepIndex)}
        </button>

        {/* Step Labels */}
        {showLabels && (
          <div className={`
            ${orientation === 'vertical' ? 'mt-2' : 'ml-3'}
            ${variant === 'minimal' ? 'hidden sm:block' : ''}
          `}>
            <div className={`
              font-medium ${sizeClasses.text} ${colors.label}
              ${orientation === 'vertical' ? 'text-center' : 'text-left'}
            `}>
              {step.title}
            </div>
            
            {showDescriptions && step.description && (
              <div className={`
                text-xs text-gray-500 mt-1
                ${orientation === 'vertical' ? 'text-center' : 'text-left'}
              `}>
                {step.description}
              </div>
            )}
            
            {showValidation && hasError && (
              <div className="text-xs text-red-500 mt-1 flex items-center">
                <Icon name="AlertCircle" size={12} className="mr-1" />
                <span>Incomplete</span>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Render connection line
  const renderConnection = (stepIndex) => {
    if (stepIndex >= steps.length - 1) return null;
    
    const isCompleted = completedSteps.includes(stepIndex + 1);
    
    return (
      <div
        key={`connection-${stepIndex}`}
        className={`
          ${orientation === 'horizontal' 
            ? 'h-0.5 flex-1 mx-2' 
            : 'w-0.5 h-8 mx-auto my-2'
          }
          transition-all duration-500
          ${isCompleted ? `bg-${progressColor}` : 'bg-gray-200'}
        `}
      />
    );
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Overall Progress Bar */}
      {showProgress && (
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">
              Overall Progress
            </span>
            <span className="text-sm text-gray-500">
              {progress}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`bg-${progressColor} h-2 rounded-full transition-all duration-500`}
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      )}

      {/* Steps Container */}
      <div className={`
        flex items-center
        ${orientation === 'horizontal' 
          ? 'flex-row justify-between' 
          : 'flex-col justify-start'
        }
        ${sizeClasses.spacing}
      `}>
        {steps.map((step, index) => (
          <React.Fragment key={step.id || index}>
            {renderStep(step, index)}
            {orientation === 'horizontal' && renderConnection(index)}
          </React.Fragment>
        ))}
      </div>

      {/* Step Summary (for detailed variant) */}
      {variant === 'detailed' && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600">
            <div className="flex justify-between items-center">
              <span>
                Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.title}
              </span>
              <span className="text-xs">
                {completedSteps.length} completed
              </span>
            </div>
            {steps[currentStep - 1]?.description && (
              <p className="mt-2 text-xs text-gray-500">
                {steps[currentStep - 1].description}
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedProgressIndicator;
