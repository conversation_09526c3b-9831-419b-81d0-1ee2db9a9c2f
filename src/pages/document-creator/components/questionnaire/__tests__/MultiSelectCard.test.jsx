import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Create a simplified version of MultiSelectCard for testing
const MultiSelectCard = ({
  options = [],
  value = null,
  onChange,
  multiSelect = false,
  allowCustom = false,
  customPlaceholder = 'Enter custom option...',
  onCustomAdd = null,
  onOptionsUpdate = null,
}) => {
  const [customValue, setCustomValue] = React.useState('');
  const [showCustomInput, setShowCustomInput] = React.useState(false);

  const handleCustomAdd = () => {
    const trimmedValue = customValue.trim();
    if (!trimmedValue) return;

    const existingOption = options.find(option =>
      option.name.toLowerCase() === trimmedValue.toLowerCase()
    );

    if (existingOption) {
      if (multiSelect) {
        const currentValues = Array.isArray(value) ? value : [];
        if (!currentValues.includes(existingOption.id)) {
          const newValues = [...currentValues, existingOption.id];
          onChange(newValues);
        }
      } else {
        onChange(existingOption.id);
      }
    } else {
      const customOption = {
        id: `custom_${Date.now()}`,
        name: trimmedValue,
        description: 'Custom option',
        isCustom: true
      };

      if (onOptionsUpdate) {
        const updatedOptions = [...options, customOption];
        onOptionsUpdate(updatedOptions);
      }

      if (multiSelect) {
        const currentValues = Array.isArray(value) ? value : [];
        const newValues = [...currentValues, customOption.id];
        onChange(newValues);
      } else {
        onChange(customOption.id);
      }

      if (onCustomAdd) {
        onCustomAdd(trimmedValue);
      }
    }

    setCustomValue('');
    setShowCustomInput(false);
  };

  return (
    <div>
      {options.map((option) => (
        <div key={option.id}>
          {option.name}
          {option.isCustom && <span>Custom</span>}
        </div>
      ))}

      {allowCustom && (
        <div>
          {!showCustomInput ? (
            <button onClick={() => setShowCustomInput(true)}>
              Add custom option
            </button>
          ) : (
            <div>
              <input
                type="text"
                value={customValue}
                onChange={(e) => setCustomValue(e.target.value)}
                placeholder={customPlaceholder}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleCustomAdd();
                  } else if (e.key === 'Escape') {
                    setShowCustomInput(false);
                    setCustomValue('');
                  }
                }}
              />
              <button onClick={handleCustomAdd} disabled={!customValue.trim()}>
                Add
              </button>
              <button onClick={() => {
                setShowCustomInput(false);
                setCustomValue('');
              }}>
                Cancel
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

describe('MultiSelectCard Custom Option Functionality', () => {
  const mockOptions = [
    { id: 'option1', name: 'Option 1', description: 'First option' },
    { id: 'option2', name: 'Option 2', description: 'Second option' },
  ];

  const defaultProps = {
    options: mockOptions,
    value: [],
    onChange: jest.fn(),
    multiSelect: true,
    allowCustom: true,
    customPlaceholder: 'Enter custom option...',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('shows add custom option button when allowCustom is true', () => {
    render(<MultiSelectCard {...defaultProps} />);
    
    expect(screen.getByText('Add custom option')).toBeInTheDocument();
  });

  test('shows custom input when add custom option button is clicked', async () => {
    const user = userEvent.setup();
    render(<MultiSelectCard {...defaultProps} />);
    
    await user.click(screen.getByText('Add custom option'));
    
    expect(screen.getByPlaceholderText('Enter custom option...')).toBeInTheDocument();
    expect(screen.getByText('Add')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  test('adds custom option and selects it when Add button is clicked', async () => {
    const user = userEvent.setup();
    const mockOnChange = jest.fn();
    const mockOnOptionsUpdate = jest.fn();
    
    render(
      <MultiSelectCard 
        {...defaultProps} 
        onChange={mockOnChange}
        onOptionsUpdate={mockOnOptionsUpdate}
      />
    );
    
    // Click add custom option
    await user.click(screen.getByText('Add custom option'));
    
    // Type custom value
    const input = screen.getByPlaceholderText('Enter custom option...');
    await user.type(input, 'My Custom Option');
    
    // Click Add button
    await user.click(screen.getByText('Add'));
    
    // Verify onOptionsUpdate was called with new option
    expect(mockOnOptionsUpdate).toHaveBeenCalledWith(
      expect.arrayContaining([
        ...mockOptions,
        expect.objectContaining({
          name: 'My Custom Option',
          description: 'Custom option',
          isCustom: true
        })
      ])
    );
    
    // Verify onChange was called with the new option selected
    expect(mockOnChange).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.stringMatching(/^custom_\d+$/)
      ])
    );
  });

  test('adds custom option with Enter key', async () => {
    const user = userEvent.setup();
    const mockOnChange = jest.fn();
    const mockOnOptionsUpdate = jest.fn();
    
    render(
      <MultiSelectCard 
        {...defaultProps} 
        onChange={mockOnChange}
        onOptionsUpdate={mockOnOptionsUpdate}
      />
    );
    
    // Click add custom option
    await user.click(screen.getByText('Add custom option'));
    
    // Type custom value and press Enter
    const input = screen.getByPlaceholderText('Enter custom option...');
    await user.type(input, 'Custom via Enter{enter}');
    
    // Verify the option was added
    expect(mockOnOptionsUpdate).toHaveBeenCalled();
    expect(mockOnChange).toHaveBeenCalled();
  });

  test('cancels custom input with Escape key', async () => {
    const user = userEvent.setup();
    render(<MultiSelectCard {...defaultProps} />);
    
    // Click add custom option
    await user.click(screen.getByText('Add custom option'));
    
    // Type something and press Escape
    const input = screen.getByPlaceholderText('Enter custom option...');
    await user.type(input, 'Some text{escape}');
    
    // Verify input is hidden and add button is shown again
    expect(screen.queryByPlaceholderText('Enter custom option...')).not.toBeInTheDocument();
    expect(screen.getByText('Add custom option')).toBeInTheDocument();
  });

  test('does not add empty custom option', async () => {
    const user = userEvent.setup();
    const mockOnChange = jest.fn();
    const mockOnOptionsUpdate = jest.fn();
    
    render(
      <MultiSelectCard 
        {...defaultProps} 
        onChange={mockOnChange}
        onOptionsUpdate={mockOnOptionsUpdate}
      />
    );
    
    // Click add custom option
    await user.click(screen.getByText('Add custom option'));
    
    // Try to add empty value
    await user.click(screen.getByText('Add'));
    
    // Verify nothing was called
    expect(mockOnOptionsUpdate).not.toHaveBeenCalled();
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  test('selects existing option if custom value matches existing option name', async () => {
    const user = userEvent.setup();
    const mockOnChange = jest.fn();
    const mockOnOptionsUpdate = jest.fn();
    
    render(
      <MultiSelectCard 
        {...defaultProps} 
        onChange={mockOnChange}
        onOptionsUpdate={mockOnOptionsUpdate}
      />
    );
    
    // Click add custom option
    await user.click(screen.getByText('Add custom option'));
    
    // Type existing option name
    const input = screen.getByPlaceholderText('Enter custom option...');
    await user.type(input, 'Option 1');
    await user.click(screen.getByText('Add'));
    
    // Verify existing option was selected, not a new one created
    expect(mockOnOptionsUpdate).not.toHaveBeenCalled();
    expect(mockOnChange).toHaveBeenCalledWith(['option1']);
  });

  test('displays custom badge for custom options', () => {
    const customOptions = [
      ...mockOptions,
      { id: 'custom_123', name: 'Custom Option', description: 'Custom option', isCustom: true }
    ];
    
    render(
      <MultiSelectCard 
        {...defaultProps} 
        options={customOptions}
      />
    );
    
    expect(screen.getByText('Custom')).toBeInTheDocument();
  });
});
