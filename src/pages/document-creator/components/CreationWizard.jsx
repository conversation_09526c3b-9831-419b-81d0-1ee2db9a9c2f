import React, { useState, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

// Import static constants
import {
  DOCUMENT_TYPES,
  FORMAT_OPTIONS,
  BASELINE_OPTIONS,
  getSubtypesForDocumentType,
  hasSubtypes,
  getDefaultSubtype
} from '../constants/documentOptions';

import CustomDropdown from './CustomDropdown';

// Import new step components
import TopicSelectionStep from './steps/TopicSelectionStep';
import SubNicheSelectionStep from './steps/SubNicheSelectionStep';
import AudienceStep from './steps/AudienceStep';
import TitleSelectionStep from './steps/TitleSelectionStep';
import ToneVoiceStep from './steps/ToneVoiceStep';
import DocumentOutlineStep from './steps/DocumentOutlineStep';
import ContentDetailsStep from './steps/ContentDetailsStep';

// Import data structure
import { defaultQuestionnaireData } from '../utils/questionnaireDataStructure';

// Import DOCX extraction service and components
import { extractContentFromDocx, testMammothLibrary } from '../../../services/docxExtractionService';
import DocxUpload from '../../../components/ui/DocxUpload';
import DocxExtractionModal from '../../../components/ui/DocxExtractionModal';

// Import PDF extraction service and components
import { extractContentFromPdf } from '../../../services/pdfExtractionService';
import PdfUpload from '../../../components/ui/PdfUpload';
import PdfExtractionModal from '../../../components/ui/PdfExtractionModal';

// Import content preview component
import ExtractedContentSummary from '../../../components/ui/ExtractedContentSummary';

import { prodLogger } from '../../../utils/prodLogger.js';
const CreationWizard = ({ currentStep, onStepChange, documentData, onDocumentDataChange }) => {
  const location = useLocation();
  const shouldResetAIContent = location.state?.resetAIContent;

  // State for handling document generation from ContentDetailsStep
  const [generateDocumentFn, setGenerateDocumentFn] = useState(null);
  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);

  // Debug logging for generateDocumentFn state
  useEffect(() => {
    prodLogger.debug('🔍 CreationWizard: generateDocumentFn state changed:', {
      type: typeof generateDocumentFn,
      isNull: generateDocumentFn === null,
      currentStep,
      timestamp: new Date().toISOString()
    });
  }, [generateDocumentFn, currentStep]);

  // DOCX extraction state
  const [selectedDocxFile, setSelectedDocxFile] = useState(null);
  const [docxValidation, setDocxValidation] = useState({ isValid: false });
  const [isExtractingDocx, setIsExtractingDocx] = useState(false);
  const [docxExtractionModal, setDocxExtractionModal] = useState({
    isOpen: false,
    type: 'error', // 'error', 'success', 'loading'
    title: '',
    message: '',
    extractedData: null
  });
  const [docxExtractionProgress, setDocxExtractionProgress] = useState(0);

  // PDF extraction state
  const [selectedPdfFile, setSelectedPdfFile] = useState(null);
  const [pdfValidation, setPdfValidation] = useState({ isValid: false });
  const [isExtractingPdf, setIsExtractingPdf] = useState(false);
  const [pdfExtractionModal, setPdfExtractionModal] = useState({
    isOpen: false,
    type: 'error', // 'error', 'success', 'loading'
    title: '',
    message: '',
    extractedData: null
  });
  const [pdfExtractionProgress, setPdfExtractionProgress] = useState(0);

  // Test mammoth library on component mount
  useEffect(() => {
    const testResult = testMammothLibrary();
    // Mammoth library test completed
    if (!testResult.success) {
      prodLogger.error('Mammoth library test failed:', testResult.error);
    }
  }, []);

  // Initialize with enhanced data structure
  const [formData, setFormData] = useState(() => {
    const baseData = {
      ...defaultQuestionnaireData,
      // Merge with any existing document data
      ...documentData,
      // Ensure backward compatibility with old format
      documentPurpose: {
        ...defaultQuestionnaireData.documentPurpose,
        primaryType: documentData.documentType || 'ebook',
        format: documentData.format || 'pdf',
        baseline: documentData.baseline || 'scratch',
      },
      topicAndNiche: {
        ...defaultQuestionnaireData.topicAndNiche,
        mainTopic: documentData.niche || '',
        language: documentData.language || 'english',
      },
      audienceAnalysis: {
        ...defaultQuestionnaireData.audienceAnalysis,
        primaryAudience: documentData.targetAudience || '',
      },
      toneAndVoice: {
        ...defaultQuestionnaireData.toneAndVoice,
        toneOfVoice: documentData.tone || 'informative',
      }
    };

    // Clear AI-generated content if reset flag is set
    if (shouldResetAIContent) {
      baseData.titleSelection = {
        ...baseData.titleSelection,
        generatedTitles: [], // Clear cached titles
        selectedTitle: ''
      };
      baseData.topicAndNiche = {
        ...baseData.topicAndNiche,
        availableSubNiches: [] // Clear cached sub-niches
      };
      baseData.documentOutline = {
        ...baseData.documentOutline,
        generatedOutline: null // Clear cached outline
      };
    }

    return baseData;
  });

  const [stepValidation, setStepValidation] = useState({});

  // High-level phases are now imported from constants

  // Detailed steps for internal navigation (kept for step content rendering)
  const detailedSteps = [
    { id: 1, title: 'Generate', icon: 'Sparkles', description: 'Set up your document' },
    { id: 2, title: 'Topic', icon: 'Target', description: 'Enter your topic' },
    { id: 3, title: 'Sub-niches', icon: 'Grid3X3', description: 'AI-generated options' },
    { id: 4, title: 'Audience', icon: 'Users', description: 'Target audience' },
    { id: 5, title: 'Title', icon: 'Type', description: 'Select title' },
    { id: 6, title: 'Tone & Voice', icon: 'MessageSquare', description: 'Set tone' },
    { id: 7, title: 'Outline', icon: 'List', description: 'Document structure' },
    { id: 8, title: 'Content Details', icon: 'FileText', description: 'Final details' }
  ];



  // Document types are now imported from constants

  // Format options are now imported from constants

  // Baseline options are now imported from constants



  // Enhanced input change handler for nested objects
  const handleInputChange = (field, value) => {
    setFormData(prev => {
      const newData = { ...prev };

      // Handle nested field paths (e.g., 'topicAndNiche.mainTopic')
      if (field.includes('.')) {
        const fieldParts = field.split('.');
        let current = newData;

        // Navigate to the parent object
        for (let i = 0; i < fieldParts.length - 1; i++) {
          if (!current[fieldParts[i]]) {
            current[fieldParts[i]] = {};
          }
          current = current[fieldParts[i]];
        }

        // Set the final value
        current[fieldParts[fieldParts.length - 1]] = value;
      } else {
        newData[field] = value;
      }

      return newData;
    });
  };

  // Handle document type changes with subtype reset
  const handleDocumentTypeChange = (documentType) => {
    handleInputChange('documentPurpose.primaryType', documentType);

    // Reset subtype when document type changes
    if (hasSubtypes(documentType)) {
      const defaultSubtype = getDefaultSubtype(documentType);
      handleInputChange('documentPurpose.subType', defaultSubtype);
    } else {
      handleInputChange('documentPurpose.subType', '');
    }
  };

  // Handle step validation updates - memoized to prevent infinite re-renders
  const handleStepValidation = useCallback((stepId, isValid) => {
    setStepValidation(prev => ({
      ...prev,
      [stepId]: isValid
    }));
  }, []);

  // Memoized validation change handlers for each step to prevent inline function recreation
  const handleStep2Validation = useCallback((isValid) => handleStepValidation(2, isValid), [handleStepValidation]);
  const handleStep3Validation = useCallback((isValid) => handleStepValidation(3, isValid), [handleStepValidation]);
  const handleStep4Validation = useCallback((isValid) => handleStepValidation(4, isValid), [handleStepValidation]);
  const handleStep5Validation = useCallback((isValid) => handleStepValidation(5, isValid), [handleStepValidation]);
  const handleStep6Validation = useCallback((isValid) => handleStepValidation(6, isValid), [handleStepValidation]);
  const handleStep7Validation = useCallback((isValid) => handleStepValidation(7, isValid), [handleStepValidation]);
  const handleStep8Validation = useCallback((isValid) => handleStepValidation(8, isValid), [handleStepValidation]);

  // Handle document generation function from ContentDetailsStep
  const handleGenerateDocumentCallback = useCallback((generateFn, isGenerating) => {
    prodLogger.debug('🔧 handleGenerateDocumentCallback called with:', {
      generateFn: typeof generateFn,
      isGenerating,
      currentStep
    });

    // Store the generate function without causing re-renders
    setGenerateDocumentFn(() => generateFn);

    // Only update isGenerating if it's different from current state
    // to prevent unnecessary re-renders
    setIsGeneratingDocument(prevState => {
      if (prevState === isGenerating) return prevState;
      return isGenerating;
    });
  }, [currentStep]);

  // Handle DOCX file selection and extraction
  const handleDocxFileSelect = async (file) => {
    if (!file) {
      setSelectedDocxFile(null);
      return;
    }

    setSelectedDocxFile(file);

    // Start extraction automatically when file is selected
    await handleDocxExtraction(file);
  };

  // Handle DOCX extraction with enhanced feedback
  const handleDocxExtraction = async (file) => {
    if (!file) {
      setDocxExtractionModal({
        isOpen: true,
        type: 'error',
        title: 'No File Selected',
        message: 'Please select a DOCX file before extracting content.',
        extractedData: null
      });
      return;
    }

    setIsExtractingDocx(true);
    setDocxExtractionProgress(0);

    // Show loading modal
    setDocxExtractionModal({
      isOpen: true,
      type: 'loading',
      title: 'Processing DOCX File...',
      message: 'Please wait while we extract content from your document.',
      extractedData: null
    });

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setDocxExtractionProgress(prev => Math.min(prev + 15, 90));
    }, 200);

    try {
      const result = await extractContentFromDocx(file);
      clearInterval(progressInterval);
      setDocxExtractionProgress(100);

      if (result.success) {
        // Update form data with extracted content
        handleInputChange('documentPurpose.importedContent', result.data);

        // Auto-populate fields based on extracted content
        if (result.data.originalTitle) {
          handleInputChange('topicAndNiche.mainTopic', result.data.originalTitle);
        }

        // Auto-select document type based on inference
        if (result.data.documentType) {
          handleInputChange('documentPurpose.primaryType', result.data.documentType);
        }

        // Auto-populate additional fields based on content analysis
        autoPopulateFromDocxContent(result.data);

        // Show success modal
        setDocxExtractionModal({
          isOpen: true,
          type: 'success',
          title: 'DOCX Content Extracted Successfully!',
          message: 'We\'ve successfully extracted content from your DOCX file.',
          extractedData: result.data
        });
      } else {
        // Show error modal with helpful information
        setDocxExtractionModal({
          isOpen: true,
          type: 'error',
          title: 'DOCX Extraction Failed',
          message: result.data.extractionError || 'Unable to extract content from the DOCX file.',
          extractedData: null
        });
      }
    } catch (error) {
      clearInterval(progressInterval);
      prodLogger.error('DOCX extraction error:', error);

      setDocxExtractionModal({
        isOpen: true,
        type: 'error',
        title: 'Extraction Error',
        message: 'An unexpected error occurred while processing the DOCX file.',
        extractedData: null
      });
    } finally {
      setIsExtractingDocx(false);
      setDocxExtractionProgress(0);
    }
  };

  // Handle DOCX modal actions
  const handleDocxModalClose = () => {
    setDocxExtractionModal(prev => ({ ...prev, isOpen: false }));
  };

  const handleDocxRetryExtraction = () => {
    handleDocxModalClose();
    // Clear current file and allow user to select a new one
    setSelectedDocxFile(null);
  };

  const handleDocxContinueToNextStep = () => {
    handleDocxModalClose();
    // Auto-advance to next step if extraction was successful
    if (docxExtractionModal.type === 'success') {
      handleNext();
    }
  };

  // Handle PDF file selection and extraction
  const handlePdfFileSelect = async (file) => {
    if (!file) {
      setSelectedPdfFile(null);
      return;
    }

    setSelectedPdfFile(file);

    // Start extraction automatically when file is selected
    await handlePdfExtraction(file);
  };

  // Handle PDF extraction with enhanced feedback
  const handlePdfExtraction = async (file) => {
    if (!file) {
      setPdfExtractionModal({
        isOpen: true,
        type: 'error',
        title: 'No File Selected',
        message: 'Please select a PDF file to extract content from.',
        extractedData: null
      });
      return;
    }

    prodLogger.debug('🔄 Starting PDF extraction for:', file.name);
    setIsExtractingPdf(true);
    setPdfExtractionProgress(0);

    // Show loading modal
    setPdfExtractionModal({
      isOpen: true,
      type: 'loading',
      title: 'Extracting PDF Content',
      message: 'Please wait while we extract text content from your PDF file...',
      extractedData: null
    });

    // Simulate progress updates with better logic
    const progressInterval = setInterval(() => {
      setPdfExtractionProgress(prev => {
        if (prev >= 90) return prev; // Stop at 90% until completion
        return Math.min(prev + Math.random() * 10 + 5, 90); // More realistic progress
      });
    }, 300); // Slower updates for better UX

    try {
      const result = await extractContentFromPdf(file);
      clearInterval(progressInterval);
      setPdfExtractionProgress(100);

      if (result.success) {
        // Update form data with extracted content
        handleInputChange('documentPurpose.importedContent', result.data);

        // Auto-populate fields based on extracted content
        if (result.data.originalTitle) {
          handleInputChange('topicAndNiche.mainTopic', result.data.originalTitle);
        }

        // Auto-select document type based on inference
        if (result.data.documentType) {
          handleInputChange('documentPurpose.primaryType', result.data.documentType);
        }

        // Show success modal
        prodLogger.debug('✅ PDF extraction successful');
        setPdfExtractionModal({
          isOpen: true,
          type: 'success',
          title: 'Content Extracted Successfully!',
          message: 'We\'ve successfully extracted content from your PDF file.',
          extractedData: result.data
        });
      } else {
        prodLogger.debug('❌ PDF extraction failed:', result.data.extractionError);
        // Show error modal with helpful information
        setPdfExtractionModal({
          isOpen: true,
          type: 'error',
          title: 'Content Extraction Failed',
          message: result.data.extractionError || 'Unable to extract content from the provided PDF file.',
          extractedData: null
        });
      }
    } catch (error) {
      clearInterval(progressInterval);
      prodLogger.error('PDF extraction error:', error);

      setPdfExtractionModal({
        isOpen: true,
        type: 'error',
        title: 'Extraction Error',
        message: 'An unexpected error occurred while processing your PDF file. Please try again.',
        extractedData: null
      });
    } finally {
      setIsExtractingPdf(false);
      setPdfExtractionProgress(0);
    }
  };

  // Handle PDF modal actions with improved error recovery
  const handlePdfModalClose = () => {
    setPdfExtractionModal(prev => ({ ...prev, isOpen: false }));
    
    // Reset extraction state on close
    if (pdfExtractionModal.type === 'error') {
      setIsExtractingPdf(false);
      setPdfExtractionProgress(0);
      // Clear any partially extracted data on error
      if (formData.documentPurpose?.importedContent?.extractionStatus !== 'success') {
        handleInputChange('documentPurpose.importedContent', null);
      }
    }
  };

  // Add retry functionality for PDF extraction
  const handlePdfRetry = async () => {
    if (selectedPdfFile) {
      prodLogger.debug('🔄 Retrying PDF extraction...');
      handlePdfModalClose();
      
      // Small delay before retry
      setTimeout(() => {
        handlePdfExtraction(selectedPdfFile);
      }, 500);
    }
  };

  const handlePdfRetryExtraction = () => {
    handlePdfModalClose();
    // Clear current file and allow user to select a new one
    setSelectedPdfFile(null);
  };

  const handlePdfContinueToNextStep = () => {
    handlePdfModalClose();
    // Auto-advance to next step if extraction was successful
    if (pdfExtractionModal.type === 'success') {
      handleNext();
    }
  };

  // Auto-populate wizard fields based on DOCX content analysis
  const autoPopulateFromDocxContent = (extractedData) => {
    const { extractedContent, headings, wordCount, documentType } = extractedData;
    const contentLower = extractedContent.toLowerCase();

    // Infer audience level based on content complexity
    let knowledgeLevel = 'intermediate';
    if (contentLower.includes('beginner') || contentLower.includes('introduction') || contentLower.includes('basics')) {
      knowledgeLevel = 'beginner';
    } else if (contentLower.includes('advanced') || contentLower.includes('expert') || contentLower.includes('professional')) {
      knowledgeLevel = 'advanced';
    }
    handleInputChange('audienceAnalysis.knowledgeLevel', knowledgeLevel);

    // Infer topic depth based on word count and structure
    let topicDepth = 'comprehensive';
    if (wordCount < 2000) {
      topicDepth = 'overview';
    } else if (wordCount < 5000) {
      topicDepth = 'detailed';
    } else if (wordCount > 10000) {
      topicDepth = 'exhaustive';
    }
    handleInputChange('topicAndNiche.topicDepth', topicDepth);

    // Infer document length based on word count
    let targetLength = 'medium';
    if (wordCount < 3000) {
      targetLength = 'short';
    } else if (wordCount > 8000) {
      targetLength = 'long';
    }
    handleInputChange('contentDetails.targetLength', targetLength);

    // Set urgency based on document type
    let urgency = 'normal';
    if (documentType === 'business') {
      urgency = 'urgent';
    }
    handleInputChange('documentPurpose.urgency', urgency);

    // Auto-populate focus areas based on headings
    if (headings && headings.length > 0) {
      const focusAreas = headings
        .filter(h => h.level <= 2) // Only main headings
        .map(h => h.text)
        .slice(0, 5); // Limit to 5 focus areas

      if (focusAreas.length > 0) {
        handleInputChange('topicAndNiche.focusAreas', focusAreas);
      }
    }

    // Infer tone based on content style
    let toneOfVoice = 'informative';
    if (contentLower.includes('you') || contentLower.includes('your')) {
      toneOfVoice = 'conversational';
    } else if (documentType === 'academic') {
      toneOfVoice = 'academic';
    } else if (documentType === 'business') {
      toneOfVoice = 'professional';
    }
    handleInputChange('toneAndVoice.toneOfVoice', toneOfVoice);

    // Set perspective based on content analysis
    let perspectiveVoice = 'third-person';
    const firstPersonCount = (contentLower.match(/\b(i|we|my|our)\b/g) || []).length;
    const secondPersonCount = (contentLower.match(/\b(you|your)\b/g) || []).length;

    if (firstPersonCount > secondPersonCount && firstPersonCount > 10) {
      perspectiveVoice = 'first-person';
    } else if (secondPersonCount > firstPersonCount && secondPersonCount > 10) {
      perspectiveVoice = 'second-person';
    }
    handleInputChange('toneAndVoice.perspectiveVoice', perspectiveVoice);
  };

  // Handle content updates from preview component
  const handleContentUpdate = (updatedData) => {
    handleInputChange('documentPurpose.importedContent', updatedData);
  };

  // Auto-save to parent component
  useEffect(() => {
    onDocumentDataChange?.(formData);
  }, [formData]);

  const validateCurrentStep = () => {
    // Use the step validation state if available
    if (stepValidation[currentStep] !== undefined) {
      return stepValidation[currentStep];
    }

    // Fallback validation for backward compatibility
    switch (currentStep) {
      case 1:
        const hasRequiredFields = formData.documentPurpose?.primaryType && formData.documentPurpose?.format && formData.documentPurpose?.baseline;
        const hasRequiredSubtype = !hasSubtypes(formData.documentPurpose?.primaryType) || formData.documentPurpose?.subType;
        return hasRequiredFields && hasRequiredSubtype;
      case 2:
        return formData.topicAndNiche?.mainTopic && formData.topicAndNiche?.language;
      case 3:
        return formData.topicAndNiche?.subNiches?.length > 0 || formData.topicAndNiche?.customSubNiche;
      case 4:
        return formData.audienceAnalysis?.primaryAudience;
      case 5:
        return formData.titleSelection?.selectedTitle;
      case 6:
        return formData.toneAndVoice?.toneOfVoice && formData.toneAndVoice?.selectedTheme;
      case 7:
        return formData.documentOutline?.approved; // Document outline step
      case 8:
        return true; // Content details step
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (validateCurrentStep() && currentStep < detailedSteps.length) {
      onStepChange(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      onStepChange(currentStep - 1);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="text-center space-y-16 max-w-4xl mx-auto px-4">
            {/* Header - Designrr Style */}
            <div className="space-y-12">
              <h2 className="text-3xl md:text-4xl font-bold text-text-primary">Project creator</h2>

              {/* First Line - Document Type and Format */}
              <div className="text-lg md:text-xl leading-relaxed text-text-secondary">
                <div className="flex flex-wrap items-center justify-center gap-2">
                  <span>I'd like to create an</span>
                  <CustomDropdown
                    value={formData.documentPurpose?.primaryType || 'ebook'}
                    onChange={handleDocumentTypeChange}
                    options={DOCUMENT_TYPES}
                    placeholder="Document Type"
                  />
                  {/* Conditional Subtype Selection */}
                  {hasSubtypes(formData.documentPurpose?.primaryType) && (
                    <>
                      <span>specifically an</span>
                      <CustomDropdown
                        value={formData.documentPurpose?.subType || getDefaultSubtype(formData.documentPurpose?.primaryType)}
                        onChange={(value) => handleInputChange('documentPurpose.subType', value)}
                        options={getSubtypesForDocumentType(formData.documentPurpose?.primaryType)}
                        placeholder="Select Subtype"
                      />
                    </>
                  )}
                  <span>to be published as</span>
                  <CustomDropdown
                    value={formData.documentPurpose?.format || 'pdf'}
                    onChange={(value) => handleInputChange('documentPurpose.format', value)}
                    options={FORMAT_OPTIONS}
                    placeholder="PDF"
                  />
                </div>
              </div>

              {/* Second Line - Baseline Selection */}
              <div className="text-lg md:text-xl leading-relaxed text-text-secondary">
                <div className="flex flex-wrap items-center justify-center gap-2">
                  <span>I'd like to</span>
                  <CustomDropdown
                    value={formData.documentPurpose?.baseline || 'scratch'}
                    onChange={(value) => handleInputChange('documentPurpose.baseline', value)}
                    options={BASELINE_OPTIONS}
                    placeholder="select"
                  />
                  <span>to be used as my baseline</span>
                </div>
              </div>

              {/* DOCX Upload - Show when import-docx is selected */}
              {formData.documentPurpose?.baseline === 'import-docx' && (
                <div className="max-w-2xl mx-auto space-y-6">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-text-primary mb-4">
                      Import from DOCX Document
                    </h3>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Upload your DOCX file:
                        </label>
                        <DocxUpload
                          onFileSelect={handleDocxFileSelect}
                          onValidationChange={setDocxValidation}
                          disabled={isExtractingDocx}
                          showPreview={true}
                        />
                      </div>

                      {/* Show extracted content preview if available */}
                      {formData.documentPurpose?.importedContent?.extractedContent && (
                        <ExtractedContentSummary
                          extractedData={formData.documentPurpose.importedContent}
                          onEdit={handleContentUpdate}
                          className="mt-4"
                          showActions={false}
                        />
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* PDF Upload - Show when import-pdf is selected */}
              {formData.documentPurpose?.baseline === 'import-pdf' && (
                <div className="max-w-2xl mx-auto space-y-6">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-text-primary mb-4">
                      Import from PDF Document
                    </h3>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Upload your PDF file:
                        </label>
                        <PdfUpload
                          onFileSelect={handlePdfFileSelect}
                          onValidationChange={setPdfValidation}
                          disabled={isExtractingPdf}
                          showPreview={true}
                        />
                      </div>

                      {/* Show extracted content preview if available */}
                      {formData.documentPurpose?.importedContent?.extractedContent && (
                        <ExtractedContentSummary
                          extractedData={formData.documentPurpose.importedContent}
                          onEdit={handleContentUpdate}
                          className="mt-4"
                          showActions={false}
                        />
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <TopicSelectionStep
            formData={formData}
            onInputChange={handleInputChange}
            onValidationChange={handleStep2Validation}
          />
        );

      case 3:
        return (
          <SubNicheSelectionStep
            formData={formData}
            onInputChange={handleInputChange}
            onValidationChange={handleStep3Validation}
          />
        );

      case 4:
        return (
          <AudienceStep
            formData={formData}
            onInputChange={handleInputChange}
            onValidationChange={handleStep4Validation}
          />
        );

      case 5:
        return (
          <TitleSelectionStep
            formData={formData}
            onInputChange={handleInputChange}
            onValidationChange={handleStep5Validation}
          />
        );

      case 6:
        return (
          <ToneVoiceStep
            formData={formData}
            onInputChange={handleInputChange}
            onValidationChange={handleStep6Validation}
          />
        );

      case 7:
        return (
          <DocumentOutlineStep
            formData={formData}
            onInputChange={handleInputChange}
            onValidationChange={handleStep7Validation}
          />
        );

      case 8:
        prodLogger.debug('🎯 Rendering ContentDetailsStep (Step 8) with props:', {
          formData: !!formData,
          onGenerateDocument: typeof handleGenerateDocumentCallback,
          currentStep,
          documentType: formData.documentPurpose?.primaryType,
          currentGenerateDocumentFn: typeof generateDocumentFn,
          timestamp: new Date().toISOString()
        });
        return (
          <ContentDetailsStep
            formData={formData}
            onInputChange={handleInputChange}
            onValidationChange={handleStep8Validation}
            onGenerateDocument={handleGenerateDocumentCallback}
            onGenerate={(data) => {
              // Generate document with collected data
              // Here you would integrate with your existing document generation API
              alert(`Creating ${data.documentPurpose?.primaryType || 'document'} in ${data.documentPurpose?.format || 'PDF'} format!\n\nTopic: ${data.topicAndNiche?.mainTopic || 'Not specified'}\nAudience: ${data.audienceAnalysis?.primaryAudience || 'Not specified'}\nTitle: ${data.titleSelection?.selectedTitle || 'Not specified'}`);
            }}
          />
        );

      case 9:
        return (
          <div className="space-y-8 max-w-4xl mx-auto px-4 text-center">
            <div className="space-y-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <Icon name="Check" size={32} className="text-green-600" />
              </div>
              <h2 className="text-2xl md:text-3xl font-bold text-text-primary">
                Document Generated Successfully!
              </h2>
              <p className="text-text-secondary text-base md:text-lg max-w-2xl mx-auto">
                Your document has been created based on your specifications. You can now download it or make further edits.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md mx-auto">
              <Button
                variant="primary"
                iconName="Download"
                iconPosition="left"
                className="w-full"
              >
                Download PDF
              </Button>
              <Button
                variant="secondary"
                iconName="Edit3"
                iconPosition="left"
                className="w-full"
              >
                Edit Document
              </Button>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center space-y-8">
            <h2 className="text-2xl font-bold text-text-primary">Step {currentStep}</h2>
            <p className="text-text-secondary">This step is under development.</p>
          </div>
        );
    }
  };

  return (
    <div className="h-full flex flex-col bg-background">



      {/* DOCX Extraction Modal */}
      <DocxExtractionModal
        isOpen={docxExtractionModal.isOpen}
        type={docxExtractionModal.type}
        title={docxExtractionModal.title}
        message={docxExtractionModal.message}
        extractedData={docxExtractionModal.extractedData}
        onClose={handleDocxModalClose}
        onRetry={handleDocxRetryExtraction}
        onContinue={handleDocxContinueToNextStep}
        progress={docxExtractionProgress}
      />

      {/* PDF Extraction Modal */}
      <PdfExtractionModal
        isOpen={pdfExtractionModal.isOpen}
        type={pdfExtractionModal.type}
        title={pdfExtractionModal.title}
        message={pdfExtractionModal.message}
        extractedData={pdfExtractionModal.extractedData}
        onClose={handlePdfModalClose}
        onRetry={handlePdfRetry}
        onContinue={handlePdfModalClose}
        progress={pdfExtractionProgress}
      />

      {/* Main Content Area - Clean Centered Layout */}
      <div className="flex-1 flex items-center justify-center px-4 md:px-8 py-8 md:py-16">
        <div className="w-full max-w-5xl">
          {/* Step Content - No Card Background for Cleaner Look */}
          <div className="mb-8 md:mb-16 animate-in fade-in duration-500">
            {renderStepContent()}
          </div>

          {/* Navigation Buttons - Designrr Style */}
          <div className="flex justify-center space-x-4">
            {currentStep > 1 && (
              <Button
                variant="ghost"
                onClick={handlePrevious}
                iconName="ChevronLeft"
                iconPosition="left"
                className="px-6 py-3 text-base rounded-lg text-text-secondary hover:text-text-primary"
              >
                Back
              </Button>
            )}

            {currentStep < detailedSteps.length ? (
              <Button
                variant="primary"
                onClick={handleNext}
                disabled={!validateCurrentStep()}
                iconName="ArrowRight"
                iconPosition="right"
                className="px-8 py-3 text-base rounded-lg bg-primary hover:bg-primary/90 text-white font-medium shadow-sm hover:shadow-md transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Continue
              </Button>
            ) : (
              <Button
                variant="primary"
                onClick={() => {
                  prodLogger.debug('🚀 BUTTON CLICKED - Debug info:', {
                    generateDocumentFn: typeof generateDocumentFn,
                    isNull: generateDocumentFn === null,
                    currentStep,
                    detailedStepsLength: detailedSteps.length,
                    timestamp: new Date().toISOString(),
                    stackTrace: new Error().stack
                  });

                  if (generateDocumentFn && typeof generateDocumentFn === 'function') {
                    prodLogger.debug('✅ Calling generateDocumentFn...');
                    generateDocumentFn();
                  } else {
                    prodLogger.error('❌ generateDocumentFn is not available or not a function:', {
                      value: generateDocumentFn,
                      type: typeof generateDocumentFn
                    });
                    alert('Error: Document generation function is not available. Please refresh the page and try again.');
                  }
                }}
                disabled={isGeneratingDocument || !generateDocumentFn}
                iconName={isGeneratingDocument ? "Loader2" : "Sparkles"}
                iconPosition="left"
                className="px-8 py-3 text-base rounded-lg bg-primary hover:bg-primary/90 text-white font-medium shadow-sm hover:shadow-md transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGeneratingDocument ? 'Generating Document...' : 'Generate Document'}
              </Button>
            )}
          </div>

          {/* Loading message when generating document */}
          {isGeneratingDocument && (
            <div className="text-center mt-4">
              <p className="text-sm text-text-secondary">
                This may take a few minutes. Please don't close this window.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreationWizard;