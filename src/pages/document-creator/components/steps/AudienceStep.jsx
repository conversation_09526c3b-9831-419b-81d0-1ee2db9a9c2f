import React, { useEffect } from 'react';
import Input from '../../../../components/ui/Input';
import { getDocumentTerminology } from '../../constants/documentOptions';

/**
 * AudienceStep - Designrr-style audience targeting
 * Simple text input for audience description matching Designrr's clean approach
 */
const AudienceStep = ({
  formData,
  onInputChange,
  onValidationChange,
  className = ''
}) => {
  // Get dynamic terminology based on document type and subtype
  const documentType = formData.documentPurpose?.primaryType || 'ebook';
  const subType = formData.documentPurpose?.subType;
  const terminology = getDocumentTerminology(documentType, subType);

  const handleAudienceChange = (value) => {
    onInputChange('audienceAnalysis.primaryAudience', value);
    // Also update the description field for consistency
    onInputChange('audienceAnalysis.audienceDescription', value);
  };

  // Validation
  useEffect(() => {
    const isValid = formData.audienceAnalysis?.primaryAudience?.trim().length > 0;
    onValidationChange?.(isValid);
  }, [formData.audienceAnalysis?.primaryAudience]);

  return (
    <div className={`space-y-8 max-w-2xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          {terminology.audiencePrompt}
        </h2>
        <p className="text-text-secondary text-base md:text-lg">
          {terminology.audienceDescription}
        </p>
      </div>

      {/* Audience Input */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          {terminology.audienceLabel}
        </label>
        <Input
          type="text"
          placeholder="parents"
          value={formData.audienceAnalysis?.primaryAudience || ''}
          onChange={(e) => handleAudienceChange(e.target.value)}
          className="w-full h-12 md:h-14 text-base md:text-lg rounded-lg border-2 focus:border-primary focus:ring-0 px-4"
        />
      </div>

      {/* Additional Context (Optional) */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-secondary">
          Additional Context (Optional)
        </label>
        <textarea
          placeholder="e.g., Busy working parents with children aged 5-12 who are looking for practical wellness strategies..."
          value={formData.audienceAnalysis?.context || ''}
          onChange={(e) => onInputChange('audienceAnalysis.context', e.target.value)}
          rows={3}
          className="w-full text-base md:text-lg rounded-lg border-2 border-border focus:border-primary focus:ring-0 px-4 py-3 resize-vertical"
        />
        <p className="text-xs text-text-secondary">
          Provide more details about your target audience to help generate more targeted content
        </p>
      </div>
    </div>
  );
};

export default AudienceStep;
