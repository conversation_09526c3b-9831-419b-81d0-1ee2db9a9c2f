import React, { useEffect } from 'react';
import CustomDropdown from '../CustomDropdown';
import MultiSelectCard from '../questionnaire/MultiSelectCard';
import {
  getToneOptionsForDocumentType,
  getThemeCategoriesForDocumentType,
  getThemeOptionsForDocumentType
} from '../../utils/questionnaireDataStructure';
import { getDocumentTerminology } from '../../constants/documentOptions';

/**
 * ToneVoiceStep - Designrr-style tone and voice selection
 * Includes tone dropdown, theme selection, and popular themes
 */
const ToneVoiceStep = ({
  formData,
  onInputChange,
  onValidationChange,
  className = ''
}) => {
  // Get dynamic terminology based on document type and subtype
  const documentType = formData.documentPurpose?.primaryType || 'ebook';
  const subType = formData.documentPurpose?.subType || null;
  const terminology = getDocumentTerminology(documentType, subType);

  // Get document type-specific tone options (with subtype support)
  const toneOptions = getToneOptionsForDocumentType(documentType, subType).map(option => ({
    id: option.id,
    name: option.name,
    description: option.description
  }));

  // Get document type-specific theme categories and options
  const themeCategories = getThemeCategoriesForDocumentType(documentType);
  const themeOptions = getThemeOptionsForDocumentType(documentType);

  // Get popular themes/categories for quick selection based on document type
  const popularThemes = themeOptions.map(option => ({
    id: option.id,
    name: option.name,
    icon: option.icon
  }));

  const handleToneChange = (toneId) => {
    onInputChange('toneAndVoice.toneOfVoice', toneId);

    // Set default writing style based on tone and document type
    const toneOption = toneOptions.find(t => t.id === toneId);
    if (toneOption) {
      // Set intelligent defaults based on document type and tone
      if (documentType === 'academic') {
        onInputChange('toneAndVoice.writingStyle', 'academic');
        onInputChange('toneAndVoice.formalityLevel', 'formal');
      } else if (documentType === 'business') {
        onInputChange('toneAndVoice.writingStyle', 'professional');
        onInputChange('toneAndVoice.formalityLevel', 'formal');
      } else {
        // eBook defaults
        if (toneId === 'conversational' || toneId === 'empathetic') {
          onInputChange('toneAndVoice.writingStyle', 'conversational');
          onInputChange('toneAndVoice.formalityLevel', 'casual');
        } else {
          onInputChange('toneAndVoice.writingStyle', 'professional');
          onInputChange('toneAndVoice.formalityLevel', 'semi-formal');
        }
      }
    }
  };

  const handleThemeChange = (themeId) => {
    onInputChange('toneAndVoice.selectedTheme', themeId);

    // Update topic if not already set
    if (!formData.topicAndNiche?.mainTopic && themeCategories.categories[themeId]) {
      const theme = themeCategories.categories[themeId];
      onInputChange('topicAndNiche.mainTopic', theme.name);
    }
  };

  // Validation
  useEffect(() => {
    const isValid = formData.toneAndVoice?.toneOfVoice &&
                   formData.toneAndVoice?.selectedTheme;
    onValidationChange?.(isValid);
  }, [formData.toneAndVoice]);

  return (
    <div className={`space-y-8 max-w-4xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          {terminology.tonePrompt}
        </h2>
      </div>

      {/* Tone of Voice Selection */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          Tone of voice
        </label>
        <CustomDropdown
          value={formData.toneAndVoice?.toneOfVoice || 'informative'}
          onChange={handleToneChange}
          options={toneOptions}
          placeholder="Informative"
          className="w-full max-w-md"
        />
      </div>

      {/* Theme Selection */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          {terminology.themeLabel}
        </label>
        <CustomDropdown
          value={formData.toneAndVoice?.selectedTheme || Object.keys(themeCategories.categories)[0]}
          onChange={handleThemeChange}
          options={themeOptions.slice(0, 8)} // Show first 8 themes in dropdown
          placeholder={themeCategories.description}
          className="w-full max-w-md"
        />
      </div>

      {/* Most Popular Categories */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-text-primary">
          Most popular {terminology.themeLabel.toLowerCase()}
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
          {popularThemes.map((theme) => (
            <button
              key={theme.id}
              onClick={() => handleThemeChange(theme.id)}
              className={`
                flex flex-col items-center p-4 rounded-lg border-2 transition-all hover:shadow-md text-center
                ${formData.toneAndVoice?.selectedTheme === theme.id
                  ? 'border-primary bg-primary/5 shadow-lg'
                  : 'border-border hover:border-primary/50'
                }
              `}
            >
              <span className="text-2xl mb-2">{theme.icon}</span>
              <span className="text-sm font-medium text-text-primary leading-tight">
                {theme.name}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Additional Voice Settings */}
      <div className="space-y-6 pt-6 border-t border-border">
        <h3 className="text-lg font-semibold text-text-primary">
          Additional Voice Settings
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Formality Level */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-text-primary">
              Formality Level
            </label>
            <CustomDropdown
              value={formData.toneAndVoice?.formalityLevel || 'semi-formal'}
              onChange={(value) => onInputChange('toneAndVoice.formalityLevel', value)}
              options={[
                { id: 'very-formal', name: 'Very Formal' },
                { id: 'formal', name: 'Formal' },
                { id: 'semi-formal', name: 'Semi-formal' },
                { id: 'casual', name: 'Casual' },
                { id: 'very-casual', name: 'Very Casual' }
              ]}
              placeholder="Semi-formal"
              className="w-full"
            />
          </div>

          {/* Perspective */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-text-primary">
              Perspective
            </label>
            <CustomDropdown
              value={formData.toneAndVoice?.perspectiveVoice || 'third-person'}
              onChange={(value) => onInputChange('toneAndVoice.perspectiveVoice', value)}
              options={[
                { id: 'first-person', name: 'First Person (I, we)' },
                { id: 'second-person', name: 'Second Person (you)' },
                { id: 'third-person', name: 'Third Person (they, it)' },
                { id: 'mixed', name: 'Mixed' }
              ]}
              placeholder="Third Person"
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Suggestion Text */}
      <div className="text-center pt-4">
        <p className="text-sm text-text-secondary">
          Didn't find your perfect tone of voice?{' '}
          <button className="text-primary hover:text-primary/80 underline">
            Share your suggestions with us
          </button>
        </p>
      </div>
    </div>
  );
};

export default ToneVoiceStep;
