import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import { supabase } from '../../../lib/supabase';

const SecuritySection = () => {
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState('');



  const handlePasswordChange = (field, value) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear messages when user starts typing
    if (passwordError) setPasswordError('');
    if (passwordSuccess) setPasswordSuccess('');
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();

    // Basic validation
    if (!passwordForm.currentPassword) {
      setPasswordError('Current password is required');
      return;
    }

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setPasswordError('New passwords do not match');
      return;
    }

    if (passwordForm.newPassword.length < 8) {
      setPasswordError('New password must be at least 8 characters long');
      return;
    }

    setPasswordLoading(true);
    setPasswordError('');
    setPasswordSuccess('');

    try {
      // MVP: Simple password update using Supabase Auth
      const { error } = await supabase.auth.updateUser({
        password: passwordForm.newPassword
      });

      if (error) {
        setPasswordError(error.message || 'Failed to update password');
      } else {
        setPasswordSuccess('Password updated successfully');
        setShowPasswordForm(false);
        setPasswordForm({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      }
    } catch (err) {
      setPasswordError('An error occurred while updating password');
    } finally {
      setPasswordLoading(false);
    }
  };



  return (
    <div className="space-y-6">
      {/* Success Message */}
      {passwordSuccess && (
        <div className="bg-success/10 border border-success/20 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Icon name="CheckCircle" size={16} color="var(--color-success)" />
            <span className="text-sm text-success">{passwordSuccess}</span>
          </div>
        </div>
      )}

      {/* Password Security - MVP: Simplified */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          <div className="flex items-start justify-between gap-3 mb-2">
            <div className="flex-1 min-w-0">
              <h2 className="text-lg font-semibold text-text-primary">Password Security</h2>
            </div>
            <div className="flex-shrink-0">
              <Button
                variant="outline"
                onClick={() => setShowPasswordForm(!showPasswordForm)}
                iconName="Key"
                iconPosition="left"
                size="sm"
                className="whitespace-nowrap text-sm lg:text-base lg:py-2 lg:px-4"
              >
                <span className="hidden sm:inline">
                  {showPasswordForm ? 'Cancel' : 'Change Password'}
                </span>
                <span className="sm:hidden">
                  {showPasswordForm ? 'Cancel' : 'Change'}
                </span>
              </Button>
            </div>
          </div>
          <p className="text-sm text-text-secondary">Update your account password</p>
        </div>

        {showPasswordForm && (
          <form onSubmit={handlePasswordSubmit} className="space-y-4 p-4 bg-background rounded-lg">
            {passwordError && (
              <div className="bg-error/10 border border-error/20 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <Icon name="AlertCircle" size={14} color="var(--color-error)" />
                  <span className="text-sm text-error">{passwordError}</span>
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">Current Password</label>
              <Input
                type="password"
                value={passwordForm.currentPassword}
                onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                placeholder="Enter current password"
                required
                disabled={passwordLoading}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">New Password</label>
              <Input
                type="password"
                value={passwordForm.newPassword}
                onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                placeholder="Enter new password (min 8 characters)"
                required
                disabled={passwordLoading}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">Confirm New Password</label>
              <Input
                type="password"
                value={passwordForm.confirmPassword}
                onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                placeholder="Confirm new password"
                required
                disabled={passwordLoading}
              />
            </div>
            <div className="flex space-x-2">
              <Button
                type="submit"
                variant="primary"
                loading={passwordLoading}
                disabled={passwordLoading}
              >
                Update Password
              </Button>
              <Button
                type="button"
                variant="ghost"
                onClick={() => setShowPasswordForm(false)}
                disabled={passwordLoading}
              >
                Cancel
              </Button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default SecuritySection;