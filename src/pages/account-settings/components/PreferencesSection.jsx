import React, { useState, useEffect, useRef } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import { useAuth } from '../../../contexts/AuthContext';
import { dbHelpers } from '../../../lib/supabase';
import { TIMEZONE_OPTIONS, getTimezonesByRegion } from '../../../utils/timezones';
import { validateAllPreferences, getFieldErrorMessage } from '../../../utils/preferencesValidation';
import { SuccessNotification, ErrorNotification } from '../../../components/auth/AuthNotification';

import { prodLogger } from '../../../utils/prodLogger.js';
const PreferencesSection = () => {
  const { user, profile, updateProfile, loading: authLoading } = useAuth();

  // Helper component for field validation errors
  const FieldError = ({ field }) => {
    const error = validationErrors[field];
    if (!error) return null;

    return (
      <div className="mt-1 flex items-center space-x-1">
        <Icon name="AlertCircle" size={12} color="var(--color-error)" />
        <span className="text-xs text-error">{getFieldErrorMessage(field, error)}</span>
      </div>
    );
  };

  // Custom Timezone Dropdown Component
  const TimezoneDropdown = ({ value, onChange, error }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const dropdownRef = useRef(null);
    const searchRef = useRef(null);

    // Group timezones by region
    const groupedTimezones = getTimezonesByRegion();
    const regionOrder = ['UTC', 'North America', 'Europe', 'Asia', 'Australia', 'Africa', 'South America'];

    // Filter timezones based on search term
    const filteredTimezones = searchTerm
      ? TIMEZONE_OPTIONS.filter(tz =>
          tz.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tz.value.toLowerCase().includes(searchTerm.toLowerCase())
        )
      : null;

    // Get current timezone display
    const currentTimezone = TIMEZONE_OPTIONS.find(tz => tz.value === value);

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
          setIsOpen(false);
          setSearchTerm('');
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Focus search when dropdown opens
    useEffect(() => {
      if (isOpen && searchRef.current) {
        searchRef.current.focus();
      }
    }, [isOpen]);

    const handleSelect = (timezone) => {
      onChange(timezone.value);
      setIsOpen(false);
      setSearchTerm('');
    };

    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    return (
      <div className="relative" ref={dropdownRef}>
        {/* Dropdown Trigger */}
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className={`w-full pl-3 pr-10 py-2 border rounded-lg bg-surface text-text-primary text-sm sm:text-base
                     focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent
                     text-left truncate overflow-hidden whitespace-nowrap cursor-pointer
                     ${error ? 'border-error' : 'border-border'}`}
          title={currentTimezone?.label || value}
        >
          {currentTimezone?.label || value}
        </button>

        {/* Chevron Icon */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <Icon
            name={isOpen ? "ChevronUp" : "ChevronDown"}
            size={16}
            color="var(--color-text-secondary)"
          />
        </div>

        {/* Dropdown Menu */}
        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-surface border border-border rounded-lg shadow-lg max-h-80 overflow-hidden">
            {/* Search Input */}
            <div className="p-3 border-b border-border">
              <div className="relative">
                <Icon
                  name="Search"
                  size={16}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary"
                />
                <input
                  ref={searchRef}
                  type="text"
                  placeholder="Search timezones..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="w-full pl-10 pr-3 py-2 text-sm border border-border rounded-md bg-background text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
            </div>

            {/* Options List */}
            <div className="max-h-64 overflow-y-auto">
              {filteredTimezones ? (
                // Filtered results
                filteredTimezones.length > 0 ? (
                  filteredTimezones.map((timezone) => (
                    <button
                      key={timezone.value}
                      type="button"
                      onClick={() => handleSelect(timezone)}
                      className={`w-full px-3 py-2 text-left text-sm hover:bg-background transition-colors
                                 ${timezone.value === value ? 'bg-primary/10 text-primary' : 'text-text-primary'}`}
                    >
                      {timezone.label}
                    </button>
                  ))
                ) : (
                  <div className="px-3 py-2 text-sm text-text-secondary">No timezones found</div>
                )
              ) : (
                // Grouped results
                regionOrder.map((region) => {
                  const timezones = groupedTimezones[region];
                  if (!timezones) return null;

                  return (
                    <div key={region}>
                      <div className="px-3 py-2 text-xs font-medium text-text-secondary bg-background border-b border-border">
                        {region}
                      </div>
                      {timezones.map((timezone) => (
                        <button
                          key={timezone.value}
                          type="button"
                          onClick={() => handleSelect(timezone)}
                          className={`w-full px-3 py-2 text-left text-sm hover:bg-background transition-colors
                                     ${timezone.value === value ? 'bg-primary/10 text-primary' : 'text-text-primary'}`}
                        >
                          {timezone.label}
                        </button>
                      ))}
                    </div>
                  );
                })
              )}
            </div>
          </div>
        )}
      </div>
    );
  };
  // MVP Essential preferences only (language removed - post-MVP feature)
  const [preferences, setPreferences] = useState({
    timezone: "UTC",
    emailNotifications: true,
    autoSave: true
  });

  const [hasChanges, setHasChanges] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [validationErrors, setValidationErrors] = useState({});
  const [isValidating, setIsValidating] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [showRetry, setShowRetry] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Load preferences from user profile
  useEffect(() => {
    if (profile) {
      // MVP Essential preferences only (language removed - post-MVP feature)
      setPreferences({
        timezone: profile.timezone || "UTC",
        emailNotifications: profile.notifications_email !== undefined ? profile.notifications_email : true,
        autoSave: profile.auto_save !== undefined ? profile.auto_save : true
      });
    }
  }, [profile]);

  // Online/offline detection
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);





  // Use comprehensive timezone list
  const timezoneOptions = TIMEZONE_OPTIONS;

  const handlePreferenceChange = (key, value) => {
    // Update preferences
    const newPreferences = {
      ...preferences,
      [key]: value
    };

    setPreferences(newPreferences);
    setHasChanges(true);

    // Clear any existing validation errors for this field
    if (validationErrors[key]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[key];
        return newErrors;
      });
    }

    // Clear global error when user makes changes
    if (error) {
      setError('');
    }
  };

  const handleSave = async () => {
    if (!user?.id) {
      setError('No user logged in');
      return;
    }

    // Check if user is online
    if (!isOnline) {
      setError('You are currently offline. Please check your internet connection and try again.');
      setShowRetry(true);
      return;
    }

    // MVP: Simplified - removed rate limiting and suspicious activity checks

    // Validate preferences before saving
    setIsValidating(true);
    const validation = validateAllPreferences(preferences);

    if (!validation.isValid) {
      setValidationErrors(validation.errors);
      setError(`Please fix ${validation.errorCount} validation error${validation.errorCount > 1 ? 's' : ''} before saving.`);
      setIsValidating(false);
      return;
    }

    // Clear validation errors if all is valid
    setValidationErrors({});
    setLoading(true);
    setError('');
    setIsValidating(false);

    try {
      // MVP: Direct preferences update without complex sanitization
      // Update preferences in the database
      const { error } = await dbHelpers.updateUserPreferences(user.id, preferences);

      if (error) {
        setError(error.message);
        return;
      }

      // Update the profile in the auth context (MVP Essential only)
      const profileUpdateResult = await updateProfile({
        timezone: preferences.timezone,
        notifications_email: preferences.emailNotifications,
        auto_save: preferences.autoSave
      });

      if (!profileUpdateResult.success) {
        throw new Error(profileUpdateResult.error || 'Failed to update profile');
      }

      // Success - clear states and show success message
      setHasChanges(false);
      setShowRetry(false);
      setSuccessMessage('Preferences saved successfully!');

      // MVP: Simplified success logging
      prodLogger.debug('Preferences updated successfully for user:', user.id);

      // Auto-hide success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);

    } catch (err) {
      prodLogger.error('Preference save error:', err);

      // Enhanced error handling with specific messages
      let errorMessage = 'Failed to save preferences';

      if (err.message?.includes('network') || err.message?.includes('fetch')) {
        errorMessage = 'Network error. Please check your connection and try again.';
        setShowRetry(true);
      } else if (err.message?.includes('validation') || err.message?.includes('Invalid')) {
        errorMessage = 'Invalid preference values. Please check your inputs.';
        setShowRetry(false);
      } else if (err.message?.includes('permission') || err.message?.includes('unauthorized')) {
        errorMessage = 'You do not have permission to update preferences.';
        setShowRetry(false);
      } else {
        errorMessage = err.message || errorMessage;
        setShowRetry(true);
      }

      setError(errorMessage);

      // MVP: Simplified error logging
      prodLogger.error('Preference update failed for user:', user.id, err);
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    setError('');
    setShowRetry(false);
    handleSave();
  };

  const handleReset = () => {
    // Reset to profile values or defaults
    if (profile) {
      // MVP Essential preferences only (language removed - post-MVP feature)
      setPreferences({
        timezone: profile.timezone || "UTC",
        emailNotifications: profile.notifications_email !== undefined ? profile.notifications_email : true,
        autoSave: profile.auto_save !== undefined ? profile.auto_save : true
      });
    }
    setHasChanges(false);
    setError('');
  };

  return (
    <div className="space-y-6">
      {/* Success Message */}
      {successMessage && (
        <SuccessNotification
          message={successMessage}
          onClose={() => setSuccessMessage('')}
        />
      )}

      {/* Offline Warning */}
      {!isOnline && (
        <div className="bg-warning/10 border border-warning/20 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Icon name="WifiOff" size={16} color="var(--color-warning)" />
            <span className="text-sm text-warning">You are currently offline. Changes will not be saved until you reconnect.</span>
          </div>
        </div>
      )}

      {/* Enhanced Error Message */}
      {error && (
        <ErrorNotification
          message={error}
          onClose={() => setError('')}
          onRetry={showRetry ? handleRetry : undefined}
        />
      )}

      {/* Save/Reset Actions */}
      {hasChanges && (
        <div className="bg-warning/10 border border-warning/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon name="AlertTriangle" size={16} color="var(--color-warning)" />
              <span className="text-sm text-warning">You have unsaved changes</span>
            </div>
            <div className="flex space-x-2">
              <Button variant="ghost" onClick={handleReset} disabled={loading}>
                Reset
              </Button>
              <Button
                variant="primary"
                onClick={handleSave}
                iconName="Save"
                iconPosition="left"
                disabled={loading || authLoading || isValidating}
                loading={loading || isValidating}
              >
                {isValidating ? 'Validating...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Regional Settings */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Regional Settings</h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">Timezone</label>
            <TimezoneDropdown
              value={preferences.timezone}
              onChange={(value) => handlePreferenceChange('timezone', value)}
              error={validationErrors.timezone}
            />
            <FieldError field="timezone" />
            <p className="text-xs text-text-secondary mt-2">
              Used for displaying timestamps and scheduling features
            </p>
          </div>
        </div>
      </div>



      {/* Notifications */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Notification Preferences</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 border border-border rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-text-primary">Email Notifications</h4>
              <p className="text-xs text-text-secondary">Receive important updates via email</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.emailNotifications}
                onChange={(e) => handlePreferenceChange('emailNotifications', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-background peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>


        </div>
      </div>

      {/* Privacy Settings */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Auto-save Settings</h3>
        

        <div className="mt-4">
          <div className="flex items-center justify-between p-3 border border-border rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-text-primary">Auto-save Documents</h4>
              <p className="text-xs text-text-secondary">Automatically save changes while editing</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.autoSave}
                onChange={(e) => handlePreferenceChange('autoSave', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-background peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreferencesSection;