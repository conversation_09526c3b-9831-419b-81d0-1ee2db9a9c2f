import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import BrandingSection from './BrandingSection';

const ProfileSection = () => {
  const { user, profile, updateProfile, loading, error } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [tempData, setTempData] = useState({
    full_name: '',
    email: ''
  });
  const [updateError, setUpdateError] = useState('');

  // Initialize profile data from auth context - MVP: Only essential fields
  useEffect(() => {
    if (profile) {
      const profileData = {
        full_name: profile.full_name || '',
        email: user?.email || ''
      };
      setTempData(profileData);
    }
  }, [profile, user]);

  const handleEdit = () => {
    setIsEditing(true);
    setUpdateError('');
  };

  const handleSave = async () => {
    try {
      setUpdateError('');
      const result = await updateProfile(tempData);

      if (result.success) {
        setIsEditing(false);
      } else {
        setUpdateError(result.error || 'Failed to update profile');
      }
    } catch (err) {
      setUpdateError('An error occurred while updating profile');
    }
  };

  const handleCancel = () => {
    // Reset to current profile data - MVP: Only essential fields
    if (profile) {
      const profileData = {
        full_name: profile.full_name || '',
        email: user?.email || ''
      };
      setTempData(profileData);
    }
    setIsEditing(false);
    setUpdateError('');
  };

  const handleInputChange = (field, value) => {
    setTempData(prev => ({
      ...prev,
      [field]: value
    }));
  };



  return (
    <div className="space-y-6">
      {/* Profile Information Section */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          <div className="flex items-start justify-between gap-3 mb-2">
            <div className="flex-1 min-w-0">
              <h2 className="text-lg font-semibold text-text-primary">Profile Information</h2>
            </div>
          </div>
          <div className="flex-shrink-0">
            {!isEditing ? (
              <Button
                variant="outline"
                onClick={handleEdit}
                iconName="Edit2"
                iconPosition="left"
                size="sm"
                className="whitespace-nowrap text-sm lg:text-base lg:py-2 lg:px-4"
              >
                <span className="hidden sm:inline">Edit Profile</span>
                <span className="sm:hidden">Edit</span>
              </Button>
            ) : (
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  onClick={handleCancel}
                  disabled={loading}
                  size="sm"
                  className="whitespace-nowrap text-sm lg:text-base lg:py-2 lg:px-4"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleSave}
                  iconName="Save"
                  iconPosition="left"
                  loading={loading}
                  disabled={loading}
                  size="sm"
                  className="whitespace-nowrap text-sm lg:text-base lg:py-2 lg:px-4"
                >
                  <span className="hidden sm:inline">
                    {loading ? 'Saving...' : 'Save Changes'}
                  </span>
                  <span className="sm:hidden">
                    {loading ? 'Saving...' : 'Save'}
                  </span>
                </Button>
              </div>
            )}
          </div>
        </div>
        <p className="text-sm text-text-secondary">Manage your personal information and preferences</p>

        {/* Error Display */}
        {(error || updateError) && (
          <div className="mb-6 p-4 bg-error/10 border border-error/20 rounded-lg">
            <div className="flex items-center space-x-2">
              <Icon name="AlertCircle" size={16} color="var(--color-error)" />
              <p className="text-sm text-error">{error || updateError}</p>
            </div>
          </div>
        )}

        {/* MVP: Simplified profile layout without photo upload */}
        <div className="space-y-6 mt-6">
        {/* Basic Information */}
        <div>
          <h4 className="text-md font-medium text-text-primary mb-4">Basic Information</h4>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">Full Name</label>
              {isEditing ? (
                <Input
                  type="text"
                  value={tempData.full_name || ''}
                  onChange={(e) => handleInputChange('full_name', e.target.value)}
                  placeholder="Enter your full name"
                  disabled={loading}
                />
              ) : (
                <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">
                  {profile?.full_name || 'Not provided'}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Email Information - MVP: Read-only */}
        <div>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">Email Address</label>
              <p className="text-sm text-text-secondary bg-background p-3 rounded-lg
                           sm:truncate sm:overflow-hidden
                           break-words overflow-wrap-anywhere
                           sm:whitespace-nowrap whitespace-normal">
                {user?.email} <span className="text-xs text-text-muted">(Cannot be changed)</span>
              </p>
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* Branding Section */}
      <BrandingSection />
    </div>
  );
};

export default ProfileSection;