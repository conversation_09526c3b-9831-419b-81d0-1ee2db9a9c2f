import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ProgressiveDisclosureCards from '../ProgressiveDisclosureCards';

// Mock the section components
jest.mock('../ProfileSection', () => {
  return function ProfileSection() {
    return <div data-testid="profile-section">Profile Section Content</div>;
  };
});

jest.mock('../SubscriptionSection', () => {
  return function SubscriptionSection() {
    return <div data-testid="subscription-section">Subscription Section Content</div>;
  };
});

jest.mock('../PreferencesSection', () => {
  return function PreferencesSection() {
    return <div data-testid="preferences-section">Preferences Section Content</div>;
  };
});

jest.mock('../SecuritySection', () => {
  return function SecuritySection() {
    return <div data-testid="security-section">Security Section Content</div>;
  };
});

jest.mock('../BillingSection', () => {
  return function BillingSection() {
    return <div data-testid="billing-section">Billing Section Content</div>;
  };
});

// Mock Icon component
jest.mock('../../../components/AppIcon', () => {
  return function Icon({ name, size, color }) {
    return <span data-testid={`icon-${name}`} data-size={size} data-color={color}>{name}</span>;
  };
});

const mockTabs = [
  { id: 'profile', label: 'Profile', icon: 'User' },
  { id: 'subscription', label: 'Subscription', icon: 'Crown' },
  { id: 'preferences', label: 'Preferences', icon: 'Settings' },
  { id: 'security', label: 'Security', icon: 'Shield' },
  { id: 'billing', label: 'Billing', icon: 'CreditCard' }
];

const mockAccountStats = {
  plan: 'Pro',
  credits: '150',
  memberSince: 'Jan 2023'
};

describe('ProgressiveDisclosureCards', () => {
  beforeEach(() => {
    // Reset any DOM changes between tests
    document.body.innerHTML = '';
  });

  test('renders account overview card', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    expect(screen.getByText('Account Overview')).toBeInTheDocument();
    expect(screen.getByText('Pro')).toBeInTheDocument();
    expect(screen.getByText('150')).toBeInTheDocument();
    expect(screen.getByText('Jan 2023')).toBeInTheDocument();
  });

  test('renders all setting category cards', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    mockTabs.forEach(tab => {
      expect(screen.getByText(tab.label)).toBeInTheDocument();
    });
  });

  test('expands card when clicked', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    const profileButton = screen.getByRole('button', { name: /profile/i });
    fireEvent.click(profileButton);

    expect(screen.getByTestId('profile-section')).toBeInTheDocument();
    expect(profileButton).toHaveAttribute('aria-expanded', 'true');
  });

  test('collapses card when clicked again', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    const profileButton = screen.getByRole('button', { name: /profile/i });
    
    // Expand
    fireEvent.click(profileButton);
    expect(screen.getByTestId('profile-section')).toBeInTheDocument();
    
    // Collapse
    fireEvent.click(profileButton);
    expect(screen.queryByTestId('profile-section')).not.toBeInTheDocument();
    expect(profileButton).toHaveAttribute('aria-expanded', 'false');
  });

  test('only one card can be expanded at a time (accordion behavior)', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    const profileButton = screen.getByRole('button', { name: /profile/i });
    const subscriptionButton = screen.getByRole('button', { name: /subscription/i });
    
    // Expand profile
    fireEvent.click(profileButton);
    expect(screen.getByTestId('profile-section')).toBeInTheDocument();
    
    // Expand subscription - should close profile
    fireEvent.click(subscriptionButton);
    expect(screen.queryByTestId('profile-section')).not.toBeInTheDocument();
    expect(screen.getByTestId('subscription-section')).toBeInTheDocument();
  });

  test('handles keyboard navigation', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    const profileButton = screen.getByRole('button', { name: /profile/i });
    
    // Test Enter key
    fireEvent.keyDown(profileButton, { key: 'Enter' });
    expect(screen.getByTestId('profile-section')).toBeInTheDocument();
    
    // Test Escape key
    fireEvent.keyDown(profileButton, { key: 'Escape' });
    expect(screen.queryByTestId('profile-section')).not.toBeInTheDocument();
  });

  test('shows loading state', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={true}
      />
    );

    expect(screen.getAllByText('Loading...')).toHaveLength(3); // Plan, Credits, Member Since
  });

  test('has proper accessibility attributes', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    const profileButton = screen.getByRole('button', { name: /profile/i });

    expect(profileButton).toHaveAttribute('aria-expanded', 'false');
    expect(profileButton).toHaveAttribute('aria-controls', 'card-content-profile');
    expect(profileButton).toHaveAttribute('aria-describedby', 'card-description-profile');
  });

  test('maintains proper touch target size', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    const profileButton = screen.getByRole('button', { name: /profile/i });
    const styles = window.getComputedStyle(profileButton);

    // Check that minimum height is 56px (optimized from 64px)
    expect(profileButton.style.minHeight).toBe('56px');
    expect(profileButton.style.minWidth).toBe('44px');
  });

  test('profile section buttons are responsive and prevent text wrapping', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    // Expand the profile card to access ProfileSection
    const profileButton = screen.getByRole('button', { name: /profile/i });
    fireEvent.click(profileButton);

    // Check that the Edit Profile button exists and has proper classes
    const editButton = screen.getByRole('button', { name: /edit/i });
    expect(editButton).toBeInTheDocument();
    expect(editButton).toHaveClass('whitespace-nowrap');

    // Verify responsive text content
    expect(editButton.textContent).toMatch(/Edit/);
  });

  test('subscription section buttons are responsive and prevent text wrapping', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    // Expand the subscription card
    const subscriptionButton = screen.getByRole('button', { name: /subscription/i });
    fireEvent.click(subscriptionButton);

    // Check that the Upgrade Plan button exists and has proper classes
    const upgradeButton = screen.getByRole('button', { name: /upgrade/i });
    expect(upgradeButton).toBeInTheDocument();
    expect(upgradeButton).toHaveClass('whitespace-nowrap');

    // Verify responsive text content
    expect(upgradeButton.textContent).toMatch(/Upgrade/);
  });

  test('security section buttons are responsive and prevent text wrapping', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    // Expand the security card
    const securityButton = screen.getByRole('button', { name: /security/i });
    fireEvent.click(securityButton);

    // Check that the Change Password button exists and has proper classes
    const changePasswordButton = screen.getByRole('button', { name: /change/i });
    expect(changePasswordButton).toBeInTheDocument();
    expect(changePasswordButton).toHaveClass('whitespace-nowrap');

    // Verify responsive text content
    expect(changePasswordButton.textContent).toMatch(/Change/);
  });

  test('billing section buttons are responsive and prevent text wrapping', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    // Expand the billing card
    const billingButton = screen.getByRole('button', { name: /billing/i });
    fireEvent.click(billingButton);

    // Check that the Add Payment Method button exists and has proper classes
    const addPaymentButton = screen.getByRole('button', { name: /add payment/i });
    expect(addPaymentButton).toBeInTheDocument();
    expect(addPaymentButton).toHaveClass('whitespace-nowrap');

    // Verify responsive text content
    expect(addPaymentButton.textContent).toMatch(/Add Payment/);
  });

  test('security section danger zone delete button is responsive and prevents text wrapping', () => {
    render(
      <ProgressiveDisclosureCards
        tabs={mockTabs}
        accountStats={mockAccountStats}
        authLoading={false}
      />
    );

    // Expand the security card
    const securityButton = screen.getByRole('button', { name: /security/i });
    fireEvent.click(securityButton);

    // Check that the Delete Account button exists and has proper classes
    const deleteButton = screen.getByRole('button', { name: /delete/i });
    expect(deleteButton).toBeInTheDocument();
    expect(deleteButton).toHaveClass('whitespace-nowrap');

    // Verify responsive text content
    expect(deleteButton.textContent).toMatch(/Delete/);

    // Verify it's a danger variant button
    expect(deleteButton).toHaveAttribute('data-variant', 'danger');
  });
});
