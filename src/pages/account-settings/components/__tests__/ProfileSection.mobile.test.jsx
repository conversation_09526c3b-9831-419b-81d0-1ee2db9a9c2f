import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ProfileSection from '../ProfileSection';

// Mock the auth context
const mockAuthContext = {
  user: { email: '<EMAIL>' },
  profile: {
    full_name: 'Test User',
    phone: '+1234567890',
    user_type: 'student',
    bio: 'Test bio',
    location: 'Test Location',
    avatar_url: null
  },
  updateProfile: jest.fn(),
  loading: false,
  error: null
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext
}));

// Mock components
jest.mock('../../../components/AppIcon', () => {
  return function Icon({ name, size }) {
    return <span data-testid={`icon-${name}`} data-size={size}>{name}</span>;
  };
});

jest.mock('../../../components/AppImage', () => {
  return function Image({ src, alt, className }) {
    return <img src={src} alt={alt} className={className} data-testid="profile-image" />;
  };
});

jest.mock('../../../components/ui/Button', () => {
  return function Button({ children, variant, size, className, iconName, iconPosition, onClick, disabled, loading, ...props }) {
    return (
      <button
        onClick={onClick}
        disabled={disabled || loading}
        className={`${className} ${size ? `size-${size}` : ''} ${variant ? `variant-${variant}` : ''}`}
        data-testid="button"
        data-variant={variant}
        data-size={size}
        {...props}
      >
        {iconName && iconPosition === 'left' && <span data-testid={`icon-${iconName}`}>{iconName}</span>}
        {children}
        {iconName && iconPosition === 'right' && <span data-testid={`icon-${iconName}`}>{iconName}</span>}
      </button>
    );
  };
});

jest.mock('../../../components/ui/Input', () => {
  return function Input({ value, onChange, placeholder, disabled, type, ...props }) {
    return (
      <input
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        data-testid="input"
        {...props}
      />
    );
  };
});

describe('ProfileSection Mobile Button Layout', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
  });

  test('renders edit profile button with responsive classes', () => {
    render(<ProfileSection />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    
    // Check that the button has responsive classes
    expect(editButton).toHaveClass('whitespace-nowrap');
    expect(editButton).toHaveClass('text-sm');
    expect(editButton).toHaveAttribute('data-size', 'sm');
  });

  test('shows abbreviated text on mobile screens', () => {
    // Mock window.innerWidth for mobile
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375, // Mobile width
    });

    render(<ProfileSection />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    
    // Should contain both full and abbreviated text with responsive classes
    expect(editButton.innerHTML).toContain('Edit Profile');
    expect(editButton.innerHTML).toContain('Edit');
  });

  test('edit button container has proper flex classes', () => {
    render(<ProfileSection />);
    
    // Find the header container
    const headerContainer = screen.getByRole('button', { name: /edit/i }).closest('.flex');
    
    expect(headerContainer).toHaveClass('flex');
    expect(headerContainer).toHaveClass('items-start');
    expect(headerContainer).toHaveClass('justify-between');
    expect(headerContainer).toHaveClass('gap-3');
  });

  test('switches to editing mode with responsive save/cancel buttons', () => {
    render(<ProfileSection />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    fireEvent.click(editButton);
    
    // Should show Cancel and Save buttons
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    const saveButton = screen.getByRole('button', { name: /save/i });
    
    expect(cancelButton).toHaveClass('whitespace-nowrap');
    expect(saveButton).toHaveClass('whitespace-nowrap');
    expect(cancelButton).toHaveAttribute('data-size', 'sm');
    expect(saveButton).toHaveAttribute('data-size', 'sm');
  });

  test('save button shows abbreviated text on mobile', () => {
    render(<ProfileSection />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    fireEvent.click(editButton);
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    
    // Should contain both full and abbreviated text
    expect(saveButton.innerHTML).toContain('Save Changes');
    expect(saveButton.innerHTML).toContain('Save');
  });

  test('buttons maintain accessibility with proper aria attributes', () => {
    render(<ProfileSection />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    
    // Button should be accessible
    expect(editButton).toBeInTheDocument();
    expect(editButton).not.toHaveAttribute('aria-hidden');
    
    // Click to edit mode
    fireEvent.click(editButton);
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    
    expect(saveButton).toBeInTheDocument();
    expect(cancelButton).toBeInTheDocument();
  });

  test('header layout prevents button text wrapping', () => {
    render(<ProfileSection />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    const buttonContainer = editButton.closest('.flex-shrink-0');
    
    // Button container should not shrink
    expect(buttonContainer).toHaveClass('flex-shrink-0');
    
    // Button should prevent text wrapping
    expect(editButton).toHaveClass('whitespace-nowrap');
  });

  test('loading state maintains button sizing', () => {
    // Mock loading state
    mockAuthContext.loading = true;
    
    render(<ProfileSection />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    fireEvent.click(editButton);
    
    const saveButton = screen.getByRole('button', { name: /saving/i });
    
    expect(saveButton).toHaveClass('whitespace-nowrap');
    expect(saveButton).toHaveAttribute('data-size', 'sm');
    expect(saveButton).toBeDisabled();
  });
});
