import { useState, useCallback, useRef, useEffect } from "react";

/**
 * Custom hook for tracking background operations during phase transitions
 * Provides smooth UX by showing progress indicators while operations complete
 */
export const useBackgroundOperations = () => {
  const [operations, setOperations] = useState(new Map());
  const operationsRef = useRef(new Map());

  // Sync ref with state for stable references
  useEffect(() => {
    operationsRef.current = operations;
  }, [operations]);

  const startOperation = useCallback((id, message = "Processing...") => {
    setOperations((prev) => {
      const next = new Map(prev);
      next.set(id, {
        id,
        message,
        startTime: Date.now(),
        completed: false,
      });
      return next;
    });
  }, []);

  const completeOperation = useCallback((id, success = true, result = null) => {
    setOperations((prev) => {
      const next = new Map(prev);
      const operation = next.get(id);

      if (operation) {
        next.set(id, {
          ...operation,
          completed: true,
          success,
          result,
          endTime: Date.now(),
          duration: Date.now() - operation.startTime,
        });

        // Auto-remove completed operations after a delay
        setTimeout(() => {
          setOperations((current) => {
            const updated = new Map(current);
            updated.delete(id);
            return updated;
          });
        }, 2000);
      }

      return next;
    });
  }, []);

  const updateOperation = useCallback((id, updates) => {
    setOperations((prev) => {
      const next = new Map(prev);
      const operation = next.get(id);

      if (operation) {
        next.set(id, { ...operation, ...updates });
      }

      return next;
    });
  }, []);

  // Computed values
  const activeOperations = Array.from(operations.values()).filter(
    (op) => !op.completed
  );
  const hasActiveOperations = activeOperations.length > 0;
  const completedOperations = Array.from(operations.values()).filter(
    (op) => op.completed
  );

  // Get status messages
  const getPrimaryMessage = useCallback(() => {
    if (activeOperations.length === 0) return null;
    if (activeOperations.length === 1) return activeOperations[0].message;
    return `Processing ${activeOperations.length} operations...`;
  }, [activeOperations]);

  const getDetailedStatus = useCallback(() => {
    return {
      active: activeOperations,
      completed: completedOperations,
      total: operations.size,
      isProcessing: hasActiveOperations,
    };
  }, [
    activeOperations,
    completedOperations,
    operations.size,
    hasActiveOperations,
  ]);

  // Utility function to run an async operation with tracking
  const trackOperation = useCallback(
    async (id, message, asyncFn) => {
      startOperation(id, message);

      try {
        const result = await asyncFn();
        completeOperation(id, true, result);
        return { success: true, result };
      } catch (error) {
        completeOperation(id, false, error);
        return { success: false, error };
      }
    },
    [startOperation, completeOperation]
  );

  return {
    // State
    operations,
    activeOperations,
    completedOperations,
    hasActiveOperations,

    // Actions
    startOperation,
    completeOperation,
    updateOperation,
    trackOperation,

    // Computed
    getPrimaryMessage,
    getDetailedStatus,
  };
};

export default useBackgroundOperations;
