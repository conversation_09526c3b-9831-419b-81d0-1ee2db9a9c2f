import { prodLogger } from '../../../utils/prodLogger.js';

/**
 * Image Migration Utilities
 * Handles conversion between old placedImages system and new image blocks
 */

/**
 * Convert old placedImages structure to image blocks within content
 * @param {Object} generatedContent - The document content with placedImages
 * @returns {Object} Updated content with image blocks integrated
 */
export const migrateImagesToBlocks = (generatedContent) => {
  if (!generatedContent || !generatedContent.placedImages) {
    return generatedContent;
  }

  const updatedContent = { ...generatedContent };
  const placedImages = generatedContent.placedImages;

  // Process each section (introduction, chapters, conclusion)
  const sections = ['introduction', 'conclusion'];
  
  // Handle introduction and conclusion
  sections.forEach(sectionKey => {
    if (updatedContent[sectionKey] && placedImages[sectionKey]) {
      const sectionImages = placedImages[sectionKey];
      const imageBlocks = convertImagesToBlocks(sectionImages, sectionKey);
      
      // Insert image blocks into the section content
      updatedContent[sectionKey] = {
        ...updatedContent[sectionKey],
        content: insertImageBlocksIntoContent(
          updatedContent[sectionKey].content || '',
          imageBlocks
        )
      };
    }
  });

  // Handle chapters
  if (updatedContent.chapters) {
    updatedContent.chapters = updatedContent.chapters.map((chapter, index) => {
      const chapterId = `chapter-${chapter.number || index + 1}`;
      
      if (placedImages[chapterId]) {
        const sectionImages = placedImages[chapterId];
        const imageBlocks = convertImagesToBlocks(sectionImages, chapterId);
        
        return {
          ...chapter,
          content: insertImageBlocksIntoContent(
            chapter.content || '',
            imageBlocks
          )
        };
      }
      
      return chapter;
    });
  }

  // Remove the old placedImages structure after migration
  delete updatedContent.placedImages;

  prodLogger.debug('✅ Legacy placedImages data cleaned up after migration');

  return updatedContent;
};

/**
 * Convert placed images array to image block markdown
 * @param {Array} images - Array of placed image objects
 * @param {string} sectionId - Section identifier
 * @returns {Array} Array of image block markdown strings
 */
const convertImagesToBlocks = (images, sectionId) => {
  if (!images || !Array.isArray(images)) return [];

  return images.map((image, index) => {
    // Create image block markdown with metadata as comment
    const imageMarkdown = `![${image.description || 'Image'}](${image.url})`;
    
    // Add metadata as HTML comment for preservation
    const metadata = {
      photographer: image.photographer,
      source: 'migrated',
      position: image.position,
      placedAt: image.placedAt,
      originalId: image.id
    };
    
    const metadataComment = `<!-- IMAGE_METADATA: ${JSON.stringify(metadata)} -->`;
    
    return {
      markdown: imageMarkdown,
      metadata: metadataComment,
      position: image.position || 'top'
    };
  });
};

/**
 * Insert image blocks into content based on their positions
 * @param {string} content - Original content
 * @param {Array} imageBlocks - Array of image block objects
 * @returns {string} Updated content with image blocks
 */
const insertImageBlocksIntoContent = (content, imageBlocks) => {
  if (!imageBlocks || imageBlocks.length === 0) return content;

  let updatedContent = content;
  
  // Group images by position
  const imagesByPosition = imageBlocks.reduce((acc, block) => {
    if (!acc[block.position]) acc[block.position] = [];
    acc[block.position].push(block);
    return acc;
  }, {});

  // Insert images at different positions
  const beforeImages = imagesByPosition.before || [];
  const topImages = imagesByPosition.top || [];
  const middleImages = imagesByPosition.middle || [];
  const bottomImages = imagesByPosition.bottom || [];
  const afterImages = imagesByPosition.after || [];

  // Build final content with images
  const parts = [];

  // Add before images
  beforeImages.forEach(block => {
    parts.push(block.metadata);
    parts.push(block.markdown);
  });

  // Add top images
  topImages.forEach(block => {
    parts.push(block.metadata);
    parts.push(block.markdown);
  });

  // Split content for middle insertion
  if (middleImages.length > 0 && updatedContent) {
    const contentLines = updatedContent.split('\n');
    const middleIndex = Math.floor(contentLines.length / 2);
    
    // Add first half of content
    if (middleIndex > 0) {
      parts.push(contentLines.slice(0, middleIndex).join('\n'));
    }
    
    // Add middle images
    middleImages.forEach(block => {
      parts.push(block.metadata);
      parts.push(block.markdown);
    });
    
    // Add second half of content
    if (middleIndex < contentLines.length) {
      parts.push(contentLines.slice(middleIndex).join('\n'));
    }
  } else {
    // No middle images, add all content
    if (updatedContent) {
      parts.push(updatedContent);
    }
  }

  // Add bottom images
  bottomImages.forEach(block => {
    parts.push(block.metadata);
    parts.push(block.markdown);
  });

  // Add after images
  afterImages.forEach(block => {
    parts.push(block.metadata);
    parts.push(block.markdown);
  });

  return parts.filter(part => part && part.trim()).join('\n\n');
};

/**
 * Extract image metadata from HTML comments in content
 * @param {string} content - Content with potential metadata comments
 * @returns {Object} Extracted metadata and cleaned content
 */
export const extractImageMetadata = (content) => {
  if (!content) return { content, metadata: [] };

  const metadataRegex = /<!-- IMAGE_METADATA: (.*?) -->/g;
  const metadata = [];
  let match;

  while ((match = metadataRegex.exec(content)) !== null) {
    try {
      const parsedMetadata = JSON.parse(match[1]);
      metadata.push(parsedMetadata);
    } catch (error) {
      prodLogger.warn('Failed to parse image metadata:', match[1]);
    }
  }

  // Remove metadata comments from content
  const cleanedContent = content.replace(metadataRegex, '').trim();

  return { content: cleanedContent, metadata };
};

/**
 * Check if content has old placedImages structure
 * @param {Object} generatedContent - Document content to check
 * @returns {boolean} True if migration is needed
 */
export const needsImageMigration = (generatedContent) => {
  return !!(
    generatedContent &&
    generatedContent.placedImages &&
    Object.keys(generatedContent.placedImages).length > 0
  );
};

/**
 * Migrate document on load if needed
 * @param {Object} documentData - Full document data
 * @returns {Object} Migrated document data
 */
export const migrateDocumentIfNeeded = (documentData) => {
  if (!documentData || !documentData.generatedContent) {
    return documentData;
  }

  if (needsImageMigration(documentData.generatedContent)) {
    prodLogger.debug('🔄 Migrating document images to block-based system...');
    
    const migratedContent = migrateImagesToBlocks(documentData.generatedContent);
    
    return {
      ...documentData,
      generatedContent: migratedContent,
      migrated: true,
      migratedAt: new Date().toISOString()
    };
  }

  return documentData;
};
