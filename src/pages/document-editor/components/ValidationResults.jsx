import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';

/**
 * ValidationResults - Component to display document validation results
 * Shows validation scores, issues, and recommendations
 */
const ValidationResults = ({ 
  validationResults, 
  isLoading = false,
  onRevalidate = null,
  className = '' 
}) => {
  const [expandedSection, setExpandedSection] = useState(null);

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
          <span className="text-sm text-gray-600">Validating document...</span>
        </div>
      </div>
    );
  }

  if (!validationResults) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="text-center py-4">
          <Icon name="FileText" size={24} className="text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">No validation results available</p>
          {onRevalidate && (
            <button
              onClick={onRevalidate}
              className="mt-2 text-sm text-primary hover:text-primary-dark"
            >
              Run Validation
            </button>
          )}
        </div>
      </div>
    );
  }

  const { overallScore, scores, issues, recommendations, isValid } = validationResults;

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const getIssueIcon = (type) => {
    switch (type) {
      case 'error': return { name: 'AlertCircle', color: 'text-red-500' };
      case 'warning': return { name: 'AlertTriangle', color: 'text-yellow-500' };
      case 'info': return { name: 'Info', color: 'text-blue-500' };
      default: return { name: 'AlertCircle', color: 'text-gray-500' };
    }
  };

  const toggleSection = (section) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getScoreBgColor(overallScore)}`}>
              <span className={`text-lg font-bold ${getScoreColor(overallScore)}`}>
                {overallScore}
              </span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Document Quality</h3>
              <p className={`text-sm ${isValid ? 'text-green-600' : 'text-red-600'}`}>
                {isValid ? 'Document meets quality standards' : 'Document needs improvement'}
              </p>
            </div>
          </div>
          {onRevalidate && (
            <button
              onClick={onRevalidate}
              className="flex items-center space-x-2 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
            >
              <Icon name="RefreshCw" size={14} />
              <span>Revalidate</span>
            </button>
          )}
        </div>
      </div>

      {/* Score Breakdown */}
      <div className="p-4 border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Score Breakdown</h4>
        <div className="grid grid-cols-2 gap-4">
          {Object.entries(scores).map(([category, score]) => (
            <div key={category} className="flex items-center justify-between">
              <span className="text-sm text-gray-600 capitalize">{category}</span>
              <span className={`text-sm font-medium ${getScoreColor(score)}`}>
                {score}/100
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Issues */}
      {issues && issues.length > 0 && (
        <div className="border-b border-gray-200">
          <button
            onClick={() => toggleSection('issues')}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center space-x-2">
              <Icon name="AlertTriangle" size={16} className="text-yellow-500" />
              <span className="text-sm font-medium text-gray-900">
                Issues ({issues.length})
              </span>
            </div>
            <Icon 
              name={expandedSection === 'issues' ? 'ChevronUp' : 'ChevronDown'} 
              size={16} 
              className="text-gray-400" 
            />
          </button>
          
          {expandedSection === 'issues' && (
            <div className="px-4 pb-4 space-y-3">
              {issues.slice(0, 5).map((issue, index) => {
                const iconInfo = getIssueIcon(issue.type);
                return (
                  <div key={index} className="flex items-start space-x-3">
                    <Icon name={iconInfo.name} size={14} className={`mt-0.5 ${iconInfo.color}`} />
                    <div className="flex-1">
                      <p className="text-sm text-gray-700">{issue.message}</p>
                      <span className="text-xs text-gray-500 capitalize">{issue.category}</span>
                    </div>
                  </div>
                );
              })}
              {issues.length > 5 && (
                <p className="text-xs text-gray-500 text-center">
                  And {issues.length - 5} more issues...
                </p>
              )}
            </div>
          )}
        </div>
      )}

      {/* Recommendations */}
      {recommendations && recommendations.length > 0 && (
        <div>
          <button
            onClick={() => toggleSection('recommendations')}
            className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center space-x-2">
              <Icon name="Lightbulb" size={16} className="text-blue-500" />
              <span className="text-sm font-medium text-gray-900">
                Recommendations ({recommendations.length})
              </span>
            </div>
            <Icon 
              name={expandedSection === 'recommendations' ? 'ChevronUp' : 'ChevronDown'} 
              size={16} 
              className="text-gray-400" 
            />
          </button>
          
          {expandedSection === 'recommendations' && (
            <div className="px-4 pb-4 space-y-2">
              {recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <Icon name="ArrowRight" size={14} className="mt-0.5 text-blue-500" />
                  <p className="text-sm text-gray-700">{recommendation}</p>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* No Issues State */}
      {(!issues || issues.length === 0) && (!recommendations || recommendations.length === 0) && (
        <div className="p-4 text-center">
          <Icon name="CheckCircle" size={24} className="text-green-500 mx-auto mb-2" />
          <p className="text-sm text-gray-600">No issues found! Your document looks great.</p>
        </div>
      )}
    </div>
  );
};

export default ValidationResults;
