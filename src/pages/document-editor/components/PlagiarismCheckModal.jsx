import React, { useState } from 'react';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

import { prodLogger } from '../../../utils/prodLogger.js';
/**
 * PlagiarismCheckModal - Modal for displaying plagiarism check results in document editor
 * Provides detailed results and actions without leaving the editor context
 */
const PlagiarismCheckModal = ({
  isOpen,
  onClose,
  results = null,
  isChecking = false,
  onStartNewCheck
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  // Handle navigation to match location in document
  const handleNavigateToMatch = (match) => {
    prodLogger.debug('Navigating to match location:', match.location);
    // TODO: Implement scroll to specific location in document
    // This would typically scroll to the chapter and highlight the matched text
  };



  if (!isOpen) return null;

  const tabs = [
    { id: 'overview', name: 'Overview', icon: 'BarChart3' },
    { id: 'matches', name: 'Matches', icon: 'Search' }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Icon name="Shield" size={24} className="text-primary" />
            <h2 className="text-xl font-semibold text-gray-900">
              Plagiarism Check Results
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <Icon name="X" size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {isChecking ? (
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Checking for Plagiarism</h3>
                <p className="text-gray-600">Analyzing your document content...</p>
              </div>
            </div>
          ) : results ? (
            <div className="flex h-96">
              {/* Tabs */}
              <div className="w-48 border-r border-gray-200 p-4">
                <nav className="space-y-2">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-primary text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Icon name={tab.icon} size={16} />
                      <span>{tab.name}</span>
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="flex-1 p-6 overflow-auto">
                {activeTab === 'overview' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="bg-red-50 p-4 rounded-lg">
                        <div className="text-2xl font-bold text-red-600">
                          {results.similarityPercentage}%
                        </div>
                        <div className="text-sm text-red-700">Similarity Found</div>
                      </div>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {results.webSources}
                        </div>
                        <div className="text-sm text-blue-700">Web Sources</div>
                      </div>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {results.academicSources}
                        </div>
                        <div className="text-sm text-green-700">Academic Sources</div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">Top Sources</h4>
                      <div className="space-y-2">
                        {results.topSources?.slice(0, 3).map((source, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex-1">
                              <div className="font-medium text-gray-900 text-sm">{source.title}</div>
                              <div className="text-xs text-gray-600">{source.url}</div>
                            </div>
                            <div className="text-sm font-medium text-red-600">
                              {source.similarity}%
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'matches' && (
                  <div className="space-y-4">
                    {results.detailedMatches && results.detailedMatches.length > 0 ? (
                      <>
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-medium text-gray-900">
                            {results.detailedMatches.length} Matches Found
                          </h4>
                          <div className="text-sm text-gray-600">
                            Sorted by similarity
                          </div>
                        </div>

                        <div className="space-y-4 max-h-80 overflow-y-auto">
                          {results.detailedMatches.map((match) => (
                            <div key={match.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                              {/* Match Header */}
                              <div className="flex items-start justify-between mb-3">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                      match.source.type === 'academic'
                                        ? 'bg-blue-100 text-blue-800'
                                        : 'bg-green-100 text-green-800'
                                    }`}>
                                      {match.source.type === 'academic' ? 'Academic' : 'Web'}
                                    </span>
                                    <span className="text-sm font-medium text-gray-900">
                                      {match.source.title}
                                    </span>
                                  </div>
                                  <a
                                    href={match.source.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-xs text-blue-600 hover:text-blue-800 hover:underline"
                                  >
                                    {match.source.url}
                                  </a>
                                </div>
                                <div className="text-right">
                                  <div className={`text-lg font-bold ${
                                    match.similarity >= 80 ? 'text-red-600' :
                                    match.similarity >= 60 ? 'text-orange-600' : 'text-yellow-600'
                                  }`}>
                                    {match.similarity}%
                                  </div>
                                  <div className="text-xs text-gray-500">similarity</div>
                                </div>
                              </div>

                              {/* Matched Text Comparison */}
                              <div className="space-y-3">
                                <div>
                                  <div className="text-xs font-medium text-gray-700 mb-1">Your Text:</div>
                                  <div className="bg-red-50 border-l-4 border-red-400 p-3 rounded">
                                    <span className="text-sm text-gray-600">{match.context.before} </span>
                                    <span className="bg-red-200 text-red-900 px-1 rounded font-medium">
                                      {match.matchedText}
                                    </span>
                                    <span className="text-sm text-gray-600"> {match.context.after}</span>
                                  </div>
                                </div>

                                <div>
                                  <div className="text-xs font-medium text-gray-700 mb-1">Source Text:</div>
                                  <div className="bg-gray-50 border-l-4 border-gray-400 p-3 rounded">
                                    <span className="text-sm text-gray-800 font-medium">
                                      {match.sourceText}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              {/* Actions */}
                              <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-100">
                                <button
                                  onClick={() => handleNavigateToMatch(match)}
                                  className="flex items-center space-x-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
                                >
                                  <Icon name="MapPin" size={14} />
                                  <span>Go to location</span>
                                </button>
                                <div className="text-xs text-gray-500">
                                  Found in {match.location.chapter}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </>
                    ) : (
                      <div className="text-center py-8">
                        <Icon name="Search" size={48} className="text-gray-400 mx-auto mb-4" />
                        <h4 className="text-lg font-medium text-gray-900 mb-2">No Matches Found</h4>
                        <p className="text-gray-600">No plagiarism matches were detected in your document.</p>
                      </div>
                    )}
                  </div>
                )}


              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <Icon name="Shield" size={48} className="text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Results Available</h3>
                <p className="text-gray-600 mb-4">Start a plagiarism check to see results here.</p>
                <Button
                  variant="primary"
                  onClick={onStartNewCheck}
                  iconName="Shield"
                >
                  Start Check
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {results && !isChecking && (
          <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
            <div className="text-sm text-gray-600">
              Checked {results.wordCount?.toLocaleString()} words in {results.processingTime}
            </div>
            <div className="flex space-x-3">
              <Button
                variant="primary"
                onClick={onStartNewCheck}
                iconName="RotateCcw"
              >
                Check Again
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlagiarismCheckModal;
