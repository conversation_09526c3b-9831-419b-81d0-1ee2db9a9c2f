import React, { useState } from 'react';
import Button from '../../../components/ui/Button';

/**
 * ImagePlaceholder - Shows image suggestions and placement options
 * Allows users to preview and place images from Unsplash
 */
const ImagePlaceholder = ({
  chapterId,
  suggestions,
  onImagePlace,
  position = 'top',
  isReviewMode = false
}) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Don't render in review mode
  if (isReviewMode) {
    return null;
  }

  if (!suggestions?.images?.length) {
    return null;
  }

  const handleImageSelect = (image) => {
    setSelectedImage(image);
  };

  const handleImagePlace = () => {
    if (selectedImage) {
      onImagePlace(chapterId, selectedImage, position);
      setSelectedImage(null);
      setShowSuggestions(false);
    }
  };

  const handleImageReplace = (image) => {
    onImagePlace(chapterId, image, position);
  };

  return (
    <div className="border-b border-gray-100 bg-blue-50/30">
      <div className="p-6">
        {!showSuggestions ? (
          // Collapsed state - show suggestion prompt
          <div className="flex items-center justify-between p-4 bg-white border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">
                  Image suggestions available
                </h4>
                <p className="text-sm text-gray-600">
                  {suggestions.images.length} images found for "{suggestions.searchQuery}"
                </p>
              </div>
            </div>
            <Button
              variant="primary"
              size="sm"
              onClick={() => setShowSuggestions(true)}
              iconName="Eye"
              iconPosition="left"
            >
              View Images
            </Button>
          </div>
        ) : (
          // Expanded state - show image grid
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">
                  Choose an image for {suggestions.chapterTitle}
                </h4>
                <p className="text-sm text-gray-600">
                  Search: "{suggestions.searchQuery}"
                </p>
              </div>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowSuggestions(false)}
                iconName="X"
              >
                Close
              </Button>
            </div>

            {/* Image Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {suggestions.images.map((image) => (
                <div
                  key={image.id}
                  className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                    selectedImage?.id === image.id
                      ? 'border-primary shadow-lg'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleImageSelect(image)}
                >
                  <div className="aspect-video">
                    <img
                      src={image.url}
                      alt={image.description}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleImageReplace(image);
                        }}
                        iconName="Plus"
                        iconPosition="left"
                      >
                        Add Image
                      </Button>
                    </div>
                  </div>

                  {/* Selection indicator */}
                  {selectedImage?.id === image.id && (
                    <div className="absolute top-2 right-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}

                  {/* Image info */}
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
                    <p className="text-white text-sm font-medium truncate">
                      {image.description}
                    </p>
                    <p className="text-white/80 text-xs">
                      by {image.photographer}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Selected image preview and actions */}
            {selectedImage && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-start space-x-4">
                  <img
                    src={selectedImage.thumbnailUrl}
                    alt={selectedImage.description}
                    className="w-20 h-16 object-cover rounded"
                  />
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900">
                      {selectedImage.description}
                    </h5>
                    <p className="text-sm text-gray-600">
                      by {selectedImage.photographer}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {selectedImage.width} × {selectedImage.height}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => setSelectedImage(null)}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={handleImagePlace}
                      iconName="Check"
                      iconPosition="left"
                    >
                      Place Image
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Placement options */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h5 className="font-medium text-gray-900 mb-2">Placement Options</h5>
              <div className="grid grid-cols-2 gap-2 text-sm">
                {suggestions.suggestedPlacements?.map((placement, index) => (
                  <div key={index} className="flex items-center space-x-2 text-gray-600">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>{placement.description}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Attribution notice */}
            <div className="text-xs text-gray-500 bg-gray-50 rounded p-2">
              📸 Images provided by Unsplash. By using these images, you agree to give appropriate credit to the photographers.
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImagePlaceholder;
