/**
 * Integration tests for Fix Grammar & Spelling feature
 * Tests the complete workflow from UI interaction to content replacement
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import DocumentCanvasMinimal from '../DocumentCanvasMinimal';

// Mock the AI service
jest.mock('../../../../services/aiService.js', () => ({
  fixGrammar: jest.fn(),
  DocGenerateError: class DocGenerateError extends Error {
    constructor(message, code) {
      super(message);
      this.code = code;
    }
  },
}));

// Mock the content processor
jest.mock('../../../../utils/contentProcessor.js', () => ({
  extractNodeContent: jest.fn(),
  replaceNodeContent: jest.fn(),
  analyzeContentChanges: jest.fn(),
  validateEditorState: jest.fn(),
}));

// Mock TipTap editor
const mockEditor = {
  state: {
    selection: {
      $from: {
        node: () => ({
          type: { name: 'paragraph' },
          textContent: 'This have grammar error',
          nodeSize: 25,
          attrs: {},
        }),
        start: () => 1,
        end: () => 24,
        before: () => 0,
        depth: 1,
      },
      from: 0,
      to: 0,
    },
    doc: {
      textBetween: () => 'This have grammar error',
      content: { size: 50 },
    },
    tr: {
      replaceWith: jest.fn().mockReturnThis(),
    },
  },
  schema: {
    text: (content) => ({ textContent: content }),
    nodes: {
      paragraph: {
        create: jest.fn((attrs, content) => ({
          type: { name: 'paragraph' },
          attrs,
          content: [content],
        })),
      },
    },
  },
  commands: {
    setTextSelection: jest.fn(),
    focus: jest.fn(),
  },
  view: {
    dispatch: jest.fn(),
  },
  getText: () => 'This have grammar error',
  isEditable: true,
  isDestroyed: false,
};

// Mock useEditor hook
jest.mock('@tiptap/react', () => ({
  useEditor: () => mockEditor,
  EditorContent: ({ editor }) => (
    <div data-testid="editor-content">
      {editor?.getText() || 'This have grammar error'}
    </div>
  ),
}));

describe('Fix Grammar & Spelling Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    const { extractNodeContent, replaceNodeContent, analyzeContentChanges, validateEditorState } = 
      require('../../../../utils/contentProcessor.js');
    
    validateEditorState.mockReturnValue({ valid: true });
    extractNodeContent.mockReturnValue({
      text: 'This have grammar error',
      selectedText: '',
      nodeContent: 'This have grammar error',
      nodeType: 'paragraph',
      hasSelection: false,
      selectionRange: { from: 0, to: 0 },
      documentContext: '',
      nodeSize: 25,
      nodePosition: 1,
      isEmpty: false,
    });
    
    analyzeContentChanges.mockReturnValue({
      hasChanges: true,
      similarity: 0.8,
      lengthChange: 0.1,
    });
    
    replaceNodeContent.mockImplementation(() => {
      // Simulate successful replacement
      return true;
    });
  });

  test('should fix grammar and maintain cursor position', async () => {
    const { fixGrammar } = require('../../../../services/aiService.js');
    fixGrammar.mockResolvedValue('This has correct grammar');

    render(<DocumentCanvasMinimal content="This have grammar error" />);

    // Find and click the DocGenerate button
    const docGenerateButton = screen.getByText('DocGenerate');
    fireEvent.click(docGenerateButton);

    // Wait for menu to appear and click Fix Grammar
    await waitFor(() => {
      const fixGrammarButton = screen.getByText('Fix spelling & grammar');
      expect(fixGrammarButton).toBeInTheDocument();
    });

    const fixGrammarButton = screen.getByText('Fix spelling & grammar');
    fireEvent.click(fixGrammarButton);

    // Wait for content preview modal
    await waitFor(() => {
      expect(screen.getByText('Fix Grammar & Spelling')).toBeInTheDocument();
    });

    // Click Apply Changes
    const applyButton = screen.getByText('Apply Changes');
    fireEvent.click(applyButton);

    // Verify the workflow
    await waitFor(() => {
      const { extractNodeContent, replaceNodeContent } = 
        require('../../../../utils/contentProcessor.js');
      
      // Verify content extraction was called
      expect(extractNodeContent).toHaveBeenCalledWith(mockEditor);
      
      // Verify AI service was called
      expect(fixGrammar).toHaveBeenCalledWith(
        'This have grammar error',
        expect.any(Object)
      );
      
      // Verify content replacement was called with correct parameters
      expect(replaceNodeContent).toHaveBeenCalledWith(
        mockEditor,
        'This has correct grammar',
        expect.objectContaining({
          text: 'This have grammar error',
          nodeType: 'paragraph',
          hasSelection: false,
        }),
        expect.objectContaining({
          preserveFormatting: true,
          focusAfter: true,
        })
      );
    });
  });

  test('should handle different node types correctly', async () => {
    const { fixGrammar } = require('../../../../services/aiService.js');
    const { extractNodeContent } = require('../../../../utils/contentProcessor.js');
    
    // Mock heading node
    extractNodeContent.mockReturnValue({
      text: 'Heading with error',
      nodeType: 'heading',
      hasSelection: false,
      selectionRange: { from: 0, to: 0 },
      documentContext: '',
      nodeSize: 20,
      nodePosition: 1,
      isEmpty: false,
    });
    
    fixGrammar.mockResolvedValue('Heading without error');

    render(<DocumentCanvasMinimal content="# Heading with error" />);

    // Trigger fix grammar workflow
    const docGenerateButton = screen.getByText('DocGenerate');
    fireEvent.click(docGenerateButton);

    await waitFor(() => {
      const fixGrammarButton = screen.getByText('Fix spelling & grammar');
      fireEvent.click(fixGrammarButton);
    });

    await waitFor(() => {
      const applyButton = screen.getByText('Apply Changes');
      fireEvent.click(applyButton);
    });

    // Verify heading-specific handling
    await waitFor(() => {
      const { replaceNodeContent } = require('../../../../utils/contentProcessor.js');
      
      expect(replaceNodeContent).toHaveBeenCalledWith(
        mockEditor,
        'Heading without error',
        expect.objectContaining({
          nodeType: 'heading',
        }),
        expect.any(Object)
      );
    });
  });

  test('should handle errors gracefully', async () => {
    const { fixGrammar, DocGenerateError } = require('../../../../services/aiService.js');
    fixGrammar.mockRejectedValue(new DocGenerateError('AI service error', 'SERVICE_ERROR'));

    render(<DocumentCanvasMinimal content="This have grammar error" />);

    // Trigger fix grammar workflow
    const docGenerateButton = screen.getByText('DocGenerate');
    fireEvent.click(docGenerateButton);

    await waitFor(() => {
      const fixGrammarButton = screen.getByText('Fix spelling & grammar');
      fireEvent.click(fixGrammarButton);
    });

    await waitFor(() => {
      const applyButton = screen.getByText('Apply Changes');
      fireEvent.click(applyButton);
    });

    // Verify error handling
    await waitFor(() => {
      // Should show error message
      expect(screen.getByText(/error/i)).toBeInTheDocument();
    });
  });

  test('should preserve document structure during replacement', async () => {
    const { fixGrammar } = require('../../../../services/aiService.js');
    const { replaceNodeContent } = require('../../../../utils/contentProcessor.js');
    
    fixGrammar.mockResolvedValue('Corrected content');

    render(<DocumentCanvasMinimal content="Original content" />);

    // Trigger workflow
    const docGenerateButton = screen.getByText('DocGenerate');
    fireEvent.click(docGenerateButton);

    await waitFor(() => {
      const fixGrammarButton = screen.getByText('Fix spelling & grammar');
      fireEvent.click(fixGrammarButton);
    });

    await waitFor(() => {
      const applyButton = screen.getByText('Apply Changes');
      fireEvent.click(applyButton);
    });

    // Verify that replaceNodeContent was called with preserveFormatting
    await waitFor(() => {
      expect(replaceNodeContent).toHaveBeenCalledWith(
        expect.any(Object),
        'Corrected content',
        expect.any(Object),
        expect.objectContaining({
          preserveFormatting: true,
          focusAfter: true,
        })
      );
    });
  });
});
