# DocumentCanvasMinimal Loading States Test Plan

## Overview
This document outlines comprehensive testing scenarios for the enhanced loading states and user feedback in the DocumentCanvasMinimal component.

## Test Scenarios

### 1. ContentPreviewModal Loading States

#### Test Case 1.1: Modal Loading State
**Scenario**: User clicks "Rewrite Content" button in ContentPreviewModal
**Expected Behavior**:
- <PERSON><PERSON> shows loading spinner and "Processing..." text
- But<PERSON> becomes disabled
- Cancel button becomes disabled
- <PERSON><PERSON> cannot be closed by clicking outside or pressing Escape
- Close (X) button becomes disabled

#### Test Case 1.2: Modal Success Flow
**Scenario**: Content processing completes successfully
**Expected Behavior**:
- Modal closes automatically
- Success toast notification appears with appropriate message
- Content in editor is updated with new text

#### Test Case 1.3: Modal Error Flow
**Scenario**: Content processing fails
**Expected Behavior**:
- Modal closes
- Error toast notification appears
- Error message displayed in DocGenerate menu with retry option

### 2. DocGenerate Menu Loading States

#### Test Case 2.1: Menu Header Loading Indicator
**Scenario**: Any DocGenerate action is in progress
**Expected Behavior**:
- Header shows "Processing..." with spinning indicator
- All menu buttons become disabled with opacity reduction
- Active action button shows spinner instead of icon

#### Test Case 2.2: Individual Button Loading States
**Scenario**: User clicks "Rewrite" option
**Expected Behavior**:
- Rewrite button shows spinner and becomes disabled
- Other buttons (Fix Grammar, Reduce, Expand) become disabled
- Button text remains visible alongside spinner

#### Test Case 2.3: Error Display in Menu
**Scenario**: Operation fails
**Expected Behavior**:
- Error message appears at top of menu with red background
- "Try Again" button appears if retry is available
- "Dismiss" button allows clearing the error
- Error auto-clears after 10 seconds

### 3. Progress Indicators for Long Operations

#### Test Case 3.1: Progress Indicator Timing
**Scenario**: Operation takes longer than 2 seconds
**Expected Behavior**:
- Progress indicator appears in top-right corner after 2-second delay
- Shows spinning animation with "Processing..." text
- Displays specific action message (e.g., "Rewriting your content...")

#### Test Case 3.2: Progress Indicator Cleanup
**Scenario**: Operation completes (success or failure)
**Expected Behavior**:
- Progress indicator disappears immediately
- No lingering UI elements

### 4. Toast Notifications

#### Test Case 4.1: Success Toast
**Scenario**: Content operation succeeds
**Expected Behavior**:
- Green toast appears in bottom-right corner
- Shows appropriate success message based on action
- Auto-dismisses after 3 seconds
- Can be manually dismissed by clicking

#### Test Case 4.2: Error Toast
**Scenario**: Content operation fails
**Expected Behavior**:
- Red toast appears with error message
- Includes hint about retry option in menu
- Auto-dismisses after 3 seconds
- Can be manually dismissed

### 5. Error Recovery Mechanisms

#### Test Case 5.1: Retry Functionality
**Scenario**: User clicks "Try Again" after failed operation
**Expected Behavior**:
- Error message clears
- Same operation retries with full loading state cycle
- If successful, shows success feedback
- If fails again, shows error with retry option again

#### Test Case 5.2: Error Dismissal
**Scenario**: User clicks "Dismiss" on error
**Expected Behavior**:
- Error message clears immediately
- Menu returns to normal state
- No retry option available until next failure

### 6. Edge Cases and Error Scenarios

#### Test Case 6.1: Network Failure
**Scenario**: Network request fails
**Expected Behavior**:
- Appropriate error message about connectivity
- Retry option available
- Loading states clear properly

#### Test Case 6.2: API Rate Limiting
**Scenario**: API returns rate limit error
**Expected Behavior**:
- Specific error message about quota/limits
- Retry option available
- Suggests trying again later

#### Test Case 6.3: Invalid Content
**Scenario**: No content selected for processing
**Expected Behavior**:
- Error message: "No content to process. Please add some text first."
- No retry option (since it's a user error)
- Clear guidance on what user needs to do

#### Test Case 6.4: Multiple Rapid Clicks
**Scenario**: User clicks multiple buttons rapidly
**Expected Behavior**:
- Only first click registers
- Subsequent clicks ignored while loading
- No duplicate requests sent
- UI remains consistent

### 7. Accessibility Testing

#### Test Case 7.1: Screen Reader Support
**Expected Behavior**:
- Loading states announced to screen readers
- Button state changes communicated
- Error messages read aloud
- Progress indicators have appropriate ARIA labels

#### Test Case 7.2: Keyboard Navigation
**Expected Behavior**:
- Disabled buttons not focusable during loading
- Escape key behavior respects loading states
- Tab navigation works correctly with dynamic content

## Manual Testing Checklist

- [ ] Test all DocGenerate actions (Rewrite, Fix Grammar, Reduce, Expand)
- [ ] Verify loading states appear and disappear correctly
- [ ] Test error scenarios with network disconnection
- [ ] Verify retry functionality works
- [ ] Test rapid clicking and duplicate prevention
- [ ] Check toast notifications timing and dismissal
- [ ] Verify progress indicators for long operations
- [ ] Test modal loading states and interactions
- [ ] Verify accessibility with screen reader
- [ ] Test keyboard navigation during loading states

## Automated Testing Notes

Key areas for unit/integration tests:
1. State management during loading cycles
2. Timer cleanup for progress indicators
3. Error handling and recovery flows
4. Toast notification lifecycle
5. Modal interaction blocking during loading
6. Button state management
