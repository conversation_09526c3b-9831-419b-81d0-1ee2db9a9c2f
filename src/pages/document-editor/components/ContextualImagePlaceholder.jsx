import React, { useState } from 'react';

/**
 * ContextualImagePlaceholder - A placeholder component that appears at AI-determined locations
 * 
 * This component:
 * - Renders at optimal image placement locations determined by AI
 * - Shows contextual information about why the image placement is suggested
 * - Triggers the contextual image selection modal when clicked
 * - Provides visual feedback for available image suggestions
 */
const ContextualImagePlaceholder = ({
  placement,
  chapterId,
  imageSuggestions = [],
  onOpenImageModal,
  isVisible = true
}) => {
  const [isHovered, setIsHovered] = useState(false);

  if (!isVisible || !placement) {
    return null;
  }

  const handleClick = () => {
    if (onOpenImageModal && chapterId) {
      onOpenImageModal(chapterId, placement.id);
    }
  };

  // Get priority-based styling
  const getPriorityStyles = () => {
    switch (placement.priority) {
      case 'high':
        return {
          border: 'border-blue-300',
          bg: 'bg-blue-50',
          hover: 'hover:bg-blue-100',
          text: 'text-blue-700',
          icon: '🎯'
        };
      case 'medium':
        return {
          border: 'border-green-300',
          bg: 'bg-green-50',
          hover: 'hover:bg-green-100',
          text: 'text-green-700',
          icon: '📍'
        };
      default:
        return {
          border: 'border-gray-300',
          bg: 'bg-gray-50',
          hover: 'hover:bg-gray-100',
          text: 'text-gray-700',
          icon: '📷'
        };
    }
  };

  const styles = getPriorityStyles();
  const hasImages = imageSuggestions && imageSuggestions.length > 0;

  return (
    <div className="my-6 flex justify-center">
      <div
        className={`
          relative max-w-md w-full p-4 rounded-lg border-2 border-dashed cursor-pointer
          transition-all duration-200 transform hover:scale-105
          ${styles.border} ${styles.bg} ${styles.hover}
          ${isHovered ? 'shadow-lg' : 'shadow-sm'}
        `}
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Main Content */}
        <div className="text-center">
          {/* Icon and Title */}
          <div className="flex items-center justify-center mb-2">
            <span className="text-2xl mr-2">{styles.icon}</span>
            <h3 className={`font-semibold ${styles.text}`}>
              Add Image Here
            </h3>
          </div>

          {/* Description */}
          <p className={`text-sm mb-3 ${styles.text} opacity-80`}>
            {placement.description}
          </p>

          {/* Contextual Hint */}
          {placement.contextualHint && (
            <p className="text-xs text-gray-600 mb-3 italic">
              {placement.contextualHint}
            </p>
          )}

          {/* Image Count Indicator */}
          {hasImages && (
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="flex -space-x-1">
                {imageSuggestions.slice(0, 3).map((image, index) => (
                  <div
                    key={image.id || index}
                    className="w-8 h-8 rounded-full border-2 border-white overflow-hidden shadow-sm"
                  >
                    <img
                      src={image.thumbnailUrl || image.url}
                      alt={image.description}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
              <span className={`text-xs font-medium ${styles.text}`}>
                {imageSuggestions.length} suggestion{imageSuggestions.length !== 1 ? 's' : ''} available
              </span>
            </div>
          )}

          {/* Call to Action */}
          <div className={`text-xs font-medium ${styles.text} opacity-90`}>
            Click to select from AI suggestions
          </div>
        </div>

        {/* Priority Badge */}
        <div className="absolute top-2 right-2">
          <span className={`
            px-2 py-1 text-xs font-medium rounded-full
            ${placement.priority === 'high' ? 'bg-blue-200 text-blue-800' : ''}
            ${placement.priority === 'medium' ? 'bg-green-200 text-green-800' : ''}
            ${placement.priority === 'low' ? 'bg-gray-200 text-gray-800' : ''}
          `}>
            {placement.priority}
          </span>
        </div>

        {/* Hover Effect Overlay */}
        {isHovered && (
          <div className="absolute inset-0 bg-white bg-opacity-20 rounded-lg pointer-events-none transition-opacity duration-200" />
        )}
      </div>
    </div>
  );
};

export default ContextualImagePlaceholder;
