import { Node } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";
import EnhancedImageNodeView from "./EnhancedImageNodeView";

/**
 * Enhanced Image Extension with Delete Controls
 *
 * This extension extends the basic Tiptap Image node to include:
 * - Hover delete button overlay
 * - Enhanced visual feedback
 * - Keyboard shortcuts for deletion
 * - Read-only mode respect
 */
export const EnhancedImageExtension = Node.create({
  name: "enhancedImage",

  // Inherit from image node
  group: "block",
  draggable: true,
  selectable: true,
  atom: true,

  addAttributes() {
    return {
      src: {
        default: null,
      },
      alt: {
        default: null,
      },
      title: {
        default: null,
      },
      width: {
        default: null,
      },
      height: {
        default: null,
      },
      class: {
        default: "tiptap-image max-w-full h-auto rounded-lg shadow-sm",
      },
      // AI-generated image attributes for export detection
      "data-ai-generated": {
        default: null,
        parseHTML: (element) => element.getAttribute("data-ai-generated"),
        renderHTML: (attributes) => {
          if (!attributes["data-ai-generated"]) return {};
          return { "data-ai-generated": attributes["data-ai-generated"] };
        },
      },
      "data-generation-id": {
        default: null,
        parseHTML: (element) => element.getAttribute("data-generation-id"),
        renderHTML: (attributes) => {
          if (!attributes["data-generation-id"]) return {};
          return { "data-generation-id": attributes["data-generation-id"] };
        },
      },
      "data-prompt": {
        default: null,
        parseHTML: (element) => element.getAttribute("data-prompt"),
        renderHTML: (attributes) => {
          if (!attributes["data-prompt"]) return {};
          return { "data-prompt": attributes["data-prompt"] };
        },
      },
      "data-style": {
        default: null,
        parseHTML: (element) => element.getAttribute("data-style"),
        renderHTML: (attributes) => {
          if (!attributes["data-style"]) return {};
          return { "data-style": attributes["data-style"] };
        },
      },
      "data-size": {
        default: null,
        parseHTML: (element) => element.getAttribute("data-size"),
        renderHTML: (attributes) => {
          if (!attributes["data-size"]) return {};
          return { "data-size": attributes["data-size"] };
        },
      },
      // Uploaded image attribute for export detection
      "data-uploaded": {
        default: null,
        parseHTML: (element) => element.getAttribute("data-uploaded"),
        renderHTML: (attributes) => {
          if (!attributes["data-uploaded"]) return {};
          return { "data-uploaded": attributes["data-uploaded"] };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "img[src]",
        getAttrs: (element) => ({
          src: element.getAttribute("src"),
          alt: element.getAttribute("alt"),
          title: element.getAttribute("title"),
          width: element.getAttribute("width"),
          height: element.getAttribute("height"),
          class:
            element.getAttribute("class") ||
            "tiptap-image max-w-full h-auto rounded-lg shadow-sm",
          // Extract data attributes for AI-generated images (critical for export detection)
          "data-ai-generated": element.getAttribute("data-ai-generated"),
          "data-generation-id": element.getAttribute("data-generation-id"),
          "data-prompt": element.getAttribute("data-prompt"),
          "data-style": element.getAttribute("data-style"),
          "data-size": element.getAttribute("data-size"),
          // Extract data attribute for uploaded images (critical for export detection)
          "data-uploaded": element.getAttribute("data-uploaded"),
        }),
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ["img", HTMLAttributes];
  },

  addCommands() {
    return {
      setEnhancedImage:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          });
        },
      deleteSelectedImage:
        () =>
        ({ editor }) => {
          const { selection } = editor.state;
          const { $from } = selection;
          const currentNode = $from.node();

          if (currentNode.type.name === this.name) {
            return editor.chain().focus().deleteSelection().run();
          }

          return false;
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      Delete: ({ editor }) => {
        const { selection } = editor.state;
        const { $from } = selection;
        const currentNode = $from.node();

        if (currentNode.type.name === this.name) {
          return editor.commands.deleteSelectedImage();
        }

        return false;
      },
      Backspace: ({ editor }) => {
        const { selection } = editor.state;
        const { $from } = selection;
        const currentNode = $from.node();

        if (currentNode.type.name === this.name) {
          return editor.commands.deleteSelectedImage();
        }

        return false;
      },
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(EnhancedImageNodeView);
  },
});

export default EnhancedImageExtension;
