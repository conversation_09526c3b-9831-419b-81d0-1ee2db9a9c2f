import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

/**
 * Simple toast notification component for operation feedback
 */
const ToastNotification = ({ message, type = 'success', duration = 3000, onClose }) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => onClose?.(), 300); // Allow fade out animation
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  if (!isVisible) return null;

  const iconName = type === 'success' ? 'Check' : type === 'error' ? 'X' : 'Info';
  const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

  return (
    <div className={`fixed bottom-4 right-4 ${bgColor} text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 transition-opacity duration-300 ${isVisible ? 'opacity-100' : 'opacity-0'} z-50`}>
      <Icon name={iconName} size={16} />
      <span className="text-sm font-medium">{message}</span>
    </div>
  );
};

export default ToastNotification;
