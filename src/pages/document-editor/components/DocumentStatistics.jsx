import React from 'react';
import Icon from '../../../components/AppIcon';

/**
 * DocumentStatistics - Component to display document statistics and metrics
 * Shows word counts, reading time, structure info, and other metrics
 */
const DocumentStatistics = ({ 
  generatedContent, 
  validationResults = null,
  className = '' 
}) => {
  if (!generatedContent) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="text-center py-4">
          <Icon name="BarChart3" size={24} className="text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">No document data available</p>
        </div>
      </div>
    );
  }

  // Calculate statistics
  const stats = calculateDocumentStatistics(generatedContent);
  const validationMetrics = validationResults?.details || {};

  const formatNumber = (num) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatDuration = (minutes) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  const StatCard = ({ icon, label, value, subtext = null, color = 'text-gray-600' }) => (
    <div className="bg-gray-50 rounded-lg p-3">
      <div className="flex items-center space-x-2 mb-1">
        <Icon name={icon} size={16} className={color} />
        <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">{label}</span>
      </div>
      <div className="text-lg font-semibold text-gray-900">{value}</div>
      {subtext && <div className="text-xs text-gray-500">{subtext}</div>}
    </div>
  );

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Icon name="BarChart3" size={20} className="text-primary" />
          <h3 className="text-lg font-semibold text-gray-900">Document Statistics</h3>
        </div>
      </div>

      {/* Main Statistics Grid */}
      <div className="p-4 space-y-4">
        {/* Word Count & Reading Time */}
        <div className="grid grid-cols-2 gap-3">
          <StatCard
            icon="FileText"
            label="Total Words"
            value={formatNumber(stats.totalWords)}
            subtext={`${stats.totalCharacters} characters`}
            color="text-blue-600"
          />
          <StatCard
            icon="Clock"
            label="Reading Time"
            value={formatDuration(stats.readingTime)}
            subtext={`${stats.wordsPerMinute} WPM`}
            color="text-green-600"
          />
        </div>

        {/* Structure */}
        <div className="grid grid-cols-2 gap-3">
          <StatCard
            icon="List"
            label="Chapters"
            value={stats.chapterCount}
            subtext={`Avg ${formatNumber(stats.averageWordsPerChapter)} words/chapter`}
            color="text-purple-600"
          />
          <StatCard
            icon="Hash"
            label="Sections"
            value={stats.sectionCount}
            subtext="Including intro & conclusion"
            color="text-orange-600"
          />
        </div>

        {/* Content Analysis */}
        <div className="grid grid-cols-2 gap-3">
          <StatCard
            icon="MessageSquare"
            label="Paragraphs"
            value={stats.paragraphCount}
            subtext={`Avg ${stats.averageWordsPerParagraph} words/paragraph`}
            color="text-indigo-600"
          />
          <StatCard
            icon="Type"
            label="Sentences"
            value={stats.sentenceCount}
            subtext={`Avg ${stats.averageWordsPerSentence} words/sentence`}
            color="text-pink-600"
          />
        </div>

        {/* Quality Metrics (if validation results available) */}
        {validationResults && (
          <>
            <div className="border-t border-gray-200 pt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Quality Metrics</h4>
              <div className="grid grid-cols-2 gap-3">
                {validationMetrics.content?.metrics?.readabilityScore && (
                  <StatCard
                    icon="Eye"
                    label="Readability"
                    value={`${validationMetrics.content.metrics.readabilityScore}/100`}
                    subtext="Flesch Reading Ease"
                    color="text-teal-600"
                  />
                )}
                {validationMetrics.structure?.metrics?.totalWords && (
                  <StatCard
                    icon="Target"
                    label="Completion"
                    value={`${Math.round((stats.totalWords / 2000) * 100)}%`}
                    subtext="Based on 2000 word target"
                    color="text-emerald-600"
                  />
                )}
              </div>
            </div>
          </>
        )}

        {/* Document Breakdown */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Content Breakdown</h4>
          <div className="space-y-2">
            {/* Introduction */}
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Introduction</span>
              <span className="font-medium">{formatNumber(stats.introductionWords)} words</span>
            </div>
            
            {/* Chapters */}
            {stats.chapterBreakdown.map((chapter, index) => (
              <div key={index} className="flex justify-between items-center text-sm">
                <span className="text-gray-600">Chapter {chapter.number}</span>
                <span className="font-medium">{formatNumber(chapter.words)} words</span>
              </div>
            ))}
            
            {/* Conclusion */}
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Conclusion</span>
              <span className="font-medium">{formatNumber(stats.conclusionWords)} words</span>
            </div>
          </div>
        </div>

        {/* Export Information */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Estimated Export Sizes</h4>
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-2 bg-gray-50 rounded">
              <div className="text-sm font-medium text-gray-900">{stats.estimatedPages}</div>
              <div className="text-xs text-gray-500">Pages (PDF)</div>
            </div>
            <div className="text-center p-2 bg-gray-50 rounded">
              <div className="text-sm font-medium text-gray-900">{stats.estimatedFileSize}</div>
              <div className="text-xs text-gray-500">File Size</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper function to calculate document statistics
const calculateDocumentStatistics = (content) => {
  let totalWords = 0;
  let totalCharacters = 0;
  let paragraphCount = 0;
  let sentenceCount = 0;

  const chapterBreakdown = [];
  
  // Introduction
  const introWords = countWords(content.introduction?.content || '');
  const introChars = (content.introduction?.content || '').length;
  totalWords += introWords;
  totalCharacters += introChars;
  paragraphCount += countParagraphs(content.introduction?.content || '');
  sentenceCount += countSentences(content.introduction?.content || '');

  // Chapters
  if (content.chapters) {
    content.chapters.forEach((chapter, index) => {
      const chapterWords = countWords(chapter.content || '');
      const chapterChars = (chapter.content || '').length;
      
      chapterBreakdown.push({
        number: chapter.number || index + 1,
        words: chapterWords,
        characters: chapterChars
      });
      
      totalWords += chapterWords;
      totalCharacters += chapterChars;
      paragraphCount += countParagraphs(chapter.content || '');
      sentenceCount += countSentences(chapter.content || '');
    });
  }

  // Conclusion
  const conclusionWords = countWords(content.conclusion?.content || '');
  const conclusionChars = (content.conclusion?.content || '').length;
  totalWords += conclusionWords;
  totalCharacters += conclusionChars;
  paragraphCount += countParagraphs(content.conclusion?.content || '');
  sentenceCount += countSentences(content.conclusion?.content || '');

  // Calculate derived statistics
  const wordsPerMinute = 200; // Average reading speed
  const readingTime = Math.ceil(totalWords / wordsPerMinute);
  const chapterCount = content.chapters?.length || 0;
  const sectionCount = chapterCount + 2; // +2 for intro and conclusion
  const averageWordsPerChapter = chapterCount > 0 ? Math.round(totalWords / chapterCount) : 0;
  const averageWordsPerParagraph = paragraphCount > 0 ? Math.round(totalWords / paragraphCount) : 0;
  const averageWordsPerSentence = sentenceCount > 0 ? Math.round(totalWords / sentenceCount) : 0;
  
  // Estimate pages (assuming ~250 words per page)
  const estimatedPages = Math.ceil(totalWords / 250);
  
  // Estimate file size (rough approximation)
  const estimatedFileSize = totalCharacters < 10000 ? '<50KB' : 
                           totalCharacters < 50000 ? '<250KB' : 
                           totalCharacters < 100000 ? '<500KB' : '>500KB';

  return {
    totalWords,
    totalCharacters,
    readingTime,
    wordsPerMinute,
    chapterCount,
    sectionCount,
    paragraphCount,
    sentenceCount,
    averageWordsPerChapter,
    averageWordsPerParagraph,
    averageWordsPerSentence,
    introductionWords: introWords,
    conclusionWords: conclusionWords,
    chapterBreakdown,
    estimatedPages,
    estimatedFileSize
  };
};

// Helper functions
const countWords = (text) => {
  if (!text) return 0;
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
};

const countParagraphs = (text) => {
  if (!text) return 0;
  return text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;
};

const countSentences = (text) => {
  if (!text) return 0;
  return text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
};

export default DocumentStatistics;
