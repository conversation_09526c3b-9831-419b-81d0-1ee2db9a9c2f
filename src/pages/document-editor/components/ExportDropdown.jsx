import React, { useState, useRef, useEffect } from 'react';

/**
 * ExportDropdown - Dropdown menu for document export options
 * Provides various export formats (PDF, Word, HTML, Text)
 */
const ExportDropdown = ({ onExport, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // <PERSON><PERSON> clicks outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  const handleExport = (format) => {
    setIsOpen(false);
    if (onExport) {
      onExport(format);
    }
  };

  const exportOptions = [
    {
      format: 'pdf',
      label: 'Export as PDF',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      description: 'Best for sharing and printing',
      primary: true
    },
    {
      format: 'docx',
      label: 'Export as Word',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      description: 'Editable Microsoft Word format'
    },
    {
      format: 'html',
      label: 'Export as HTML',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      description: 'Web-ready HTML format'
    },
    {
      format: 'txt',
      label: 'Export as Text',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      ),
      description: 'Plain text format'
    }
  ];

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Export Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-1.5 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <span>Export</span>
        <svg 
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg py-2 z-50">
          <div className="px-3 py-2 border-b border-gray-100">
            <h3 className="text-sm font-medium text-gray-900">Export Document</h3>
            <p className="text-xs text-gray-600">Choose your preferred format</p>
          </div>
          
          <div className="py-1">
            {exportOptions.map((option) => (
              <button
                key={option.format}
                onClick={() => handleExport(option.format)}
                className={`w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors ${
                  option.primary ? 'border-b border-gray-100' : ''
                }`}
              >
                <div className="flex items-start space-x-3 relative">
                  <div className={`mt-0.5 ${option.primary ? 'text-primary' : 'text-gray-500'}`}>
                    {option.icon}
                  </div>
                  <div className="flex-1">
                    <div className={`text-sm font-medium ${
                      option.primary ? 'text-primary' : 'text-gray-900'
                    }`}>
                      {option.label}
                    </div>
                    <div className="text-xs text-gray-600 mt-0.5">
                      {option.description}
                    </div>
                  </div>
                  {option.primary && (
                    <div className="text-xs bg-primary/10 text-primary px-2 py-1 absolute block z-20 right-0 top -0 rounded-full">
                      Recommended
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>
          
          <div className="px-4 py-2 border-t border-gray-100 bg-gray-50">
            <p className="text-xs text-gray-600">
              Export preserves formatting, images, and document structure
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExportDropdown;
