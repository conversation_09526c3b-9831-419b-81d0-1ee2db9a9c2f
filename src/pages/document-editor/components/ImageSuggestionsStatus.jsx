import React from 'react';
import { imageSuggestionsService } from '../../../services/imageSuggestionsService';

/**
 * ImageSuggestionsStatus - Debug component to show image suggestions status
 * Only shown in development mode
 */
const ImageSuggestionsStatus = ({ imageSuggestions, documentId, onRegenerateSuggestions }) => {
  // Only show in development
  if (import.meta.env.PROD) {
    return null;
  }

  const cacheStats = imageSuggestionsService.getCacheStats();
  const isValid = imageSuggestionsService.validateImageSuggestions(imageSuggestions);
  const suggestionCount = Object.keys(imageSuggestions || {}).length;

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-3 rounded-lg shadow-lg text-xs max-w-xs z-50">
      <div className="font-semibold mb-2">Image Suggestions Status</div>
      
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>Document ID:</span>
          <span className="truncate ml-2">{documentId?.slice(-8) || 'None'}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Status:</span>
          <span className={`ml-2 ${isValid ? 'text-green-400' : 'text-red-400'}`}>
            {isValid ? 'Valid' : 'Invalid/Missing'}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Chapters:</span>
          <span className="ml-2">{suggestionCount}</span>
        </div>
        
        <div className="border-t border-gray-600 pt-1 mt-2">
          <div className="flex justify-between">
            <span>Cache Entries:</span>
            <span className="ml-2">{cacheStats.totalEntries}</span>
          </div>
          
          <div className="flex justify-between">
            <span>Valid/Expired:</span>
            <span className="ml-2">
              <span className="text-green-400">{cacheStats.validEntries}</span>
              /
              <span className="text-red-400">{cacheStats.expiredEntries}</span>
            </span>
          </div>
        </div>

        {suggestionCount > 0 && (
          <div className="border-t border-gray-600 pt-1 mt-2">
            <div className="text-xs text-gray-400">Chapters with Images:</div>
            {Object.keys(imageSuggestions || {}).map(chapterKey => {
              const chapter = imageSuggestions[chapterKey];
              const imageCount = chapter?.images?.length || 0;
              return (
                <div key={chapterKey} className="flex justify-between">
                  <span className="truncate">{chapterKey}:</span>
                  <span className="ml-2">{imageCount} images</span>
                </div>
              );
            })}
          </div>
        )}
        
        {/* DEBUG: Show raw data structure */}
        <div className="border-t border-gray-600 pt-1 mt-2">
          <div className="text-xs text-gray-400">Debug Info:</div>
          <div className="text-xs text-yellow-300">
            Type: {typeof imageSuggestions}
          </div>
          <div className="text-xs text-yellow-300">
            JSON: {JSON.stringify(imageSuggestions).substring(0, 100)}...
          </div>
          
          {onRegenerateSuggestions && (
            <button
              onClick={onRegenerateSuggestions}
              className="mt-1 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Force Regenerate
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImageSuggestionsStatus;
