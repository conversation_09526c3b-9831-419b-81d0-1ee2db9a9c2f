import React, { useState } from 'react';
import { NodeViewWrapper } from '@tiptap/react';

import { prodLogger } from '../../../utils/prodLogger.js';
/**
 * Enhanced Image Node View with Delete Controls
 * 
 * This component renders images with:
 * - Hover delete button overlay
 * - Visual selection states
 * - Read-only mode awareness
 * - Smooth animations and transitions
 */
const EnhancedImageNodeView = ({ node, editor, deleteNode, selected }) => {
  const [isHovered, setIsHovered] = useState(false);
  
  // Extract image attributes
  const {
    src,
    alt,
    title,
    width,
    height,
    class: className,
    // Extract data attributes for AI-generated images
    'data-ai-generated': dataAiGenerated,
    'data-generation-id': dataGenerationId,
    'data-prompt': dataPrompt,
    'data-style': dataStyle,
    'data-size': dataSize,
    // Extract data attribute for uploaded images
    'data-uploaded': dataUploaded
  } = node.attrs;
  
  // Check if editor is in read-only mode
  const isReadOnly = editor ? !editor.isEditable : false;
  
  // Handle delete button click
  const handleDelete = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (deleteNode) {
      deleteNode();
      
      // Show success feedback
      setTimeout(() => {
        prodLogger.debug('✅ Image deleted successfully. Press Ctrl+Z to undo.');
      }, 100);
    }
  };
  
  // Handle image click for selection
  const handleImageClick = (e) => {
    if (!isReadOnly) {
      // Focus the editor and select this node
      editor?.commands.focus();
    }
  };

  return (
    <NodeViewWrapper
      className={`enhanced-image-wrapper ${selected ? 'selected' : ''} ${isReadOnly ? 'read-only' : ''}`}
      onMouseEnter={() => !isReadOnly && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative inline-block">
        <img
          src={src}
          alt={alt || ''}
          title={title || ''}
          width={width || undefined}
          height={height || undefined}
          className={`${className || 'tiptap-image max-w-full h-auto rounded-lg shadow-sm'} ${selected && !isReadOnly ? 'selected-image' : ''}`}
          onClick={handleImageClick}
          draggable={false}
          // Include data attributes for AI-generated images (critical for export detection)
          {...(dataAiGenerated && { 'data-ai-generated': dataAiGenerated })}
          {...(dataGenerationId && { 'data-generation-id': dataGenerationId })}
          {...(dataPrompt && { 'data-prompt': dataPrompt })}
          {...(dataStyle && { 'data-style': dataStyle })}
          {...(dataSize && { 'data-size': dataSize })}
          // Include data attribute for uploaded images (critical for export detection)
          {...(dataUploaded && { 'data-uploaded': dataUploaded })}
        />
        
        {/* Hover delete overlay - only show in edit mode */}
        {!isReadOnly && (
          <div 
            className={`absolute top-2 right-2 transition-opacity duration-200 z-10 ${
              isHovered ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <button
              onClick={handleDelete}
              className="image-delete-button bg-red-500 hover:bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110"
              title="Delete image (Del)"
              aria-label="Delete image"
            >
              <svg 
                width="14" 
                height="14" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2"
                strokeLinecap="round" 
                strokeLinejoin="round"
              >
                <polyline points="3,6 5,6 21,6"></polyline>
                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
              </svg>
            </button>
          </div>
        )}
      </div>

      {/* Enhanced CSS styles */}
      <style dangerouslySetInnerHTML={{
        __html: `
          .enhanced-image-wrapper {
            display: block;
            margin: 1rem 0;
            line-height: 0;
          }
          
          /* Remove any default spacing from the container */
          .enhanced-image-wrapper > div {
            line-height: 0;
            font-size: 0;
          }
          
          /* Selection border directly on the image */
          .enhanced-image-wrapper img.selected-image {
            box-shadow: 0 0 0 2px #3b82f6;
            filter: brightness(1.05);
          }
          
          /* Override any default ProseMirror selection styles */
          .enhanced-image-wrapper.ProseMirror-selectednode,
          .enhanced-image-wrapper.ProseMirror-selectednode * {
            outline: none !important;
            border: none !important;
          }
          
          .enhanced-image-wrapper:hover img {
            transition: all 0.2s ease;
          }
          
          .enhanced-image-wrapper.read-only {
            pointer-events: none;
          }
          
          /* Reset any inherited spacing */
          .enhanced-image-wrapper img {
            display: block;
            margin: 0;
            padding: 0;
            vertical-align: top;
            cursor: pointer;
          }
          
          .enhanced-image-wrapper.read-only img {
            cursor: default;
          }
          
          /* Delete button styling */
          .image-delete-button {
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 20;
            position: relative;
          }
          
          .image-delete-button:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
          }
        `
      }} />
    </NodeViewWrapper>
  );
};

export default EnhancedImageNodeView;
