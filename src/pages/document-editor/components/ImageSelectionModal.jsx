import React, { useState, useEffect } from 'react';
import Button from '../../../components/ui/Button';

import { prodLogger } from '../../../utils/prodLogger.js';
/**
 * ImageSelectionModal - Modal overlay for selecting images from Unsplash
 * Provides search functionality and image grid for contextual image insertion
 */
const ImageSelectionModal = ({
  isOpen,
  onClose,
  onImageSelect,
  imageSuggestions = {},
  chapterId = null,
  isReviewMode = false
}) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedImage(null);
      setSearchTerm('');
    }
  }, [isOpen]);

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, onClose]);

  const handleImageSelect = (image) => {
    prodLogger.debug('📸 Image selected in modal:', image);
    setSelectedImage(image);
  };

  const handleConfirmSelection = () => {
    prodLogger.debug('✅ Confirming image selection:', { selectedImage, hasHandler: !!onImageSelect });
    if (selectedImage && onImageSelect) {
      onImageSelect(selectedImage);
      onClose();
    } else {
      prodLogger.warn('⚠️ Cannot confirm selection:', {
        hasSelectedImage: !!selectedImage,
        hasHandler: !!onImageSelect
      });
    }
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) return;
    
    setIsSearching(true);
    // TODO: Implement search functionality with Unsplash API
    // For now, we'll use existing suggestions
    setTimeout(() => {
      setIsSearching(false);
    }, 1000);
  };

  // Don't render modal in review mode
  if (!isOpen || isReviewMode) return null;

  // Get images from suggestions or use empty array
  const availableImages = chapterId && imageSuggestions[chapterId]
    ? imageSuggestions[chapterId].images || []
    : [];

  prodLogger.debug('🖼️ ImageSelectionModal render:', {
    isOpen,
    chapterId,
    hasImageSuggestions: !!imageSuggestions,
    imageSuggestionsKeys: Object.keys(imageSuggestions || {}),
    availableImagesCount: availableImages.length,
    availableImages: availableImages.slice(0, 2) // Log first 2 images for debugging
  });

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Add Image</h2>
            <p className="text-sm text-gray-600 mt-1">
              Choose an image to add to your document
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Search Bar */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex space-x-3">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search for images..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            <Button
              onClick={handleSearch}
              disabled={!searchTerm.trim() || isSearching}
              className="px-6"
            >
              {isSearching ? 'Searching...' : 'Search'}
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {availableImages.length > 0 ? (
            <>
              <h3 className="text-sm font-medium text-gray-900 mb-4">
                Suggested Images
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                {availableImages.map((image) => (
                  <div
                    key={image.id}
                    className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                      selectedImage?.id === image.id
                        ? 'border-primary shadow-lg'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleImageSelect(image)}
                  >
                    <div className="aspect-video">
                      <img
                        src={image.thumbnailUrl || image.url}
                        alt={image.description}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                      {selectedImage?.id === image.id && (
                        <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="p-3">
                      <p className="text-sm text-gray-900 font-medium truncate">
                        {image.description}
                      </p>
                      <p className="text-xs text-gray-600">
                        by {image.photographer}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No images available</h3>
              <p className="text-gray-600">
                Use the search bar above to find images from Unsplash
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            {selectedImage ? (
              <span>Selected: {selectedImage.description}</span>
            ) : (
              <span>Select an image to continue</span>
            )}
          </div>
          <div className="flex space-x-3">
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleConfirmSelection}
              disabled={!selectedImage}
            >
              Add Image
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageSelectionModal;
