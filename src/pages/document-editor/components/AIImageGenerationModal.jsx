import React, { useState, useEffect, useRef } from 'react';
import Button from '../../../components/ui/Button';
import AppIcon from '../../../components/AppIcon';
import { prodLogger } from '../../../utils/prodLogger.js';
import {
  IMAGE_STYLES,
  isGeminiImageConfigured,
  validateGenerationOptions
} from '../../../services/geminiImageService';

/**
 * AIImageGenerationModal - Modal for AI image generation
 * 
 * Provides interface for:
 * - Text prompt input with suggestions
 * - Style and model selection
 * - Size/aspect ratio configuration
 * - Advanced generation options
 * - Cost estimation and progress tracking
 */
const AIImageGenerationModal = ({
  isOpen,
  onClose,
  onGenerate,
  onCancel,
  onInsertImage,
  isGenerating = false,
  generationProgress = null,
  generatedResult = null
}) => {
  // Form state
  const [prompt, setPrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState('photorealistic');

  // UI state
  const [promptError, setPromptError] = useState('');

  // Refs
  const promptInputRef = useRef(null);

  // Helper function to get user-friendly status messages
  const getStatusMessage = (status) => {
    switch (status) {
      case 'starting': return 'Initializing...';
      case 'processing': return 'Creating your image...';
      case 'completed': return 'Complete!';
      case 'failed': return 'Generation failed';
      case 'canceled': return 'Canceled';
      default: return 'Processing...';
    }
  };

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setPrompt('');
      setSelectedStyle('photorealistic');
      setPromptError('');

      // Focus prompt input after modal opens
      setTimeout(() => {
        if (promptInputRef.current) {
          promptInputRef.current.focus();
        }
      }, 100);
    }
  }, [isOpen]);

  // Handle form submission
  const handleGenerate = () => {
    // Validate prompt
    if (!prompt.trim()) {
      setPromptError('Please enter a description for your image');
      promptInputRef.current?.focus();
      return;
    }

    if (prompt.trim().length < 3) {
      setPromptError('Please enter a more detailed description (at least 3 characters)');
      promptInputRef.current?.focus();
      return;
    }

    setPromptError('');

    // Prepare generation options with defaults
    const options = validateGenerationOptions({
      model: 'stable-diffusion', // Default model
      style: selectedStyle,
      size: 'landscape', // Default size
      num_inference_steps: 20, // Default quality
      guidance_scale: 7.5 // Default guidance
    });

    // Call generation handler
    onGenerate(prompt.trim(), options);
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && !isGenerating) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, isGenerating, onClose]);

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget && !isGenerating) {
      onClose();
    }
  };

  if (!isOpen) return null;

  // Check if Gemini Image Generation is configured
  const isConfigured = isGeminiImageConfigured();

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <AppIcon name="ImagePlus" size="sm" className="text-white" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900">Generate Image</h2>
          </div>
          {!isGenerating && (
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="Close modal"
            >
              <AppIcon name="X" size="sm" className="text-gray-500" />
            </button>
          )}
        </div>

        {/* Generation Progress */}
        {isGenerating && generationProgress && (
          <div className="mx-6 mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-blue-900">Generating Image...</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-blue-700">
                      {Math.round(generationProgress.progress * 100)}%
                    </span>
                    {onCancel && (
                      <button
                        onClick={onCancel}
                        className="text-xs text-blue-600 hover:text-blue-800 underline"
                      >
                        Cancel
                      </button>
                    )}
                  </div>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${generationProgress.progress * 100}%` }}
                  ></div>
                </div>
                <p className="text-xs text-blue-600 mt-2">
                  {getStatusMessage(generationProgress.status)} • {Math.round(generationProgress.elapsed / 1000)}s
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Generated Image Preview */}
        {generatedResult && !isGenerating && (
          <div className="mx-6 mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="text-center">
              <h3 className="text-sm font-medium text-green-900 mb-3">Image Generated Successfully!</h3>
              <div className="mb-4">
                <img
                  src={generatedResult.imageUrl}
                  alt={generatedResult.prompt}
                  className="max-w-full h-auto rounded-lg shadow-sm mx-auto"
                  style={{ maxHeight: '200px' }}
                />
              </div>
              <div className="flex justify-center space-x-3">
                <Button
                  variant="secondary"
                  onClick={() => onGenerate(generatedResult.prompt, generatedResult.options)}
                  iconName="RefreshCw"
                  iconPosition="left"
                  size="sm"
                >
                  Regenerate
                </Button>
                <Button
                  variant="primary"
                  onClick={async () => {
                    if (onInsertImage) {
                      try {
                        await onInsertImage(generatedResult);
                        // onClose will be called by the handler if successful
                      } catch (error) {
                        prodLogger.error('Failed to insert image:', error);
                        // Don't close modal on error so user can try again
                      }
                    } else {
                      onClose();
                    }
                  }}
                  iconName="Check"
                  iconPosition="left"
                  size="sm"
                >
                  Insert Image
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Form Content */}
        <div className={`p-6 space-y-4 ${generatedResult && !isGenerating ? 'opacity-50' : ''}`}>
          {/* Prompt Input - Made more prominent */}
          <div>
            <label htmlFor="prompt" className="block text-base font-medium text-gray-900 mb-3">
              What would you like to generate?
            </label>
            <textarea
              ref={promptInputRef}
              id="prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="A professional office workspace with modern furniture and natural lighting..."
              className={`w-full px-4 py-3 border rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base ${
                promptError ? 'border-red-300' : 'border-gray-300'
              }`}
              rows={3}
              disabled={isGenerating}
            />
            {promptError && (
              <p className="text-sm text-red-600 mt-2">{promptError}</p>
            )}
            <p className="text-xs text-gray-500 mt-2">
              Be specific and descriptive for best results
            </p>
          </div>

          {/* Style Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-2">Style</label>
            <select
              value={selectedStyle}
              onChange={(e) => setSelectedStyle(e.target.value)}
              disabled={isGenerating}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {Object.entries(IMAGE_STYLES).map(([key, style]) => (
                <option key={key} value={key}>
                  {style.name}
                </option>
              ))}
            </select>
          </div>


        </div>

        {/* Footer */}
        <div className="flex items-center justify-end p-6 border-t border-gray-200 space-x-3">
          {generatedResult && !isGenerating ? (
            // Result state - show different options
            <>
              <Button
                variant="secondary"
                onClick={() => {
                  // Reset to form state
                  onClose();
                }}
              >
                Close
              </Button>
              <Button
                variant="secondary"
                onClick={() => onGenerate(generatedResult.prompt, generatedResult.options)}
                iconName="RefreshCw"
                iconPosition="left"
              >
                Try Again
              </Button>
            </>
          ) : (
            // Form/generating state
            <>
              <Button
                variant="secondary"
                onClick={onClose}
                disabled={isGenerating}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleGenerate}
                disabled={isGenerating || !prompt.trim()}
                iconName={isGenerating ? null : "ImagePlus"}
                iconPosition="left"
              >
                {isGenerating ? 'Generating...' : 'Generate Image'}
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIImageGenerationModal;
