import React, { useState, useRef, useEffect } from 'react';

/**
 * InlineContentToolbar - Contextual toolbar for adding content between blocks
 * Shows "+" button on hover to add images and other content types
 */
const InlineContentToolbar = ({
  onAddImage,
  position = 'between',
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const toolbarRef = useRef(null);

  // Handle clicks outside to close menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (toolbarRef.current && !toolbarRef.current.contains(event.target)) {
        setShowMenu(false);
      }
    };

    if (showMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showMenu]);

  const handleAddImage = () => {
    setShowMenu(false);
    if (onAddImage) {
      onAddImage();
    }
  };

  return (
    <div
      className={`relative group ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => {
        setIsVisible(false);
        setShowMenu(false);
      }}
    >
      {/* Hover area */}
      <div className="h-8 flex items-center justify-center">
        {/* Plus button */}
        {isVisible && (
          <div ref={toolbarRef} className="relative">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="w-8 h-8 bg-white border border-gray-300 rounded-full shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-center text-gray-600 hover:text-gray-800 hover:border-gray-400"
              title="Add content"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </button>

            {/* Content menu */}
            {showMenu && (
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg py-2 min-w-[160px] z-50">
                <button
                  onClick={handleAddImage}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3"
                >
                  <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span>Add Image</span>
                </button>

                {/* Future content types can be added here */}
                <button
                  className="w-full px-4 py-2 text-left text-sm text-gray-400 cursor-not-allowed flex items-center space-x-3"
                  disabled
                >
                  <svg className="w-4 h-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <span>Add Table</span>
                  <span className="text-xs text-gray-300 ml-auto">Soon</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default InlineContentToolbar;
