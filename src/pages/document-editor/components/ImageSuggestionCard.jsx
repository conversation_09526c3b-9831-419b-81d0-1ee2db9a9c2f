import React from 'react';

/**
 * ImageSuggestionCard - Unified trigger card component for contextual image suggestions
 *
 * This component renders as a clean, consistent trigger card within document content
 * at AI-determined optimal locations. Features:
 * - Gallery icon on the left
 * - "Image suggestions available" text with context
 * - Blue "View Images" button on the right
 * - Consistent design across all devices (mobile and desktop)
 * - Opens ContextualImageSelectionModal when clicked
 *
 * @param {Object} props - Component props
 * @param {string} props.chapterId - ID of the chapter this suggestion belongs to
 * @param {string} props.placementId - Unique ID for this placement location
 * @param {number} props.imageCount - Number of available images for this suggestion
 * @param {string} props.searchQuery - Search query used to find images
 * @param {Object} props.placement - Placement object with contextual information
 * @param {Function} props.onViewImages - Callback when "View Images" button is clicked
 * @param {string} props.className - Additional CSS classes to apply
 */
const ImageSuggestionCard = (props) => {
  // Safely destructure props
  const {
    chapterId,
    placementId,
    imageCount = 0,
    searchQuery = '',
    placement = null,
    onViewImages,
    className = ''
  } = props;

    const handleViewImages = () => {
    if (onViewImages) {
      onViewImages(chapterId, placementId, placement);
    }
  };

  // Don't render if no images available
  if (imageCount === 0) {
    return null;
  }

  // Always use modal-trigger design for consistent UX across all devices

  // Unified trigger design for all devices
  return (
    <div
      className={`image-suggestion-card my-4 ${className}`}
      data-chapter-id={chapterId}
      data-placement-id={placementId}
    >
      {/* Clean, simple trigger design for all devices */}
      <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-4 transition-all duration-200 hover:shadow-md">
        <div className="flex items-center justify-between">
          {/* Left section: Icon and content */}
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            {/* Gallery icon */}
            <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
              <svg
                className="w-5 h-5 text-blue-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-gray-900 text-sm sm:text-base">
                Image suggestions available
              </h4>
              <p className="text-xs sm:text-sm text-gray-500 truncate">
                {imageCount} image{imageCount !== 1 ? 's' : ''} for "{searchQuery}"
              </p>
            </div>
          </div>

          {/* Right section: CTA button */}
          <div className="flex-shrink-0 ml-3">
            <button
              onClick={handleViewImages}
              className="bg-blue-500 hover:bg-blue-600 active:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 min-h-[44px] flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label={`View ${imageCount} image suggestions for ${searchQuery}`}
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
              <span className="hidden sm:inline">View Images</span>
              <span className="sm:hidden">View</span>
            </button>
          </div>
        </div>
      </div>

      {/* Optional: Contextual hint for placement */}
      {placement?.contextualHint && (
        <div className="mt-2 px-4">
          <p className="text-xs text-gray-500 italic">
            💡 {placement.contextualHint}
          </p>
        </div>
      )}
    </div>
  );
};

export default ImageSuggestionCard;
