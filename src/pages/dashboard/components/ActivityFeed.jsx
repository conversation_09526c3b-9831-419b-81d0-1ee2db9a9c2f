import React from 'react';
import Icon from '../../../components/AppIcon';

const ActivityFeed = ({ activities, compact = false }) => {
  const getActivityIcon = (type) => {
    switch (type) {
      case 'document_created':
        return { name: 'FileText', variant: 'success', bg: 'bg-success-light' };
      case 'plagiarism_check':
        return { name: 'Shield', variant: 'warning', bg: 'bg-warning-light' };
      case 'template_used':
        return { name: 'Library', variant: 'primary', bg: 'bg-primary-light' };
      default:
        return { name: 'Activity', variant: 'muted', bg: 'bg-surface-secondary' };
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - new Date(timestamp);
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return new Date(timestamp).toLocaleDateString('en-GB');
  };

  return (
    <div className={compact ? "" : "bg-white rounded-lg border border-border p-6 shadow-card"}>
      {!compact && (
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-text-primary">Recent Activity</h3>
          <button className="text-sm text-primary hover:text-primary/80 transition-colors duration-300 font-medium">
            View All
          </button>
        </div>
      )}

      <div className={compact ? "space-y-3" : "space-y-4"}>
        {activities.length === 0 ? (
          !compact && (
            <div className="text-center py-8">
              <Icon name="Activity" size={32} variant="muted" className="mx-auto mb-2" />
              <p className="text-sm text-text-secondary">No recent activity</p>
            </div>
          )
        ) : (
          activities.map((activity) => {
            const iconConfig = getActivityIcon(activity.type);
            return (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`w-8 h-8 rounded-md flex items-center justify-center ${iconConfig.bg}`}>
                  <Icon name={iconConfig.name} size="sm" variant={iconConfig.variant} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className={`${compact ? 'text-sm' : 'text-sm'} text-text-primary leading-relaxed`}>
                    {activity.description}
                  </p>
                  <p className="text-xs text-text-secondary mt-1">
                    {formatTimeAgo(activity.timestamp)}
                  </p>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default ActivityFeed;