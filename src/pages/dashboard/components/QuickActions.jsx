import React from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import { useAuth } from '../../../contexts/AuthContext';
import { useProjects } from '../../../hooks/useProjects';

const QuickActions = ({ recentTemplates }) => {
  const navigate = useNavigate();
  const { profile } = useAuth();
  const { projects } = useProjects();

  const primaryActions = [
    {
      title: 'Create New Document',
      description: 'Start with AI-powered document creation',
      icon: 'Plus',
      color: 'primary',
      action: () => navigate('/document-creator')
    },
    {
      title: 'Check Plagiarism',
      description: 'Verify originality of your content',
      icon: 'Shield',
      color: 'warning',
      action: () => navigate('/plagiarism-checker')
    }
  ];

  const getColorClasses = (color) => {
    switch (color) {
      case 'primary':
        return 'bg-primary text-primary-foreground hover:bg-primary/90';
      case 'secondary':
        return 'bg-secondary text-secondary-foreground hover:bg-secondary/90';
      case 'warning':
        return 'bg-warning text-warning-foreground hover:bg-warning/90';
      default:
        return 'bg-primary text-primary-foreground hover:bg-primary/90';
    }
  };

  const handleTemplateClick = (template) => {
    navigate('/document-creator', { state: { templateId: template.id } });
  };

  return (
    <div className="space-y-6">
      {/* Primary Actions */}
      <div className="bg-surface rounded-lg border border-border p-6 shadow-elevation-1">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Quick Actions</h3>
        <div className="space-y-3">
          {primaryActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className={`w-full p-4 rounded-lg transition-standard text-left ${getColorClasses(action.color)}`}
            >
              <div className="flex items-center space-x-3">
                <Icon name={action.icon} size={20} />
                <div className="flex-1">
                  <h4 className="font-medium">{action.title}</h4>
                  <p className="text-sm opacity-90">{action.description}</p>
                </div>
                <Icon name="ChevronRight" size={16} />
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Quick Start Options */}
      <div className="bg-surface rounded-lg border border-border p-6 shadow-elevation-1">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-text-primary">Quick Start</h3>
        </div>

        <div className="space-y-2">
          <div className="text-center py-6">
            <Icon name="FileText" size={32} className="mx-auto text-text-secondary mb-2" />
            <p className="text-sm text-text-secondary mb-4">Start creating your document</p>
            <Button
              variant="primary"
              onClick={() => navigate('/document-creator')}
              iconName="Plus"
              size="sm"
            >
              Create Document
            </Button>
          </div>
                  <p className="text-xs text-text-secondary">{template.category}</p>
                </div>
                <Icon name="ChevronRight" size={14} className="text-text-secondary" />
              </button>
            ))
          )}
        </div>
      </div>

      {/* Usage Statistics */}
      <div className="bg-surface rounded-lg border border-border p-6 shadow-elevation-1">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Usage Statistics</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon name="FileText" size={16} className="text-text-secondary" />
              <span className="text-sm text-text-secondary">Documents Created</span>
            </div>
            <span className="text-sm font-medium text-text-primary">
              {profile?.documents_created || projects?.length || 0}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon name="Wand2" size={16} className="text-text-secondary" />
              <span className="text-sm text-text-secondary">AI Generations</span>
            </div>
            <span className="text-sm font-medium text-text-primary">
              {profile?.ai_generations_used || 0}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon name="HardDrive" size={16} className="text-text-secondary" />
              <span className="text-sm text-text-secondary">Storage Used</span>
            </div>
            <span className="text-sm font-medium text-text-primary">
              {profile?.storage_used_mb || 0} MB
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickActions;