import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useSubscriptionGuard } from '../hooks/useSubscriptionGuard.jsx';
import subscriptionService from '../services/subscriptionService';
import stripeService from '../services/stripeService';
import Button from '../components/ui/Button';
import PublicHeader from '../components/ui/PublicHeader';
import Icon from '../components/AppIcon';
import { prodLogger } from '../utils/prodLogger';

const PricingPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isAuthenticated } = useAuth();
  const { currentTier, startSubscription } = useSubscriptionGuard();
  const [billingPeriod, setBillingPeriod] = useState('monthly');
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [subscribingTo, setSubscribingTo] = useState(null);
  const [autoCheckoutTriggered, setAutoCheckoutTriggered] = useState(false);

  // Handle auto-checkout from landing page
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const preSelectedPlan = searchParams.get('plan');
    const preSelectedBilling = searchParams.get('billing');
    const autoCheckout = searchParams.get('auto') === 'true';
    
    // Debug logging
    console.log('PricingPage URL params:', {
      preSelectedPlan,
      preSelectedBilling,
      autoCheckout,
      searchParams: searchParams.toString()
    });
    
    // Set billing period if specified
    if (preSelectedBilling && ['monthly', 'yearly'].includes(preSelectedBilling)) {
      setBillingPeriod(preSelectedBilling);
    }
    
    // Auto-trigger checkout if conditions are met
    if (
      preSelectedPlan && 
      autoCheckout && 
      isAuthenticated && 
      user &&
      !autoCheckoutTriggered &&
      !loading
    ) {
      setAutoCheckoutTriggered(true);
      
      // Normalize plan name to lowercase
      const normalizedPlan = preSelectedPlan.toLowerCase();
      
      console.log('Auto-checkout triggered:', {
        originalPlan: preSelectedPlan,
        normalizedPlan: normalizedPlan,
        billingPeriod: preSelectedBilling
      });
      
      // Small delay to ensure UI is ready
      setTimeout(() => {
        handleSubscribe(normalizedPlan);
      }, 1500);
      
      // Track auto-checkout initiation
      if (typeof gtag !== 'undefined') {
        gtag('event', 'auto_checkout_initiated', {
          'plan_type': normalizedPlan,
          'billing_cycle': preSelectedBilling
        });
      }
    }
  }, [isAuthenticated, user, loading, autoCheckoutTriggered, location.search]);

  // Helper function to get plan price for analytics
  const getPlanPrice = (plan, billing) => {
    const prices = {
      basic: { monthly: 9, yearly: 84 },
      standard: { monthly: 19, yearly: 180 },
      pro: { monthly: 39, yearly: 348 }
    };
    return prices[plan]?.[billing] || 0;
  };

  // Load subscription plans
  useEffect(() => {
    const loadPlans = async () => {
      try {
        const tierComparison = await subscriptionService.getTierComparison();
        setPlans(tierComparison);
      } catch (error) {
        prodLogger.error('Failed to load pricing plans:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPlans();
  }, []);

  const handleSubscribe = async (tier) => {
    if (!isAuthenticated) {
      // Redirect to auth with plan selection
      const redirectUrl = `/auth?mode=register&plan=${tier}&billing=${billingPeriod}`;
      window.location.href = redirectUrl;
      return;
    }

    // Normalize tier name to lowercase to match Stripe config
    const normalizedTier = tier.toLowerCase();
    
    // Validate tier
    const validTiers = ['basic', 'standard', 'pro'];
    if (!validTiers.includes(normalizedTier)) {
      prodLogger.error('Invalid tier provided:', { tier, normalizedTier, validTiers });
      return;
    }

    try {
      setSubscribingTo(normalizedTier);
      
      console.log('handleSubscribe called with:', {
        originalTier: tier,
        normalizedTier,
        billingPeriod,
        userId: user.id,
        userEmail: user.email
      });
      
      // Track checkout initiation
      if (typeof gtag !== 'undefined') {
        gtag('event', 'checkout_initiated', {
          'plan_type': normalizedTier,
          'billing_cycle': billingPeriod,
          'user_id': user.id
        });
      }
      
      const { url } = await stripeService.createCheckoutSession(
        normalizedTier,
        billingPeriod,
        user.id,
        user.email
      );
      
      if (url) {
        window.location.href = url;
      }
    } catch (error) {
      prodLogger.error('Subscription failed:', error);
      // Handle error (show toast, etc.)
    } finally {
      setSubscribingTo(null);
    }
  };

  const getPlanButtonText = (tier) => {
    if (currentTier === tier) return 'Current Plan';
    if (tier === 'basic' && currentTier === 'free') return 'Start Free Trial';
    if (tier === 'standard' && ['free', 'basic'].includes(currentTier)) return 'Start Free Trial';
    if (tier === 'pro' && ['free', 'basic', 'standard'].includes(currentTier)) return 'Start Free Trial';
    return 'Upgrade';
  };

  const isPlanDisabled = (tier) => {
    return currentTier === tier;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading pricing plans...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Show loading state during auto-checkout */}
      {autoCheckoutTriggered && subscribingTo && (
        <div className="auto-checkout-loading">
          <h3>Setting up your subscription...</h3>
          <p>Please wait while we redirect you to checkout.</p>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mt-4"></div>
        </div>
      )}
      
      {/* Header */}
      <div className="bg-surface border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-text-primary mb-4">
              Choose Your Plan
            </h1>
            <p className="text-xl text-text-secondary max-w-3xl mx-auto">
              Start with a 7-day free trial on any plan. No credit card required.
              Upgrade, downgrade, or cancel anytime.
            </p>
          </div>

          {/* Billing Period Toggle */}
          <div className="flex justify-center mt-8">
            <div className="bg-background rounded-lg p-1 border border-border">
              <button
                onClick={() => setBillingPeriod('monthly')}
                className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingPeriod === 'monthly'
                    ? 'bg-primary text-white'
                    : 'text-text-secondary hover:text-text-primary'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingPeriod('yearly')}
                className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingPeriod === 'yearly'
                    ? 'bg-primary text-white'
                    : 'text-text-secondary hover:text-text-primary'
                }`}
              >
                Yearly
                <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                  Save up to 26%
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan) => (
            <div
              key={plan.tier}
              className={`bg-surface rounded-lg border-2 p-8 relative ${
                plan.tier === 'standard'
                  ? 'border-primary shadow-lg scale-105'
                  : 'border-border'
              }`}
            >
              {plan.tier === 'standard' && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-text-primary mb-2">
                  {plan.name.replace('RapidDoc AI - ', '')}
                </h3>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-text-primary">
                    ${billingPeriod === 'monthly' ? plan.price_monthly : plan.price_yearly}
                  </span>
                  <span className="text-text-secondary">
                    /{billingPeriod === 'monthly' ? 'month' : 'year'}
                  </span>
                </div>
                {billingPeriod === 'yearly' && plan.savings_yearly > 0 && (
                  <p className="text-green-600 text-sm font-medium">
                    Save ${plan.savings_yearly}/year
                  </p>
                )}
              </div>

              <Button
                variant={plan.tier === 'standard' ? 'primary' : 'outline'}
                className="w-full mb-8"
                onClick={() => handleSubscribe(plan.tier)}
                disabled={isPlanDisabled(plan.tier) || subscribingTo === plan.tier}
                loading={subscribingTo === plan.tier}
              >
                {getPlanButtonText(plan.tier)}
              </Button>

              <div className="space-y-4">
                <div className="flex items-center">
                  <Icon name="Check" size={16} className="text-green-500 mr-3" />
                  <span className="text-text-secondary">
                    {plan.features.documents_limit} documents/month
                  </span>
                </div>
                <div className="flex items-center">
                  <Icon name="Check" size={16} className="text-green-500 mr-3" />
                  <span className="text-text-secondary">
                    {plan.features.ai_generations_limit} AI requests/month
                  </span>
                </div>
                <div className="flex items-center">
                  <Icon name="Check" size={16} className="text-green-500 mr-3" />
                  <span className="text-text-secondary">
                    {plan.features.ai_image_generations_limit} AI images/month
                  </span>
                </div>
                <div className="flex items-center">
                  <Icon name="Check" size={16} className="text-green-500 mr-3" />
                  <span className="text-text-secondary">
                    {plan.features.storage_limit_gb}GB cloud storage
                  </span>
                </div>
                <div className="flex items-center">
                  <Icon name="Check" size={16} className="text-green-500 mr-3" />
                  <span className="text-text-secondary">
                    Up to {plan.features.max_file_upload_mb}MB file uploads
                  </span>
                </div>
                
                {plan.features.custom_templates && (
                  <div className="flex items-center">
                    <Icon name="Check" size={16} className="text-green-500 mr-3" />
                    <span className="text-text-secondary">Custom templates</span>
                  </div>
                )}
                
                {plan.features.priority_processing && (
                  <div className="flex items-center">
                    <Icon name="Check" size={16} className="text-green-500 mr-3" />
                    <span className="text-text-secondary">Priority processing</span>
                  </div>
                )}
                
                {plan.features.ai_model_selection && (
                  <div className="flex items-center">
                    <Icon name="Check" size={16} className="text-green-500 mr-3" />
                    <span className="text-text-secondary">AI model selection</span>
                  </div>
                )}
                
                {plan.features.phone_support && (
                  <div className="flex items-center">
                    <Icon name="Check" size={16} className="text-green-500 mr-3" />
                    <span className="text-text-secondary">Phone support</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold text-text-primary mb-8">
            Frequently Asked Questions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="text-left">
              <h3 className="font-semibold text-text-primary mb-2">
                Can I change plans anytime?
              </h3>
              <p className="text-text-secondary">
                Yes, you can upgrade, downgrade, or cancel your subscription at any time.
                Changes take effect at your next billing cycle.
              </p>
            </div>
            <div className="text-left">
              <h3 className="font-semibold text-text-primary mb-2">
                What happens after the free trial?
              </h3>
              <p className="text-text-secondary">
                After your 7-day free trial, you'll be charged for your selected plan.
                You can cancel anytime during the trial with no charges.
              </p>
            </div>
            <div className="text-left">
              <h3 className="font-semibold text-text-primary mb-2">
                Do you offer refunds?
              </h3>
              <p className="text-text-secondary">
                We offer a 30-day money-back guarantee for all paid plans.
                Contact support for assistance.
              </p>
            </div>
            <div className="text-left">
              <h3 className="font-semibold text-text-primary mb-2">
                Is my data secure?
              </h3>
              <p className="text-text-secondary">
                Yes, we use enterprise-grade security and encryption to protect your data.
                Your documents are stored securely and never shared.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingPage;
