import React, { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { useNavigate, useLocation } from 'react-router-dom'
import LoginForm from '../../components/auth/LoginForm'
import RegisterForm from '../../components/auth/RegisterForm'
import ForgotPasswordForm from '../../components/auth/ForgotPasswordForm'

const AuthPage = () => {
  const { isAuthenticated, loading, user } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()
  const [authMode, setAuthMode] = useState('login') // 'login', 'register', 'forgot-password'

  // Parse URL parameters and store plan selection
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search)
    const mode = searchParams.get('mode')
    const preSelectedPlan = searchParams.get('plan')
    const preSelectedBilling = searchParams.get('billing')
    
    // Set authentication mode if specified
    if (mode && ['login', 'register', 'forgot-password'].includes(mode)) {
      setAuthMode(mode)
    }
    
    // Store pre-selected plan in sessionStorage for later use
    if (preSelectedPlan && preSelectedBilling && typeof window !== 'undefined') {
      sessionStorage.setItem('preSelectedPlan', preSelectedPlan)
      sessionStorage.setItem('preSelectedBilling', preSelectedBilling)
      
      // Optional: Show selected plan in UI
      sessionStorage.setItem('planSelectionSource', 'landing_page')
    }
  }, [location.search])

  // Redirect authenticated users
  useEffect(() => {
    if (isAuthenticated && !loading && user) {
      // Safely check for browser environment
      const preSelectedPlan = typeof window !== 'undefined' ? sessionStorage.getItem('preSelectedPlan') : null
      const preSelectedBilling = typeof window !== 'undefined' ? sessionStorage.getItem('preSelectedBilling') : null
      
      if (preSelectedPlan && preSelectedBilling) {
        // Clear from storage and redirect to pricing with auto-checkout
        if (typeof window !== 'undefined') {
          sessionStorage.removeItem('preSelectedPlan')
          sessionStorage.removeItem('preSelectedBilling')
          sessionStorage.removeItem('planSelectionSource')
        }
        
        navigate(`/pricing?plan=${preSelectedPlan}&billing=${preSelectedBilling}&auto=true`)
      } else {
        const from = location.state?.from?.pathname || '/dashboard'
        navigate(from, { replace: true })
      }
    }
  }, [isAuthenticated, loading, user, navigate, location])

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if already authenticated (will redirect)
  if (isAuthenticated) {
    return null
  }

  const renderAuthForm = () => {
    switch (authMode) {
      case 'register':
        return (
          <RegisterForm
            onSwitchToLogin={() => setAuthMode('login')}
          />
        )
      case 'forgot-password':
        return (
          <ForgotPasswordForm
            onSwitchToLogin={() => setAuthMode('login')}
          />
        )
      default:
        return (
          <LoginForm
            onSwitchToRegister={() => setAuthMode('register')}
            onSwitchToForgotPassword={() => setAuthMode('forgot-password')}
          />
        )
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-surface to-background">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      
      <div className="relative min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Branding */}
          <div className="hidden lg:block">
            <div className="text-center lg:text-left">
              <h1 className="text-5xl font-bold text-text-primary mb-6">
                Welcome to{' '}
                <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                  RapidDoc AI
                </span>
              </h1>
              <p className="text-xl text-text-secondary mb-8 leading-relaxed">
                Create professional documents with the power of AI. From business reports to academic papers, 
                RapidDoc AI helps you write better, faster.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                  <span className="text-text-secondary">AI-powered document generation</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                  <span className="text-text-secondary">Professional templates library</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                  <span className="text-text-secondary">Built-in plagiarism detection</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                  <span className="text-text-secondary">Export to multiple formats</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Auth Form */}
          <div className="w-full">
            {/* Add plan selection indicator */}
            {typeof window !== 'undefined' && sessionStorage.getItem('preSelectedPlan') && (
              <div className="plan-selection-banner mb-6">
                <p>
                  Selected Plan: <strong>{sessionStorage.getItem('preSelectedPlan')}</strong> 
                  ({sessionStorage.getItem('preSelectedBilling')})
                </p>
                <small>Complete registration to continue with your selected plan</small>
              </div>
            )}
            
            {renderAuthForm()}
          </div>
        </div>
      </div>

      {/* Mobile Branding */}
      <div className="lg:hidden text-center p-8 bg-surface/50 backdrop-blur-sm">
        <h2 className="text-2xl font-bold text-text-primary mb-4">
          Powered by <span className="text-primary">RapidDoc AI</span>
        </h2>
        <p className="text-text-secondary">
          Create professional documents with AI assistance
        </p>
      </div>
    </div>
  )
}

export default AuthPage
