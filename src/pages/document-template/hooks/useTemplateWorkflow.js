import { useState, useCallback, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  fetchCoverTemplates,
  fetchTemplateCategories,
  incrementTemplateUsage,
} from "../../../services/templateService.js";
import {
  extractEnhancedContent,
  convertToPreviewData,
} from "../../../services/enhancedContentExtraction.js";
import {
  generatePreviewData,
  updatePreviewWithNewTemplate,
} from "../../../services/previewService.js";
import { exportDocument } from "../../../services/exportService.js";
import { useErrorMonitor } from "../../../utils/useErrorMonitor.js";
import { convertAIContentToHTML } from "../../../utils/contentConverter.js";
import coverPreviewService from "../../../services/coverPreviewService.js";
import overlayPreviewService from "../../../services/overlayPreviewService.js";

/**
 * Template Workflow Hook
 * Manages state and logic for template selection, preview, and export workflow
 */
const useTemplateWorkflow = (
  documentId,
  documentData,
  generatedContent,
  userProfile = null,
  user = null
) => {
  const navigate = useNavigate();
  const logger = useErrorMonitor("TemplateWorkflow", { documentId });

  // Workflow state
  const [currentPhase, setCurrentPhase] = useState("template"); // template, cover-preview, export
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Template data
  const [templates, setTemplates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [templateLoading, setTemplateLoading] = useState(false);

  // Category filtering
  const [selectedCategory, setSelectedCategory] = useState("");

  // Cover preview data (lightweight, cover-only)
  const [coverPreviewData, setCoverPreviewData] = useState(null);
  const [coverPreviewLoading, setCoverPreviewLoading] = useState(false);

  // Full preview data (generated only during export)
  const [previewData, setPreviewData] = useState(null);
  const [previewLoading, setPreviewLoading] = useState(false);

  // Export state
  const [exportFormat, setExportFormat] = useState("pdf");
  const [exportLoading, setExportLoading] = useState(false);
  const [exportResult, setExportResult] = useState(null);

  // Load templates and categories on mount
  useEffect(() => {
    loadTemplatesAndCategories();
  }, []);

  /**
   * Load templates and categories from API
   */
  const loadTemplatesAndCategories = useCallback(async () => {
    setTemplateLoading(true);
    setError(null);

    try {
      logger.info("Loading templates and categories");

      const [templatesResult, categoriesResult] = await Promise.all([
        fetchCoverTemplates({ limit: 100, useCache: false }), // Force fresh fetch for debugging
        fetchTemplateCategories(),
      ]);

      if (templatesResult.success) {
        setTemplates(templatesResult.templates);
        logger.info("Templates loaded successfully", {
          count: templatesResult.templates.length,
        });
      } else {
        throw new Error(templatesResult.error || "Failed to load templates");
      }

      if (categoriesResult.success) {
        setCategories(categoriesResult.categories);
        logger.info("Categories loaded successfully", {
          count: categoriesResult.categories.length,
        });
      }
    } catch (err) {
      logger.error("Failed to load templates and categories", err);
      setError(err.message);
    } finally {
      setTemplateLoading(false);
    }
  }, [logger]);

  /**
   * Handle template selection - now generates cover-only preview
   */
  const handleTemplateSelect = useCallback(
    async (template) => {
      try {
        setSelectedTemplate(template);
        logger.info("Template selected", {
          templateId: template.id,
          templateName: template.name,
        });

        // Generate cover-only preview when template is selected
        await generateCoverPreview(template);

        // Automatically navigate to cover preview phase after successful preview generation
        setCurrentPhase("cover-preview");
        logger.info(
          "Auto-navigated to cover preview phase after template selection"
        );
      } catch (err) {
        logger.error("Error selecting template", err);
        setError(err.message);
      }
    },
    [logger]
  );

  /**
   * Generate cover-only preview for template selection
   */
  const generateCoverPreview = useCallback(
    async (template = selectedTemplate) => {
      if (!template || !documentData) return;

      setCoverPreviewLoading(true);
      setError(null);

      try {
        logger.info("Generating cover preview", { templateId: template.id });

        // Generate cover-only preview using the new service with user profile data
        const coverPreview = await coverPreviewService.generateCoverPreview(
          template,
          documentData,
          {
            generatedContent,
            userProfile,
            user,
          }
        );

        setCoverPreviewData(coverPreview);
        logger.info("Cover preview generated successfully", {
          templateId: template.id,
          previewType: coverPreview.metadata.previewType,
        });
      } catch (err) {
        logger.error("Failed to generate cover preview", err);
        setError(err.message);
      } finally {
        setCoverPreviewLoading(false);
      }
    },
    [selectedTemplate, documentData, logger]
  );

  /**
   * Generate full preview data for export (only called during export process)
   */
  const generateFullPreview = useCallback(
    async (template = selectedTemplate) => {
      if (!template || !generatedContent) return null;

      setPreviewLoading(true);
      setError(null);

      try {
        logger.info("Generating full document preview for export", {
          templateId: template.id,
        });

        // Create a mock editor instance for content extraction
        const htmlContent = convertAIContentToHTML(generatedContent, {}, false);

        const mockEditorInstance = {
          getHTML: () => htmlContent,
          getText: () => htmlContent?.replace(/<[^>]*>/g, "") || "",
          getJSON: () => ({ content: [] }),
        };

        // Extract enhanced content
        const enhancedContent = extractEnhancedContent(mockEditorInstance);

        // Generate full preview data
        const preview = await generatePreviewData(
          template,
          enhancedContent,
          documentData
        );

        setPreviewData(preview);
        logger.info("Full preview generated successfully for export", {
          templateId: template.id,
          pageCount: preview.pages?.length || 0,
        });

        return preview;
      } catch (err) {
        logger.error("Failed to generate full preview", err);
        setError(err.message);
        return null;
      } finally {
        setPreviewLoading(false);
      }
    },
    [selectedTemplate, generatedContent, documentData, logger]
  );

  /**
   * Handle template change in cover preview
   */
  const handleTemplateChange = useCallback(
    async (newTemplate) => {
      if (!newTemplate) return;

      setCoverPreviewLoading(true);
      try {
        logger.info("Changing template in cover preview", {
          oldTemplateId: selectedTemplate?.id,
          newTemplateId: newTemplate.id,
        });

        // Update cover preview with new template
        const updatedCoverPreview =
          await coverPreviewService.updateCoverPreview(
            newTemplate,
            documentData,
            {
              generatedContent,
              userProfile,
              user,
            }
          );

        setCoverPreviewData(updatedCoverPreview);
        setSelectedTemplate(newTemplate);

        logger.info("Template changed successfully in cover preview");
      } catch (err) {
        logger.error("Failed to change template", err);
        setError(err.message);
      } finally {
        setCoverPreviewLoading(false);
      }
    },
    [selectedTemplate, documentData, logger]
  );

  /**
   * Handle export with selected template - now generates full preview during export
   */
  const handleExport = useCallback(
    async (format = exportFormat) => {
      if (!selectedTemplate) {
        setError("Please select a template before exporting");
        return;
      }

      setExportLoading(true);
      setError(null);

      try {
        logger.info("Starting export with cover prepending", {
          format,
          templateId: selectedTemplate.id,
          templateName: selectedTemplate.name,
        });

        // Generate full preview for export if not already available
        if (!previewData) {
          logger.info("Generating full document preview for export");
          await generateFullPreview(selectedTemplate);
        }

        // Track template usage
        await incrementTemplateUsage(selectedTemplate.id);

        // Export document with template (cover will be prepended automatically)
        const result = await exportDocument(
          format,
          documentData,
          generatedContent,
          {
            selectedTemplate: selectedTemplate,
          }
        );

        if (result.success) {
          setExportResult(result);
          logger.info("Export completed successfully with cover prepending", {
            format,
          });
        } else {
          throw new Error(result.error || "Export failed");
        }
      } catch (err) {
        logger.error("Export failed", err);
        setError(err.message);
      } finally {
        setExportLoading(false);
      }
    },
    [
      exportFormat,
      selectedTemplate,
      previewData,
      documentData,
      generatedContent,
      logger,
      generateFullPreview,
    ]
  );

  /**
   * Navigation handlers
   */
  const handleBackToEditor = useCallback(() => {
    navigate(`/document-editor/${documentId}`);
  }, [navigate, documentId]);

  const handlePhaseChange = useCallback(
    (phase) => {
      setCurrentPhase(phase);
      setError(null); // Clear errors when changing phases

      // Clear text overlay customizations when navigating back to template selection
      if (phase === "template" && selectedTemplate) {
        try {
          const storageKey = `text-overlay-customizations-${selectedTemplate.id}`;
          sessionStorage.removeItem(storageKey);
          logger.info(
            "🧹 Cleared text overlay customizations when returning to template selection",
            {
              templateId: selectedTemplate.id,
            }
          );
        } catch (error) {
          logger.warn("Failed to clear text overlay customizations:", error);
        }
      }
    },
    [selectedTemplate, logger]
  );

  /**
   * Category filter handler
   */
  const handleCategoryChange = useCallback(
    (category) => {
      setSelectedCategory(category === selectedCategory ? "" : category);
    },
    [selectedCategory]
  );

  /**
   * Update cover preview with customizations
   */
  const updateCoverPreviewWithCustomizations = useCallback(
    async (template, documentData, customizations) => {
      try {
        logger.info("Updating cover preview with customizations", {
          templateId: template.id,
          customizationCount: Object.keys(customizations).length,
        });

        // Generate preview with customizations using overlay preview service
        const updatedPreview = await overlayPreviewService.queuePreviewUpdate(
          `template-${template.id}`,
          () =>
            coverPreviewService.generateCoverPreviewWithCustomizations(
              template,
              documentData,
              customizations,
              {
                generatedContent,
                userProfile,
                user,
              }
            ),
          300 // 300ms debounce
        );

        setCoverPreviewData(updatedPreview);
        logger.info("Cover preview updated with customizations successfully");

        return updatedPreview;
      } catch (err) {
        logger.error("Failed to update cover preview with customizations", err);
        setError(err.message);
        throw err;
      }
    },
    [documentData, generatedContent, userProfile, user, logger]
  );

  /**
   * Reset workflow state
   */
  const resetWorkflow = useCallback(() => {
    setCurrentPhase("template");
    setSelectedTemplate(null);
    setCoverPreviewData(null);
    setPreviewData(null);
    setExportResult(null);
    setError(null);
    setSelectedCategory("");
  }, []);

  // Filtered templates based on category
  const filteredTemplates = templates.filter((template) => {
    const matchesCategory =
      !selectedCategory || template.category === selectedCategory;
    return matchesCategory;
  });

  return {
    // State
    currentPhase,
    loading:
      loading ||
      templateLoading ||
      coverPreviewLoading ||
      previewLoading ||
      exportLoading,
    error,

    // Template data
    templates: filteredTemplates,
    allTemplates: templates,
    categories,
    selectedTemplate,
    templateLoading,

    // Category filtering
    selectedCategory,

    // Cover preview data (lightweight)
    coverPreviewData,
    coverPreviewLoading,

    // Full preview data (for export)
    previewData,
    previewLoading,

    // Export state
    exportFormat,
    exportLoading,
    exportResult,

    // Actions
    handleTemplateSelect,
    handleTemplateChange,
    handleExport,
    handleBackToEditor,
    handlePhaseChange,
    handleCategoryChange,
    generateCoverPreview,
    generateFullPreview,
    updateCoverPreviewWithCustomizations,
    resetWorkflow,
    loadTemplatesAndCategories,

    // Setters
    setExportFormat,
    setError,
  };
};

export default useTemplateWorkflow;
