import React, { useState } from 'react';
import CustomCoverUpload from './CustomCoverUpload.jsx';

import { prodLogger } from '../../../utils/prodLogger.js';
/**
 * Template Selection Interface
 * Provides template browsing with search, filtering, and selection
 */
const TemplateSelectionInterface = ({
  templates = [],
  categories = [],
  selectedTemplate = null,
  selectedCategory = '',
  loading = false,
  error = null,
  onTemplateSelect = null,
  onCategoryChange = null,
  documentData = null,
  onDocumentDataUpdate = null,
  onSkipTemplate = null, // New prop for skip template functionality
  onProceedWithTemplate = null, // New prop for proceeding with selected template
  className = ''
}) => {

  if (loading) {
    return (
      <div className={`template-selection-interface ${className}`}>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading templates...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`template-selection-interface ${className}`}>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Templates</h3>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Inline styles to ensure immediate visual changes */}
      <style>{`
        .template-card-grid {
          width: 100% !important;
          min-width: 0 !important; /* Allow shrinking in grid */
        }
        .template-grid {
          gap: 8px !important;
        }
        @media (min-width: 640px) {
          .template-grid {
            gap: 12px !important;
          }
        }
        .template-card-grid img {
          width: 100% !important;
          height: 100% !important;
          object-fit: cover !important;
          object-position: center !important;
        }
      `}</style>
      <div className={`template-selection-interface flex flex-col h-full ${className}`}>
        {/* Header with Category Filters and Skip Template Option */}
        <div className="template-header p-4 sm:p-6 border-b border-gray-200 bg-white">
          <div className="flex flex-col gap-4">
            {/* Bottom Row: Category Filters */}
            <div className="flex flex-col sm:flex-row lg:items-center gap-3 sm:gap-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">Browse Templates:</span>
              </div>
              {/* Category Filters */}
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => onCategoryChange?.('')}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    selectedCategory === ''
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  All Categories
                </button>
                {categories.map(category => (
                  <button
                    key={category}
                    onClick={() => onCategoryChange?.(category)}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors capitalize ${
                      selectedCategory === category
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
              {onSkipTemplate && (
                <button
                    onClick={onSkipTemplate}
                    className="px-4 py-2 bg-white text-blue-700 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors flex items-center justify-center space-x-2 text-sm font-medium shadow-sm"
                    title="Export with simple formatting - no template needed"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 9l3 3m0 0l-3 3m3-3H8" />
                    </svg>
                    <span>Skip Template</span>
                  </button>
              )}
            </div>
          </div>
        </div>

        {/* Custom Cover Upload Section */}
        {documentData && onDocumentDataUpdate && (
          <div className="border-b border-gray-200 bg-gray-50 p-4 sm:p-6">
            <CustomCoverUpload
              documentData={documentData}
              onCoverImageUpdate={(coverConfig) => {
                const updatedData = {
                  ...documentData,
                  contentDetails: {
                    ...documentData.contentDetails,
                    customCoverImage: coverConfig
                  }
                };
                onDocumentDataUpdate(updatedData);
              }}
              onToggleCustomCover={(enabled) => {
                // Optional: Handle template selection changes when custom cover is toggled
                if (!enabled && !selectedTemplate) {
                  // Could auto-select a default template here
                }
              }}
            />
          </div>
        )}

        {/* Template Grid/List */}
        <div className="flex-1 overflow-y-auto p-6 min-h-0">
          {templates.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Templates Found</h3>
                <p className="text-gray-600">
                  {selectedCategory
                    ? 'No templates found in this category'
                    : 'No templates are available at the moment'
                  }
                </p>
                {selectedCategory && (
                  <button
                    onClick={() => onCategoryChange?.('')}
                    className="mt-3 text-blue-500 hover:text-blue-600 font-medium"
                  >
                    Show all categories
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="template-grid grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-2 sm:gap-3">
              {templates.map(template => (
                <TemplateCard
                  key={template.id}
                  template={template}
                  isSelected={selectedTemplate?.id === template.id}
                  onSelect={() => onTemplateSelect?.(template)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

/**
 * Template Card Component
 * Displays individual image overlay template with preview
 */
const TemplateCard = ({
  template,
  isSelected = false,
  onSelect = null
}) => {
  const [previewImage, setPreviewImage] = useState(null);
  const [imageError, setImageError] = useState(false);
  const [loading, setLoading] = useState(true);

  // Generate cover thumbnail when component mounts
  React.useEffect(() => {
    const generateCoverThumbnail = async () => {
      try {
        setLoading(true);

        // Import the cover preview service
        const { default: coverPreviewService } = await import('../../../services/coverPreviewService.js');

        // Sample data for thumbnail
        const sampleData = {
          title: 'Sample Title',
          author: 'Author Name',
          description: 'This is how your document description will appear on the cover.'
        };

        const thumbnailImage = await coverPreviewService.generateCoverThumbnail(template, sampleData);
        if (thumbnailImage) {
          setPreviewImage(thumbnailImage);
          setImageError(false);
        } else {
          setImageError(true);
        }
      } catch (error) {
        prodLogger.error('Error generating cover thumbnail:', error);
        setImageError(true);
      } finally {
        setLoading(false);
      }
    };

    if (template && template.background_image_url) {
      generateCoverThumbnail();
    }
  }, [template]);

  return (
    <div
      className={`template-card-grid border-2 rounded-lg cursor-pointer transition-all hover:shadow-lg active:scale-95 w-full ${
        isSelected
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-200 hover:border-gray-300'
      }`}
      onClick={onSelect}
      role="button"
      tabIndex={0}
      aria-label={`Select template: ${template.name}`}
      aria-pressed={isSelected}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onSelect();
        }
      }}
    >
      {/* Template Preview */}
      <div className="aspect-[4/5] w-full bg-gray-100 rounded-t-lg overflow-hidden relative">
        {loading ? (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
            <div className="text-center p-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-xs text-gray-500">Loading...</p>
            </div>
          </div>
        ) : previewImage && !imageError ? (
          <img
            src={previewImage}
            alt={template.name}
            className="w-full h-full object-cover object-center"
            onError={() => setImageError(true)}
          />
        ) : template.thumbnail_url && !imageError ? (
          <img
            src={template.thumbnail_url}
            alt={template.name}
            className="w-full h-full object-cover object-center"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
            <div className="text-center p-4">
              <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p className="text-xs text-gray-500">Preview</p>
            </div>
          </div>
        )}

        {/* Selection Indicator */}
        {isSelected && (
          <div className="absolute top-1 right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center shadow-md">
            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        )}

        {/* Premium Badge */}
        {template.is_premium && (
          <div className="absolute top-1 left-1">
            <span className="px-1 py-0.5 bg-yellow-100 text-yellow-800 text-xs rounded font-medium">
              Pro
            </span>
          </div>
        )}
      </div>

      {/* Template Info */}
      <div className="p-2">
        <h4 className="font-medium text-gray-900 text-xs truncate leading-tight">{template.name}</h4>
        <p className="text-xs text-gray-500 mt-0.5 capitalize">{template.category}</p>
      </div>
    </div>
  );
};

export default TemplateSelectionInterface;
