import React, { useState } from 'react';
import TextOverlayEditor from '../../../components/TextOverlayEditor/TextOverlayEditor';
import LogoOverlayEditor from '../../../components/LogoOverlayEditor/LogoOverlayEditor';
import InteractiveTemplateCanvas from '../../../components/InteractiveTemplateCanvas/InteractiveTemplateCanvas';
import useTextOverlayEditor from '../../../hooks/useTextOverlayEditor';
import useLogoOverlayEditor from '../../../hooks/useLogoOverlayEditor';
import { templateHasLogoOverlays } from '../../../services/logoOverlayService';

import { prodLogger } from '../../../utils/prodLogger.js';
/**
 * Cover Preview Interface
 * Shows cover-only preview for template design iteration
 * Replaces the full document preview during template selection phase
 */
const CoverPreviewInterface = ({
  selectedTemplate = null,
  documentData = {},
  coverPreviewData = null,
  loading = false,
  error = null,
  onBack = null,
  onProceedToExport = null,
  onPreviewUpdate = null,
  className = ''
}) => {
  const [isTextEditorVisible, setIsTextEditorVisible] = useState(false);
  const [isLogoEditorVisible, setIsLogoEditorVisible] = useState(false);
  const [selectedTextOverlayId, setSelectedTextOverlayId] = useState(null);
  const [selectedLogoOverlayId, setSelectedLogoOverlayId] = useState(null);

  // Check if template supports logo overlays
  const hasLogoOverlays = templateHasLogoOverlays(selectedTemplate);

  // Initialize text overlay editor
  const {
    customizations,
    isPreviewUpdating,
    error: editorError,
    hasCustomizations,
    handleCustomizationChange,
    handleUndo,
    handleReset,
    canUndo
  } = useTextOverlayEditor(selectedTemplate, documentData, {
    onPreviewUpdate: onPreviewUpdate
  });

  // Initialize logo overlay editor (only if template supports logos)
  const {
    logoCustomizations,
    isPreviewUpdating: isLogoPreviewUpdating,
    error: logoEditorError,
    hasCustomizations: hasLogoCustomizations,
    handleLogoChange,
    handleLogoDelete,
    handleUndo: handleLogoUndo,
    handleReset: handleLogoReset,
    canUndo: canLogoUndo
  } = useLogoOverlayEditor(selectedTemplate, documentData, {
    onPreviewUpdate: onPreviewUpdate
  });

  // Handle text overlay selection from interactive canvas
  const handleTextSelect = (overlayId) => {
    setSelectedTextOverlayId(overlayId);
    if (overlayId && !isTextEditorVisible) {
      setIsTextEditorVisible(true);
    }
    // Close logo editor when text is selected
    if (overlayId && isLogoEditorVisible) {
      setIsLogoEditorVisible(false);
    }
  };

  // Handle logo overlay selection from interactive canvas
  const handleLogoSelect = (logoOverlayId) => {
    setSelectedLogoOverlayId(logoOverlayId);
    if (logoOverlayId && !isLogoEditorVisible) {
      setIsLogoEditorVisible(true);
    }
    // Close text editor when logo is selected
    if (logoOverlayId && isTextEditorVisible) {
      setIsTextEditorVisible(false);
    }
  };

  // Handle logo changes from logo customization panel
  const handleLogoCustomizationChange = (logoData) => {
    // Update preview with logo
    if (onPreviewUpdate && selectedTemplate) {
      const enhancedDocumentData = {
        ...documentData,
        logo: logoData
      };

      onPreviewUpdate(selectedTemplate, enhancedDocumentData, customizations);
    }

    prodLogger.debug('🎨 Logo updated in cover preview', {
      logoId: logoData?.id,
      logoName: logoData?.name,
      settings: logoData?.settings
    });
  };

  // Handle logo settings changes
  const handleLogoSettingsChange = (settings) => {
    // Update preview with new logo settings
    if (onPreviewUpdate && selectedTemplate) {
      const enhancedDocumentData = {
        ...documentData,
        logoSettings: settings
      };

      onPreviewUpdate(selectedTemplate, enhancedDocumentData, customizations);
    }
  };

  // Apply loaded customizations to preview when template loads
  const [hasAppliedInitialCustomizations, setHasAppliedInitialCustomizations] = React.useState(false);

  React.useEffect(() => {
    if (selectedTemplate && hasCustomizations && onPreviewUpdate && !isPreviewUpdating && !hasAppliedInitialCustomizations) {
      prodLogger.debug('🔄 Applying loaded customizations to preview', {
        templateId: selectedTemplate.id,
        customizationCount: Object.keys(customizations).length
      });

      // Apply the loaded customizations to update the preview
      onPreviewUpdate(selectedTemplate, documentData, customizations);
      setHasAppliedInitialCustomizations(true);
    }
  }, [selectedTemplate?.id, hasCustomizations, onPreviewUpdate, documentData, isPreviewUpdating, hasAppliedInitialCustomizations, customizations]);

  // Reset the applied flag when template changes
  React.useEffect(() => {
    setHasAppliedInitialCustomizations(false);
  }, [selectedTemplate?.id]);

  if (loading) {
    return (
      <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Generating cover preview...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Preview Error</h3>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!coverPreviewData) {
    return (
      <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <p className="text-gray-600">Please select a template to generate cover preview</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
      {/* Preview Header */}
      <CoverPreviewHeader
        onBack={onBack}
        onProceedToExport={onProceedToExport}
        onToggleTextEditor={() => setIsTextEditorVisible(!isTextEditorVisible)}
        isTextEditorVisible={isTextEditorVisible}
        hasCustomizations={hasCustomizations}
        canUndo={canUndo}
        onUndo={handleUndo}
        onReset={handleReset}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Cover Preview Content */}
        <div className={`flex-1 bg-gray-100 overflow-auto transition-all duration-300 ${
          (isTextEditorVisible || isLogoEditorVisible) ? 'lg:mr-80' : ''
        }`}>
          <div className="mt-4 mx-auto sm:mt-6 text-center max-w-2xl">
            <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">
              Cover Design Preview
              {(isPreviewUpdating || isLogoPreviewUpdating) && (
                <span className="ml-2 inline-flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="ml-1 text-sm text-blue-600">Updating...</span>
                </span>
              )}
            </h2>
            {(editorError || logoEditorError) && (
              <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
                {editorError || logoEditorError}
              </div>
            )}
          </div>

          {/* Template Info - Show logo support status */}
          {hasLogoOverlays && (
            <div className="max-w-2xl mx-auto px-4 mb-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-700 font-medium">
                    This template supports interactive logo editing
                  </span>
                </div>
                <p className="text-xs text-green-600 mt-1">
                  Click on logo areas in the preview to customize them
                </p>
              </div>
            </div>
          )}

          <InteractiveCoverPreviewContainer
            selectedTemplate={selectedTemplate}
            documentData={documentData}
            customizations={customizations}
            logoCustomizations={logoCustomizations}
            onCustomizationChange={handleCustomizationChange}
            onLogoCustomizationChange={handleLogoChange}
            onTextSelect={handleTextSelect}
            onLogoSelect={handleLogoSelect}
            selectedTextOverlayId={selectedTextOverlayId}
            selectedLogoOverlayId={selectedLogoOverlayId}
            coverPreviewData={coverPreviewData}
            isTextEditorVisible={isTextEditorVisible}
            isLogoEditorVisible={isLogoEditorVisible}
            hasLogoOverlays={hasLogoOverlays}
          />
        </div>

        {/* Text Overlay Editor */}
        <TextOverlayEditor
          template={selectedTemplate}
          customizations={customizations}
          onCustomizationChange={handleCustomizationChange}
          onReset={handleReset}
          onUndo={handleUndo}
          canUndo={canUndo}
          isVisible={isTextEditorVisible}
          onToggleVisibility={setIsTextEditorVisible}
          selectedOverlayId={selectedTextOverlayId}
          onOverlaySelect={setSelectedTextOverlayId}
        />

        {/* Logo Overlay Editor - Only show if template supports logos */}
        {hasLogoOverlays && (
          <LogoOverlayEditor
            template={selectedTemplate}
            selectedLogoOverlayId={selectedLogoOverlayId}
            logoCustomizations={logoCustomizations}
            onLogoChange={handleLogoChange}
            onLogoDelete={handleLogoDelete}
            onReset={handleLogoReset}
            onUndo={handleLogoUndo}
            canUndo={canLogoUndo}
            isVisible={isLogoEditorVisible}
            onToggleVisibility={setIsLogoEditorVisible}
          />
        )}
      </div>
    </div>
  );
};

/**
 * Interactive Cover Preview Container
 * Provides Canva-style interactive text editing with drag-and-drop functionality
 * with responsive sizing optimized for different screen sizes
 */
const InteractiveCoverPreviewContainer = ({
  selectedTemplate,
  documentData,
  customizations,
  logoCustomizations,
  onCustomizationChange,
  onLogoCustomizationChange,
  onTextSelect,
  onLogoSelect,
  selectedTextOverlayId,
  selectedLogoOverlayId,
  coverPreviewData,
  isTextEditorVisible = false,
  isLogoEditorVisible = false,
  hasLogoOverlays = false
}) => {
  // Use enhanced document data from cover preview if available, fallback to raw documentData
  const enhancedDocumentData = coverPreviewData?.metadata?.documentData || documentData;

  // Responsive container sizing based on viewport and text editor state
  const getResponsiveContainerClass = () => {
    // Base responsive classes optimized for each screen size:
    // Mobile (up to 640px): Use most of screen width for optimal mobile experience
    // Large mobile/small tablet (641px to 768px): Slightly constrained for better UX
    // Tablet (769px to 1024px): Medium size for comfortable viewing
    // Desktop (1025px+): Smaller size to prevent scrolling

    let baseClasses = "w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl";

    // When text or logo editor is open on large screens, use smaller template to fit better
    if (isTextEditorVisible || isLogoEditorVisible) {
      baseClasses = "w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-lg xl:max-w-xl";
    }

    return baseClasses;
  };

  return (
    <div className="interactive-cover-preview-container p-4 sm:p-6 md:p-8 flex items-center justify-center min-h-full">
      <div className="cover-preview-wrapper w-full flex flex-col items-center">
        {/* Interactive Template Canvas with Responsive Sizing */}
        <div className={getResponsiveContainerClass()} style={{ aspectRatio: '8.5/11' }}>
          <InteractiveTemplateCanvas
            template={selectedTemplate}
            documentData={enhancedDocumentData}
            customizations={customizations}
            logoCustomizations={logoCustomizations}
            onCustomizationChange={onCustomizationChange}
            onLogoCustomizationChange={onLogoCustomizationChange}
            onTextSelect={onTextSelect}
            onLogoSelect={onLogoSelect}
            selectedOverlayId={selectedTextOverlayId}
            selectedLogoOverlayId={selectedLogoOverlayId}
            isInteractive={true}
            className="w-full h-full shadow-lg rounded-lg overflow-hidden"
          />
        </div>

        {/* Interactive Instructions */}
        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600">
            {selectedTextOverlayId
              ? "Drag the selected text to reposition it, or use the editor panel to customize styling"
              : selectedLogoOverlayId
              ? "Logo selected - use the editor panel to replace or remove it"
              : hasLogoOverlays
              ? "Click on text elements to edit them, or click on logo areas to customize logos"
              : "Click on any text element to select and customize it"
            }
          </p>
        </div>
      </div>
    </div>
  );
};



/**
 * Cover Preview Header Component
 */
const CoverPreviewHeader = ({
  onBack,
  onProceedToExport,
  onToggleTextEditor,
  isTextEditorVisible,
  hasCustomizations,
  canUndo,
  onUndo,
  onReset
}) => {
  return (
    <div className={`cover-preview-header border-b border-gray-200 bg-white transition-all duration-300 ${
      isTextEditorVisible ? 'lg:mr-80' : ''
    }`}>
      {/* Mobile-First Responsive Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between px-4 py-2 gap-2 sm:gap-4">

        {/* Top Row on Mobile / Left Section on Desktop */}
        <div className="flex items-center justify-between sm:justify-start">
          {/* Back Button */}
          <button
            onClick={onBack}
            className="flex items-center space-x-1 sm:space-x-2 text-gray-600 hover:text-gray-800 transition-colors p-2 sm:p-0 -ml-2 sm:ml-0"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span className="text-sm sm:text-base hidden xs:inline sm:inline">Back</span>
            <span className="text-sm hidden sm:inline">to Templates</span>
          </button>

          {/* Mobile Buttons Row - Edit Text Style and Export */}
          <div className="sm:hidden flex items-center space-x-2">
            {/* Edit Text Style Button - Mobile */}
            <button
              onClick={onToggleTextEditor}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1 ${
                isTextEditorVisible
                  ? 'bg-blue-100 text-blue-700 border border-blue-300'
                  : 'bg-gray-100 text-gray-700 border border-gray-300'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              <span>Edit</span>
              {hasCustomizations && (
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              )}
            </button>

            {/* Export Button - Mobile Position */}
            <button
              onClick={onProceedToExport}
              className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>Export</span>
            </button>
          </div>
        </div>

        {/* Desktop-Only Right Section */}
        <div className="hidden sm:flex items-center space-x-3">
          {/* Text Editor Toggle */}
          <button
            onClick={onToggleTextEditor}
            className={`px-3 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 ${
              isTextEditorVisible
                ? 'bg-blue-100 text-blue-700 border border-blue-300'
                : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
            }`}
            title="Edit text styling"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <span>Edit Text</span>
            {hasCustomizations && (
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
            )}
          </button>

          {/* Undo Button */}
          {canUndo && (
            <button
              onClick={onUndo}
              className="px-3 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              title="Undo last change"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
              </svg>
            </button>
          )}

          <button
            onClick={onProceedToExport}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 whitespace-nowrap"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
            <span>Proceed to Export</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default CoverPreviewInterface;
