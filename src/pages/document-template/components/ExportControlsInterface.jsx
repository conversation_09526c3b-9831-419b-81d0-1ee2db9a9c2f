import React, { useState } from 'react';

/**
 * Export Controls Interface
 * Provides format selection and export functionality for template workflow
 */
const ExportControlsInterface = ({
  selectedTemplate = null,
  previewData = null,
  documentData = {},
  exportFormat = 'pdf',
  exportLoading = false,
  exportResult = null,
  exportError = null,
  onFormatChange = null,
  onExport = null,
  onBack = null,
  className = ''
}) => {
  const [selectedQuality, setSelectedQuality] = useState('high');
  const [includeMetadata, setIncludeMetadata] = useState(true);

  // Export format options
  const exportFormats = [
    {
      id: 'pdf',
      name: 'PDF',
      description: 'Best for sharing and printing',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      supportsTemplates: true,
      recommended: true,
      features: ['High quality', 'Print ready', 'Universal compatibility']
    },
    {
      id: 'html',
      name: 'HTML',
      description: 'Web-ready format with styling',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      supportsTemplates: true,
      recommended: false,
      features: ['Web compatible', 'Styled content', 'Interactive elements']
    },
    {
      id: 'docx',
      name: 'Word',
      description: 'Editable Microsoft Word format',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      supportsTemplates: false,
      recommended: false,
      features: ['Editable content', 'Microsoft Word', 'Limited template support']
    }
  ];

  const qualityOptions = [
    { id: 'high', name: 'High Quality', description: 'Best for printing (larger file)' },
    { id: 'medium', name: 'Medium Quality', description: 'Balanced size and quality' },
    { id: 'low', name: 'Low Quality', description: 'Smaller file size' }
  ];

  const handleExport = () => {
    if (onExport) {
      onExport(exportFormat, {
        quality: selectedQuality,
        includeMetadata: includeMetadata
      });
    }
  };

  // Show export result
  if (exportResult) {
    return (
      <div className={`export-controls-interface ${className}`}>
        <div className="flex items-center justify-center h-96">
          <div className="text-center max-w-md">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Export Successful!</h3>
            <p className="text-gray-600 mb-6">{exportResult.message}</p>
            
            <div className="space-y-3">
              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Export Another Document
              </button>
              <button
                onClick={onBack}
                className="w-full px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Back to Preview
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show export error
  if (exportError) {
    return (
      <div className={`export-controls-interface ${className}`}>
        <div className="flex items-center justify-center h-96">
          <div className="text-center max-w-md">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Export Failed</h3>
            <p className="text-red-600 mb-6">{exportError}</p>
            
            <div className="space-y-3">
              <button
                onClick={handleExport}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={onBack}
                className="w-full px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Back to Preview
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`export-controls-interface flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="export-header p-6 border-b border-gray-200 bg-white">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Export Document</h2>
        <p className="text-gray-600">Choose your export format and settings</p>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Export Summary */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Export Summary</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-700 mb-2">Document</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <div>Title: {documentData.title || 'Untitled Document'}</div>
                  <div>Author: {documentData.author || 'Unknown'}</div>
                  <div>Pages: {previewData?.metadata?.totalPages || 0}</div>
                  <div>Words: {previewData?.metadata?.content?.wordCount?.toLocaleString() || 0}</div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-700 mb-2">Template</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <div>Name: {selectedTemplate?.name || 'None'}</div>
                  <div>Category: {selectedTemplate?.category || 'N/A'}</div>
                  <div>Type: Cover template</div>
                </div>
              </div>
            </div>
          </div>

          {/* Format Selection */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose Export Format</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {exportFormats.map(format => (
                <div
                  key={format.id}
                  onClick={() => onFormatChange?.(format.id)}
                  className={`relative p-6 border-2 rounded-lg cursor-pointer transition-all hover:shadow-md ${
                    exportFormat === format.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex flex-col items-center text-center">
                    <div className={`mb-3 ${exportFormat === format.id ? 'text-blue-600' : 'text-gray-600'}`}>
                      {format.icon}
                    </div>
                    <h4 className={`font-semibold mb-2 ${exportFormat === format.id ? 'text-blue-900' : 'text-gray-900'}`}>
                      {format.name}
                    </h4>
                    <p className="text-sm text-gray-600 mb-3">{format.description}</p>
                    
                    <div className="space-y-1">
                      {format.features.map(feature => (
                        <div key={feature} className="text-xs text-gray-500">
                          • {feature}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {format.recommended && (
                    <span className="absolute top-2 right-2 px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                      Recommended
                    </span>
                  )}
                  
                  {!format.supportsTemplates && (
                    <span className="absolute top-2 left-2 px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full">
                      No Template
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Export Options */}
          {exportFormat === 'pdf' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">PDF Options</h3>
              
              <div className="space-y-4">
                {/* Quality Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Quality</label>
                  <div className="space-y-2">
                    {qualityOptions.map(option => (
                      <label key={option.id} className="flex items-center">
                        <input
                          type="radio"
                          name="quality"
                          value={option.id}
                          checked={selectedQuality === option.id}
                          onChange={(e) => setSelectedQuality(e.target.value)}
                          className="mr-3"
                        />
                        <div>
                          <div className="font-medium text-gray-900">{option.name}</div>
                          <div className="text-sm text-gray-600">{option.description}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Metadata Option */}
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={includeMetadata}
                      onChange={(e) => setIncludeMetadata(e.target.checked)}
                      className="mr-3"
                    />
                    <div>
                      <div className="font-medium text-gray-900">Include document metadata</div>
                      <div className="text-sm text-gray-600">Add title, author, and creation date to PDF properties</div>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="export-footer flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
        <div className="text-sm text-gray-600">
          Ready to export {previewData?.metadata?.totalPages || 0} pages with {selectedTemplate?.name || 'no template'}
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={onBack}
            disabled={exportLoading}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            Back to Preview
          </button>
          
          <button
            onClick={handleExport}
            disabled={exportLoading || !selectedTemplate}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors flex items-center space-x-2"
          >
            {exportLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Exporting...</span>
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>Export Document</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExportControlsInterface;
