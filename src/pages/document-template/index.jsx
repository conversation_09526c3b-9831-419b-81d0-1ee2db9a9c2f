import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import { useAuth } from '../../contexts/AuthContext';
import { useUserActivity } from '../../hooks/useUserActivity';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import DocumentWorkflowHeader from '../document-editor/components/DocumentWorkflowHeader';
import TemplateSelectionInterface from './components/TemplateSelectionInterface';
import CoverPreviewInterface from './components/CoverPreviewInterface';
import ExportControlsInterface from './components/ExportControlsInterface';
import { documentStorage } from '../../services/documentStorageService';
import { useErrorMonitor } from '../../utils/useErrorMonitor';
import useTemplateWorkflow from './hooks/useTemplateWorkflow';

import { prodLogger } from '../../utils/prodLogger.js';
/**
 * DocumentTemplate - Template selection, preview, and export workflow
 * Separate page for template-related functionality after document editing
 */
const DocumentTemplate = () => {
  const { documentId } = useParams();
  const navigate = useNavigate();
  const { contentMargin } = useSidebar();
  const { user, profile } = useAuth();
  const { trackSkipTemplateUsage } = useUserActivity();

  // Initialize error monitoring
  const logger = useErrorMonitor('DocumentTemplate', { documentId });

  // Document data state
  const [documentData, setDocumentData] = useState(null);
  const [generatedContent, setGeneratedContent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize template workflow hook with user profile data
  const templateWorkflow = useTemplateWorkflow(documentId, documentData, generatedContent, profile, user);

  // Load document data on mount
  useEffect(() => {
    loadDocumentData();
  }, [documentId]);

  const loadDocumentData = async () => {
    try {
      setLoading(true);
      setError(null);

      logger.info('Loading document data for template workflow', { documentId });

      // Load document from database
      const result = await documentStorage.loadDocument(documentId);

      if (result.success && result.data) {
        const data = result.data;

        logger.info('Document data loaded successfully', {
          documentId,
          source: result.source,
          hasGeneratedContent: !!data.generated_content,
          lastModified: data.updated_at
        });

        // Convert database format to component format (same as document editor)
        const documentData = {
          ...data.questionnaire_data,
          generatedContent: data.generated_content,
          lastModified: data.updated_at,
          createdAt: data.created_at,
          documentId: documentId,
          projectId: documentId
        };

        const generatedContent = data.generated_content;

        setDocumentData(documentData);
        setGeneratedContent(generatedContent);

        logger.info('Template workflow data prepared', {
          hasDocumentData: !!documentData,
          hasGeneratedContent: !!generatedContent,
          contentTitle: generatedContent?.title
        });
      } else {
        // If document not found, redirect back to document editor
        logger.warn('Document not found for template workflow, redirecting to editor', {
          documentId,
          error: result.error,
          resultKeys: Object.keys(result || {})
        });
        navigate(`/document-editor/${documentId}`);
        return;
      }

    } catch (err) {
      logger.error('Failed to load document data for template workflow', err, {
        documentId,
        errorMessage: err.message,
        errorStack: err.stack
      });
      setError(err.message || 'Failed to load document data');
    } finally {
      setLoading(false);
    }
  };

  // Navigation handlers
  const handleBackToEditor = () => {
    navigate(`/document-editor/${documentId}`);
  };

  const handlePhaseNavigation = (phase) => {
    // Handle navigation between workflow phases
    switch (phase) {
      case 'Generate':
        // Navigate back to document creator
        navigate('/document-creator');
        break;
      case 'Edit Content':
        handleBackToEditor();
        break;
      case 'Review':
        navigate(`/document-editor/${documentId}/review`);
        break;
      case 'template':
        // Already on template page
        break;
      case 'Publish':
        // CRITICAL: Save selected template before navigating to publish page
        if (templateWorkflow.selectedTemplate) {
          prodLogger.debug('💾 Saving selected template before navigation:', {
            templateId: templateWorkflow.selectedTemplate.id,
            templateName: templateWorkflow.selectedTemplate.name,
            documentId
          });

          // Save template selection in sessionStorage for the publish page
          sessionStorage.setItem(`selectedTemplate_${documentId}`, JSON.stringify(templateWorkflow.selectedTemplate));

          // Also save in localStorage as backup
          localStorage.setItem('lastSelectedTemplate', JSON.stringify(templateWorkflow.selectedTemplate));

          prodLogger.debug('✅ Template saved successfully for publish page');
        } else {
          prodLogger.warn('⚠️ No template selected when navigating to publish page');
        }

        // Navigate to publish page
        navigate(`/document-editor/${documentId}/publish`);
        break;
      default:
        // Fallback for internal template workflow phases
        templateWorkflow.handlePhaseChange(phase);
    }
  };

  // Handle skip template and go directly to publish
  const handleSkipTemplate = async () => {
    prodLogger.debug('🚀 Skipping template selection, navigating to publish');
    
    // Store user preference for skip template
    try {
      localStorage.setItem('docforge_skip_template_preference', 'true');
      localStorage.setItem('docforge_skip_template_last_used', new Date().toISOString());
      prodLogger.debug('💾 Skip template preference saved to localStorage');
    } catch (error) {
      prodLogger.warn('⚠️ Failed to save skip template preference:', error);
    }

    // Track skip template usage for analytics
    try {
      await trackSkipTemplateUsage();
      prodLogger.debug('📊 Skip template usage tracked for analytics');
    } catch (error) {
      prodLogger.warn('⚠️ Failed to track skip template usage:', error);
    }

    // Navigate to publish phase with skip template flag
    navigate(`/document-editor/${documentId}/publish`, {
      state: { skipTemplate: true }
    });
  };

  // Handle proceed with selected template
  const handleProceedWithTemplate = () => {
    if (templateWorkflow.selectedTemplate) {
      prodLogger.debug('🎨 Proceeding with selected template:', templateWorkflow.selectedTemplate.name);
      
      // Save template selection in sessionStorage for the publish page
      sessionStorage.setItem(`selectedTemplate_${documentId}`, JSON.stringify(templateWorkflow.selectedTemplate));
      
      // Also save in localStorage as backup
      localStorage.setItem('lastSelectedTemplate', JSON.stringify(templateWorkflow.selectedTemplate));
      
      // Navigate to publish page
      navigate(`/document-editor/${documentId}/publish`);
    }
  };

  // Loading state - using consistent pattern from review and publish phases
  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <main className={`${contentMargin} ml-0`}>
          <DocumentWorkflowHeader
            currentPhase="template"
            onPhaseClick={handlePhaseNavigation}
          />
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">Loading templates...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to Load Document</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <div className="space-x-3">
            <button
              onClick={loadDocumentData}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Retry
            </button>
            <button
              onClick={handleBackToEditor}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Back to Editor
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Missing document data
  if (!documentData || !generatedContent) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Document Not Ready</h2>
          <p className="text-gray-600 mb-4">
            Please complete the document editing and review process before selecting a template.
          </p>
          <button
            onClick={handleBackToEditor}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Back to Editor
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />
      
      <main className={`${contentMargin} ml-0 pt-16`}>
        {/* Document Workflow Header */}
        <DocumentWorkflowHeader
          currentPhase="template"
          onPhaseClick={handlePhaseNavigation}
        />



        {/* Template Workflow Phases */}
        <div className="flex-1" style={{ height: 'calc(100vh - 8rem)' }}>
          {templateWorkflow.currentPhase === 'template' && (
            <TemplateSelectionInterface
              templates={templateWorkflow.templates}
              categories={templateWorkflow.categories}
              selectedTemplate={templateWorkflow.selectedTemplate}
              selectedCategory={templateWorkflow.selectedCategory}
              loading={templateWorkflow.templateLoading}
              error={templateWorkflow.error}
              onTemplateSelect={templateWorkflow.handleTemplateSelect}
              onCategoryChange={templateWorkflow.handleCategoryChange}
              onSkipTemplate={handleSkipTemplate}
              onProceedWithTemplate={handleProceedWithTemplate}
            />
          )}

          {templateWorkflow.currentPhase === 'cover-preview' && (
            <CoverPreviewInterface
              selectedTemplate={templateWorkflow.selectedTemplate}
              templates={templateWorkflow.allTemplates}
              documentData={documentData}
              coverPreviewData={templateWorkflow.coverPreviewData}
              loading={templateWorkflow.coverPreviewLoading}
              error={templateWorkflow.error}
              onTemplateChange={templateWorkflow.handleTemplateChange}
              onBack={() => templateWorkflow.handlePhaseChange('template')}
              onProceedToExport={() => handlePhaseNavigation('Publish')}
              onPreviewUpdate={templateWorkflow.updateCoverPreviewWithCustomizations}
            />
          )}

          {templateWorkflow.currentPhase === 'export' && (
            <ExportControlsInterface
              selectedTemplate={templateWorkflow.selectedTemplate}
              previewData={templateWorkflow.previewData}
              documentData={documentData}
              exportFormat={templateWorkflow.exportFormat}
              exportLoading={templateWorkflow.exportLoading}
              exportResult={templateWorkflow.exportResult}
              exportError={templateWorkflow.error}
              onFormatChange={templateWorkflow.setExportFormat}
              onExport={templateWorkflow.handleExport}
              onBack={() => templateWorkflow.handlePhaseChange('preview')}
            />
          )}
        </div>
      </main>
    </div>
  );
};



export default DocumentTemplate;
