/**
 * Admin Dashboard
 * Main admin interface for managing users, monitoring activity, and system oversight
 */

import React, { useState } from 'react';
import { useAdminSecurity, useAdminStats, useAdminActivityMonitor } from '../../hooks/useAdminSecurity';
import AdminSecurityPanel from './components/AdminSecurityPanel';
import AdminActivityLog from './components/AdminActivityLog';
import AdminUserManagement from './components/AdminUserManagement';
import AdminStats from './components/AdminStats';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const { isSecure, needsAttention, adminInfo, loading: securityLoading } = useAdminSecurity();
  const { stats, loading: statsLoading } = useAdminStats();
  const { activities, loading: activitiesLoading } = useAdminActivityMonitor();

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'users', name: 'User Management', icon: '👥', requiresRole: 'super_admin' },
    { id: 'activity', name: 'Activity Log', icon: '📋' },
    { id: 'security', name: 'Security', icon: '🔒' },
    { id: 'templates', name: 'Templates', icon: '📄' }
  ];

  // Filter tabs based on user role
  const availableTabs = tabs.filter(tab => {
    if (!tab.requiresRole) return true;
    return adminInfo?.admin_role === 'super_admin';
  });

  if (securityLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isSecure) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <div className="text-red-600 text-4xl mb-4">⚠️</div>
            <h2 className="text-xl font-bold text-red-800 mb-2">Security Check Required</h2>
            <p className="text-red-700 mb-4">
              {needsAttention ? 'Security issues detected. Please contact your administrator.' : 'Admin access verification failed.'}
            </p>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Return to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">
                Welcome, {adminInfo?.full_name || 'Admin'} ({adminInfo?.admin_role})
              </p>
            </div>
            
            {/* Security Status Indicator */}
            <div className="flex items-center space-x-4">
              <div className={`flex items-center px-3 py-1 rounded-full text-sm ${
                isSecure 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  isSecure ? 'bg-green-500' : 'bg-yellow-500'
                }`}></div>
                {isSecure ? 'Secure' : 'Attention Required'}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {availableTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <AdminStats stats={stats} loading={statsLoading} />
            
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => setActiveTab('templates')}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  <div className="text-2xl mb-2">📄</div>
                  <div className="font-medium">Manage Templates</div>
                  <div className="text-sm text-gray-500">Create and edit document templates</div>
                </button>
                
                <button
                  onClick={() => setActiveTab('activity')}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  <div className="text-2xl mb-2">📋</div>
                  <div className="font-medium">View Activity</div>
                  <div className="text-sm text-gray-500">Monitor admin actions and logs</div>
                </button>
                
                <button
                  onClick={() => setActiveTab('security')}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  <div className="text-2xl mb-2">🔒</div>
                  <div className="font-medium">Security Center</div>
                  <div className="text-sm text-gray-500">Monitor security and access</div>
                </button>
              </div>
            </div>

            {/* Recent Activity Preview */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
                <button
                  onClick={() => setActiveTab('activity')}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  View All
                </button>
              </div>
              
              {activitiesLoading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                </div>
              ) : (
                <div className="space-y-3">
                  {activities.slice(0, 5).map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                      <div className="flex items-center space-x-3">
                        <div className="text-sm font-medium text-gray-900">
                          {activity.action.replace(/_/g, ' ')}
                        </div>
                        <div className="text-xs text-gray-500">
                          by {activity.admin_user?.full_name || 'Unknown'}
                        </div>
                      </div>
                      <div className="text-xs text-gray-400">
                        {new Date(activity.created_at).toLocaleString()}
                      </div>
                    </div>
                  ))}
                  
                  {activities.length === 0 && (
                    <div className="text-center py-4 text-gray-500">
                      No recent activity
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'users' && <AdminUserManagement />}
        {activeTab === 'activity' && <AdminActivityLog activities={activities} loading={activitiesLoading} />}
        {activeTab === 'security' && <AdminSecurityPanel />}
        {activeTab === 'templates' && (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-gray-900">Template Management</h3>
              <button
                onClick={() => window.location.href = '/admin/templates'}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Open Template Manager
              </button>
            </div>
            <p className="text-gray-600">
              Manage document templates, create new templates, and configure template settings.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
