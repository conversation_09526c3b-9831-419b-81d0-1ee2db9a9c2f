import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase.js';
import imageOverlayService from '../../services/imageOverlayService.js';
import templateService from '../../services/templateService.js';
import TextOverlayEditor from './components/TextOverlayEditor.jsx';
import TemplateCreationForm from './components/TemplateCreationForm.jsx';
import ConfirmationModal from '../../components/ui/ConfirmationModal';

import { prodLogger } from '../../utils/prodLogger.js';
/**
 * Template Manager - Admin interface for managing image overlay templates
 * Features:
 * - Upload background images
 * - Configure text overlay positions
 * - Preview templates in real-time
 * - Manage template metadata
 */
const TemplateManager = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [previewData, setPreviewData] = useState({
    title: 'Sample Document Title',
    author: 'John Doe',
    description: 'This is a sample description to show how the template will look with your content.'
  });

  // Confirmation modal state
  const [confirmModal, setConfirmModal] = useState({
    isOpen: false,
    template: null,
    isLoading: false
  });

  // Custom fonts state for template editing
  const [customFonts, setCustomFonts] = useState([]);

  // Load templates on component mount
  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('cover_templates')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTemplates(data || []);
    } catch (error) {
      prodLogger.error('Error loading templates:', error);
    } finally {
      setLoading(false);
    }
  };

  const deleteTemplate = (template) => {
    setConfirmModal({
      isOpen: true,
      template,
      isLoading: false
    });
  };

  const handleConfirmDelete = async () => {
    if (!confirmModal.template) return;

    setConfirmModal(prev => ({ ...prev, isLoading: true }));

    try {
      // Use the template service to handle proper deletion with storage cleanup
      const result = await templateService.deleteTemplate(confirmModal.template.id);

      if (!result.success) {
        throw new Error(result.error);
      }

      // Update UI
      setTemplates(templates.filter(t => t.id !== confirmModal.template.id));
      if (selectedTemplate?.id === confirmModal.template.id) {
        setSelectedTemplate(null);
      }
      setConfirmModal({ isOpen: false, template: null, isLoading: false });

    } catch (error) {
      prodLogger.error('Error deleting template:', error);
      alert('Failed to delete template');
      setConfirmModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleCloseConfirmModal = () => {
    if (!confirmModal.isLoading) {
      setConfirmModal({ isOpen: false, template: null, isLoading: false });
    }
  };

  const duplicateTemplate = async (template) => {
    try {
      const newTemplate = {
        ...template,
        id: `${template.id}-copy-${Date.now()}`,
        name: `${template.name} (Copy)`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('cover_templates')
        .insert([newTemplate]);

      if (error) throw error;
      
      await loadTemplates();
    } catch (error) {
      prodLogger.error('Error duplicating template:', error);
      alert('Failed to duplicate template');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading templates...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Template Manager</h1>
              <p className="text-gray-600">Manage image overlay templates for DocForge AI</p>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create Template
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Template List */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-4 border-b">
                <h2 className="text-lg font-semibold text-gray-900">Templates ({templates.length})</h2>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {templates.map((template) => (
                  <div
                    key={template.id}
                    className={`p-4 border-b cursor-pointer hover:bg-gray-50 ${
                      selectedTemplate?.id === template.id ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                    onClick={() => setSelectedTemplate(template)}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 truncate">{template.name}</h3>
                        <p className="text-sm text-gray-500 capitalize">{template.category}</p>
                        <p className="text-xs text-gray-400 mt-1">
                          {template.usage_count} uses • {new Date(template.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex space-x-1 ml-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            duplicateTemplate(template);
                          }}
                          className="text-gray-400 hover:text-blue-600 p-1"
                          title="Duplicate"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteTemplate(template);
                          }}
                          className="text-gray-400 hover:text-red-600 p-1"
                          title="Delete"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Template Details & Preview */}
          <div className="lg:col-span-2">
            {selectedTemplate ? (
              <TemplateEditor
                template={selectedTemplate}
                previewData={previewData}
                onUpdate={loadTemplates}
                onPreviewDataChange={setPreviewData}
              />
            ) : (
              <div className="bg-white rounded-lg shadow-sm border h-96 flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p>Select a template to edit</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Create Template Modal */}
      {showCreateModal && (
        <TemplateCreationForm
          onClose={() => setShowCreateModal(false)}
          onSuccess={(newTemplate) => {
            setShowCreateModal(false);
            loadTemplates();
            // Optionally select the newly created template
            setSelectedTemplate(newTemplate);
          }}
        />
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmModal.isOpen}
        onClose={handleCloseConfirmModal}
        onConfirm={handleConfirmDelete}
        title="Delete Template"
        message={`Are you sure you want to delete the template "${confirmModal.template?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
        isLoading={confirmModal.isLoading}
      />
    </div>
  );
};

/**
 * Template Editor Component
 * Allows editing template properties and text overlays
 */
const TemplateEditor = ({ template, previewData, onUpdate, onPreviewDataChange }) => {
  const [editedTemplate, setEditedTemplate] = useState(template);
  const [previewImage, setPreviewImage] = useState(null);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('properties'); // 'properties' or 'overlays'

  // Update editedTemplate when template prop changes (fix for template switching bug)
  useEffect(() => {
    setEditedTemplate(template);
  }, [template]);

  // Generate preview when template or preview data changes
  useEffect(() => {
    generatePreview();
  }, [editedTemplate, previewData]);

  const generatePreview = async () => {
    try {
      const canvas = await imageOverlayService.renderTemplate(editedTemplate, previewData);
      const imageData = imageOverlayService.exportAsImage(canvas, 'png', 0.8);
      setPreviewImage(imageData);
    } catch (error) {
      prodLogger.error('Error generating preview:', error);
      setPreviewImage(null);
    }
  };

  const saveTemplate = async () => {
    try {
      setSaving(true);

      // Prepare custom fonts data for database storage
      const customFontsData = {
        fonts: customFonts.map(font => ({
          name: font.name,
          category: font.category || 'Custom',
          fallback: font.fallback,
          googleFont: font.googleFont || false,
          customUrl: font.customUrl,
          addedAt: new Date().toISOString()
        }))
      };

      const { error } = await supabase
        .from('cover_templates')
        .update({
          name: editedTemplate.name,
          description: editedTemplate.description,
          category: editedTemplate.category,
          tags: editedTemplate.tags,
          text_overlays: editedTemplate.text_overlays,
          custom_fonts: customFontsData,
          is_premium: editedTemplate.is_premium,
          updated_at: new Date().toISOString()
        })
        .eq('id', editedTemplate.id);

      if (error) throw error;

      await onUpdate();
      alert('Template saved successfully!');
      prodLogger.debug('Template saved with custom fonts:', {
        templateId: editedTemplate.id,
        customFontsCount: customFonts.length
      });
    } catch (error) {
      prodLogger.error('Error saving template:', error);
      alert('Failed to save template');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <div className="p-6">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Edit Template</h2>
            <p className="text-gray-600">Configure template properties and text overlays</p>
          </div>
          <button
            onClick={saveTemplate}
            disabled={saving}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('properties')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'properties'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Properties
            </button>
            <button
              onClick={() => setActiveTab('overlays')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overlays'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Text Overlays
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'properties' ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Template Properties */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
                <input
                  type="text"
                  value={editedTemplate.name}
                  onChange={(e) => setEditedTemplate({ ...editedTemplate, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={editedTemplate.description || ''}
                  onChange={(e) => setEditedTemplate({ ...editedTemplate, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  value={editedTemplate.category}
                  onChange={(e) => setEditedTemplate({ ...editedTemplate, category: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="Business">Business</option>
                  <option value="Academic">Academic</option>
                  <option value="Creative">Creative</option>
                  <option value="Technical">Technical</option>
                  <option value="Personal">Personal</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_premium"
                  checked={editedTemplate.is_premium}
                  onChange={(e) => setEditedTemplate({ ...editedTemplate, is_premium: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_premium" className="ml-2 block text-sm text-gray-900">
                  Premium Template
                </label>
              </div>
            </div>

            {/* Preview */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Preview</label>
              <div className="border border-gray-300 rounded-lg p-4 bg-gray-50">
                {previewImage ? (
                  <img
                    src={previewImage}
                    alt="Template Preview"
                    className="w-full h-auto max-h-64 object-contain mx-auto"
                  />
                ) : (
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p>Generating preview...</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Preview Data Controls */}
              <div className="mt-4 space-y-2">
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">Preview Title</label>
                  <input
                    type="text"
                    value={previewData.title}
                    onChange={(e) => onPreviewDataChange({ ...previewData, title: e.target.value })}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">Preview Author</label>
                  <input
                    type="text"
                    value={previewData.author}
                    onChange={(e) => onPreviewDataChange({ ...previewData, author: e.target.value })}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Text Overlay Editor Tab */
          <div className="h-96">
            <TextOverlayEditor
              template={editedTemplate}
              onTemplateChange={setEditedTemplate}
              onCustomFontsChange={setCustomFonts}
            />
          </div>
        )}
      </div>
    </div>
  );
};



export default TemplateManager;
