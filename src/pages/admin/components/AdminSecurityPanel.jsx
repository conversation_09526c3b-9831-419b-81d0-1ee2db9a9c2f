/**
 * Admin Security Panel Component
 * Displays security status, alerts, and monitoring information
 */

import React from 'react';
import { useAdminSecurity } from '../../../hooks/useAdminSecurity';

const AdminSecurityPanel = () => {
  const { 
    isSecure, 
    needsAttention, 
    adminInfo, 
    suspiciousActivity,
    suspiciousDetails,
    lastSecurityCheck,
    recheckSecurity 
  } = useAdminSecurity();

  return (
    <div className="space-y-6">
      {/* Security Status Overview */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Security Status</h3>
          <button
            onClick={recheckSecurity}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
          >
            Refresh Status
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className={`p-4 rounded-lg border ${
            isSecure ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${
                isSecure ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <div>
                <div className="font-medium text-gray-900">Overall Security</div>
                <div className={`text-sm ${
                  isSecure ? 'text-green-600' : 'text-red-600'
                }`}>
                  {isSecure ? 'Secure' : 'Attention Required'}
                </div>
              </div>
            </div>
          </div>

          <div className={`p-4 rounded-lg border ${
            !suspiciousActivity ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'
          }`}>
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${
                !suspiciousActivity ? 'bg-green-500' : 'bg-yellow-500'
              }`}></div>
              <div>
                <div className="font-medium text-gray-900">Activity Monitor</div>
                <div className={`text-sm ${
                  !suspiciousActivity ? 'text-green-600' : 'text-yellow-600'
                }`}>
                  {!suspiciousActivity ? 'Normal' : 'Suspicious Activity'}
                </div>
              </div>
            </div>
          </div>

          <div className="p-4 rounded-lg border bg-blue-50 border-blue-200">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full mr-3 bg-blue-500"></div>
              <div>
                <div className="font-medium text-gray-900">Admin Role</div>
                <div className="text-sm text-blue-600 capitalize">
                  {adminInfo?.admin_role?.replace('_', ' ') || 'Unknown'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Security Alerts */}
      {(needsAttention || suspiciousActivity) && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Security Alerts</h3>
          
          {suspiciousActivity && suspiciousDetails && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <div className="flex items-start">
                <div className="text-yellow-600 mr-3">⚠️</div>
                <div>
                  <div className="font-medium text-yellow-800">Suspicious Activity Detected</div>
                  <ul className="mt-2 text-sm text-yellow-700">
                    {suspiciousDetails.map((detail, index) => (
                      <li key={index}>• {detail}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {needsAttention && !suspiciousActivity && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="text-red-600 mr-3">🔒</div>
                <div>
                  <div className="font-medium text-red-800">Security Attention Required</div>
                  <div className="mt-2 text-sm text-red-700">
                    Your admin session requires security verification.
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Security Information */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Security Information</h3>
        
        <div className="space-y-4">
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <span className="text-sm font-medium text-gray-600">Admin Since</span>
            <span className="text-sm text-gray-900">
              {adminInfo?.admin_granted_at 
                ? new Date(adminInfo.admin_granted_at).toLocaleDateString()
                : 'Unknown'
              }
            </span>
          </div>
          
          <div className="flex justify-between items-center py-2 border-b border-gray-100">
            <span className="text-sm font-medium text-gray-600">Last Security Check</span>
            <span className="text-sm text-gray-900">
              {lastSecurityCheck 
                ? lastSecurityCheck.toLocaleTimeString()
                : 'Never'
              }
            </span>
          </div>
          
          <div className="flex justify-between items-center py-2">
            <span className="text-sm font-medium text-gray-600">Session Status</span>
            <span className={`text-sm ${isSecure ? 'text-green-600' : 'text-red-600'}`}>
              {isSecure ? 'Valid' : 'Invalid'}
            </span>
          </div>
        </div>
      </div>

      {/* Security Best Practices */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Security Best Practices</h3>
        
        <div className="space-y-3 text-sm text-gray-600">
          <div className="flex items-start">
            <div className="text-green-500 mr-2">✓</div>
            <div>Always sign out when finished with admin tasks</div>
          </div>
          <div className="flex items-start">
            <div className="text-green-500 mr-2">✓</div>
            <div>Monitor the activity log regularly for unusual actions</div>
          </div>
          <div className="flex items-start">
            <div className="text-green-500 mr-2">✓</div>
            <div>Report any suspicious activity immediately</div>
          </div>
          <div className="flex items-start">
            <div className="text-green-500 mr-2">✓</div>
            <div>Use admin privileges only when necessary</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSecurityPanel;
