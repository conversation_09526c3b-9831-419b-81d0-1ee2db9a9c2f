/**
 * Admin Statistics Component
 * Displays key admin metrics and system statistics
 */

import React from 'react';

const AdminStats = ({ stats, loading }) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const statCards = [
    {
      title: 'Total Admins',
      value: stats.totalAdmins,
      icon: '👥',
      color: 'blue',
      description: 'Active admin users'
    },
    {
      title: 'Recent Activity',
      value: stats.recentActivity,
      icon: '📊',
      color: 'green',
      description: 'Actions in last 24h'
    },
    {
      title: 'Security Alerts',
      value: stats.securityAlerts || 0,
      icon: '🔒',
      color: stats.securityAlerts > 0 ? 'red' : 'gray',
      description: 'Security incidents'
    },
    {
      title: 'System Status',
      value: 'Healthy',
      icon: '✅',
      color: 'green',
      description: 'Overall system health'
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-50 text-blue-600 border-blue-200',
      green: 'bg-green-50 text-green-600 border-green-200',
      red: 'bg-red-50 text-red-600 border-red-200',
      gray: 'bg-gray-50 text-gray-600 border-gray-200'
    };
    return colors[color] || colors.gray;
  };

  return (
    <div className="space-y-6">
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
              </div>
              <div className={`p-3 rounded-full ${getColorClasses(stat.color)}`}>
                <span className="text-xl">{stat.icon}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Role Distribution */}
      {stats.roleDistribution && Object.keys(stats.roleDistribution).length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Admin Role Distribution</h3>
          <div className="space-y-3">
            {Object.entries(stats.roleDistribution).map(([role, count]) => (
              <div key={role} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    role === 'super_admin' ? 'bg-red-500' :
                    role === 'admin' ? 'bg-blue-500' :
                    'bg-green-500'
                  }`}></div>
                  <span className="text-sm font-medium text-gray-700 capitalize">
                    {role.replace('_', ' ')}
                  </span>
                </div>
                <span className="text-sm text-gray-500">{count}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* System Health Indicators */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">System Health</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <div>
              <div className="text-sm font-medium text-gray-700">Database</div>
              <div className="text-xs text-gray-500">Connected</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <div>
              <div className="text-sm font-medium text-gray-700">Authentication</div>
              <div className="text-xs text-gray-500">Active</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <div>
              <div className="text-sm font-medium text-gray-700">Storage</div>
              <div className="text-xs text-gray-500">Available</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminStats;
