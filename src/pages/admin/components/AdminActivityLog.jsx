/**
 * Admin Activity Log Component
 * Displays detailed admin activity logs with filtering and search
 */

import React, { useState, useMemo } from 'react';

const AdminActivityLog = ({ activities, loading }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAction, setFilterAction] = useState('all');
  const [filterUser, setFilterUser] = useState('all');

  // Get unique actions and users for filters
  const { uniqueActions, uniqueUsers } = useMemo(() => {
    const actions = new Set();
    const users = new Set();
    
    activities.forEach(activity => {
      actions.add(activity.action);
      if (activity.admin_user?.full_name) {
        users.add(activity.admin_user.full_name);
      }
    });
    
    return {
      uniqueActions: Array.from(actions),
      uniqueUsers: Array.from(users)
    };
  }, [activities]);

  // Filter activities based on search and filters
  const filteredActivities = useMemo(() => {
    return activities.filter(activity => {
      const matchesSearch = searchTerm === '' || 
        activity.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.admin_user?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.resource_type?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesAction = filterAction === 'all' || activity.action === filterAction;
      const matchesUser = filterUser === 'all' || activity.admin_user?.full_name === filterUser;
      
      return matchesSearch && matchesAction && matchesUser;
    });
  }, [activities, searchTerm, filterAction, filterUser]);

  const getActionIcon = (action) => {
    if (action.includes('create')) return '➕';
    if (action.includes('delete')) return '🗑️';
    if (action.includes('update')) return '✏️';
    if (action.includes('grant')) return '🔑';
    if (action.includes('revoke')) return '🚫';
    if (action.includes('security')) return '🔒';
    if (action.includes('login') || action.includes('access')) return '🚪';
    return '📋';
  };

  const getActionColor = (action) => {
    if (action.includes('delete') || action.includes('revoke')) return 'text-red-600';
    if (action.includes('create') || action.includes('grant')) return 'text-green-600';
    if (action.includes('security') || action.includes('violation')) return 'text-orange-600';
    if (action.includes('update')) return 'text-blue-600';
    return 'text-gray-600';
  };

  const formatDetails = (details) => {
    if (!details) return null;
    
    try {
      const parsed = typeof details === 'string' ? JSON.parse(details) : details;
      return Object.entries(parsed).map(([key, value]) => (
        <div key={key} className="text-xs text-gray-500">
          <span className="font-medium">{key}:</span> {String(value)}
        </div>
      ));
    } catch {
      return <div className="text-xs text-gray-500">{String(details)}</div>;
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Activity Log</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="h-3 bg-gray-200 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Activity Log</h3>
          <div className="text-sm text-gray-500">
            {filteredActivities.length} of {activities.length} activities
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <input
              type="text"
              placeholder="Search activities..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <select
              value={filterAction}
              onChange={(e) => setFilterAction(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Actions</option>
              {uniqueActions.map(action => (
                <option key={action} value={action}>
                  {action.replace(/_/g, ' ')}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <select
              value={filterUser}
              onChange={(e) => setFilterUser(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Users</option>
              {uniqueUsers.map(user => (
                <option key={user} value={user}>{user}</option>
              ))}
            </select>
          </div>
          
          <div>
            <button
              onClick={() => {
                setSearchTerm('');
                setFilterAction('all');
                setFilterUser('all');
              }}
              className="w-full px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Activity List */}
      <div className="divide-y divide-gray-200">
        {filteredActivities.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            {activities.length === 0 ? 'No activities found' : 'No activities match your filters'}
          </div>
        ) : (
          filteredActivities.map((activity) => (
            <div key={activity.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-start space-x-4">
                {/* Action Icon */}
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <span className="text-sm">{getActionIcon(activity.action)}</span>
                  </div>
                </div>

                {/* Activity Details */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className={`font-medium ${getActionColor(activity.action)}`}>
                        {activity.action.replace(/_/g, ' ')}
                      </span>
                      {activity.resource_type && (
                        <span className="text-sm text-gray-500">
                          on {activity.resource_type}
                        </span>
                      )}
                      {activity.resource_id && (
                        <span className="text-xs text-gray-400 font-mono">
                          #{activity.resource_id.slice(-8)}
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(activity.created_at).toLocaleString()}
                    </div>
                  </div>

                  <div className="mt-1 flex items-center space-x-4">
                    <div className="text-sm text-gray-600">
                      by <span className="font-medium">
                        {activity.admin_user?.full_name || 'Unknown User'}
                      </span>
                    </div>
                    {activity.ip_address && (
                      <div className="text-xs text-gray-400">
                        IP: {activity.ip_address}
                      </div>
                    )}
                  </div>

                  {/* Additional Details */}
                  {activity.details && (
                    <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                      {formatDetails(activity.details)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Load More Button (if needed) */}
      {filteredActivities.length >= 50 && (
        <div className="p-6 border-t border-gray-200 text-center">
          <button className="text-blue-600 hover:text-blue-700 font-medium">
            Load More Activities
          </button>
        </div>
      )}
    </div>
  );
};

export default AdminActivityLog;
