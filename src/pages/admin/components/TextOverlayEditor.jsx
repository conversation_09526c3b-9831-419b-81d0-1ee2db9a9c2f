import React, { useState, useRef, useEffect } from 'react';

import { prodLogger } from '../../../utils/prodLogger.js';
import fontLoader from '../../../utils/fontLoader.js';
import { PRELOADED_GOOGLE_FONTS } from '../../../utils/preloadedGoogleFonts.js';

// Use centralized preloaded Google Fonts registry
const GOOGLE_FONTS = PRELOADED_GOOGLE_FONTS;

// Font loading utilities - now using centralized fontLoader
const loadGoogleFont = async (fontName) => {
  try {
    await fontLoader.loadGoogleFont(fontName);
    prodLogger.debug(`Google font loaded via fontLoader: ${fontName}`);
  } catch (error) {
    prodLogger.error(`Failed to load Google font: ${fontName}`, error);
    throw error;
  }
};

// Load custom Google Font from URL - now using centralized fontLoader
const loadCustomGoogleFont = async (fontUrl, fontFamily = null) => {
  try {
    // Extract font family name if not provided
    if (!fontFamily) {
      const extractedNames = extractFontNamesFromUrl(fontUrl);
      if (extractedNames.length === 0) {
        throw new Error('Could not extract font family name from URL');
      }
      fontFamily = extractedNames[0];
    }

    // Load the font using centralized fontLoader
    await fontLoader.loadCustomFont(fontUrl, fontFamily);
    prodLogger.debug(`Custom font loaded via fontLoader: ${fontFamily} from ${fontUrl}`);

    return fontFamily;
  } catch (error) {
    prodLogger.error(`Failed to load custom font from ${fontUrl}:`, error);
    throw error;
  }
};

// Extract font family names from Google Fonts URL
const extractFontNamesFromUrl = (fontUrl) => {
  try {
    const url = new URL(fontUrl);
    const familyParam = url.searchParams.get('family');
    if (!familyParam) return [];

    // Handle multiple families separated by &family=
    const families = fontUrl.split('family=').slice(1);
    return families.map(family => {
      // Extract font name before colon or ampersand
      const fontName = family.split(':')[0].split('&')[0];
      return fontName.replace(/\+/g, ' ');
    });
  } catch (error) {
    prodLogger.error('Error extracting font names:', error);
    return [];
  }
};

/**
 * Text Overlay Editor Component
 * Visual editor for positioning and styling text overlays on template backgrounds
 */
const TextOverlayEditor = ({ template, onTemplateChange, onCustomFontsChange = null }) => {
  const canvasRef = useRef(null);
  const [selectedOverlay, setSelectedOverlay] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [backgroundImage, setBackgroundImage] = useState(null);
  const [scale, setScale] = useState(1);

  // Custom font management state
  const [customFonts, setCustomFonts] = useState([]);
  const [customFontUrl, setCustomFontUrl] = useState('');
  const [customFontName, setCustomFontName] = useState('');
  const [fontLoadingStatus, setFontLoadingStatus] = useState('');
  const [isLoadingFont, setIsLoadingFont] = useState(false);

  // Reset selected overlay when template changes
  useEffect(() => {
    setSelectedOverlay(null);
  }, [template.id]);

  // Register existing custom fonts with fontLoader when component mounts
  useEffect(() => {
    customFonts.forEach(font => {
      if (font.customUrl) {
        fontLoader.registerCustomFont(font.name, font.customUrl);
        prodLogger.debug(`Registered existing custom font: ${font.name}`);
      }
    });
  }, [customFonts]);

  // Notify parent component when custom fonts change
  useEffect(() => {
    if (onCustomFontsChange) {
      onCustomFontsChange(customFonts);
    }
  }, [customFonts, onCustomFontsChange]);

  // Load background image
  useEffect(() => {
    if (template.background_image_url) {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        setBackgroundImage(img);
        // Calculate scale to fit canvas
        const canvas = canvasRef.current;
        if (canvas) {
          const scaleX = canvas.width / img.width;
          const scaleY = canvas.height / img.height;
          setScale(Math.min(scaleX, scaleY, 1));
        }
      };
      img.src = template.background_image_url;
    }
  }, [template.background_image_url]);

  // Draw canvas
  useEffect(() => {
    drawCanvas();
  }, [backgroundImage, template.text_overlays, selectedOverlay, scale]);

  // Add global mouse event listeners for drag operations
  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseMove = (e) => handleMouseMove(e);
      const handleGlobalMouseUp = (e) => handleMouseUp(e);

      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDragging, selectedOverlay, dragStart, scale, backgroundImage, template]);

  const drawCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas || !backgroundImage) return;

    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background image
    const scaledWidth = backgroundImage.width * scale;
    const scaledHeight = backgroundImage.height * scale;
    const offsetX = (canvas.width - scaledWidth) / 2;
    const offsetY = (canvas.height - scaledHeight) / 2;

    ctx.drawImage(backgroundImage, offsetX, offsetY, scaledWidth, scaledHeight);

    // Draw overlay rectangles
    if (template.text_overlays?.overlays) {
      template.text_overlays.overlays.forEach((overlay, index) => {
        if (!overlay?.position) return;

        const x = offsetX + (overlay.position.x * scale);
        const y = offsetY + (overlay.position.y * scale);
        const width = overlay.position.width * scale;
        const height = overlay.position.height * scale;

        // Draw overlay rectangle
        ctx.strokeStyle = selectedOverlay === index ? '#3B82F6' : '#EF4444';
        ctx.lineWidth = 2;
        ctx.setLineDash(selectedOverlay === index ? [] : [5, 5]);
        ctx.strokeRect(x, y, width, height);

        // Draw overlay label
        ctx.fillStyle = selectedOverlay === index ? '#3B82F6' : '#EF4444';
        ctx.font = '12px Arial';
        ctx.fillText(overlay.id || `Overlay ${index + 1}`, x, y - 5);

        // Draw resize handles for selected overlay
        if (selectedOverlay === index) {
          const handleSize = 8;
          ctx.fillStyle = '#3B82F6';
          ctx.strokeStyle = '#FFFFFF';
          ctx.lineWidth = 1;
          ctx.setLineDash([]);

          // Corner handles with white border for better visibility
          const handles = [
            [x - handleSize/2, y - handleSize/2],
            [x + width - handleSize/2, y - handleSize/2],
            [x - handleSize/2, y + height - handleSize/2],
            [x + width - handleSize/2, y + height - handleSize/2]
          ];

          handles.forEach(([hx, hy]) => {
            ctx.fillRect(hx, hy, handleSize, handleSize);
            ctx.strokeRect(hx, hy, handleSize, handleSize);
          });
        }
      });
    }
  };

  const getCanvasCoordinates = (e) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: (e.clientX - rect.left) * (canvas.width / rect.width),
      y: (e.clientY - rect.top) * (canvas.height / rect.height)
    };
  };

  const getOverlayAtPosition = (x, y) => {
    if (!template.text_overlays?.overlays || !backgroundImage || !canvasRef.current) return -1;

    const canvas = canvasRef.current;
    const scaledWidth = backgroundImage.width * scale;
    const scaledHeight = backgroundImage.height * scale;
    const offsetX = (canvas.width - scaledWidth) / 2;
    const offsetY = (canvas.height - scaledHeight) / 2;

    // Check overlays from top to bottom (reverse order for proper z-index handling)
    for (let i = template.text_overlays.overlays.length - 1; i >= 0; i--) {
      const overlay = template.text_overlays.overlays[i];
      if (!overlay?.position) continue;

      const overlayX = offsetX + (overlay.position.x * scale);
      const overlayY = offsetY + (overlay.position.y * scale);
      const overlayWidth = overlay.position.width * scale;
      const overlayHeight = overlay.position.height * scale;

      if (x >= overlayX && x <= overlayX + overlayWidth &&
          y >= overlayY && y <= overlayY + overlayHeight) {
        return i;
      }
    }
    return -1;
  };

  const handleMouseDown = (e) => {
    e.preventDefault();
    const coords = getCanvasCoordinates(e);
    const overlayIndex = getOverlayAtPosition(coords.x, coords.y);

    if (overlayIndex !== -1) {
      setSelectedOverlay(overlayIndex);
      setIsDragging(true);
      setDragStart(coords);
    } else {
      setSelectedOverlay(null);
    }
  };

  const handleMouseMove = (e) => {
    if (!isDragging || selectedOverlay === null || !backgroundImage || !template.text_overlays?.overlays) return;

    e.preventDefault();
    const coords = getCanvasCoordinates(e);
    const deltaX = coords.x - dragStart.x;
    const deltaY = coords.y - dragStart.y;

    // Convert delta to template coordinates
    const templateDeltaX = deltaX / scale;
    const templateDeltaY = deltaY / scale;

    // Update overlay position
    const updatedOverlays = [...template.text_overlays.overlays];
    const overlay = updatedOverlays[selectedOverlay];

    if (overlay) {
      overlay.position.x = Math.max(0, Math.min(
        template.background_image_width - overlay.position.width,
        overlay.position.x + templateDeltaX
      ));
      overlay.position.y = Math.max(0, Math.min(
        template.background_image_height - overlay.position.height,
        overlay.position.y + templateDeltaY
      ));

      onTemplateChange({
        ...template,
        text_overlays: {
          ...template.text_overlays,
          overlays: updatedOverlays
        }
      });

      setDragStart(coords);
    }
  };

  const handleMouseUp = (e) => {
    if (isDragging) {
      e.preventDefault();
      setIsDragging(false);
    }
  };

  const updateOverlayProperty = (overlayIndex, property, value) => {
    const updatedOverlays = [...template.text_overlays.overlays];
    
    if (property.includes('.')) {
      const [parent, child] = property.split('.');
      updatedOverlays[overlayIndex][parent][child] = value;
    } else {
      updatedOverlays[overlayIndex][property] = value;
    }

    onTemplateChange({
      ...template,
      text_overlays: {
        ...template.text_overlays,
        overlays: updatedOverlays
      }
    });
  };

  // Custom font loading functions
  const handleLoadCustomFont = async () => {
    if (!customFontUrl.trim()) {
      setFontLoadingStatus('Please enter a Google Fonts URL');
      return;
    }

    setIsLoadingFont(true);
    setFontLoadingStatus('Loading font...');

    try {
      // Load the custom font using centralized fontLoader
      const fontFamily = await loadCustomGoogleFont(customFontUrl);

      // Auto-fill the detected font name
      setCustomFontName(fontFamily);
      setFontLoadingStatus(`Font loaded! Detected: ${fontFamily}`);

    } catch (error) {
      prodLogger.error('Error loading custom font:', error);
      setFontLoadingStatus(`Error: ${error.message}`);
    } finally {
      setIsLoadingFont(false);
    }
  };

  const handleAddCustomFont = () => {
    if (!customFontName.trim()) {
      setFontLoadingStatus('Please enter a font family name');
      return;
    }

    if (!customFontUrl.trim()) {
      setFontLoadingStatus('Please load a font first');
      return;
    }

    // Check if font already exists
    const allFonts = [...GOOGLE_FONTS, ...customFonts];
    if (allFonts.some(font => font.name.toLowerCase() === customFontName.toLowerCase())) {
      setFontLoadingStatus('Font already exists in the list');
      return;
    }

    // Register the custom font with the centralized fontLoader
    fontLoader.registerCustomFont(customFontName, customFontUrl);

    // Add to custom fonts list
    const newCustomFont = {
      name: customFontName,
      category: 'Custom',
      fallback: `"${customFontName}", sans-serif`,
      googleFont: true,
      customUrl: customFontUrl
    };

    setCustomFonts(prev => [...prev, newCustomFont]);
    setFontLoadingStatus(`✅ "${customFontName}" added to font list and registered!`);

    // Clear inputs
    setCustomFontUrl('');
    setCustomFontName('');
  };

  const removeCustomFont = (fontName) => {
    setCustomFonts(prev => prev.filter(font => font.name !== fontName));
    setFontLoadingStatus(`Removed "${fontName}" from custom fonts`);
  };

  const addNewOverlay = () => {
    const newOverlay = {
      id: `overlay_${Date.now()}`,
      type: 'text',
      placeholder: '{{title}}',
      position: {
        x: 50,
        y: 50,
        width: 200,
        height: 40
      },
      styling: {
        fontSize: 24,
        fontFamily: 'Arial',
        fontWeight: 'normal',
        color: '#000000',
        textAlign: 'left',
        lineHeight: 1.2,
        verticalAlign: 'top'
      }
    };

    const updatedOverlays = [...(template.text_overlays?.overlays || []), newOverlay];
    onTemplateChange({
      ...template,
      text_overlays: {
        ...template.text_overlays,
        overlays: updatedOverlays
      }
    });

    setSelectedOverlay(updatedOverlays.length - 1);
  };

  const deleteOverlay = (overlayIndex) => {
    const updatedOverlays = template.text_overlays.overlays.filter((_, index) => index !== overlayIndex);
    onTemplateChange({
      ...template,
      text_overlays: {
        ...template.text_overlays,
        overlays: updatedOverlays
      }
    });

    if (selectedOverlay === overlayIndex) {
      setSelectedOverlay(null);
    } else if (selectedOverlay > overlayIndex) {
      setSelectedOverlay(selectedOverlay - 1);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <div className="p-4 border-b">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900">Text Overlay Editor</h3>
          <button
            onClick={addNewOverlay}
            className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
          >
            Add Overlay
          </button>
        </div>
      </div>

      <div className="p-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Canvas */}
          <div className="lg:col-span-2">
            <div className="border border-gray-300 rounded-lg overflow-hidden">
              <canvas
                ref={canvasRef}
                width={600}
                height={400}
                className={`w-full h-auto ${isDragging ? 'cursor-grabbing' : 'cursor-crosshair'}`}
                onMouseDown={handleMouseDown}
                style={{ userSelect: 'none' }}
              />
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Click and drag overlays to reposition them. Selected overlay is highlighted in blue.
            </p>
          </div>

          {/* Overlay Properties */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Overlay Properties</h4>
            
            {selectedOverlay !== null && template.text_overlays?.overlays[selectedOverlay] ? (
              <OverlayPropertiesPanel
                overlay={template.text_overlays.overlays[selectedOverlay]}
                overlayIndex={selectedOverlay}
                onUpdate={updateOverlayProperty}
                onDelete={() => deleteOverlay(selectedOverlay)}
                customFonts={customFonts}
                customFontUrl={customFontUrl}
                setCustomFontUrl={setCustomFontUrl}
                customFontName={customFontName}
                setCustomFontName={setCustomFontName}
                fontLoadingStatus={fontLoadingStatus}
                setFontLoadingStatus={setFontLoadingStatus}
                isLoadingFont={isLoadingFont}
                setIsLoadingFont={setIsLoadingFont}
                handleLoadCustomFont={handleLoadCustomFont}
                handleAddCustomFont={handleAddCustomFont}
                removeCustomFont={removeCustomFont}
              />
            ) : (
              <div className="text-center text-gray-500 py-8">
                <svg className="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.121 2.122" />
                </svg>
                <p className="text-sm">Select an overlay to edit its properties</p>
              </div>
            )}

            {/* Overlay List */}
            <div className="mt-6">
              <h5 className="font-medium text-gray-900 mb-2">All Overlays</h5>
              <div className="space-y-2">
                {template.text_overlays?.overlays?.map((overlay, index) => (
                  <div
                    key={overlay.id}
                    className={`p-2 border rounded cursor-pointer ${
                      selectedOverlay === index ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedOverlay(index)}
                  >
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">{overlay.id}</span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteOverlay(index);
                        }}
                        className="text-red-500 hover:text-red-700 text-xs"
                      >
                        Delete
                      </button>
                    </div>
                    <p className="text-xs text-gray-600">{overlay.placeholder}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Overlay Properties Panel Component
 */
const OverlayPropertiesPanel = ({
  overlay,
  overlayIndex,
  onUpdate,
  onDelete,
  customFonts,
  customFontUrl,
  setCustomFontUrl,
  customFontName,
  setCustomFontName,
  fontLoadingStatus,
  setFontLoadingStatus,
  isLoadingFont,
  setIsLoadingFont,
  handleLoadCustomFont,
  handleAddCustomFont,
  removeCustomFont
}) => {
  return (
    <div className="space-y-4">
      {/* Basic Properties */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">ID</label>
        <input
          type="text"
          value={overlay.id}
          onChange={(e) => onUpdate(overlayIndex, 'id', e.target.value)}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Placeholder</label>
        <input
          type="text"
          value={overlay.placeholder}
          onChange={(e) => onUpdate(overlayIndex, 'placeholder', e.target.value)}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
        />
      </div>

      {/* Position & Dimensions */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Position & Dimensions</label>
        <div className="space-y-3">
          {/* Position Controls */}
          <div>
            <label className="block text-xs text-gray-600 mb-1">Position (X, Y coordinates)</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-gray-500">X (pixels from left)</label>
                <input
                  type="number"
                  value={overlay.position.x}
                  onChange={(e) => onUpdate(overlayIndex, 'position.x', parseInt(e.target.value))}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                  title="Horizontal position from left edge of template"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500">Y (pixels from top)</label>
                <input
                  type="number"
                  value={overlay.position.y}
                  onChange={(e) => onUpdate(overlayIndex, 'position.y', parseInt(e.target.value))}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                  title="Vertical position from top edge of template"
                />
              </div>
            </div>
          </div>

          {/* Dimension Controls */}
          <div>
            <label className="block text-xs text-gray-600 mb-1">Text Boundary Box</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-gray-500">Width (text wrap limit)</label>
                <input
                  type="number"
                  value={overlay.position.width}
                  onChange={(e) => onUpdate(overlayIndex, 'position.width', parseInt(e.target.value))}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                  title="Maximum width for text wrapping - text will wrap to new lines within this boundary"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500">Height (vertical space)</label>
                <input
                  type="number"
                  value={overlay.position.height}
                  onChange={(e) => onUpdate(overlayIndex, 'position.height', parseInt(e.target.value))}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                  title="Maximum height for text area - controls vertical space available for multiple lines"
                />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              💡 Width controls text wrapping, Height defines the vertical boundary for multi-line text
            </p>
          </div>
        </div>
      </div>

      {/* Styling */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Styling</label>
        <div className="space-y-2">
          <div>
            <label className="block text-xs text-gray-600">Font Size</label>
            <input
              type="number"
              value={overlay.styling.fontSize}
              onChange={(e) => onUpdate(overlayIndex, 'styling.fontSize', parseInt(e.target.value))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            />
          </div>
          
          {/* Custom Google Fonts Section */}
          <div className="border border-gray-200 rounded-lg p-3 bg-gray-50">
            <div className="flex items-center justify-between mb-2">
              <label className="block text-xs font-medium text-gray-700">
                🔤 Add Custom Google Fonts
              </label>
            </div>

            {/* Google Fonts URL Input */}
            <div className="space-y-2">
              <div>
                <label className="block text-xs text-gray-600 mb-1">Google Fonts CSS URL</label>
                <div className="flex gap-2">
                  <input
                    type="url"
                    value={customFontUrl}
                    onChange={(e) => setCustomFontUrl(e.target.value)}
                    placeholder="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400;700&display=swap"
                    className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 font-mono"
                    title="Paste Google Fonts CSS link here"
                  />
                  <button
                    onClick={handleLoadCustomFont}
                    disabled={isLoadingFont || !customFontUrl.trim()}
                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoadingFont ? '⏳' : 'Load'}
                  </button>
                </div>
              </div>

              {/* Custom Font Name Input */}
              <div>
                <label className="block text-xs text-gray-600 mb-1">Font Family Name</label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={customFontName}
                    onChange={(e) => setCustomFontName(e.target.value)}
                    placeholder="e.g., Roboto Slab"
                    className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                    title="Enter the exact font family name"
                  />
                  <button
                    onClick={handleAddCustomFont}
                    disabled={!customFontName.trim()}
                    className="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Add
                  </button>
                </div>
              </div>

              {/* Status Message */}
              {fontLoadingStatus && (
                <div className={`text-xs p-2 rounded ${
                  fontLoadingStatus.includes('Error') || fontLoadingStatus.includes('Please')
                    ? 'bg-red-50 text-red-700 border border-red-200'
                    : fontLoadingStatus.includes('✅')
                    ? 'bg-green-50 text-green-700 border border-green-200'
                    : 'bg-blue-50 text-blue-700 border border-blue-200'
                }`}>
                  {fontLoadingStatus}
                </div>
              )}

              {/* Custom Fonts List */}
              {customFonts.length > 0 && (
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Custom Fonts ({customFonts.length})</label>
                  <div className="space-y-1 max-h-20 overflow-y-auto">
                    {customFonts.map((font, index) => (
                      <div key={index} className="flex items-center justify-between bg-white px-2 py-1 rounded border text-xs">
                        <span style={{ fontFamily: font.fallback }}>{font.name}</span>
                        <button
                          onClick={() => removeCustomFont(font.name)}
                          className="text-red-500 hover:text-red-700 ml-2"
                          title="Remove custom font"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div>
            <label className="block text-xs text-gray-600 mb-1">Font Family</label>
            <select
              value={overlay.styling.fontFamily}
              onChange={(e) => {
                // Check both predefined and custom fonts
                const allFonts = [...GOOGLE_FONTS, ...customFonts];
                const selectedFont = allFonts.find(font => font.name === e.target.value);

                if (selectedFont?.googleFont) {
                  if (selectedFont.customUrl) {
                    // Custom font - ensure it's registered with fontLoader
                    fontLoader.registerCustomFont(selectedFont.name, selectedFont.customUrl);
                    prodLogger.debug(`Using custom font: ${selectedFont.name}`);
                  } else {
                    // Predefined Google font
                    loadGoogleFont(selectedFont.name);
                  }
                }
                onUpdate(overlayIndex, 'styling.fontFamily', e.target.value);
              }}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
              style={{ fontFamily: overlay.styling.fontFamily }}
            >
              <optgroup label="System Fonts">
                {GOOGLE_FONTS.filter(font => font.category === 'System').map(font => (
                  <option key={font.name} value={font.name} style={{ fontFamily: font.fallback }}>
                    {font.name}
                  </option>
                ))}
              </optgroup>
              <optgroup label="Sans Serif">
                {GOOGLE_FONTS.filter(font => font.category === 'Sans Serif').map(font => (
                  <option key={font.name} value={font.name} style={{ fontFamily: font.fallback }}>
                    {font.name}
                  </option>
                ))}
              </optgroup>
              <optgroup label="Serif">
                {GOOGLE_FONTS.filter(font => font.category === 'Serif').map(font => (
                  <option key={font.name} value={font.name} style={{ fontFamily: font.fallback }}>
                    {font.name}
                  </option>
                ))}
              </optgroup>
              <optgroup label="Display & Decorative">
                {GOOGLE_FONTS.filter(font => font.category === 'Display').map(font => (
                  <option key={font.name} value={font.name} style={{ fontFamily: font.fallback }}>
                    {font.name}
                  </option>
                ))}
              </optgroup>
              <optgroup label="Handwriting">
                {GOOGLE_FONTS.filter(font => font.category === 'Handwriting').map(font => (
                  <option key={font.name} value={font.name} style={{ fontFamily: font.fallback }}>
                    {font.name}
                  </option>
                ))}
              </optgroup>
              {customFonts.length > 0 && (
                <optgroup label="Custom Fonts">
                  {customFonts.map(font => (
                    <option key={font.name} value={font.name} style={{ fontFamily: font.fallback }}>
                      {font.name} ⭐
                    </option>
                  ))}
                </optgroup>
              )}
            </select>
            <p className="text-xs text-gray-500 mt-1">Google Fonts are loaded automatically when selected</p>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-gray-600">Font Weight</label>
              <select
                value={overlay.styling.fontWeight || 'normal'}
                onChange={(e) => onUpdate(overlayIndex, 'styling.fontWeight', e.target.value)}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
              >
                <option value="100">Thin (100)</option>
                <option value="200">Extra Light (200)</option>
                <option value="300">Light (300)</option>
                <option value="normal">Normal (400)</option>
                <option value="500">Medium (500)</option>
                <option value="600">Semi Bold (600)</option>
                <option value="bold">Bold (700)</option>
                <option value="800">Extra Bold (800)</option>
                <option value="900">Black (900)</option>
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-600">Font Style</label>
              <select
                value={overlay.styling.fontStyle || 'normal'}
                onChange={(e) => onUpdate(overlayIndex, 'styling.fontStyle', e.target.value)}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
              >
                <option value="normal">Normal</option>
                <option value="italic">Italic</option>
                <option value="oblique">Oblique</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-xs text-gray-600 mb-1">Text Decoration</label>
            <div className="flex gap-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={overlay.styling.textDecoration?.includes('underline') || false}
                  onChange={(e) => {
                    const currentDecoration = overlay.styling.textDecoration || 'none';
                    let newDecoration = currentDecoration === 'none' ? '' : currentDecoration;

                    if (e.target.checked) {
                      newDecoration = newDecoration ? `${newDecoration} underline` : 'underline';
                    } else {
                      newDecoration = newDecoration.replace('underline', '').trim();
                    }

                    onUpdate(overlayIndex, 'styling.textDecoration', newDecoration || 'none');
                  }}
                  className="mr-1"
                />
                <span className="text-xs">Underline</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={overlay.styling.textDecoration?.includes('line-through') || false}
                  onChange={(e) => {
                    const currentDecoration = overlay.styling.textDecoration || 'none';
                    let newDecoration = currentDecoration === 'none' ? '' : currentDecoration;

                    if (e.target.checked) {
                      newDecoration = newDecoration ? `${newDecoration} line-through` : 'line-through';
                    } else {
                      newDecoration = newDecoration.replace('line-through', '').trim();
                    }

                    onUpdate(overlayIndex, 'styling.textDecoration', newDecoration || 'none');
                  }}
                  className="mr-1"
                />
                <span className="text-xs">Strike</span>
              </label>
            </div>
          </div>

          <div>
            <label className="block text-xs text-gray-600">Text Capitalization</label>
            <select
              value={overlay.styling.textTransform || 'none'}
              onChange={(e) => onUpdate(overlayIndex, 'styling.textTransform', e.target.value)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            >
              <option value="none">None (Original)</option>
              <option value="uppercase">UPPERCASE</option>
              <option value="lowercase">lowercase</option>
              <option value="capitalize">Capitalize Each Word</option>
              <option value="capitalize-first">Capitalize First Letter Only</option>
            </select>
            <p className="text-xs text-gray-500 mt-1">Transform text appearance without changing the original content</p>
          </div>

          <div>
            <label className="block text-xs text-gray-600 mb-1">Color</label>
            <div className="flex gap-2">
              <input
                type="color"
                value={overlay.styling.color}
                onChange={(e) => onUpdate(overlayIndex, 'styling.color', e.target.value)}
                className="w-12 h-8 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 cursor-pointer"
                title="Click to open color picker"
              />
              <input
                type="text"
                value={overlay.styling.color}
                onChange={(e) => {
                  // Validate hex color format
                  const hexColor = e.target.value;
                  if (hexColor.match(/^#[0-9A-Fa-f]{6}$/) || hexColor.match(/^#[0-9A-Fa-f]{3}$/)) {
                    onUpdate(overlayIndex, 'styling.color', hexColor);
                  } else if (hexColor === '' || hexColor.startsWith('#')) {
                    // Allow partial typing
                    onUpdate(overlayIndex, 'styling.color', hexColor);
                  }
                }}
                placeholder="#FF5733"
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 font-mono"
                title="Enter hex color code (e.g., #FF5733)"
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">Use color picker or enter hex code (e.g., #FF5733)</p>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-gray-600">Line Height</label>
              <input
                type="number"
                step="0.1"
                min="0.5"
                max="3"
                value={overlay.styling.lineHeight || 1.2}
                onChange={(e) => onUpdate(overlayIndex, 'styling.lineHeight', parseFloat(e.target.value))}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                title="Line spacing multiplier (1.0 = normal, 1.5 = 1.5x spacing)"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600">Letter Spacing</label>
              <input
                type="number"
                step="0.1"
                min="-2"
                max="5"
                value={overlay.styling.letterSpacing || 0}
                onChange={(e) => onUpdate(overlayIndex, 'styling.letterSpacing', parseFloat(e.target.value))}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                title="Letter spacing in pixels (0 = normal, positive = wider, negative = tighter)"
              />
            </div>
          </div>

          <div>
            <label className="block text-xs text-gray-600">Text Align</label>
            <select
              value={overlay.styling.textAlign}
              onChange={(e) => onUpdate(overlayIndex, 'styling.textAlign', e.target.value)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            >
              <option value="left">Left</option>
              <option value="center">Center</option>
              <option value="right">Right</option>
            </select>
          </div>
        </div>
      </div>

      {/* Delete Button */}
      <button
        onClick={onDelete}
        className="w-full bg-red-600 text-white py-2 rounded text-sm hover:bg-red-700"
      >
        Delete Overlay
      </button>
    </div>
  );
};

export default TextOverlayEditor;
