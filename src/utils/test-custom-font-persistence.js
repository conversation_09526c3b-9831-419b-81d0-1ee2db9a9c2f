/**
 * Custom Font Persistence Test Script
 * Verifies that custom fonts persist from admin editor to document-template preview
 */

import fontLoader from './fontLoader.js';

// Mock DOM for testing
if (typeof document === 'undefined') {
  global.document = {
    createElement: () => ({
      href: '',
      rel: '',
      onload: null,
      onerror: null
    }),
    head: {
      appendChild: () => {},
      querySelector: () => null
    },
    querySelector: () => null,
    fonts: {
      load: () => Promise.resolve(),
      check: () => true
    }
  };
}

/**
 * Test template with custom fonts structure
 */
const mockTemplateWithCustomFonts = {
  id: 'test-template-001',
  name: 'Test Template',
  custom_fonts: {
    fonts: [
      {
        name: 'Roboto Slab',
        category: 'Custom',
        fallback: '"Roboto Slab", serif',
        googleFont: true,
        customUrl: 'https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400;700&display=swap',
        addedAt: '2024-01-15T10:30:00Z'
      },
      {
        name: 'Playfair Display',
        category: 'Custom',
        fallback: '"Playfair Display", serif',
        googleFont: true,
        customUrl: 'https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap',
        addedAt: '2024-01-15T10:35:00Z'
      }
    ]
  },
  text_overlays: {
    overlays: [
      {
        id: 'title',
        styling: {
          fontFamily: 'Roboto Slab'
        }
      },
      {
        id: 'subtitle',
        styling: {
          fontFamily: 'Playfair Display'
        }
      }
    ]
  }
};

/**
 * Test custom font registration from template
 */
async function testCustomFontRegistrationFromTemplate() {
  console.log('🧪 Testing custom font registration from template...');
  
  // Clear any existing fonts
  fontLoader.clearCache();
  
  // Simulate the registerTemplateCustomFonts function
  const registerTemplateCustomFonts = async (template) => {
    if (!template?.custom_fonts?.fonts) {
      return;
    }

    const customFonts = template.custom_fonts.fonts;
    
    customFonts.forEach(font => {
      if (font.customUrl && font.name) {
        fontLoader.registerCustomFont(font.name, font.customUrl);
        console.log(`✅ Registered: ${font.name} -> ${font.customUrl}`);
      }
    });
  };
  
  // Register fonts from template
  await registerTemplateCustomFonts(mockTemplateWithCustomFonts);
  
  // Verify fonts are registered
  const robotoRegistered = fontLoader.hasCustomFont('Roboto Slab');
  const playfairRegistered = fontLoader.hasCustomFont('Playfair Display');
  
  console.log(`✅ Roboto Slab registered: ${robotoRegistered}`);
  console.log(`✅ Playfair Display registered: ${playfairRegistered}`);
  
  return robotoRegistered && playfairRegistered;
}

/**
 * Test font loading with custom URLs
 */
async function testCustomFontLoading() {
  console.log('🧪 Testing custom font loading...');
  
  try {
    // Test ensureFontLoaded with registered custom fonts
    const robotoResult = await fontLoader.ensureFontLoaded('Roboto Slab');
    const playfairResult = await fontLoader.ensureFontLoaded('Playfair Display');
    
    console.log(`✅ Roboto Slab loaded: ${robotoResult === 'Roboto Slab'}`);
    console.log(`✅ Playfair Display loaded: ${playfairResult === 'Playfair Display'}`);
    
    return robotoResult === 'Roboto Slab' && playfairResult === 'Playfair Display';
  } catch (error) {
    console.error(`❌ Font loading failed: ${error.message}`);
    return false;
  }
}

/**
 * Test template data structure
 */
function testTemplateDataStructure() {
  console.log('🧪 Testing template data structure...');
  
  const hasCustomFonts = !!mockTemplateWithCustomFonts.custom_fonts;
  const hasFontsArray = !!mockTemplateWithCustomFonts.custom_fonts?.fonts;
  const fontsCount = mockTemplateWithCustomFonts.custom_fonts?.fonts?.length || 0;
  
  console.log(`✅ Template has custom_fonts: ${hasCustomFonts}`);
  console.log(`✅ Template has fonts array: ${hasFontsArray}`);
  console.log(`✅ Fonts count: ${fontsCount}`);
  
  return hasCustomFonts && hasFontsArray && fontsCount > 0;
}

/**
 * Run all tests
 */
async function runCustomFontPersistenceTests() {
  console.log('🚀 Starting custom font persistence tests...\n');
  
  const tests = [
    { name: 'Template Data Structure', test: testTemplateDataStructure },
    { name: 'Custom Font Registration', test: testCustomFontRegistrationFromTemplate },
    { name: 'Custom Font Loading', test: testCustomFontLoading }
  ];
  
  const results = [];
  
  for (const { name, test } of tests) {
    console.log(`\n--- ${name} ---`);
    try {
      const result = await test();
      results.push(result);
      console.log(`${result ? '✅ PASSED' : '❌ FAILED'}: ${name}\n`);
    } catch (error) {
      console.error(`❌ ERROR in ${name}: ${error.message}\n`);
      results.push(false);
    }
  }
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`📊 Test Results: ${passed}/${total} passed`);
  
  if (passed === total) {
    console.log('🎉 All custom font persistence tests passed!');
    console.log('✅ Custom fonts should now persist from admin editor to document-template preview.');
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
  }
  
  return passed === total;
}

// Export for use in other modules
export { 
  runCustomFontPersistenceTests, 
  testCustomFontRegistrationFromTemplate, 
  testCustomFontLoading,
  testTemplateDataStructure 
};

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runCustomFontPersistenceTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}
