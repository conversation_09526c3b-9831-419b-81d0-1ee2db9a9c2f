/**
 * Conversion Funnel Tracking Utilities
 * For monitoring landing page to subscription conversions
 */

// Helper function to get plan price for analytics
const getPlanPrice = (plan, billing) => {
  const prices = {
    basic: { monthly: 9, yearly: 84 },
    standard: { monthly: 19, yearly: 180 },
    pro: { monthly: 39, yearly: 348 },
  };
  return prices[plan]?.[billing] || 0;
};

// Conversion tracking functions
export const trackConversionFunnel = {
  landingPageView: () => {
    if (typeof gtag !== "undefined") {
      gtag("event", "landing_page_view", {
        page_location: window.location.href,
      });
    }
  },

  planSelected: (plan, billing) => {
    if (typeof gtag !== "undefined") {
      gtag("event", "plan_selected", {
        plan_type: plan,
        billing_cycle: billing,
        value: getPlanPrice(plan, billing),
        source: "landing_page",
      });
    }
  },

  registrationStarted: (plan, billing) => {
    if (typeof gtag !== "undefined") {
      gtag("event", "begin_checkout", {
        currency: "USD",
        value: getPlanPrice(plan, billing),
        items: [
          {
            item_id: `${plan}_${billing}`,
            item_name: `${plan} Plan (${billing})`,
            category: "subscription",
            quantity: 1,
            price: getPlanPrice(plan, billing),
          },
        ],
      });
    }
  },

  registrationCompleted: (plan, billing, userId) => {
    if (typeof gtag !== "undefined") {
      gtag("event", "sign_up", {
        method: "email",
        user_id: userId,
        custom_parameters: {
          plan_type: plan,
          billing_cycle: billing,
        },
      });
    }
  },

  checkoutInitiated: (plan, billing, userId) => {
    if (typeof gtag !== "undefined") {
      gtag("event", "checkout_initiated", {
        plan_type: plan,
        billing_cycle: billing,
        user_id: userId,
        value: getPlanPrice(plan, billing),
        currency: "USD",
      });
    }
  },

  autoCheckoutInitiated: (plan, billing) => {
    if (typeof gtag !== "undefined") {
      gtag("event", "auto_checkout_initiated", {
        plan_type: plan,
        billing_cycle: billing,
        value: getPlanPrice(plan, billing),
      });
    }
  },

  checkoutCompleted: (plan, billing, userId, subscriptionId) => {
    if (typeof gtag !== "undefined") {
      gtag("event", "purchase", {
        transaction_id: subscriptionId,
        value: getPlanPrice(plan, billing),
        currency: "USD",
        user_id: userId,
        items: [
          {
            item_id: `${plan}_${billing}`,
            item_name: `${plan} Plan (${billing})`,
            category: "subscription",
            quantity: 1,
            price: getPlanPrice(plan, billing),
          },
        ],
      });
    }
  },

  signUpFromLanding: (plan, billing) => {
    if (typeof gtag !== "undefined") {
      gtag("event", "sign_up_from_landing", {
        plan_type: plan,
        billing_cycle: billing,
        value: getPlanPrice(plan, billing),
      });
    }
  },
};

// Utility functions for plan selection storage
export const planSelectionUtils = {
  store: (plan, billing) => {
    if (typeof window === "undefined") return;

    const data = {
      plan: plan,
      billing: billing,
      timestamp: Date.now(),
    };

    // Store in both localStorage and sessionStorage for redundancy
    localStorage.setItem("selected_plan", JSON.stringify(data));
    sessionStorage.setItem("preSelectedPlan", plan);
    sessionStorage.setItem("preSelectedBilling", billing);
    sessionStorage.setItem("planSelectionSource", "landing_page");
  },

  retrieve: () => {
    if (typeof window === "undefined") return null;

    const preSelectedPlan = sessionStorage.getItem("preSelectedPlan");
    const preSelectedBilling = sessionStorage.getItem("preSelectedBilling");

    if (preSelectedPlan && preSelectedBilling) {
      return {
        plan: preSelectedPlan,
        billing: preSelectedBilling,
        source: sessionStorage.getItem("planSelectionSource"),
      };
    }

    // Fallback to localStorage
    try {
      const stored = localStorage.getItem("selected_plan");
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  },

  clear: () => {
    if (typeof window === "undefined") return;

    sessionStorage.removeItem("preSelectedPlan");
    sessionStorage.removeItem("preSelectedBilling");
    sessionStorage.removeItem("planSelectionSource");
    localStorage.removeItem("selected_plan");
  },

  isFromLanding: () => {
    if (typeof window === "undefined") return false;
    return sessionStorage.getItem("planSelectionSource") === "landing_page";
  },
};

export { getPlanPrice };
