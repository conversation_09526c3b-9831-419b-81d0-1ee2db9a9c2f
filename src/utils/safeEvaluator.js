import { prodLogger } from "./prodLogger.js";

/**
 * Safe Expression Evaluator
 * Provides a secure alternative to Function() constructor for evaluating simple conditions
 */

// Allowed operators for security
const ALLOWED_OPERATORS = {
  "===": (a, b) => a === b,
  "==": (a, b) => a == b,
  "!==": (a, b) => a !== b,
  "!=": (a, b) => a != b,
  ">": (a, b) => a > b,
  "<": (a, b) => a < b,
  ">=": (a, b) => a >= b,
  "<=": (a, b) => a <= b,
  "&&": (a, b) => a && b,
  "||": (a, b) => a || b,
  "+": (a, b) => a + b,
  "-": (a, b) => a - b,
  "*": (a, b) => a * b,
  "/": (a, b) => a / b,
  "%": (a, b) => a % b,
};

// Allowed functions for security
const ALLOWED_FUNCTIONS = {
  includes: (arr, item) => (Array.isArray(arr) ? arr.includes(item) : false),
  length: (val) => val?.length || 0,
  toLowerCase: (str) => (typeof str === "string" ? str.toLowerCase() : ""),
  toUpperCase: (str) => (typeof str === "string" ? str.toUpperCase() : ""),
  toString: (val) => String(val),
  parseInt: (val) => parseInt(val, 10),
  parseFloat: (val) => parseFloat(val),
};

/**
 * Safely evaluate a simple expression with restricted operators
 * @param {string} expression - The expression to evaluate
 * @param {Object} data - Data context for variable resolution
 * @returns {boolean|number|string} Result of evaluation
 */
export const safeEvaluate = (expression, data = {}) => {
  try {
    // Basic validation
    if (typeof expression !== "string") {
      return false;
    }

    // Remove extra whitespace
    expression = expression.trim();

    // Handle simple comparisons (field operator value)
    const comparisonMatch = expression.match(
      /^(\w+(?:\.\w+)*)\s*([><=!]+)\s*(.+)$/
    );
    if (comparisonMatch) {
      const [, field, operator, valueStr] = comparisonMatch;

      if (!ALLOWED_OPERATORS[operator]) {
        prodLogger.warn("Unsafe operator detected:", operator);
        return false;
      }

      const fieldValue = getNestedValue(data, field);
      const compareValue = parseValue(valueStr);

      return ALLOWED_OPERATORS[operator](fieldValue, compareValue);
    }

    // Handle simple logical expressions (value1 && value2, value1 || value2)
    const logicalMatch = expression.match(/^(.+)\s*(&&|\|\|)\s*(.+)$/);
    if (logicalMatch) {
      const [, left, operator, right] = logicalMatch;

      if (!ALLOWED_OPERATORS[operator]) {
        prodLogger.warn("Unsafe operator detected:", operator);
        return false;
      }

      const leftValue = safeEvaluate(left.trim(), data);
      const rightValue = safeEvaluate(right.trim(), data);

      return ALLOWED_OPERATORS[operator](leftValue, rightValue);
    }

    // Handle simple field access
    if (/^\w+(?:\.\w+)*$/.test(expression)) {
      return getNestedValue(data, expression);
    }

    // Handle simple literals
    return parseValue(expression);
  } catch (error) {
    prodLogger.error("Error in safe evaluation:", error);
    return false;
  }
};

/**
 * Get nested value from object using dot notation
 * @param {Object} obj - Source object
 * @param {string} path - Dot notation path
 * @returns {any} Value at path
 */
const getNestedValue = (obj, path) => {
  if (!obj || typeof path !== "string") return undefined;

  return path.split(".").reduce((current, key) => {
    return current && typeof current === "object" ? current[key] : undefined;
  }, obj);
};

/**
 * Parse a string value to appropriate type
 * @param {string} valueStr - String representation of value
 * @returns {any} Parsed value
 */
const parseValue = (valueStr) => {
  if (typeof valueStr !== "string") return valueStr;

  const trimmed = valueStr.trim();

  // Boolean values
  if (trimmed === "true") return true;
  if (trimmed === "false") return false;
  if (trimmed === "null") return null;
  if (trimmed === "undefined") return undefined;

  // Numbers
  if (/^-?\d+$/.test(trimmed)) return parseInt(trimmed, 10);
  if (/^-?\d*\.\d+$/.test(trimmed)) return parseFloat(trimmed);

  // Strings (remove quotes if present)
  if (
    (trimmed.startsWith('"') && trimmed.endsWith('"')) ||
    (trimmed.startsWith("'") && trimmed.endsWith("'"))
  ) {
    return trimmed.slice(1, -1);
  }

  return trimmed;
};

export default safeEvaluate;
