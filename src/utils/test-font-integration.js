/**
 * Font Integration Test Script
 * Verifies that custom fonts are properly integrated between TextOverlayEditor and fontLoader
 */

import fontLoader from './fontLoader.js';

// Mock DOM for testing
if (typeof document === 'undefined') {
  global.document = {
    createElement: () => ({
      href: '',
      rel: '',
      onload: null,
      onerror: null
    }),
    head: {
      appendChild: () => {},
      querySelector: () => null
    },
    querySelector: () => null,
    fonts: {
      load: () => Promise.resolve(),
      check: () => true
    }
  };
}

/**
 * Test custom font registration and retrieval
 */
async function testCustomFontRegistration() {
  console.log('🧪 Testing custom font registration...');
  
  const fontFamily = 'Roboto Slab';
  const customUrl = 'https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400;700&display=swap';
  
  // Register custom font
  fontLoader.registerCustomFont(fontFamily, customUrl);
  
  // Verify registration
  const hasFont = fontLoader.hasCustomFont(fontFamily);
  const retrievedUrl = fontLoader.getCustomFontUrl(fontFamily);
  
  console.log(`✅ Font registered: ${hasFont}`);
  console.log(`✅ URL retrieved: ${retrievedUrl === customUrl}`);
  
  return hasFont && retrievedUrl === customUrl;
}

/**
 * Test font loading with custom URL
 */
async function testCustomFontLoading() {
  console.log('🧪 Testing custom font loading...');
  
  const fontFamily = 'Playfair Display';
  const customUrl = 'https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap';
  
  // Register custom font
  fontLoader.registerCustomFont(fontFamily, customUrl);
  
  try {
    // Test ensureFontLoaded with custom font
    const result = await fontLoader.ensureFontLoaded(fontFamily);
    console.log(`✅ Font loaded successfully: ${result === fontFamily}`);
    return result === fontFamily;
  } catch (error) {
    console.error(`❌ Font loading failed: ${error.message}`);
    return false;
  }
}

/**
 * Test cache clearing
 */
function testCacheClear() {
  console.log('🧪 Testing cache clearing...');
  
  const fontFamily = 'Test Font';
  const customUrl = 'https://fonts.googleapis.com/css2?family=Test+Font:wght@400&display=swap';
  
  // Register font
  fontLoader.registerCustomFont(fontFamily, customUrl);
  const beforeClear = fontLoader.hasCustomFont(fontFamily);
  
  // Clear cache
  fontLoader.clearCache();
  const afterClear = fontLoader.hasCustomFont(fontFamily);
  
  console.log(`✅ Font before clear: ${beforeClear}`);
  console.log(`✅ Font after clear: ${!afterClear}`);
  
  return beforeClear && !afterClear;
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('🚀 Starting font integration tests...\n');
  
  const tests = [
    testCustomFontRegistration,
    testCustomFontLoading,
    testCacheClear
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test();
      results.push(result);
      console.log('');
    } catch (error) {
      console.error(`❌ Test failed: ${error.message}\n`);
      results.push(false);
    }
  }
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`📊 Test Results: ${passed}/${total} passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Font integration is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
  }
  
  return passed === total;
}

// Export for use in other modules
export { runTests, testCustomFontRegistration, testCustomFontLoading, testCacheClear };

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}
