/**
 * React hook for using the error monitor in components
 */
import { useEffect, useMemo } from 'react';
import errorMonitor from './errorMonitor';

/**
 * Hook to create a component-specific error logger
 * 
 * @param {string} componentName - Name of the component using the hook
 * @param {Object} defaultContext - Default context to include with all logs
 * @returns {Object} - Logger methods and error monitor utilities
 */
export function useErrorMonitor(componentName, defaultContext = {}) {
    // Create a context logger for this component
    const logger = useMemo(() => {
        return errorMonitor.createContextLogger(componentName, defaultContext);
    }, [componentName]);

    // Log component mount/unmount for debugging
    useEffect(() => {
        logger.debug('Component mounted');

        return () => {
            logger.debug('Component unmounted');
        };
    }, []);

    // Return the logger and additional utilities
    return {
        // Logger methods
        debug: logger.debug,
        info: logger.info,
        warn: logger.warn,
        error: logger.error,
        critical: logger.critical,

        // Utility to wrap async functions with error handling
        withErrorHandling: (asyncFn, errorContext = {}) => {
            return async (...args) => {
                try {
                    return await asyncFn(...args);
                } catch (error) {
                    logger.error(error, {
                        ...errorContext,
                        args: args.map(arg =>
                            typeof arg === 'object' ?
                                (arg ? Object.keys(arg) : null) :
                                String(arg)
                        )
                    });
                    throw error;
                }
            };
        },

        // Create an error boundary fallback renderer
        createErrorFallback: (title = 'Something went wrong') => {
            return (error) => {
                logger.error(error, { source: 'ErrorBoundary' });

                // Instead of returning JSX directly, return a render function
                // that the component can use to render the fallback UI
                return {
                    render: (React) => {
                        // The component will pass React when using this function
                        const { createElement: h, Fragment } = React;

                        return h('div', { className: 'error-boundary-fallback' },
                            h('h3', null, title),
                            h('p', null, 'An error occurred. Please try again or contact support if the problem persists.'),
                            process.env.NODE_ENV !== 'production' ?
                                h('details', null,
                                    h('summary', null, 'Error details'),
                                    h('pre', null, error.message),
                                    h('pre', null, error.stack)
                                ) : null
                        );
                    },
                    error,
                    title
                };
            };
        }
    };
}

export default useErrorMonitor;