/**
 * Progress tracking utilities for document workflow phases
 * Handles progress calculation and database updates for document creation workflow
 */

import { projectsService } from '../services/projectsService';

import { prodLogger } from './prodLogger.js';
/**
 * Workflow phases and their corresponding progress percentages
 */
export const WORKFLOW_PROGRESS = {
  'Generate': 25,
  'Edit Content': 50,
  'Review': 75,
  'Publish': 100
};

/**
 * Calculate progress percentage based on current workflow phase
 * @param {string} currentPhase - Current workflow phase
 * @returns {number} Progress percentage (0-100)
 */
export const calculateProgressFromPhase = (currentPhase) => {
  return WORKFLOW_PROGRESS[currentPhase] || 0;
};

/**
 * Update document progress in database based on current phase
 * @param {string} documentId - Document ID
 * @param {string} currentPhase - Current workflow phase
 * @returns {Promise<Object>} Service response
 */
export const updateDocumentProgress = async (documentId, currentPhase) => {
  try {
    if (!documentId || !currentPhase) {
      prodLogger.warn('Missing documentId or currentPhase for progress update', {
        documentId,
        currentPhase
      });
      return { success: false, error: 'Missing required parameters' };
    }

    const progress = calculateProgressFromPhase(currentPhase);
    
    prodLogger.debug(`Updating document progress: ${documentId} → ${currentPhase} (${progress}%)`);

    const response = await projectsService.updateProject(documentId, {
      progress: progress,
      status: getStatusFromPhase(currentPhase)
    });

    if (response.success) {
      prodLogger.debug(`Document progress updated successfully: ${progress}%`);
    } else {
      prodLogger.error('Failed to update document progress:', response.error);
    }

    return response;
  } catch (error) {
    prodLogger.error('Error updating document progress:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get document status based on current workflow phase
 * @param {string} currentPhase - Current workflow phase
 * @returns {string} Document status
 */
export const getStatusFromPhase = (currentPhase) => {
  switch (currentPhase) {
    case 'Generate':
      return 'draft';
    case 'Edit Content':
      return 'in_progress';
    case 'Review':
      return 'review';
    case 'Publish':
      return 'completed';
    default:
      return 'draft';
  }
};

/**
 * Get the next phase in the workflow
 * @param {string} currentPhase - Current workflow phase
 * @returns {string|null} Next phase or null if at the end
 */
export const getNextPhase = (currentPhase) => {
  const phases = ['Generate', 'Edit Content', 'Review', 'Publish'];
  const currentIndex = phases.indexOf(currentPhase);
  
  if (currentIndex >= 0 && currentIndex < phases.length - 1) {
    return phases[currentIndex + 1];
  }
  
  return null;
};

/**
 * Get the previous phase in the workflow
 * @param {string} currentPhase - Current workflow phase
 * @returns {string|null} Previous phase or null if at the beginning
 */
export const getPreviousPhase = (currentPhase) => {
  const phases = ['Generate', 'Edit Content', 'Review', 'Publish'];
  const currentIndex = phases.indexOf(currentPhase);
  
  if (currentIndex > 0) {
    return phases[currentIndex - 1];
  }
  
  return null;
};

/**
 * Check if a phase transition is valid
 * @param {string} fromPhase - Current phase
 * @param {string} toPhase - Target phase
 * @returns {boolean} Whether the transition is valid
 */
export const isValidPhaseTransition = (fromPhase, toPhase) => {
  const phases = ['Generate', 'Edit Content', 'Review', 'Publish'];
  const fromIndex = phases.indexOf(fromPhase);
  const toIndex = phases.indexOf(toPhase);

  // Allow moving to any previous phase or the next phase
  return fromIndex >= 0 && toIndex >= 0 && toIndex <= fromIndex + 1;
};

/**
 * Get the current workflow phase based on document status
 * @param {string} status - Document status ('draft', 'in_progress', 'review', 'completed')
 * @returns {string} Current workflow phase
 */
export const getPhaseFromStatus = (status) => {
  switch (status) {
    case 'draft':
      return 'Generate';
    case 'in_progress':
      return 'Edit Content';
    case 'review':
      return 'Review';
    case 'completed':
      return 'Publish';
    default:
      return 'Generate';
  }
};

/**
 * Get the appropriate route for a document based on its current status/progress
 * @param {Object} document - Document object with id, status, and progress
 * @returns {string} Route path for the document's current phase
 */
export const getDocumentRoute = (document) => {
  if (!document || !document.id) {
    prodLogger.warn('Invalid document object provided to getDocumentRoute');
    return '/dashboard';
  }

  const { id, status, progress } = document;

  // Handle edge case where progress might be more accurate than status
  // This ensures we route correctly even if status/progress are slightly out of sync
  if (typeof progress === 'number') {
    if (progress <= 25) {
      return '/document-creator';
    } else if (progress <= 50) {
      return `/document-editor/${id}`;
    } else if (progress <= 75) {
      return `/document-editor/${id}/review`;
    } else {
      return `/document-editor/${id}/publish`;
    }
  }

  // Fallback to status-based routing
  switch (status) {
    case 'draft':
      return '/document-creator';
    case 'in_progress':
      return `/document-editor/${id}`;
    case 'review':
      return `/document-editor/${id}/review`;
    case 'completed':
      return `/document-editor/${id}/publish`;
    case 'archived':
      // For archived documents, route to a read-only view or dashboard
      prodLogger.debug('Archived document clicked, routing to dashboard');
      return '/dashboard';
    default:
      prodLogger.warn(`Unknown document status: ${status}, defaulting to creator`);
      return '/document-creator';
  }
};

/**
 * Update progress when entering a new phase
 * This is the main function to call when transitioning between phases
 * @param {string} documentId - Document ID
 * @param {string} newPhase - Phase being entered
 * @param {string} previousPhase - Previous phase (optional, for validation)
 * @returns {Promise<Object>} Service response
 */
// Backward compatibility alias
export const updateProjectProgress = updateDocumentProgress;

export const handlePhaseTransition = async (documentId, newPhase, previousPhase = null) => {
  try {
    // Validate transition if previous phase is provided
    if (previousPhase && !isValidPhaseTransition(previousPhase, newPhase)) {
      prodLogger.warn(`Invalid phase transition: ${previousPhase} → ${newPhase}`);
      // Still allow the transition but log the warning
    }

    // Update progress in database
    const result = await updateDocumentProgress(documentId, newPhase);
    
    if (result.success) {
      prodLogger.debug(`Phase transition completed: ${documentId} → ${newPhase}`);
    }
    
    return result;
  } catch (error) {
    prodLogger.error('Error handling phase transition:', error);
    return { success: false, error: error.message };
  }
};
