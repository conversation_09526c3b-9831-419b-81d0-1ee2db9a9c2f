/**
 * Phase Mapping Utilities
 * 
 * Utilities for mapping between document creator steps and workflow phases,
 * and standardizing phase data structures across both editor and creator workflows.
 */

/**
 * Standard workflow phases used across both document editor and creator
 */
export const STANDARD_WORKFLOW_PHASES = [
  {
    id: 'Generate',
    title: 'Generate',
    icon: 'Sparkles',
    description: 'AI content generation'
  },
  {
    id: 'Edit Content',
    title: 'Edit Content',
    icon: 'Edit3',
    description: 'Review and modify content'
  },
  {
    id: 'Review',
    title: 'Review',
    icon: 'Eye',
    description: 'Review document content'
  },
  {
    id: 'Publish',
    title: 'Publish',
    icon: 'Send',
    description: 'Export and share'
  }
];

/**
 * Extended workflow phases including template selection
 * Used when template functionality is enabled
 */
export const EXTENDED_WORKFLOW_PHASES = [
  {
    id: 'Generate',
    title: 'Generate',
    icon: 'Sparkles',
    description: 'AI content generation'
  },
  {
    id: 'Edit Content',
    title: 'Edit Content',
    icon: 'Edit3',
    description: 'Review and modify content'
  },
  {
    id: 'Review',
    title: 'Review',
    icon: 'Eye',
    description: 'Review document content'
  },
  {
    id: 'template',
    title: 'Choose Template',
    icon: 'Layout',
    description: 'Select document template'
  },
  {
    id: 'Publish',
    title: 'Publish',
    icon: 'Send',
    description: 'Export and share'
  }
];

/**
 * Maps document creator step numbers (1-8) to workflow phases
 * 
 * Step mapping:
 * - Steps 1-2: Generate phase (Document setup, Topic selection)
 * - Steps 3-6: Edit Content phase (Sub-niches, Audience, Title, Tone & Voice)
 * - Step 7: Review phase (Document outline)
 * - Step 8: Publish phase (Content details & generation)
 * 
 * @param {number} currentStep - Current step number (1-8)
 * @returns {string} Phase ID ('Generate', 'Edit Content', 'Review', 'Publish')
 */
export const mapCreatorStepToPhase = (currentStep) => {
  if (currentStep <= 2) return 'Generate';
  if (currentStep <= 6) return 'Edit Content';
  if (currentStep === 7) return 'Review';
  return 'Publish';
};

/**
 * Gets the phase index for progress calculation
 * @param {string} phaseId - Phase ID
 * @param {Array} phases - Optional phases array, defaults to EXTENDED_WORKFLOW_PHASES
 * @returns {number} Phase index (0-4 for extended workflow)
 */
export const getPhaseIndex = (phaseId, phases = EXTENDED_WORKFLOW_PHASES) => {
  return phases.findIndex(phase => phase.id === phaseId);
};

/**
 * Maps creator step to phase index for progress calculation
 * @param {number} currentStep - Current step number (1-8)
 * @returns {number} Phase index (0-3)
 */
export const mapCreatorStepToPhaseIndex = (currentStep) => {
  const phaseId = mapCreatorStepToPhase(currentStep);
  return getPhaseIndex(phaseId);
};

/**
 * Converts document creator WORKFLOW_PHASES format to standard format
 * @param {Array} creatorPhases - Creator phases with numeric IDs
 * @returns {Array} Standardized phases with string IDs
 */
export const standardizePhases = (creatorPhases) => {
  if (!creatorPhases) return STANDARD_WORKFLOW_PHASES;
  
  return creatorPhases.map(phase => ({
    ...phase,
    id: phase.title || phase.id // Use title as ID for consistency
  }));
};

/**
 * Determines phase state based on current phase and target phase
 * @param {string} targetPhaseId - The phase to check state for
 * @param {string} currentPhaseId - The current active phase
 * @param {Array} phases - Optional phases array, defaults to EXTENDED_WORKFLOW_PHASES
 * @returns {string} 'completed', 'active', or 'upcoming'
 */
export const getPhaseState = (targetPhaseId, currentPhaseId, phases = EXTENDED_WORKFLOW_PHASES) => {
  const targetIndex = getPhaseIndex(targetPhaseId, phases);
  const currentIndex = getPhaseIndex(currentPhaseId, phases);

  if (targetIndex < currentIndex) {
    return 'completed';
  } else if (targetIndex === currentIndex) {
    return 'active';
  } else {
    return 'upcoming';
  }
};

/**
 * Gets the current phase for creator mode based on step number
 * @param {number} currentStep - Current step number (1-8)
 * @returns {Object} Phase object with id, title, icon, description
 */
export const getCurrentPhaseForCreator = (currentStep) => {
  const phaseId = mapCreatorStepToPhase(currentStep);
  return STANDARD_WORKFLOW_PHASES.find(phase => phase.id === phaseId);
};
