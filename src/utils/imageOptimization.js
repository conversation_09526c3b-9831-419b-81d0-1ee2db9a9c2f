/**
 * Image Optimization Utilities
 * 
 * Provides image compression, format conversion, and lazy loading capabilities
 * for production-ready image handling.
 */

import { createLogger } from './logger.js';

import { prodLogger } from './prodLogger.js';
// Create logger for this module
const logger = createLogger('ImageOptimization');

// Image optimization configuration
export const IMAGE_OPTIMIZATION_CONFIG = {
    // Compression settings
    JPEG_QUALITY: 0.85, // 85% quality for JPEG compression
    WEBP_QUALITY: 0.80, // 80% quality for WebP compression
    PNG_COMPRESSION_LEVEL: 6, // PNG compression level (0-9)
    
    // Size limits
    MAX_WIDTH: 1920, // Maximum width for images
    MAX_HEIGHT: 1080, // Maximum height for images
    THUMBNAIL_SIZE: 300, // Thumbnail size
    
    // Format preferences
    PREFERRED_FORMAT: 'webp',
    FALLBACK_FORMAT: 'jpeg',
    
    // Lazy loading
    INTERSECTION_THRESHOLD: 0.1, // 10% of image visible before loading
    ROOT_MARGIN: '50px', // Load images 50px before they come into view
    
    // Caching
    CACHE_DURATION: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
    MAX_CACHE_SIZE: 50 * 1024 * 1024 // 50MB cache limit
};

/**
 * Check if WebP format is supported by the browser
 * @returns {Promise<boolean>} Whether WebP is supported
 */
export const isWebPSupported = () => {
    return new Promise((resolve) => {
        const webP = new Image();
        webP.onload = webP.onerror = () => {
            resolve(webP.height === 2);
        };
        webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    });
};

/**
 * Compress image using canvas
 * @param {File|Blob} imageFile - Image file to compress
 * @param {Object} options - Compression options
 * @returns {Promise<Blob>} Compressed image blob
 */
export const compressImage = async (imageFile, options = {}) => {
    const {
        quality = IMAGE_OPTIMIZATION_CONFIG.JPEG_QUALITY,
        maxWidth = IMAGE_OPTIMIZATION_CONFIG.MAX_WIDTH,
        maxHeight = IMAGE_OPTIMIZATION_CONFIG.MAX_HEIGHT,
        format = IMAGE_OPTIMIZATION_CONFIG.PREFERRED_FORMAT
    } = options;

    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = () => {
            try {
                // Calculate new dimensions
                const { width, height } = calculateOptimalDimensions(
                    img.width,
                    img.height,
                    maxWidth,
                    maxHeight
                );

                // Set canvas dimensions
                canvas.width = width;
                canvas.height = height;

                // Draw and compress image
                ctx.drawImage(img, 0, 0, width, height);

                // Convert to blob with compression
                const mimeType = format === 'webp' ? 'image/webp' : 
                                format === 'png' ? 'image/png' : 'image/jpeg';

                canvas.toBlob(
                    (blob) => {
                        if (blob) {
                            resolve(blob);
                        } else {
                            reject(new Error('Failed to compress image'));
                        }
                    },
                    mimeType,
                    quality
                );
            } catch (error) {
                reject(error);
            }
        };

        img.onerror = () => {
            reject(new Error('Failed to load image for compression'));
        };

        // Load image
        if (imageFile instanceof File || imageFile instanceof Blob) {
            img.src = URL.createObjectURL(imageFile);
        } else {
            img.src = imageFile;
        }
    });
};

/**
 * Calculate optimal image dimensions while maintaining aspect ratio
 * @param {number} originalWidth - Original image width
 * @param {number} originalHeight - Original image height
 * @param {number} maxWidth - Maximum allowed width
 * @param {number} maxHeight - Maximum allowed height
 * @returns {Object} Optimal dimensions {width, height}
 */
export const calculateOptimalDimensions = (originalWidth, originalHeight, maxWidth, maxHeight) => {
    let { width, height } = { width: originalWidth, height: originalHeight };

    // Calculate aspect ratio
    const aspectRatio = originalWidth / originalHeight;

    // Scale down if too large
    if (width > maxWidth) {
        width = maxWidth;
        height = width / aspectRatio;
    }

    if (height > maxHeight) {
        height = maxHeight;
        width = height * aspectRatio;
    }

    return {
        width: Math.round(width),
        height: Math.round(height)
    };
};

/**
 * Generate thumbnail from image
 * @param {File|Blob|string} imageSource - Image source
 * @param {number} size - Thumbnail size (square)
 * @returns {Promise<Blob>} Thumbnail blob
 */
export const generateThumbnail = async (imageSource, size = IMAGE_OPTIMIZATION_CONFIG.THUMBNAIL_SIZE) => {
    return compressImage(imageSource, {
        maxWidth: size,
        maxHeight: size,
        quality: 0.7,
        format: 'jpeg'
    });
};

/**
 * Convert image to optimal format based on browser support
 * @param {File|Blob} imageFile - Image file to convert
 * @returns {Promise<Blob>} Converted image blob
 */
export const convertToOptimalFormat = async (imageFile) => {
    const webpSupported = await isWebPSupported();
    const targetFormat = webpSupported ? 'webp' : IMAGE_OPTIMIZATION_CONFIG.FALLBACK_FORMAT;

    return compressImage(imageFile, {
        format: targetFormat,
        quality: webpSupported ?
            IMAGE_OPTIMIZATION_CONFIG.WEBP_QUALITY :
            IMAGE_OPTIMIZATION_CONFIG.JPEG_QUALITY
    });
};

/**
 * Compress base64 image data for AI-generated images
 * @param {string} base64Data - Base64 image data (without data URL prefix)
 * @param {Object} options - Compression options
 * @param {string} options.format - Target format ('webp', 'jpeg', 'png')
 * @param {number} options.quality - Compression quality (0-1)
 * @param {number} options.maxWidth - Maximum width in pixels
 * @param {number} options.maxHeight - Maximum height in pixels
 * @returns {Promise<{base64Data: string, mimeType: string, originalSize: number, compressedSize: number}>}
 */
export const compressBase64Image = async (base64Data, options = {}) => {
    const {
        format = 'webp',
        quality = 0.75,
        maxWidth = 1024,
        maxHeight = 1024
    } = options;

    try {
        // Check WebP support and fallback if needed
        const webpSupported = await isWebPSupported();
        const targetFormat = (format === 'webp' && !webpSupported) ? 'jpeg' : format;
        const targetQuality = targetFormat === 'webp' ? quality : Math.min(quality + 0.1, 0.9); // Slightly higher quality for JPEG

        // Calculate original size
        const originalSize = Math.ceil(base64Data.length * 0.75); // Approximate binary size

        // Create image element to load the base64 data
        const img = new Image();
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        return new Promise((resolve, reject) => {
            img.onload = () => {
                try {
                    // Calculate new dimensions while maintaining aspect ratio
                    let { width, height } = img;
                    const aspectRatio = width / height;

                    if (width > maxWidth) {
                        width = maxWidth;
                        height = width / aspectRatio;
                    }
                    if (height > maxHeight) {
                        height = maxHeight;
                        width = height * aspectRatio;
                    }

                    // Set canvas dimensions
                    canvas.width = Math.round(width);
                    canvas.height = Math.round(height);

                    // Draw and compress image
                    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                    // Convert to target format
                    const mimeType = `image/${targetFormat}`;
                    const compressedDataUrl = canvas.toDataURL(mimeType, targetQuality);

                    // Extract base64 data (remove data URL prefix)
                    const compressedBase64 = compressedDataUrl.split(',')[1];
                    const compressedSize = Math.ceil(compressedBase64.length * 0.75);

                    // Log compression results
                    const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);
                    prodLogger.debug(`🗜️ Image compressed: ${originalSize} bytes → ${compressedSize} bytes (${compressionRatio}% reduction)`);

                    resolve({
                        base64Data: compressedBase64,
                        mimeType,
                        originalSize,
                        compressedSize,
                        compressionRatio: parseFloat(compressionRatio),
                        dimensions: { width: canvas.width, height: canvas.height }
                    });
                } catch (error) {
                    reject(new Error(`Image compression failed: ${error.message}`));
                }
            };

            img.onerror = () => {
                reject(new Error('Failed to load image for compression'));
            };

            // Load the base64 image
            img.src = `data:image/png;base64,${base64Data}`;
        });

    } catch (error) {
        prodLogger.error('❌ Base64 image compression failed:', error);
        throw error;
    }
};

/**
 * Lazy loading observer for images
 */
export class LazyImageLoader {
    constructor(options = {}) {
        this.options = {
            threshold: IMAGE_OPTIMIZATION_CONFIG.INTERSECTION_THRESHOLD,
            rootMargin: IMAGE_OPTIMIZATION_CONFIG.ROOT_MARGIN,
            ...options
        };
        
        this.observer = null;
        this.loadedImages = new Set();
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver(
                this.handleIntersection.bind(this),
                this.options
            );
        }
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && !this.loadedImages.has(entry.target)) {
                this.loadImage(entry.target);
                this.observer.unobserve(entry.target);
            }
        });
    }

    loadImage(img) {
        const src = img.dataset.src;
        if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            this.loadedImages.add(img);
            
            img.onload = () => {
                img.classList.add('loaded');
            };
            
            img.onerror = () => {
                logger.warn(`Failed to load lazy image: ${src}`);
                img.classList.add('error');
            };
        }
    }

    observe(img) {
        if (this.observer) {
            this.observer.observe(img);
        } else {
            // Fallback for browsers without IntersectionObserver
            this.loadImage(img);
        }
    }

    disconnect() {
        if (this.observer) {
            this.observer.disconnect();
        }
    }
}

/**
 * Image cache for optimized loading
 */
export class ImageCache {
    constructor() {
        this.cache = new Map();
        this.cacheSize = 0;
        this.maxSize = IMAGE_OPTIMIZATION_CONFIG.MAX_CACHE_SIZE;
        this.cacheDuration = IMAGE_OPTIMIZATION_CONFIG.CACHE_DURATION;
    }

    async get(url) {
        const cached = this.cache.get(url);
        
        if (cached && Date.now() - cached.timestamp < this.cacheDuration) {
            return cached.blob;
        }
        
        // Remove expired entry
        if (cached) {
            this.remove(url);
        }
        
        return null;
    }

    async set(url, blob) {
        // Check if we need to make space
        while (this.cacheSize + blob.size > this.maxSize && this.cache.size > 0) {
            this.removeOldest();
        }
        
        this.cache.set(url, {
            blob,
            size: blob.size,
            timestamp: Date.now()
        });
        
        this.cacheSize += blob.size;
    }

    remove(url) {
        const cached = this.cache.get(url);
        if (cached) {
            this.cache.delete(url);
            this.cacheSize -= cached.size;
        }
    }

    removeOldest() {
        let oldest = null;
        let oldestTime = Date.now();
        
        for (const [url, cached] of this.cache.entries()) {
            if (cached.timestamp < oldestTime) {
                oldest = url;
                oldestTime = cached.timestamp;
            }
        }
        
        if (oldest) {
            this.remove(oldest);
        }
    }

    clear() {
        this.cache.clear();
        this.cacheSize = 0;
    }

    getStats() {
        return {
            size: this.cache.size,
            totalSize: this.cacheSize,
            maxSize: this.maxSize,
            utilization: (this.cacheSize / this.maxSize) * 100
        };
    }
}

// Create singleton instances
export const lazyImageLoader = new LazyImageLoader();
export const imageCache = new ImageCache();

// Export all optimization functions
export default {
    isWebPSupported,
    compressImage,
    calculateOptimalDimensions,
    generateThumbnail,
    convertToOptimalFormat,
    LazyImageLoader,
    ImageCache,
    lazyImageLoader,
    imageCache,
    IMAGE_OPTIMIZATION_CONFIG
};
