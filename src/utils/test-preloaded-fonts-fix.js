/**
 * Test script to verify preloaded Google Fonts fix
 * Tests that preloaded Google Fonts are properly loaded during preview generation
 */

import { 
  extractPreloadedGoogleFontsFromTemplate,
  extractPreloadedGoogleFontsFromCustomizations,
  isPreloadedGoogleFont,
  PRELOADED_GOOGLE_FONTS
} from './preloadedGoogleFonts.js';

/**
 * Mock template with preloaded Google Fonts
 */
const mockTemplateWithPreloadedFonts = {
  id: 'test-template-preloaded',
  name: 'Test Template with Preloaded Fonts',
  text_overlays: {
    overlays: [
      {
        id: 'title',
        styling: {
          fontFamily: 'Bebas Neue'  // Preloaded Google Font
        }
      },
      {
        id: 'subtitle',
        styling: {
          fontFamily: 'Dancing Script'  // Preloaded Google Font
        }
      },
      {
        id: 'author',
        styling: {
          fontFamily: 'Arial'  // System font
        }
      }
    ]
  },
  custom_fonts: {
    fonts: []  // No custom fonts
  }
};

/**
 * Mock customizations with preloaded Google Fonts
 */
const mockCustomizations = {
  title: {
    styling: {
      fontFamily: 'Playfair Display'  // Preloaded Google Font
    }
  },
  subtitle: {
    styling: {
      fontSize: 24  // No font change
    }
  }
};

/**
 * Test preloaded Google Fonts detection
 */
function testPreloadedGoogleFontsDetection() {
  console.log('🧪 Testing preloaded Google Fonts detection...');
  
  // Test individual font detection
  const tests = [
    { font: 'Bebas Neue', expected: true },
    { font: 'Dancing Script', expected: true },
    { font: 'Arial', expected: false },  // System font
    { font: 'Custom Font', expected: false },  // Not in registry
    { font: 'Playfair Display', expected: true }
  ];
  
  tests.forEach(test => {
    const result = isPreloadedGoogleFont(test.font);
    const status = result === test.expected ? '✅' : '❌';
    console.log(`${status} ${test.font}: ${result} (expected: ${test.expected})`);
  });
  
  return tests.every(test => isPreloadedGoogleFont(test.font) === test.expected);
}

/**
 * Test font extraction from template
 */
function testFontExtractionFromTemplate() {
  console.log('\n🧪 Testing font extraction from template...');
  
  const extractedFonts = extractPreloadedGoogleFontsFromTemplate(mockTemplateWithPreloadedFonts);
  console.log('Extracted fonts:', extractedFonts);
  
  const expectedFonts = ['Bebas Neue', 'Dancing Script'];
  const isCorrect = expectedFonts.every(font => extractedFonts.includes(font)) &&
                   extractedFonts.length === expectedFonts.length;
  
  console.log(`✅ Expected: ${expectedFonts.join(', ')}`);
  console.log(`${isCorrect ? '✅' : '❌'} Extracted: ${extractedFonts.join(', ')}`);
  
  return isCorrect;
}

/**
 * Test font extraction from customizations
 */
function testFontExtractionFromCustomizations() {
  console.log('\n🧪 Testing font extraction from customizations...');
  
  const extractedFonts = extractPreloadedGoogleFontsFromCustomizations(mockCustomizations);
  console.log('Extracted fonts:', extractedFonts);
  
  const expectedFonts = ['Playfair Display'];
  const isCorrect = expectedFonts.every(font => extractedFonts.includes(font)) &&
                   extractedFonts.length === expectedFonts.length;
  
  console.log(`✅ Expected: ${expectedFonts.join(', ')}`);
  console.log(`${isCorrect ? '✅' : '❌'} Extracted: ${extractedFonts.join(', ')}`);
  
  return isCorrect;
}

/**
 * Test complete font registry
 */
function testFontRegistry() {
  console.log('\n🧪 Testing font registry...');
  
  const totalFonts = PRELOADED_GOOGLE_FONTS.length;
  const googleFonts = PRELOADED_GOOGLE_FONTS.filter(font => font.googleFont).length;
  const systemFonts = PRELOADED_GOOGLE_FONTS.filter(font => font.isSystemFont).length;
  
  console.log(`📊 Font Registry Summary:`);
  console.log(`   Total fonts: ${totalFonts}`);
  console.log(`   Google Fonts: ${googleFonts}`);
  console.log(`   System fonts: ${systemFonts}`);
  
  // Check for key fonts
  const keyFonts = ['Bebas Neue', 'Dancing Script', 'Playfair Display', 'Arial'];
  const allKeyFontsPresent = keyFonts.every(font => 
    PRELOADED_GOOGLE_FONTS.some(f => f.name === font)
  );
  
  console.log(`${allKeyFontsPresent ? '✅' : '❌'} All key fonts present: ${keyFonts.join(', ')}`);
  
  return allKeyFontsPresent && totalFonts > 0;
}

/**
 * Run all tests
 */
export async function runPreloadedFontsTests() {
  console.log('🚀 Running Preloaded Google Fonts Fix Tests\n');
  
  const results = [
    testPreloadedGoogleFontsDetection(),
    testFontExtractionFromTemplate(),
    testFontExtractionFromCustomizations(),
    testFontRegistry()
  ];
  
  const allPassed = results.every(result => result);
  
  console.log('\n📋 Test Results Summary:');
  console.log(`${allPassed ? '✅' : '❌'} All tests ${allPassed ? 'PASSED' : 'FAILED'}`);
  
  if (allPassed) {
    console.log('\n🎉 Preloaded Google Fonts fix is working correctly!');
    console.log('   - Font detection works');
    console.log('   - Template font extraction works');
    console.log('   - Customization font extraction works');
    console.log('   - Font registry is complete');
  } else {
    console.log('\n❌ Some tests failed. Please check the implementation.');
  }
  
  return allPassed;
}

// Export test data for external use
export {
  mockTemplateWithPreloadedFonts,
  mockCustomizations
};
