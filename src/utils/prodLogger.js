// Production Logger Configuration
// Replaces direct console usage with production-safe logging

const isProd = import.meta.env.PROD;

// Production-safe logger that only logs errors in production
export const prodLogger = {
  debug: (...args) => {
    if (!isProd) console.debug(...args);
  },
  info: (...args) => {
    if (!isProd) console.info(...args);
  },
  warn: (...args) => {
    if (!isProd) console.warn(...args);
    // In production, could send to monitoring service
  },
  error: (...args) => {
    console.error(...args);
    // Always log errors, even in production
    // Could send to error monitoring service
  },
  log: (...args) => {
    if (!isProd) console.log(...args);
  },
};

export default prodLogger;
