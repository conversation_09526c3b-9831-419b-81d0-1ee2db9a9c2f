import { prodLogger } from './prodLogger.js';

/**
 * Validation Utilities
 * 
 * Centralized validation functions for document data, content, and images.
 * Consolidates validation logic from multiple services to eliminate duplication.
 */

// Image configuration constants
export const IMAGE_CONFIG = {
    MAX_RETRIES: 3,
    RETRY_DELAY_BASE: 1000, // Base delay in milliseconds
    TIMEOUT: 10000, // 10 seconds timeout
    MAX_SIZE: 10 * 1024 * 1024, // 10MB max file size
    SUPPORTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
    DEFAULT_WIDTH: 600,
    DEFAULT_HEIGHT: 400
};

/**
 * Get file extension from content type
 * @param {string} contentType - MIME content type
 * @returns {string} File extension
 */
const getExtensionFromContentType = (contentType) => {
    const typeMap = {
        'image/jpeg': '.jpg',
        'image/jpg': '.jpg',
        'image/png': '.png',
        'image/gif': '.gif',
        'image/webp': '.webp',
        'image/bmp': '.bmp',
        'image/svg+xml': '.svg'
    };
    return typeMap[contentType] || '.jpg';
};

/**
 * Validate and sanitize document data for DOCX generation
 * @param {Object} documentData - Document metadata to validate
 * @returns {Object} Validation result with sanitized data and errors
 */
export const validateDocumentData = (documentData) => {
    const result = {
        isValid: true,
        sanitizedData: {},
        errors: [],
        warnings: []
    };

    try {
        if (!documentData || typeof documentData !== 'object') {
            result.isValid = false;
            result.errors.push('Document data must be an object');
            return result;
        }

        // Sanitize and validate title
        const title = documentData.title;
        if (title && typeof title === 'string') {
            const sanitizedTitle = title.trim().substring(0, 255);
            if (sanitizedTitle.length === 0) {
                result.warnings.push('Document title is empty after sanitization');
                result.sanitizedData.title = 'Untitled Document';
            } else {
                result.sanitizedData.title = sanitizedTitle;
                if (sanitizedTitle.length < title.length) {
                    result.warnings.push('Document title was truncated to 255 characters');
                }
            }
        } else {
            result.sanitizedData.title = 'Untitled Document';
            if (title) {
                result.warnings.push('Invalid title format, using default');
            }
        }

        // Sanitize and validate author
        const author = documentData.author;
        if (author && typeof author === 'string') {
            const sanitizedAuthor = author.trim().substring(0, 100);
            if (sanitizedAuthor.length > 0) {
                result.sanitizedData.author = sanitizedAuthor;
                if (sanitizedAuthor.length < author.length) {
                    result.warnings.push('Author name was truncated to 100 characters');
                }
            } else {
                result.sanitizedData.author = 'Unknown Author';
                result.warnings.push('Author name is empty after sanitization');
            }
        } else {
            result.sanitizedData.author = 'Unknown Author';
            if (author) {
                result.warnings.push('Invalid author format, using default');
            }
        }

        // Sanitize and validate description
        const description = documentData.description;
        if (description && typeof description === 'string') {
            const sanitizedDescription = description.trim().substring(0, 500);
            if (sanitizedDescription.length > 0) {
                result.sanitizedData.description = sanitizedDescription;
                if (sanitizedDescription.length < description.length) {
                    result.warnings.push('Description was truncated to 500 characters');
                }
            } else {
                result.sanitizedData.description = '';
            }
        } else {
            result.sanitizedData.description = '';
            if (description) {
                result.warnings.push('Invalid description format, using empty string');
            }
        }

        return result;

    } catch (error) {
        // Log validation errors - these are important for debugging
        if (import.meta.env.DEV) {
            prodLogger.error('Error validating document data:', error);
        }
        result.isValid = false;
        result.errors.push(`Validation failed: ${error.message}`);
        return result;
    }
};

/**
 * Validate content before processing
 * @param {string} content - Content to validate
 * @param {string} contentType - Type of content ('html' or 'markdown')
 * @returns {Object} Validation result
 */
export const validateContent = (content, contentType = 'html') => {
    const result = {
        isValid: true,
        errors: [],
        warnings: []
    };

    if (!content || typeof content !== 'string') {
        result.isValid = false;
        result.errors.push('Content must be a non-empty string');
        return result;
    }

    const trimmedContent = content.trim();
    if (trimmedContent.length === 0) {
        result.isValid = false;
        result.errors.push('Content cannot be empty');
        return result;
    }

    // Basic content length validation
    if (trimmedContent.length < 10) {
        result.warnings.push('Content is very short (less than 10 characters)');
    }

    if (trimmedContent.length > 1000000) { // 1MB limit
        result.warnings.push('Content is very large (over 1MB), processing may be slow');
    }

    // Content type specific validation
    if (contentType === 'html') {
        // Basic HTML validation
        if (!trimmedContent.includes('<') && !trimmedContent.includes('>')) {
            result.warnings.push('Content marked as HTML but contains no HTML tags');
        }
    } else if (contentType === 'markdown') {
        // Basic Markdown validation
        const hasMarkdownPatterns = /[#*_`\[\]]/g.test(trimmedContent);
        if (!hasMarkdownPatterns) {
            result.warnings.push('Content marked as Markdown but contains no Markdown patterns');
        }
    }

    return result;
};

/**
 * Validate image URL format and accessibility
 * @param {string} url - Image URL to validate
 * @returns {Object} Validation result with isValid and error message
 */
export const validateImageUrl = (url) => {
    if (!url || typeof url !== 'string') {
        return { isValid: false, error: 'Invalid URL: URL is empty or not a string' };
    }

    const trimmedUrl = url.trim();
    if (!trimmedUrl) {
        return { isValid: false, error: 'Invalid URL: URL is empty after trimming' };
    }

    // Check if URL is properly formatted
    try {
        const urlObj = new URL(trimmedUrl);

        // Check protocol
        if (!['http:', 'https:'].includes(urlObj.protocol)) {
            return { isValid: false, error: 'Invalid URL: Only HTTP and HTTPS protocols are supported' };
        }

        // Check for common image file extensions
        const pathname = urlObj.pathname.toLowerCase();
        const hasImageExtension = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'].some(ext =>
            pathname.endsWith(ext)
        );

        // If no extension, we'll still try to download and validate by content type
        return {
            isValid: true,
            hasImageExtension,
            url: trimmedUrl
        };
    } catch (error) {
        return { isValid: false, error: `Invalid URL format: ${error.message}` };
    }
};

/**
 * Validate image format based on content type
 * @param {string} contentType - Content type from HTTP response
 * @returns {Object} Validation result with isValid and normalized format
 */
export const validateImageFormat = (contentType) => {
    if (!contentType) {
        return { isValid: false, error: 'No content type provided' };
    }

    const normalizedType = contentType.toLowerCase().split(';')[0].trim();

    if (IMAGE_CONFIG.SUPPORTED_FORMATS.includes(normalizedType)) {
        return {
            isValid: true,
            format: normalizedType,
            extension: getExtensionFromContentType(normalizedType)
        };
    }

    return {
        isValid: false,
        error: `Unsupported image format: ${normalizedType}. Supported formats: ${IMAGE_CONFIG.SUPPORTED_FORMATS.join(', ')}`
    };
};

/**
 * Validate file size
 * @param {number} size - File size in bytes
 * @param {number} maxSize - Maximum allowed size in bytes
 * @returns {Object} Validation result
 */
export const validateFileSize = (size, maxSize = IMAGE_CONFIG.MAX_SIZE) => {
    if (typeof size !== 'number' || size < 0) {
        return { isValid: false, error: 'Invalid file size' };
    }

    if (size > maxSize) {
        return {
            isValid: false,
            error: `File too large: ${size} bytes (max: ${maxSize} bytes)`
        };
    }

    return { isValid: true };
};

// Export all validation functions
export default {
    validateDocumentData,
    validateContent,
    validateImageUrl,
    validateImageFormat,
    validateFileSize,
    IMAGE_CONFIG
};
