/**
 * Template Validation Tests
 * Comprehensive tests for template validation utilities
 */

import {
  validateTemplateName,
  validateTemplateDescription,
  validateTemplateCategory,
  validateTemplateTags,
  validateImageFile,
  validateImageDimensions,
  validateTextOverlay,
  validateCompleteTemplate
} from '../templateValidation';

describe('Template Validation', () => {
  describe('validateTemplateName', () => {
    test('should validate valid template names', () => {
      const result = validateTemplateName('Valid Template Name');
      expect(result.isValid).toBe(true);
      expect(result.value).toBe('Valid Template Name');
    });

    test('should reject empty names', () => {
      const result = validateTemplateName('');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('required');
    });

    test('should reject names that are too long', () => {
      const longName = 'a'.repeat(101);
      const result = validateTemplateName(longName);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('100 characters');
    });

    test('should reject names that are too short', () => {
      const result = validateTemplateName('ab');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('at least 3 characters');
    });

    test('should reject names with invalid characters', () => {
      const result = validateTemplateName('Invalid<Name>');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('invalid characters');
    });

    test('should trim whitespace', () => {
      const result = validateTemplateName('  Valid Name  ');
      expect(result.isValid).toBe(true);
      expect(result.value).toBe('Valid Name');
    });
  });

  describe('validateTemplateDescription', () => {
    test('should validate valid descriptions', () => {
      const result = validateTemplateDescription('A valid description');
      expect(result.isValid).toBe(true);
      expect(result.value).toBe('A valid description');
    });

    test('should allow empty descriptions', () => {
      const result = validateTemplateDescription('');
      expect(result.isValid).toBe(true);
      expect(result.value).toBe('');
    });

    test('should reject descriptions that are too long', () => {
      const longDesc = 'a'.repeat(501);
      const result = validateTemplateDescription(longDesc);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('500 characters');
    });
  });

  describe('validateTemplateCategory', () => {
    test('should validate valid categories', () => {
      const result = validateTemplateCategory('Business');
      expect(result.isValid).toBe(true);
      expect(result.value).toBe('Business');
    });

    test('should reject invalid categories', () => {
      const result = validateTemplateCategory('InvalidCategory');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Invalid category');
    });

    test('should reject empty categories', () => {
      const result = validateTemplateCategory('');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('required');
    });
  });

  describe('validateTemplateTags', () => {
    test('should validate valid tag arrays', () => {
      const result = validateTemplateTags(['tag1', 'tag2', 'tag3']);
      expect(result.isValid).toBe(true);
      expect(result.value).toEqual(['tag1', 'tag2', 'tag3']);
    });

    test('should allow empty tag arrays', () => {
      const result = validateTemplateTags([]);
      expect(result.isValid).toBe(true);
      expect(result.value).toEqual([]);
    });

    test('should reject too many tags', () => {
      const manyTags = Array(11).fill('tag');
      const result = validateTemplateTags(manyTags);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Maximum 10 tags');
    });

    test('should reject duplicate tags', () => {
      const result = validateTemplateTags(['tag1', 'tag2', 'tag1']);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Duplicate tag');
    });

    test('should reject tags that are too long', () => {
      const longTag = 'a'.repeat(31);
      const result = validateTemplateTags([longTag]);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('30 characters');
    });
  });

  describe('validateImageFile', () => {
    test('should validate valid image files', () => {
      const mockFile = {
        type: 'image/jpeg',
        size: 1024 * 1024 // 1MB
      };
      const result = validateImageFile(mockFile);
      expect(result.isValid).toBe(true);
    });

    test('should reject invalid file types', () => {
      const mockFile = {
        type: 'text/plain',
        size: 1024
      };
      const result = validateImageFile(mockFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Invalid file type');
    });

    test('should reject files that are too large', () => {
      const mockFile = {
        type: 'image/jpeg',
        size: 11 * 1024 * 1024 // 11MB
      };
      const result = validateImageFile(mockFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('too large');
    });

    test('should reject files that are too small', () => {
      const mockFile = {
        type: 'image/jpeg',
        size: 500 // 500 bytes
      };
      const result = validateImageFile(mockFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('too small');
    });
  });

  describe('validateImageDimensions', () => {
    test('should validate valid dimensions', () => {
      const result = validateImageDimensions({ width: 1200, height: 1600 });
      expect(result.isValid).toBe(true);
    });

    test('should reject dimensions that are too small', () => {
      const result = validateImageDimensions({ width: 200, height: 200 });
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('too small');
    });

    test('should reject dimensions that are too large', () => {
      const result = validateImageDimensions({ width: 6000, height: 6000 });
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('too large');
    });

    test('should reject invalid dimension values', () => {
      const result = validateImageDimensions({ width: -100, height: 1600 });
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('positive');
    });
  });

  describe('validateTextOverlay', () => {
    const validOverlay = {
      id: 'title',
      type: 'text',
      placeholder: '{{title}}',
      position: { x: 50, y: 100, width: 400, height: 60 },
      styling: {
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000'
      }
    };

    test('should validate valid text overlays', () => {
      const result = validateTextOverlay(validOverlay);
      expect(result.isValid).toBe(true);
    });

    test('should reject overlays without required fields', () => {
      const invalidOverlay = { ...validOverlay };
      delete invalidOverlay.id;
      const result = validateTextOverlay(invalidOverlay);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('ID is required');
    });

    test('should reject overlays with invalid position', () => {
      const invalidOverlay = {
        ...validOverlay,
        position: { x: -10, y: 100, width: 400, height: 60 }
      };
      const result = validateTextOverlay(invalidOverlay);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Invalid X position');
    });

    test('should reject overlays with invalid styling', () => {
      const invalidOverlay = {
        ...validOverlay,
        styling: { ...validOverlay.styling, fontSize: -5 }
      };
      const result = validateTextOverlay(invalidOverlay);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Font size must be between');
    });
  });

  describe('validateCompleteTemplate', () => {
    const validTemplate = {
      name: 'Test Template',
      description: 'A test template',
      category: 'Business',
      tags: ['test', 'business'],
      background_image_width: 1200,
      background_image_height: 1600,
      text_overlays: {
        overlays: [{
          id: 'title',
          type: 'text',
          placeholder: '{{title}}',
          position: { x: 50, y: 100, width: 400, height: 60 },
          styling: {
            fontSize: 24,
            fontFamily: 'Arial',
            color: '#000000'
          }
        }]
      }
    };

    test('should validate complete valid templates', () => {
      const result = validateCompleteTemplate(validTemplate);
      expect(result.isValid).toBe(true);
      expect(result.errorCount).toBe(0);
    });

    test('should collect multiple validation errors', () => {
      const invalidTemplate = {
        ...validTemplate,
        name: '', // Invalid name
        category: 'InvalidCategory', // Invalid category
        tags: Array(11).fill('tag') // Too many tags
      };
      const result = validateCompleteTemplate(invalidTemplate);
      expect(result.isValid).toBe(false);
      expect(result.errorCount).toBeGreaterThan(1);
      expect(result.errors.name).toBeDefined();
      expect(result.errors.category).toBeDefined();
      expect(result.errors.tags).toBeDefined();
    });
  });
});
