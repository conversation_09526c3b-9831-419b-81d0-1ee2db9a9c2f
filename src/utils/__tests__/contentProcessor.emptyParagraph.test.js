/**
 * Test for Empty Paragraph Bug Fix
 * Ensures that DocGenerate content replacement doesn't create unwanted empty paragraphs
 */

import { replaceNodeContent } from '../contentProcessor.js';

// Mock TipTap editor with proper node selection and commands
const createMockEditor = (nodeType = 'paragraph', nodeAttrs = {}) => {
  const mockCommands = {
    setTextSelection: jest.fn().mockReturnThis(),
    setParagraph: jest.fn().mockReturnThis(),
    setHeading: jest.fn().mockReturnThis(),
    setBlockquote: jest.fn().mockReturnThis(),
    setCodeBlock: jest.fn().mockReturnThis(),
    insertContent: jest.fn().mockReturnThis(),
    focus: jest.fn().mockReturnThis(),
  };

  return {
    state: {
      selection: {
        $from: {
          node: () => ({
            type: { name: nodeType },
            textContent: 'Original content',
            nodeSize: 20,
            attrs: nodeAttrs,
          }),
          depth: 1,
          before: (depth) => depth === 1 ? 0 : 10, // Mock node position
        },
      },
    },
    commands: mockCommands,
    _mockCommands: mockCommands, // Expose for testing
  };
};

describe('Empty Paragraph Bug Fix', () => {
  it('should replace paragraph content without creating empty paragraphs', () => {
    const editor = createMockEditor('paragraph');
    const extractionResult = {
      hasSelection: false,
      nodeType: 'paragraph',
    };
    const newContent = 'New paragraph content';

    replaceNodeContent(editor, newContent, extractionResult, {
      preserveFormatting: true,
      focusAfter: true,
    });

    // Verify the correct sequence of commands
    expect(editor._mockCommands.setTextSelection).toHaveBeenCalledWith({
      from: 0,
      to: 20,
    });
    expect(editor._mockCommands.setParagraph).toHaveBeenCalled();
    expect(editor._mockCommands.insertContent).toHaveBeenCalledWith(newContent);
  });

  it('should replace heading content while preserving level', () => {
    const editor = createMockEditor('heading', { level: 2 });
    const extractionResult = {
      hasSelection: false,
      nodeType: 'heading',
    };
    const newContent = 'New heading content';

    replaceNodeContent(editor, newContent, extractionResult);

    // Verify heading level is preserved
    expect(editor._mockCommands.setTextSelection).toHaveBeenCalled();
    expect(editor._mockCommands.setHeading).toHaveBeenCalledWith({ level: 2 });
    expect(editor._mockCommands.insertContent).toHaveBeenCalledWith(newContent);
  });

  it('should replace list item content correctly', () => {
    const editor = createMockEditor('listItem');
    const extractionResult = {
      hasSelection: false,
      nodeType: 'listItem',
    };
    const newContent = 'New list item content';

    replaceNodeContent(editor, newContent, extractionResult);

    // Verify list item replacement
    expect(editor._mockCommands.setTextSelection).toHaveBeenCalled();
    expect(editor._mockCommands.insertContent).toHaveBeenCalledWith(newContent);
  });

  it('should replace blockquote content while maintaining formatting', () => {
    const editor = createMockEditor('blockquote');
    const extractionResult = {
      hasSelection: false,
      nodeType: 'blockquote',
    };
    const newContent = 'New blockquote content';

    replaceNodeContent(editor, newContent, extractionResult);

    // Verify blockquote formatting is maintained
    expect(editor._mockCommands.setTextSelection).toHaveBeenCalled();
    expect(editor._mockCommands.setBlockquote).toHaveBeenCalled();
    expect(editor._mockCommands.insertContent).toHaveBeenCalledWith(newContent);
  });

  it('should replace code block content while preserving code formatting', () => {
    const editor = createMockEditor('codeBlock');
    const extractionResult = {
      hasSelection: false,
      nodeType: 'codeBlock',
    };
    const newContent = 'console.log("new code");';

    replaceNodeContent(editor, newContent, extractionResult);

    // Verify code block formatting is preserved
    expect(editor._mockCommands.setTextSelection).toHaveBeenCalled();
    expect(editor._mockCommands.setCodeBlock).toHaveBeenCalled();
    expect(editor._mockCommands.insertContent).toHaveBeenCalledWith(newContent);
  });

  it('should handle focus positioning correctly', (done) => {
    const editor = createMockEditor('paragraph');
    const extractionResult = {
      hasSelection: false,
      nodeType: 'paragraph',
    };
    const newContent = 'New content with focus';

    replaceNodeContent(editor, newContent, extractionResult, {
      focusAfter: true,
    });

    // Check that focus is called after a timeout
    setTimeout(() => {
      expect(editor._mockCommands.focus).toHaveBeenCalledWith('end');
      done();
    }, 10);
  });

  it('should handle errors gracefully during focus positioning', (done) => {
    const editor = createMockEditor('paragraph');
    // Mock focus to throw an error
    editor._mockCommands.focus = jest.fn()
      .mockImplementationOnce(() => { throw new Error('Focus failed'); })
      .mockReturnThis();

    const extractionResult = {
      hasSelection: false,
      nodeType: 'paragraph',
    };
    const newContent = 'New content';

    replaceNodeContent(editor, newContent, extractionResult, {
      focusAfter: true,
    });

    // Check that fallback focus is called
    setTimeout(() => {
      expect(editor._mockCommands.focus).toHaveBeenCalledTimes(2);
      expect(editor._mockCommands.focus).toHaveBeenLastCalledWith();
      done();
    }, 10);
  });
});
