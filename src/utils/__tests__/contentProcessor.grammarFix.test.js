/**
 * Tests for Fix Grammar & Spelling Bug Fixes
 * Tests cursor positioning and node replacement functionality
 */

import {
  extractNodeContent,
  replaceNodeContent,
} from "../contentProcessor.js";

// Mock TipTap editor with proper transaction support
const createMockEditor = (options = {}) => {
  const {
    text = "Sample text content",
    nodeType = "paragraph",
    nodeAttrs = {},
    selectionFrom = 0,
    selectionTo = 0,
    hasSelection = false,
  } = options;

  const mockNode = {
    type: { name: nodeType },
    textContent: text,
    nodeSize: text.length + 2, // +2 for node boundaries
    attrs: nodeAttrs,
    content: {
      size: text.length,
    },
  };

  const mockSchema = {
    text: (content) => ({ textContent: content, type: 'text' }),
    nodes: {
      paragraph: {
        create: (attrs, content) => ({
          type: { name: 'paragraph' },
          attrs,
          content: Array.isArray(content) ? content : [content],
          textContent: content?.textContent || '',
        }),
      },
      heading: {
        create: (attrs, content) => ({
          type: { name: 'heading' },
          attrs,
          content: Array.isArray(content) ? content : [content],
          textContent: content?.textContent || '',
        }),
      },
      blockquote: {
        create: (attrs, content) => ({
          type: { name: 'blockquote' },
          attrs,
          content: Array.isArray(content) ? content : [content],
          textContent: content?.textContent || '',
        }),
      },
      codeBlock: {
        create: (attrs, content) => ({
          type: { name: 'codeBlock' },
          attrs,
          content: Array.isArray(content) ? content : [content],
          textContent: content?.textContent || '',
        }),
      },
      listItem: {
        create: (attrs, content) => ({
          type: { name: 'listItem' },
          attrs,
          content: Array.isArray(content) ? content : [content],
          textContent: content?.textContent || '',
        }),
      },
    },
  };

  const mockCommands = {
    setTextSelection: jest.fn().mockReturnThis(),
    focus: jest.fn().mockReturnThis(),
  };

  const mockTransaction = {
    replaceWith: jest.fn().mockReturnThis(),
  };

  const mockState = {
    selection: {
      $from: {
        node: () => mockNode,
        start: () => 1, // Start of node content
        end: () => 1 + text.length, // End of node content
        before: (depth) => 0, // Node position
        depth: 1,
      },
      from: hasSelection ? selectionFrom : 0,
      to: hasSelection ? selectionTo : 0,
    },
    doc: {
      textBetween: (from, to) => text.slice(from, to),
      content: {
        size: text.length + 10, // Mock document size
      },
    },
    tr: mockTransaction,
  };

  return {
    state: mockState,
    schema: mockSchema,
    commands: mockCommands,
    view: {
      dispatch: jest.fn(),
    },
    getText: () => text,
    isEditable: true,
    isDestroyed: false,
    _mockCommands: mockCommands,
    _mockTransaction: mockTransaction,
  };
};

describe("Fix Grammar & Spelling Bug Fixes", () => {
  describe("Cursor Positioning Fix", () => {
    test("should position cursor at end of replaced content, not document end", (done) => {
      const editor = createMockEditor({
        text: "This have grammar error",
        nodeType: "paragraph",
      });

      const extractionResult = {
        hasSelection: false,
        nodeType: "paragraph",
      };

      const newContent = "This has correct grammar";

      replaceNodeContent(editor, newContent, extractionResult, {
        preserveFormatting: true,
        focusAfter: true,
      });

      // Check that cursor positioning is called with correct position
      setTimeout(() => {
        expect(editor._mockCommands.setTextSelection).toHaveBeenCalledWith(
          1 + newContent.length // Start of content + content length
        );
        expect(editor._mockCommands.focus).toHaveBeenCalled();
        done();
      }, 10);
    });

    test("should handle cursor positioning for headings correctly", (done) => {
      const editor = createMockEditor({
        text: "Heading with error",
        nodeType: "heading",
        nodeAttrs: { level: 2 },
      });

      const extractionResult = {
        hasSelection: false,
        nodeType: "heading",
      };

      const newContent = "Heading without error";

      replaceNodeContent(editor, newContent, extractionResult, {
        focusAfter: true,
      });

      setTimeout(() => {
        expect(editor._mockCommands.setTextSelection).toHaveBeenCalledWith(
          1 + newContent.length
        );
        done();
      }, 10);
    });
  });

  describe("Node Replacement Fix", () => {
    test("should replace paragraph content in-place using transactions", () => {
      const editor = createMockEditor({
        text: "Original paragraph text",
        nodeType: "paragraph",
      });

      const extractionResult = {
        hasSelection: false,
        nodeType: "paragraph",
      };

      const newContent = "Corrected paragraph text";

      replaceNodeContent(editor, newContent, extractionResult);

      // Verify transaction-based replacement
      expect(editor._mockTransaction.replaceWith).toHaveBeenCalledWith(
        1, // Start of node content
        1 + "Original paragraph text".length, // End of node content
        expect.any(Array) // New content
      );
      expect(editor.view.dispatch).toHaveBeenCalledWith(editor._mockTransaction);
    });

    test("should preserve heading level during replacement", () => {
      const editor = createMockEditor({
        text: "Original heading",
        nodeType: "heading",
        nodeAttrs: { level: 3 },
      });

      const extractionResult = {
        hasSelection: false,
        nodeType: "heading",
      };

      const newContent = "Corrected heading";

      replaceNodeContent(editor, newContent, extractionResult);

      // Verify heading node creation with preserved level
      expect(editor.schema.nodes.heading.create).toHaveBeenCalledWith(
        { level: 3 },
        expect.objectContaining({ textContent: newContent })
      );
    });

    test("should handle blockquote replacement correctly", () => {
      const editor = createMockEditor({
        text: "Quote with error",
        nodeType: "blockquote",
      });

      const extractionResult = {
        hasSelection: false,
        nodeType: "blockquote",
      };

      const newContent = "Quote without error";

      replaceNodeContent(editor, newContent, extractionResult);

      // Verify blockquote structure is maintained
      expect(editor.schema.nodes.blockquote.create).toHaveBeenCalled();
      expect(editor._mockTransaction.replaceWith).toHaveBeenCalled();
    });

    test("should handle list item replacement correctly", () => {
      const editor = createMockEditor({
        text: "List item with error",
        nodeType: "listItem",
      });

      const extractionResult = {
        hasSelection: false,
        nodeType: "listItem",
      };

      const newContent = "List item without error";

      replaceNodeContent(editor, newContent, extractionResult);

      // Verify list item structure is maintained
      expect(editor.schema.nodes.listItem.create).toHaveBeenCalled();
      expect(editor._mockTransaction.replaceWith).toHaveBeenCalled();
    });
  });

  describe("Error Handling", () => {
    test("should handle cursor positioning errors gracefully", (done) => {
      const editor = createMockEditor();
      
      // Mock setTextSelection to throw an error
      editor._mockCommands.setTextSelection.mockImplementation(() => {
        throw new Error("Position out of bounds");
      });

      const extractionResult = {
        hasSelection: false,
        nodeType: "paragraph",
      };

      replaceNodeContent(editor, "New content", extractionResult, {
        focusAfter: true,
      });

      setTimeout(() => {
        // Should still call focus as fallback
        expect(editor._mockCommands.focus).toHaveBeenCalled();
        done();
      }, 10);
    });
  });
});
