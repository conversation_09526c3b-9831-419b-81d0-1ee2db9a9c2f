/**
 * Cache Manager - Handles clearing and managing RapidDoc AI cached data
 * Provides specific solutions for data synchronization issues
 */

import { prodLogger } from "./prodLogger.js";

// Get document ID from current URL
const getCurrentDocumentId = () => {
  const path = window.location.pathname;
  const match = path.match(/\/document-editor\/([^\/]+)/);
  return match ? match[1] : null;
};

// Clear all RapidDoc AI related data
export const clearAllRapidDocData = () => {
  prodLogger.debug("🧹 CLEARING ALL RapidDoc AI DATA");
  prodLogger.debug("==================================");

  const keysToRemove = [];

  // Find all RapidDoc related keys
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (
      key &&
      (key.startsWith("document-") ||
        key.startsWith("rapiddoc_") ||
        key === "sidebar-collapsed" ||
        key === "auth-token" ||
        key === "user-data")
    ) {
      keysToRemove.push(key);
    }
  }

  // Remove all found keys
  keysToRemove.forEach((key) => {
    localStorage.removeItem(key);
    prodLogger.debug("✅ Removed:", key);
  });

  prodLogger.debug(`✅ Cleared ${keysToRemove.length} items from localStorage`);
  prodLogger.debug("💡 Recommendation: Refresh the page completely (Ctrl+F5)");

  return keysToRemove.length;
};

// Clear specific document data
export const clearDocumentData = (documentId = null) => {
  const docId = documentId || getCurrentDocumentId();

  if (!docId) {
    prodLogger.warn("❌ No document ID provided or found in URL");
    return false;
  }

  prodLogger.debug("🧹 CLEARING DOCUMENT DATA");
  prodLogger.debug("==========================");
  prodLogger.debug("Document ID:", docId);

  const keysToRemove = [
    `document-${docId}`,
    `rapiddoc_cached_titleSelection.generatedTitles`,
    `rapiddoc_cached_topicAndNiche.availableSubNiches`,
    `rapiddoc_cached_documentOutline.generatedOutline`,
    "rapiddoc_session_metadata",
  ];

  let removedCount = 0;
  keysToRemove.forEach((key) => {
    if (localStorage.getItem(key)) {
      localStorage.removeItem(key);
      prodLogger.debug("✅ Removed:", key);
      removedCount++;
    }
  });

  prodLogger.debug(`✅ Cleared ${removedCount} document-related items`);
  prodLogger.debug(
    "💡 Recommendation: Navigate back to document creator and recreate document"
  );

  return removedCount > 0;
};

// Clear only image-related data while preserving document structure
export const clearImageData = (documentId = null) => {
  const docId = documentId || getCurrentDocumentId();

  if (!docId) {
    prodLogger.warn("❌ No document ID provided or found in URL");
    return false;
  }

  prodLogger.debug("🖼️ CLEARING IMAGE DATA ONLY");
  prodLogger.debug("============================");
  prodLogger.debug("Document ID:", docId);

  // Clear image suggestions cache if available
  try {
    const {
      imageSuggestionsService,
    } = require("../services/imageSuggestionsService");
    if (imageSuggestionsService) {
      imageSuggestionsService.cleanupCache();
      prodLogger.debug("✅ Cleared image suggestions cache");
    }
  } catch (error) {
    // Ignore if service not available during development
  }

  try {
    const documentKey = `document-${docId}`;
    const rawData = localStorage.getItem(documentKey);

    if (!rawData) {
      prodLogger.warn("❌ No document data found");
      return false;
    }

    const documentData = JSON.parse(rawData);

    // Remove legacy placedImages
    if (
      documentData.generatedContent &&
      documentData.generatedContent.placedImages
    ) {
      delete documentData.generatedContent.placedImages;
      prodLogger.debug("✅ Removed legacy placedImages");
    }

    // Remove images from chapter content
    if (
      documentData.generatedContent &&
      documentData.generatedContent.chapters
    ) {
      documentData.generatedContent.chapters.forEach((chapter, index) => {
        if (chapter.content) {
          const originalContent = chapter.content;
          // Remove markdown images but preserve other content
          chapter.content = chapter.content
            .replace(/!\[.*?\]\(.*?\)/g, "")
            .replace(/\n\n+/g, "\n\n");

          if (originalContent !== chapter.content) {
            prodLogger.debug(
              `✅ Removed images from Chapter ${chapter.number || index + 1}`
            );
          }
        }
      });
    }

    // Update timestamp
    documentData.lastModified = new Date().toISOString();
    documentData.imageDataCleared = new Date().toISOString();

    // Save cleaned data
    localStorage.setItem(documentKey, JSON.stringify(documentData));
    prodLogger.debug("✅ Saved cleaned document data");
    prodLogger.debug("💡 Recommendation: Refresh the page to see changes");

    return true;
  } catch (error) {
    prodLogger.error("❌ Error clearing image data:", error);
    return false;
  }
};

// Force document migration and cleanup
export const forceDocumentCleanup = (documentId = null) => {
  const docId = documentId || getCurrentDocumentId();

  if (!docId) {
    prodLogger.debug("❌ No document ID provided or found in URL");
    return false;
  }

  prodLogger.debug("🔧 FORCING DOCUMENT CLEANUP");
  prodLogger.debug("============================");
  prodLogger.debug("Document ID:", docId);

  try {
    const documentKey = `document-${docId}`;
    const rawData = localStorage.getItem(documentKey);

    if (!rawData) {
      prodLogger.debug("❌ No document data found");
      return false;
    }

    const documentData = JSON.parse(rawData);

    // Force clean legacy data
    if (documentData.generatedContent) {
      // Remove legacy placedImages completely
      delete documentData.generatedContent.placedImages;

      // Mark as migrated
      documentData.migrated = true;
      documentData.migratedAt = new Date().toISOString();
      documentData.forceCleaned = new Date().toISOString();

      prodLogger.debug("✅ Removed all legacy image data");
      prodLogger.debug("✅ Marked document as migrated");
    }

    // Save cleaned data
    localStorage.setItem(documentKey, JSON.stringify(documentData));
    prodLogger.debug("✅ Document cleanup complete");
    prodLogger.debug("💡 Recommendation: Refresh the page to see changes");

    return true;
  } catch (error) {
    prodLogger.error("❌ Error during cleanup:", error);
    return false;
  }
};

// Diagnostic function to check current state
export const checkDocumentState = (documentId = null) => {
  const docId = documentId || getCurrentDocumentId();

  if (!docId) {
    prodLogger.debug("❌ No document ID provided or found in URL");
    return null;
  }

  prodLogger.debug("🔍 DOCUMENT STATE CHECK");
  prodLogger.debug("========================");
  prodLogger.debug("Document ID:", docId);

  try {
    const documentKey = `document-${docId}`;
    const rawData = localStorage.getItem(documentKey);

    if (!rawData) {
      prodLogger.debug("❌ No document data found");
      return null;
    }

    const documentData = JSON.parse(rawData);
    const generatedContent = documentData.generatedContent;

    const state = {
      hasGeneratedContent: !!generatedContent,
      hasLegacyPlacedImages: !!(
        generatedContent && generatedContent.placedImages
      ),
      legacyImageCount: 0,
      chaptersWithBlockImages: 0,
      totalChapters: 0,
      isMigrated: !!documentData.migrated,
      lastModified: documentData.lastModified,
    };

    if (generatedContent) {
      // Count legacy images
      if (generatedContent.placedImages) {
        state.legacyImageCount = Object.keys(
          generatedContent.placedImages
        ).reduce(
          (total, key) => total + generatedContent.placedImages[key].length,
          0
        );
      }

      // Count chapters with block images
      if (generatedContent.chapters) {
        state.totalChapters = generatedContent.chapters.length;
        state.chaptersWithBlockImages = generatedContent.chapters.filter(
          (ch) => ch.content && ch.content.includes("![")
        ).length;
      }
    }

    prodLogger.debug("📊 Current State:", state);

    // Recommendations
    prodLogger.debug("\n💡 Recommendations:");
    if (state.hasLegacyPlacedImages) {
      prodLogger.debug(
        "⚠️ Legacy placedImages detected - run forceDocumentCleanup()"
      );
    }
    if (!state.isMigrated) {
      prodLogger.debug(
        "⚠️ Document not marked as migrated - run forceDocumentCleanup()"
      );
    }
    if (state.legacyImageCount > 0 && state.chaptersWithBlockImages > 0) {
      prodLogger.debug(
        "⚠️ Both legacy and block images present - data conflict possible"
      );
    }
    if (state.legacyImageCount === 0 && state.chaptersWithBlockImages === 0) {
      prodLogger.debug("✅ No image data conflicts detected");
    }

    return state;
  } catch (error) {
    prodLogger.error("❌ Error checking document state:", error);
    return null;
  }
};

// Export functions to global scope for console access
if (typeof window !== "undefined") {
  window.RapidDocCache = {
    clearAll: clearAllRapidDocData,
    clearDocument: clearDocumentData,
    clearImages: clearImageData,
    forceCleanup: forceDocumentCleanup,
    checkState: checkDocumentState,
  };

  prodLogger.debug("🔧 RapidDoc Cache Manager Available:");
  prodLogger.debug(
    "- window.RapidDocCache.clearAll() - Clear all RapidDoc data"
  );
  prodLogger.debug(
    "- window.RapidDocCache.clearDocument(docId) - Clear specific document"
  );
  prodLogger.debug(
    "- window.RapidDocCache.clearImages(docId) - Clear only image data"
  );
  prodLogger.debug(
    "- window.RapidDocCache.forceCleanup(docId) - Force cleanup and migration"
  );
  prodLogger.debug(
    "- window.RapidDocCache.checkState(docId) - Check current document state"
  );
}
