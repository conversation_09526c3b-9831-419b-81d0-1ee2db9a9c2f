import { prodLogger } from './prodLogger.js';

/**
 * Text Overlay State Synchronization Utilities
 * Provides debugging and monitoring tools for state synchronization issues
 */

/**
 * Debug function to check state synchronization
 * @param {string} templateId - Template ID to check
 * @returns {Object} State synchronization info
 */
export const debugTextOverlayState = (templateId) => {
  if (!templateId) {
    return { error: 'No template ID provided' };
  }

  const storageKey = `text-overlay-customizations-${templateId}`;
  
  try {
    const storedCustomizations = sessionStorage.getItem(storageKey);
    const parsedCustomizations = storedCustomizations ? JSON.parse(storedCustomizations) : null;
    
    return {
      templateId,
      storageKey,
      hasStoredCustomizations: !!storedCustomizations,
      storedCustomizations: parsedCustomizations,
      customizationCount: parsedCustomizations ? Object.keys(parsedCustomizations).length : 0,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      templateId,
      storageKey,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Clear all text overlay customizations from session storage
 * @returns {Object} Cleanup result
 */
export const clearAllTextOverlayCustomizations = () => {
  try {
    const keys = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && key.startsWith('text-overlay-customizations-')) {
        keys.push(key);
      }
    }
    
    keys.forEach(key => sessionStorage.removeItem(key));
    
    return {
      success: true,
      clearedKeys: keys,
      count: keys.length,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Monitor state synchronization for debugging
 * @param {string} templateId - Template ID to monitor
 * @param {Object} currentCustomizations - Current customizations in memory
 * @returns {Object} Synchronization status
 */
export const monitorStateSynchronization = (templateId, currentCustomizations = {}) => {
  const storageState = debugTextOverlayState(templateId);
  const memoryCustomizationCount = Object.keys(currentCustomizations).length;
  
  const isInSync = storageState.customizationCount === memoryCustomizationCount;
  
  return {
    templateId,
    isInSync,
    storage: {
      hasCustomizations: storageState.hasStoredCustomizations,
      count: storageState.customizationCount,
      data: storageState.storedCustomizations
    },
    memory: {
      hasCustomizations: memoryCustomizationCount > 0,
      count: memoryCustomizationCount,
      data: currentCustomizations
    },
    recommendations: isInSync ? [] : [
      'State mismatch detected between storage and memory',
      'Consider refreshing the preview to sync state',
      'Check for race conditions in state updates'
    ],
    timestamp: new Date().toISOString()
  };
};

/**
 * Validate customization data structure
 * @param {Object} customizations - Customizations to validate
 * @returns {Object} Validation result
 */
export const validateCustomizations = (customizations) => {
  const errors = [];
  const warnings = [];
  
  if (!customizations || typeof customizations !== 'object') {
    errors.push('Customizations must be an object');
    return { valid: false, errors, warnings };
  }
  
  Object.entries(customizations).forEach(([overlayId, overlay]) => {
    if (!overlayId || typeof overlayId !== 'string') {
      errors.push(`Invalid overlay ID: ${overlayId}`);
    }
    
    if (!overlay || typeof overlay !== 'object') {
      errors.push(`Invalid overlay data for ${overlayId}`);
      return;
    }
    
    // Validate styling properties
    if (overlay.styling) {
      const { fontSize, color, fontWeight, textAlign } = overlay.styling;
      
      if (fontSize !== undefined && (typeof fontSize !== 'number' || fontSize < 8 || fontSize > 72)) {
        warnings.push(`Font size ${fontSize} for ${overlayId} is outside recommended range (8-72px)`);
      }
      
      if (color !== undefined && typeof color !== 'string') {
        errors.push(`Invalid color format for ${overlayId}: ${color}`);
      }
      
      if (fontWeight !== undefined && !['normal', 'bold', '300', '400', '500', '600', '700'].includes(fontWeight)) {
        warnings.push(`Unusual font weight for ${overlayId}: ${fontWeight}`);
      }
      
      if (textAlign !== undefined && !['left', 'center', 'right', 'justify'].includes(textAlign)) {
        errors.push(`Invalid text alignment for ${overlayId}: ${textAlign}`);
      }
    }
    
    // Validate position properties
    if (overlay.position) {
      const { x, y, width, height } = overlay.position;
      
      [x, y, width, height].forEach((value, index) => {
        const prop = ['x', 'y', 'width', 'height'][index];
        if (value !== undefined && (typeof value !== 'number' || value < 0)) {
          errors.push(`Invalid ${prop} value for ${overlayId}: ${value}`);
        }
      });
    }
  });
  
  return {
    valid: errors.length === 0,
    errors,
    warnings,
    overlayCount: Object.keys(customizations).length,
    timestamp: new Date().toISOString()
  };
};

// Development-only debugging helpers
if (process.env.NODE_ENV === 'development') {
  // Make debugging functions available globally in development
  window.debugTextOverlayState = debugTextOverlayState;
  window.clearAllTextOverlayCustomizations = clearAllTextOverlayCustomizations;
  window.monitorStateSynchronization = monitorStateSynchronization;
  window.validateCustomizations = validateCustomizations;
  
  prodLogger.debug('🔧 Text Overlay debugging tools available:');
  prodLogger.debug('- window.debugTextOverlayState(templateId)');
  prodLogger.debug('- window.clearAllTextOverlayCustomizations()');
  prodLogger.debug('- window.monitorStateSynchronization(templateId, customizations)');
  prodLogger.debug('- window.validateCustomizations(customizations)');
}
