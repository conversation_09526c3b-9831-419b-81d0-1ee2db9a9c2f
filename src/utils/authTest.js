// Authentication System Test Utility
// This file contains functions to test the authentication system

import { supabase } from '../lib/supabase'

import { prodLogger } from './prodLogger.js';
export const authTests = {
  // Test basic connection to Supabase
  async testConnection() {
    try {
      const { data, error } = await supabase.from('auth.users').select('count').limit(1)

      if (error) {
        return { success: false, error: error.message }
      }
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  },

  // Test session retrieval
  async testSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()

      if (error) {
        return { success: false, error: error.message }
      }

      if (session) {
        return { success: true, session }
      } else {
        return { success: true, session: null }
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  },

  // Test user profile creation (mock)
  async testProfileCreation(userId, profileData) {
    try {

      // This would normally create a profile in the database
      // For now, we'll just validate the data structure
      const requiredFields = ['id', 'email']
      const missingFields = requiredFields.filter(field => !profileData[field])

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`)
      }

      return { success: true, data: profileData }
    } catch (error) {
      return { success: false, error: error.message }
    }
  },

  // Test authentication flow (mock)
  async testAuthFlow() {

    const results = {
      connection: await this.testConnection(),
      session: await this.testSession(),
      profileStructure: await this.testProfileCreation('test-user-id', {
        id: 'test-user-id',
        email: '<EMAIL>',
        full_name: 'Test User',
        user_type: 'student'
      })
    }

    const allPassed = Object.values(results).every(result => result.success)

    if (allPassed) {
      prodLogger.debug('🎉 All authentication tests passed!')
    } else {
      prodLogger.debug('⚠️  Some authentication tests failed')
    }

    return { success: allPassed, results }
  },

  // Test environment variables
  testEnvironment() {
    prodLogger.debug('🔍 Testing environment configuration...')

    const requiredEnvVars = [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY'
    ]

    const envStatus = requiredEnvVars.map(varName => {
      const value = import.meta.env[varName]
      return {
        variable: varName,
        present: !!value,
        value: value ? `${value.substring(0, 10)}...` : 'Not set'
      }
    })

    console.table(envStatus)

    const allPresent = envStatus.every(env => env.present)

    if (allPresent) {
      prodLogger.debug('✅ All required environment variables are set')
    } else {
      prodLogger.debug('❌ Some required environment variables are missing')
    }

    return { success: allPresent, envStatus }
  },

  // Test local storage functionality
  testLocalStorage() {
    prodLogger.debug('🔍 Testing local storage functionality...')

    try {
      const testKey = 'rapiddoc_test'
      const testValue = { test: true, timestamp: Date.now() }

      // Test write
      localStorage.setItem(testKey, JSON.stringify(testValue))

      // Test read
      const retrieved = JSON.parse(localStorage.getItem(testKey))

      // Test delete
      localStorage.removeItem(testKey)

      if (retrieved.test === testValue.test) {
        prodLogger.debug('✅ Local storage working correctly')
        return { success: true }
      } else {
        throw new Error('Retrieved value does not match stored value')
      }
    } catch (error) {
      prodLogger.error('❌ Local storage test failed:', error)
      return { success: false, error: error.message }
    }
  },

  // Run all tests
  async runAllTests() {
    prodLogger.debug('🧪 Running comprehensive authentication system tests...')
    prodLogger.debug('='.repeat(60))

    const testResults = {
      environment: this.testEnvironment(),
      localStorage: this.testLocalStorage(),
      connection: await this.testConnection(),
      session: await this.testSession()
    }

    const overallSuccess = Object.values(testResults).every(result => result.success)

    prodLogger.debug('='.repeat(60))
    prodLogger.debug('📋 FINAL TEST SUMMARY:')
    prodLogger.debug('='.repeat(60))

    Object.entries(testResults).forEach(([testName, result]) => {
      const status = result.success ? '✅ PASS' : '❌ FAIL'
      const message = result.error ? ` - ${result.error}` : ''
      prodLogger.debug(`${status} ${testName.toUpperCase()}${message}`)
    })

    prodLogger.debug('='.repeat(60))

    if (overallSuccess) {
      prodLogger.debug('🎉 ALL TESTS PASSED! Authentication system is ready.')
    } else {
      prodLogger.debug('⚠️  SOME TESTS FAILED. Please check the configuration.')
    }

    return {
      success: overallSuccess,
      results: testResults,
      summary: {
        total: Object.keys(testResults).length,
        passed: Object.values(testResults).filter(r => r.success).length,
        failed: Object.values(testResults).filter(r => !r.success).length
      }
    }
  }
}

// Auto-run tests in development mode
if (import.meta.env.DEV) {
  prodLogger.debug('🔧 Development mode detected - authentication tests available')
  prodLogger.debug('Run authTests.runAllTests() in the console to test the auth system')

  // Make tests available globally in development
  window.authTests = authTests
}

export default authTests
