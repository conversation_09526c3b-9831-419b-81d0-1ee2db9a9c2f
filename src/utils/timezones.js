import { prodLogger } from './prodLogger.js';

/**
 * Comprehensive timezone list for user preferences
 * Organized by regions for better UX
 */

export const TIMEZONE_OPTIONS = [
  // Africa
  { value: "Africa/Abidjan", label: "Abidjan (GMT+0)", region: "Africa" },
  { value: "Africa/Accra", label: "Accra (GMT+0)", region: "Africa" },
  { value: "Africa/Addis_Ababa", label: "Addis Ababa (GMT+3)", region: "Africa" },
  { value: "Africa/Algiers", label: "Algiers (GMT+1)", region: "Africa" },
  { value: "Africa/Cairo", label: "Cairo (GMT+2)", region: "Africa" },
  { value: "Africa/Casablanca", label: "Casablanca (GMT+1)", region: "Africa" },
  { value: "Africa/Johannesburg", label: "Johannesburg (GMT+2)", region: "Africa" },
  { value: "Africa/Lagos", label: "Lagos (GMT+1)", region: "Africa" },
  { value: "Africa/Nairobi", label: "Nairobi (GMT+3)", region: "Africa" },
  { value: "Africa/Tunis", label: "Tunis (GMT+1)", region: "Africa" },

  // Asia
  { value: "Asia/Bangkok", label: "Bangkok (GMT+7)", region: "Asia" },
  { value: "Asia/Beijing", label: "Beijing (GMT+8)", region: "Asia" },
  { value: "Asia/Calcutta", label: "Kolkata (GMT+5:30)", region: "Asia" },
  { value: "Asia/Dubai", label: "Dubai (GMT+4)", region: "Asia" },
  { value: "Asia/Hong_Kong", label: "Hong Kong (GMT+8)", region: "Asia" },
  { value: "Asia/Jakarta", label: "Jakarta (GMT+7)", region: "Asia" },
  { value: "Asia/Jerusalem", label: "Jerusalem (GMT+2)", region: "Asia" },
  { value: "Asia/Karachi", label: "Karachi (GMT+5)", region: "Asia" },
  { value: "Asia/Kuala_Lumpur", label: "Kuala Lumpur (GMT+8)", region: "Asia" },
  { value: "Asia/Manila", label: "Manila (GMT+8)", region: "Asia" },
  { value: "Asia/Seoul", label: "Seoul (GMT+9)", region: "Asia" },
  { value: "Asia/Shanghai", label: "Shanghai (GMT+8)", region: "Asia" },
  { value: "Asia/Singapore", label: "Singapore (GMT+8)", region: "Asia" },
  { value: "Asia/Tokyo", label: "Tokyo (GMT+9)", region: "Asia" },

  // Australia
  { value: "Australia/Adelaide", label: "Adelaide (GMT+9:30)", region: "Australia" },
  { value: "Australia/Brisbane", label: "Brisbane (GMT+10)", region: "Australia" },
  { value: "Australia/Melbourne", label: "Melbourne (GMT+10)", region: "Australia" },
  { value: "Australia/Perth", label: "Perth (GMT+8)", region: "Australia" },
  { value: "Australia/Sydney", label: "Sydney (GMT+10)", region: "Australia" },

  // Europe
  { value: "Europe/Amsterdam", label: "Amsterdam (GMT+1)", region: "Europe" },
  { value: "Europe/Berlin", label: "Berlin (GMT+1)", region: "Europe" },
  { value: "Europe/Brussels", label: "Brussels (GMT+1)", region: "Europe" },
  { value: "Europe/Budapest", label: "Budapest (GMT+1)", region: "Europe" },
  { value: "Europe/Dublin", label: "Dublin (GMT+0)", region: "Europe" },
  { value: "Europe/Istanbul", label: "Istanbul (GMT+3)", region: "Europe" },
  { value: "Europe/London", label: "London (GMT+0)", region: "Europe" },
  { value: "Europe/Madrid", label: "Madrid (GMT+1)", region: "Europe" },
  { value: "Europe/Moscow", label: "Moscow (GMT+3)", region: "Europe" },
  { value: "Europe/Paris", label: "Paris (GMT+1)", region: "Europe" },
  { value: "Europe/Rome", label: "Rome (GMT+1)", region: "Europe" },
  { value: "Europe/Stockholm", label: "Stockholm (GMT+1)", region: "Europe" },
  { value: "Europe/Vienna", label: "Vienna (GMT+1)", region: "Europe" },
  { value: "Europe/Warsaw", label: "Warsaw (GMT+1)", region: "Europe" },
  { value: "Europe/Zurich", label: "Zurich (GMT+1)", region: "Europe" },

  // North America
  { value: "America/Anchorage", label: "Anchorage (GMT-9)", region: "North America" },
  { value: "America/Chicago", label: "Chicago (GMT-6)", region: "North America" },
  { value: "America/Denver", label: "Denver (GMT-7)", region: "North America" },
  { value: "America/Los_Angeles", label: "Los Angeles (GMT-8)", region: "North America" },
  { value: "America/Mexico_City", label: "Mexico City (GMT-6)", region: "North America" },
  { value: "America/New_York", label: "New York (GMT-5)", region: "North America" },
  { value: "America/Phoenix", label: "Phoenix (GMT-7)", region: "North America" },
  { value: "America/Toronto", label: "Toronto (GMT-5)", region: "North America" },
  { value: "America/Vancouver", label: "Vancouver (GMT-8)", region: "North America" },

  // South America
  { value: "America/Argentina/Buenos_Aires", label: "Buenos Aires (GMT-3)", region: "South America" },
  { value: "America/Bogota", label: "Bogota (GMT-5)", region: "South America" },
  { value: "America/Caracas", label: "Caracas (GMT-4)", region: "South America" },
  { value: "America/Lima", label: "Lima (GMT-5)", region: "South America" },
  { value: "America/Santiago", label: "Santiago (GMT-3)", region: "South America" },
  { value: "America/Sao_Paulo", label: "São Paulo (GMT-3)", region: "South America" },

  // UTC and Common
  { value: "UTC", label: "Coordinated Universal Time (UTC)", region: "UTC" },
  { value: "GMT", label: "Greenwich Mean Time (GMT)", region: "UTC" },
];

/**
 * Get timezones grouped by region
 */
export const getTimezonesByRegion = () => {
  const grouped = {};
  TIMEZONE_OPTIONS.forEach(tz => {
    if (!grouped[tz.region]) {
      grouped[tz.region] = [];
    }
    grouped[tz.region].push(tz);
  });
  return grouped;
};

/**
 * Get user's detected timezone
 */
export const getUserTimezone = () => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    prodLogger.warn('Could not detect user timezone:', error);
    return 'UTC';
  }
};

/**
 * Validate if a timezone is supported
 */
export const isValidTimezone = (timezone) => {
  return TIMEZONE_OPTIONS.some(tz => tz.value === timezone);
};

/**
 * Get timezone display name
 */
export const getTimezoneDisplayName = (timezone) => {
  const tz = TIMEZONE_OPTIONS.find(tz => tz.value === timezone);
  return tz ? tz.label : timezone;
};
