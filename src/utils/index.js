/**
 * Utility Module Barrel Exports
 *
 * MINIMAL BARREL EXPORT - Only functions NOT used by DOCX services
 * to prevent circular dependency issues.
 *
 * DOCX services use direct imports for all their dependencies.
 */

// Validation utilities - Only functions NOT used by DOCX services
export { validateFileSize } from "./validation.js";

// Image processing utilities - Only functions NOT used by DOCX services
export { calculateImageDimensions } from "./imageProcessing.js";

// Content processing utilities - Only functions NOT used by DOCX services
export { processHTMLElement } from "./contentProcessing.js";

// Error handling utilities - Only functions NOT used by DOCX services
export {
  shouldRetryError,
  createDetailedErrorReport,
  logError,
  ERROR_CATEGORIES,
} from "./errorHandling.js";

// Logger utilities - Keep for other non-DOCX services
export {
  default as logger,
  debug,
  info,
  warn,
  error,
  critical,
  createLogger,
} from "./logger.js";

// Minimal namespaced exports - Only functions NOT used by DOCX services
export const Validation = {
  validateFileSize,
};

export const ImageProcessing = {
  calculateImageDimensions,
};

export const ContentProcessing = {
  processHTMLElement,
};

export const ErrorHandling = {
  shouldRetryError,
  createDetailedErrorReport,
  logError,
  ERROR_CATEGORIES,
};

// Re-export existing utilities that are already properly organized
export { default as errorMonitor, ErrorSeverity } from "./errorMonitor.js";
export { default as performanceTracker } from "./performanceTracker.js";
export { performanceMonitor } from "./performance.js";

// Minimal default export
export default {
  Validation,
  ImageProcessing,
  ContentProcessing,
  ErrorHandling,
  Logger: {
    logger,
    debug,
    info,
    warn,
    error,
    critical,
    createLogger,
  },
  Monitoring: {
    errorMonitor,
    performanceTracker,
    performanceMonitor,
  },
};
