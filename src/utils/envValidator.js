import { prodLogger } from "./prodLogger.js";

/**
 * Environment Variables Validator
 * Validates that all required environment variables are properly configured
 */

const REQUIRED_ENV_VARS = ["VITE_SUPABASE_URL", "VITE_SUPABASE_ANON_KEY"];

const OPTIONAL_ENV_VARS = [
  "VITE_GEMINI_API_KEY",
  "VITE_ANTHROPIC_API_KEY",
  "VITE_UNSPLASH_ACCESS_KEY",
  "VITE_REPLICATE_API_KEY",
  "VITE_GOOGLE_ANALYTICS_ID",
  "VITE_ADSENSE_ID",
  "VITE_PERPLEXITY_API_KEY",
  "VITE_STRIPE_PUBLISHABLE_KEY",
];

const DANGEROUS_DEFAULT_VALUES = [
  "your-supabase-url-here",
  "your-supabase-anon-key-here",
  "your-gemini-api-key-here",
  "your-anthropic-api-key-here",
  "your-unsplash-access-key-here",
  "your-replicate-api-key-here",
  "your-google-analytics-id-here",
  "your-adsense-id-here",
  "your-perplexity-api-key-here",
  "your-stripe-publishable-key-here",
];

/**
 * Validate environment variables configuration
 * @returns {Object} Validation results
 */
export const validateEnvironment = () => {
  const results = {
    isValid: true,
    errors: [],
    warnings: [],
    missing: [],
    placeholders: [],
    configured: [],
  };

  // Check required variables
  REQUIRED_ENV_VARS.forEach((varName) => {
    const value = import.meta.env[varName];

    if (!value) {
      results.errors.push(`Missing required environment variable: ${varName}`);
      results.missing.push(varName);
      results.isValid = false;
    } else if (DANGEROUS_DEFAULT_VALUES.includes(value)) {
      results.errors.push(
        `Environment variable ${varName} contains placeholder value`
      );
      results.placeholders.push(varName);
      results.isValid = false;
    } else {
      results.configured.push(varName);
    }
  });

  // Check optional variables
  OPTIONAL_ENV_VARS.forEach((varName) => {
    const value = import.meta.env[varName];

    if (!value) {
      results.warnings.push(
        `Optional environment variable not set: ${varName}`
      );
    } else if (DANGEROUS_DEFAULT_VALUES.includes(value)) {
      results.warnings.push(
        `Environment variable ${varName} contains placeholder value`
      );
      results.placeholders.push(varName);
    } else {
      results.configured.push(varName);
    }
  });

  return results;
};

/**
 * Check if environment is production ready
 * @returns {boolean} True if production ready
 */
export const isProductionReady = () => {
  const validation = validateEnvironment();

  // Must have all required variables configured
  if (!validation.isValid) return false;

  // Should have at least one AI service configured
  const aiServices = ["VITE_GEMINI_API_KEY", "VITE_ANTHROPIC_API_KEY"];

  const hasAIService = aiServices.some((service) =>
    validation.configured.includes(service)
  );

  return hasAIService;
};

/**
 * Get environment status summary
 * @returns {Object} Status summary
 */
export const getEnvironmentStatus = () => {
  const validation = validateEnvironment();
  const prodReady = isProductionReady();

  return {
    isValid: validation.isValid,
    isProductionReady: prodReady,
    requiredConfigured: REQUIRED_ENV_VARS.filter((v) =>
      validation.configured.includes(v)
    ).length,
    totalRequired: REQUIRED_ENV_VARS.length,
    optionalConfigured: OPTIONAL_ENV_VARS.filter((v) =>
      validation.configured.includes(v)
    ).length,
    totalOptional: OPTIONAL_ENV_VARS.length,
    errors: validation.errors,
    warnings: validation.warnings,
    recommendations: generateRecommendations(validation),
  };
};

/**
 * Generate recommendations based on validation results
 * @param {Object} validation - Validation results
 * @returns {Array} Array of recommendation strings
 */
const generateRecommendations = (validation) => {
  const recommendations = [];

  if (validation.missing.length > 0) {
    recommendations.push("Set up missing required environment variables");
  }

  if (validation.placeholders.length > 0) {
    recommendations.push("Replace placeholder values with actual API keys");
  }

  if (
    !validation.configured.includes("VITE_GEMINI_API_KEY") &&
    !validation.configured.includes("VITE_ANTHROPIC_API_KEY")
  ) {
    recommendations.push(
      "Configure at least one AI service (Gemini or Anthropic)"
    );
  }

  if (!validation.configured.includes("VITE_UNSPLASH_ACCESS_KEY")) {
    recommendations.push(
      "Configure Unsplash API for image search functionality"
    );
  }

  return recommendations;
};

// Run validation in development
if (import.meta.env.DEV) {
  const status = getEnvironmentStatus();

  if (!status.isValid) {
    prodLogger.error("❌ Environment validation failed:", status.errors);
  } else if (!status.isProductionReady) {
    prodLogger.warn("⚠️ Environment not production ready:", status.warnings);
  } else {
    prodLogger.debug("✅ Environment properly configured");
  }
}

export default validateEnvironment;
