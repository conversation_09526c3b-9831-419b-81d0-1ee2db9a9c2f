/**
 * Preloaded Google Fonts Registry for DocForge AI
 * Centralized registry of Google Fonts available in the admin editor
 * that need special handling during preview generation
 */

import { prodLogger } from "./prodLogger.js";

// Popular Google Fonts available in admin editor (matches TextOverlayEditor.jsx)
export const PRELOADED_GOOGLE_FONTS = [
  // System fonts (always available)
  {
    name: "Arial",
    category: "System",
    fallback: "Arial, sans-serif",
    isSystemFont: true,
  },
  {
    name: "Georgia",
    category: "System",
    fallback: "Georgia, serif",
    isSystemFont: true,
  },
  {
    name: "Times New Roman",
    category: "System",
    fallback: '"Times New Roman", serif',
    isSystemFont: true,
  },
  {
    name: "Helvetica",
    category: "System",
    fallback: "Helvetica, sans-serif",
    isSystemFont: true,
  },

  // Popular Google Fonts (require loading)
  {
    name: "Open Sans",
    category: "Sans Serif",
    fallback: '"Open Sans", sans-serif',
    googleFont: true,
  },
  {
    name: "Roboto",
    category: "Sans Serif",
    fallback: "Roboto, sans-serif",
    googleFont: true,
  },
  {
    name: "Lato",
    category: "Sans Serif",
    fallback: "Lato, sans-serif",
    googleFont: true,
  },
  {
    name: "Montserrat",
    category: "Sans Serif",
    fallback: "Montserrat, sans-serif",
    googleFont: true,
  },
  {
    name: "Source Sans Pro",
    category: "Sans Serif",
    fallback: '"Source Sans Pro", sans-serif',
    googleFont: true,
  },
  {
    name: "Oswald",
    category: "Sans Serif",
    fallback: "Oswald, sans-serif",
    googleFont: true,
  },
  {
    name: "Raleway",
    category: "Sans Serif",
    fallback: "Raleway, sans-serif",
    googleFont: true,
  },
  {
    name: "Poppins",
    category: "Sans Serif",
    fallback: "Poppins, sans-serif",
    googleFont: true,
  },

  // Serif fonts
  {
    name: "Playfair Display",
    category: "Serif",
    fallback: '"Playfair Display", serif',
    googleFont: true,
  },
  {
    name: "Merriweather",
    category: "Serif",
    fallback: "Merriweather, serif",
    googleFont: true,
  },
  {
    name: "Lora",
    category: "Serif",
    fallback: "Lora, serif",
    googleFont: true,
  },
  {
    name: "Crimson Text",
    category: "Serif",
    fallback: '"Crimson Text", serif',
    googleFont: true,
  },

  // Display fonts
  {
    name: "Bebas Neue",
    category: "Display",
    fallback: '"Bebas Neue", cursive',
    googleFont: true,
  },
  {
    name: "Dancing Script",
    category: "Handwriting",
    fallback: '"Dancing Script", cursive',
    googleFont: true,
  },
  {
    name: "Pacifico",
    category: "Handwriting",
    fallback: "Pacifico, cursive",
    googleFont: true,
  },
  {
    name: "Lobster",
    category: "Display",
    fallback: "Lobster, cursive",
    googleFont: true,
  },
];

/**
 * Check if a font is a preloaded Google Font
 * @param {string} fontName - Font family name
 * @returns {boolean} True if it's a preloaded Google Font
 */
export const isPreloadedGoogleFont = (fontName) => {
  return PRELOADED_GOOGLE_FONTS.some(
    (font) => font.name === fontName && font.googleFont === true
  );
};

/**
 * Check if a font is a system font
 * @param {string} fontName - Font family name
 * @returns {boolean} True if it's a system font
 */
export const isSystemFont = (fontName) => {
  return PRELOADED_GOOGLE_FONTS.some(
    (font) => font.name === fontName && font.isSystemFont === true
  );
};

/**
 * Get preloaded Google Font info
 * @param {string} fontName - Font family name
 * @returns {Object|null} Font info or null if not found
 */
export const getPreloadedGoogleFont = (fontName) => {
  return PRELOADED_GOOGLE_FONTS.find((font) => font.name === fontName) || null;
};

/**
 * Get all preloaded Google Fonts (excluding system fonts)
 * @returns {Array} Array of Google Font objects
 */
export const getPreloadedGoogleFonts = () => {
  return PRELOADED_GOOGLE_FONTS.filter((font) => font.googleFont === true);
};

/**
 * Extract preloaded Google Fonts from template text overlays
 * @param {Object} template - Template with text_overlays
 * @returns {Array} Array of preloaded Google Font names used in template
 */
export const extractPreloadedGoogleFontsFromTemplate = (template) => {
  if (!template?.text_overlays?.overlays) {
    return [];
  }

  const preloadedFonts = new Set();

  template.text_overlays.overlays.forEach((overlay) => {
    if (overlay.styling?.fontFamily) {
      const fontName = overlay.styling.fontFamily;
      if (isPreloadedGoogleFont(fontName)) {
        preloadedFonts.add(fontName);
      }
    }
  });

  return Array.from(preloadedFonts);
};

/**
 * Extract preloaded Google Fonts from customizations
 * @param {Object} customizations - User customizations
 * @returns {Array} Array of preloaded Google Font names used in customizations
 */
export const extractPreloadedGoogleFontsFromCustomizations = (
  customizations
) => {
  if (!customizations || typeof customizations !== "object") {
    return [];
  }

  const preloadedFonts = new Set();

  Object.values(customizations).forEach((customization) => {
    if (customization?.styling?.fontFamily) {
      const fontName = customization.styling.fontFamily;
      if (isPreloadedGoogleFont(fontName)) {
        preloadedFonts.add(fontName);
      }
    }
  });

  return Array.from(preloadedFonts);
};

/**
 * Log preloaded Google Fonts usage for debugging
 * @param {Array} fontNames - Array of font names
 * @param {string} context - Context for logging (e.g., 'template', 'customizations')
 */
export const logPreloadedGoogleFontsUsage = (
  fontNames,
  context = "unknown"
) => {
  if (fontNames.length > 0) {
    prodLogger.debug(
      `🔤 Preloaded Google Fonts detected in ${context}:`,
      fontNames
    );
  }
};
