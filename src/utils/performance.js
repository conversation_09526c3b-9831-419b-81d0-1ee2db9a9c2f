// Performance optimization utilities
import React from 'react';

import { prodLogger } from './prodLogger.js';
// Debounce function for search and input handling
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Throttle function for scroll and resize events
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Lazy loading hook for images and components
export const useLazyLoad = (ref, options = {}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const [hasLoaded, setHasLoaded] = React.useState(false);

  // Memoize options to prevent unnecessary re-renders
  const memoizedOptions = React.useMemo(() => ({
    threshold: 0.1,
    rootMargin: '50px',
    ...options
  }), [options.threshold, options.rootMargin]);

  React.useEffect(() => {
    const currentRef = ref.current; // Capture ref value for cleanup

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
        }
      },
      memoizedOptions
    );

    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [ref, hasLoaded, memoizedOptions]);

  return isVisible;
};

// Preload critical resources
export const preloadResource = (href, as = 'script') => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  document.head.appendChild(link);
};

// Optimize images with lazy loading and WebP support
export const optimizeImage = (src, options = {}) => {
  const {
    width,
    height,
    quality = 80,
    format = 'webp'
  } = options;

  // Check if browser supports WebP
  const supportsWebP = () => {
    const canvas = document.createElement('canvas');
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  };

  const optimizedSrc = supportsWebP() && format === 'webp' 
    ? src.replace(/\.(jpg|jpeg|png)$/i, '.webp')
    : src;

  return {
    src: optimizedSrc,
    loading: 'lazy',
    ...(width && { width }),
    ...(height && { height })
  };
};

// Virtual scrolling for large lists
export const useVirtualScroll = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = React.useState(0);
  
  const visibleStart = Math.floor(scrollTop / itemHeight);
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  );
  
  const visibleItems = items.slice(visibleStart, visibleEnd);
  
  const totalHeight = items.length * itemHeight;
  const offsetY = visibleStart * itemHeight;
  
  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop
  };
};

// Memoization helper for expensive calculations
export const memoize = (fn) => {
  const cache = new Map();
  
  return (...args) => {
    const key = JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = fn(...args);
    cache.set(key, result);
    
    return result;
  };
};

// Bundle splitting helper
export const loadComponent = (componentPath) => {
  return React.lazy(() => import(componentPath));
};

// Performance monitoring
export const performanceMonitor = {
  // Measure component render time
  measureRender: (componentName, renderFn) => {
    const start = performance.now();
    const result = renderFn();
    const end = performance.now();
    
    if (process.env.NODE_ENV === 'development') {
      prodLogger.debug(`${componentName} render time: ${end - start}ms`);
    }
    
    return result;
  },

  // Measure API call time
  measureAPI: async (apiName, apiCall) => {
    const start = performance.now();
    try {
      const result = await apiCall();
      const end = performance.now();
      
      if (process.env.NODE_ENV === 'development') {
        prodLogger.debug(`${apiName} API call time: ${end - start}ms`);
      }
      
      return result;
    } catch (error) {
      const end = performance.now();
      prodLogger.error(`${apiName} API call failed after ${end - start}ms:`, error);
      throw error;
    }
  },

  // Monitor memory usage
  checkMemory: () => {
    if (performance.memory) {
      const { usedJSHeapSize, totalJSHeapSize, jsHeapSizeLimit } = performance.memory;
      
      if (process.env.NODE_ENV === 'development') {
        prodLogger.debug('Memory usage:', {
          used: `${Math.round(usedJSHeapSize / 1048576)} MB`,
          total: `${Math.round(totalJSHeapSize / 1048576)} MB`,
          limit: `${Math.round(jsHeapSizeLimit / 1048576)} MB`
        });
      }
      
      return {
        usedMB: Math.round(usedJSHeapSize / 1048576),
        totalMB: Math.round(totalJSHeapSize / 1048576),
        limitMB: Math.round(jsHeapSizeLimit / 1048576)
      };
    }
    return null;
  }
};

// CSS optimization helpers
export const cssOptimization = {
  // Remove unused CSS classes (development helper)
  findUnusedClasses: (usedClasses) => {
    const allStyleSheets = Array.from(document.styleSheets);
    const unusedClasses = [];
    
    allStyleSheets.forEach(sheet => {
      try {
        const rules = Array.from(sheet.cssRules || sheet.rules);
        rules.forEach(rule => {
          if (rule.selectorText) {
            const classes = rule.selectorText.match(/\.[a-zA-Z0-9_-]+/g);
            if (classes) {
              classes.forEach(className => {
                const cleanClass = className.substring(1);
                if (!usedClasses.includes(cleanClass)) {
                  unusedClasses.push(cleanClass);
                }
              });
            }
          }
        });
      } catch (e) {
        // Cross-origin stylesheets can't be accessed
      }
    });
    
    return [...new Set(unusedClasses)];
  },

  // Critical CSS extraction helper
  extractCriticalCSS: (selector) => {
    const element = document.querySelector(selector);
    if (!element) return '';
    
    const computedStyle = window.getComputedStyle(element);
    const criticalProperties = [
      'display', 'position', 'top', 'left', 'width', 'height',
      'margin', 'padding', 'border', 'background', 'color',
      'font-family', 'font-size', 'font-weight', 'line-height'
    ];
    
    let css = `${selector} {\n`;
    criticalProperties.forEach(prop => {
      const value = computedStyle.getPropertyValue(prop);
      if (value && value !== 'initial' && value !== 'normal') {
        css += `  ${prop}: ${value};\n`;
      }
    });
    css += '}\n';
    
    return css;
  }
};

// Animation performance helpers
export const animationHelpers = {
  // Use requestAnimationFrame for smooth animations
  animate: (callback, duration = 1000) => {
    const start = performance.now();
    
    const frame = (currentTime) => {
      const elapsed = currentTime - start;
      const progress = Math.min(elapsed / duration, 1);
      
      callback(progress);
      
      if (progress < 1) {
        requestAnimationFrame(frame);
      }
    };
    
    requestAnimationFrame(frame);
  },

  // Easing functions for smooth animations
  easing: {
    linear: t => t,
    easeInQuad: t => t * t,
    easeOutQuad: t => t * (2 - t),
    easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
    easeInCubic: t => t * t * t,
    easeOutCubic: t => (--t) * t * t + 1,
    easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
  }
};
