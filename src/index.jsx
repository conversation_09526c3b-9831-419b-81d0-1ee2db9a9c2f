import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import "./styles/tailwind.css";
import "./styles/index.css";

// Import debug utilities in development
if (import.meta.env.DEV) {
  // DISABLE_DEBUG_UTILS flag - set to true to disable debug utilities
  const DISABLE_DEBUG_UTILS = true;
  
  // Only load minimal environment checker
  window.checkEnv = () => {
    return import.meta.env.DEV ? 'Development Mode' : 'Production Mode';
  };
  
  // Only load other debug utilities if explicitly enabled
  if (!DISABLE_DEBUG_UTILS) {
    // Check environment variables
    import('./utils/envChecker.js');
    
    // Removed supabaseDebug - utility was deleted during cleanup

    // Load auth test utilities
    import('./utils/authTest.js').then((module) => {
      window.authTests = module.authTests;
    });

    // Load Unsplash test utilities
    import('./services/unsplashService.js').then((module) => {
      window.testUnsplashIntegration = module.testUnsplashIntegration;
      window.getUnsplashStatus = module.getUnsplashStatus;
    });
  }
}

// Register service worker in production for PWA/offline support
if (import.meta.env.PROD && typeof navigator !== "undefined" && "serviceWorker" in navigator) {
  window.addEventListener("load", () => {
    navigator.serviceWorker
      .register("/sw.js")
      .catch(() => {
        // Avoid noisy logs in production
      });
  });
}

const container = document.getElementById("root");
const root = createRoot(container);

root.render(<App />);
