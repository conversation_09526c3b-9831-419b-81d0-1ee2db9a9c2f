import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import notificationService, {
  createSuccessNotification,
  createErrorNotification,
  createWarningNotification,
  createInfoNotification,
  createLoadingNotification,
  createProjectNotification,
  createTemplateNotification,
  createExportNotification,
  createUploadNotification,
  createGenerationNotification,
  createFontNotification,
  NOTIFICATION_TYPES_EXTENDED,
  NOTIFICATION_CATEGORIES,
  NOTIFICATION_PRIORITIES
} from '../services/notificationService.js';

/**
 * Notification Context for DocForge AI
 * 
 * Provides centralized notification state management and methods
 * throughout the React component tree with performance optimization.
 */

const NotificationContext = createContext(null);

/**
 * Notification Provider Component
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 */
export const NotificationProvider = ({ children }) => {
  // State for notifications and toasts
  const [state, setState] = useState(() => notificationService.getState());
  const [isInitialized, setIsInitialized] = useState(false);

  // Subscribe to notification service changes
  useEffect(() => {
    const unsubscribe = notificationService.subscribe((event, data, newState) => {
      setState(newState);
    });

    // Mark as initialized after first subscription
    setIsInitialized(true);

    return unsubscribe;
  }, []);

  // Memoized notification methods to prevent unnecessary re-renders
  const addNotification = useCallback((notification) => {
    return notificationService.add(notification);
  }, []);

  const removeNotification = useCallback((id) => {
    return notificationService.remove(id);
  }, []);

  const markAsRead = useCallback((id) => {
    return notificationService.markAsRead(id);
  }, []);

  const markAllAsRead = useCallback(() => {
    return notificationService.markAllAsRead();
  }, []);

  const clearNotifications = useCallback(() => {
    return notificationService.clear();
  }, []);

  const removeToast = useCallback((id) => {
    return notificationService.removeToast(id);
  }, []);

  // Convenience methods for different notification types
  const showSuccess = useCallback((title, message, options = {}) => {
    const notification = createSuccessNotification(title, message, options);
    return addNotification(notification);
  }, [addNotification]);

  const showError = useCallback((title, message, options = {}) => {
    const notification = createErrorNotification(title, message, options);
    return addNotification(notification);
  }, [addNotification]);

  const showWarning = useCallback((title, message, options = {}) => {
    const notification = createWarningNotification(title, message, options);
    return addNotification(notification);
  }, [addNotification]);

  const showInfo = useCallback((title, message, options = {}) => {
    const notification = createInfoNotification(title, message, options);
    return addNotification(notification);
  }, [addNotification]);

  const showLoading = useCallback((title, message, options = {}) => {
    const notification = createLoadingNotification(title, message, options);
    return addNotification(notification);
  }, [addNotification]);

  // Category-specific notification methods
  const showProjectNotification = useCallback((type, title, message, options = {}) => {
    const notification = createProjectNotification(type, title, message, options);
    return addNotification(notification);
  }, [addNotification]);

  const showTemplateNotification = useCallback((type, title, message, options = {}) => {
    const notification = createTemplateNotification(type, title, message, options);
    return addNotification(notification);
  }, [addNotification]);

  const showExportNotification = useCallback((type, title, message, options = {}) => {
    const notification = createExportNotification(type, title, message, options);
    return addNotification(notification);
  }, [addNotification]);

  const showUploadNotification = useCallback((type, title, message, options = {}) => {
    const notification = createUploadNotification(type, title, message, options);
    return addNotification(notification);
  }, [addNotification]);

  const showGenerationNotification = useCallback((type, title, message, options = {}) => {
    const notification = createGenerationNotification(type, title, message, options);
    return addNotification(notification);
  }, [addNotification]);

  const showFontNotification = useCallback((type, title, message, options = {}) => {
    const notification = createFontNotification(type, title, message, options);
    return addNotification(notification);
  }, [addNotification]);

  // Filter methods
  const getByCategory = useCallback((category) => {
    return notificationService.getByCategory(category);
  }, []);

  const getByType = useCallback((type) => {
    return notificationService.getByType(type);
  }, []);

  // Memoized context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    // State
    notifications: state.notifications,
    toasts: state.toasts,
    unreadCount: state.unreadCount,
    totalCount: state.totalCount,
    isInitialized,

    // Core methods
    addNotification,
    removeNotification,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    removeToast,

    // Convenience methods
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,

    // Category-specific methods
    showProjectNotification,
    showTemplateNotification,
    showExportNotification,
    showUploadNotification,
    showGenerationNotification,
    showFontNotification,

    // Filter methods
    getByCategory,
    getByType,

    // Constants
    NOTIFICATION_TYPES: NOTIFICATION_TYPES_EXTENDED,
    NOTIFICATION_CATEGORIES,
    NOTIFICATION_PRIORITIES
  }), [
    state,
    isInitialized,
    addNotification,
    removeNotification,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    removeToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,
    showProjectNotification,
    showTemplateNotification,
    showExportNotification,
    showUploadNotification,
    showGenerationNotification,
    showFontNotification,
    getByCategory,
    getByType
  ]);

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};

/**
 * Hook to use notification context
 * @returns {Object} Notification context value
 * @throws {Error} If used outside NotificationProvider
 */
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  
  return context;
};

/**
 * Hook for toast notifications only
 * @returns {Object} Toast-specific methods and state
 */
export const useToasts = () => {
  const { toasts, removeToast, showSuccess, showError, showWarning, showInfo } = useNotifications();
  
  return {
    toasts,
    removeToast,
    showSuccess,
    showError,
    showWarning,
    showInfo
  };
};

/**
 * Hook for notification center functionality
 * @returns {Object} Notification center methods and state
 */
export const useNotificationCenter = () => {
  const {
    notifications,
    unreadCount,
    totalCount,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    removeNotification,
    getByCategory,
    getByType
  } = useNotifications();
  
  return {
    notifications,
    unreadCount,
    totalCount,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    removeNotification,
    getByCategory,
    getByType
  };
};

export default NotificationContext;
