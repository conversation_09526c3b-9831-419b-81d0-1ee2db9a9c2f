import React from 'react';
import { NotificationProvider as ContextProvider } from '../../contexts/NotificationContext.jsx';
import ToastContainer from './ToastContainer.jsx';

/**
 * Notification Provider Wrapper
 * 
 * Combines the notification context provider with the toast container
 * to provide a complete notification system setup.
 */
const NotificationProvider = ({ children }) => {
  return (
    <ContextProvider>
      {children}
      <ToastContainer />
    </ContextProvider>
  );
};

export default NotificationProvider;
