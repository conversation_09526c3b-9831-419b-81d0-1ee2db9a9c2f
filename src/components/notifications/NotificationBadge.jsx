import React from 'react';

/**
 * Notification Badge Component
 * 
 * Displays unread notification count with consistent styling
 * that matches the existing design system.
 */
const NotificationBadge = ({ 
  count, 
  maxCount = 99, 
  className = '',
  size = 'default' // 'small', 'default', 'large'
}) => {
  if (!count || count <= 0) return null;

  const displayCount = count > maxCount ? `${maxCount}+` : count;
  
  const sizeClasses = {
    small: 'w-4 h-4 text-xs',
    default: 'w-5 h-5 text-xs',
    large: 'w-6 h-6 text-sm'
  };

  return (
    <span 
      className={`
        absolute -top-1 -right-1 
        ${sizeClasses[size]}
        bg-error text-error-foreground 
        rounded-full flex items-center justify-center 
        font-medium border-2 border-surface
        animate-pulse-subtle
        ${className}
      `}
      aria-label={`${count} unread notifications`}
    >
      {displayCount}
    </span>
  );
};

export default NotificationBadge;
