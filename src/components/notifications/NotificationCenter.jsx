import React, { useState, useRef, useEffect } from 'react';
import Icon from '../AppIcon';
import Button from '../ui/Button';
import NotificationItem from './NotificationItem.jsx';
import NotificationBadge from './NotificationBadge.jsx';
import { useNotificationCenter } from '../../contexts/NotificationContext.jsx';

/**
 * Notification Center Component
 * 
 * Replaces the existing StatusNotification component with a unified
 * notification system that integrates with the notification service.
 */
const NotificationCenter = ({ className = '' }) => {
  const {
    notifications,
    unreadCount,
    totalCount,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    removeNotification,
    getByCategory
  } = useNotificationCenter();

  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        !buttonRef.current?.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Handle notification click
  const handleNotificationClick = (notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }

    // Handle specific notification actions based on category
    switch (notification.category) {
      case 'project':
        // Could navigate to project
        break;
      case 'template':
        // Could open template selector
        break;
      case 'export':
        // Could show export status
        break;
      default:
        break;
    }
  };

  // Get filtered notifications
  const filteredNotifications = selectedCategory === 'all' 
    ? notifications 
    : getByCategory(selectedCategory);

  // Get unique categories for filter
  const categories = ['all', ...new Set(notifications.map(n => n.category))];

  return (
    <div className={`relative ${className}`}>
      {/* Notification Bell Button */}
      <Button
        ref={buttonRef}
        variant="ghost"
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 relative"
        aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ''}`}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <Icon name="Bell" size={18} />
        <NotificationBadge count={unreadCount} />
      </Button>

      {/* Notification Panel */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute right-0 mt-2 w-96 bg-surface rounded-lg shadow-elevation-3 border border-border z-1100 max-h-96 overflow-hidden animate-slide-in-down"
          role="dialog"
          aria-label="Notifications panel"
        >
          {/* Header */}
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-semibold text-text-primary">
                Notifications
                {totalCount > 0 && (
                  <span className="ml-2 text-xs text-text-secondary">
                    ({totalCount})
                  </span>
                )}
              </h3>
              <div className="flex items-center space-x-2">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    onClick={markAllAsRead}
                    className="text-xs px-2 py-1"
                    aria-label="Mark all notifications as read"
                  >
                    Mark all read
                  </Button>
                )}
                <Button
                  variant="ghost"
                  onClick={clearNotifications}
                  className="text-xs px-2 py-1"
                  aria-label="Clear all notifications"
                >
                  Clear all
                </Button>
              </div>
            </div>

            {/* Category Filter */}
            {categories.length > 2 && (
              <div className="flex flex-wrap gap-1">
                {categories.map(category => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "secondary" : "ghost"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className="text-xs px-2 py-1 h-auto capitalize"
                  >
                    {category}
                  </Button>
                ))}
              </div>
            )}
          </div>

          {/* Notifications List */}
          <div className="max-h-80 overflow-y-auto">
            {filteredNotifications.length === 0 ? (
              <div className="p-8 text-center">
                <Icon name="Bell" size={32} className="mx-auto text-text-secondary mb-2" />
                <p className="text-sm text-text-secondary">
                  {selectedCategory === 'all' 
                    ? 'No notifications' 
                    : `No ${selectedCategory} notifications`
                  }
                </p>
              </div>
            ) : (
              <div className="divide-y divide-border">
                {filteredNotifications.map((notification) => (
                  <div key={notification.id} className="group">
                    <NotificationItem
                      notification={notification}
                      onClick={handleNotificationClick}
                      onDismiss={removeNotification}
                      variant="default"
                      showDismiss={true}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {filteredNotifications.length > 0 && (
            <div className="p-3 border-t border-border bg-background/50">
              <p className="text-xs text-text-secondary text-center">
                {selectedCategory === 'all' 
                  ? `${totalCount} total notifications`
                  : `${filteredNotifications.length} ${selectedCategory} notifications`
                }
              </p>
            </div>
          )}
        </div>
      )}

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-1050"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}
    </div>
  );
};

export default NotificationCenter;
