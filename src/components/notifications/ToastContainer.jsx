import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { useToasts } from '../../contexts/NotificationContext.jsx';
import NotificationItem from './NotificationItem.jsx';

/**
 * Toast Container Component
 * 
 * Manages the display of toast notifications with automatic dismissal,
 * stacking, and smooth animations.
 */
const ToastContainer = () => {
  const { toasts, removeToast } = useToasts();
  const [visibleToasts, setVisibleToasts] = useState([]);

  // Manage toast visibility and auto-dismissal
  useEffect(() => {
    const newVisibleToasts = toasts.map(toast => ({
      ...toast,
      isVisible: true
    }));

    setVisibleToasts(newVisibleToasts);

    // Set up auto-dismissal timers
    const timers = toasts.map(toast => {
      if (toast.duration && toast.duration > 0) {
        return setTimeout(() => {
          handleToastDismiss(toast.id);
        }, toast.duration);
      }
      return null;
    }).filter(Boolean);

    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [toasts]);

  const handleToastDismiss = (id) => {
    // First, mark as not visible for animation
    setVisibleToasts(prev => 
      prev.map(toast => 
        toast.id === id ? { ...toast, isVisible: false } : toast
      )
    );

    // Then remove after animation completes
    setTimeout(() => {
      removeToast(id);
    }, 300);
  };

  if (visibleToasts.length === 0) return null;

  const toastElements = (
    <div 
      className="fixed top-20 right-4 z-1200 space-y-2 pointer-events-none"
      role="region"
      aria-label="Notifications"
      aria-live="polite"
    >
      {visibleToasts.map((toast, index) => (
        <div
          key={toast.id}
          className={`
            pointer-events-auto
            transform transition-all duration-300 ease-out
            ${toast.isVisible 
              ? 'translate-x-0 opacity-100 scale-100' 
              : 'translate-x-full opacity-0 scale-95'
            }
          `}
          style={{
            transitionDelay: `${index * 50}ms`
          }}
        >
          <NotificationItem
            notification={toast}
            onDismiss={handleToastDismiss}
            variant="toast"
            showDismiss={true}
            className="animate-slide-in-right"
          />
        </div>
      ))}
    </div>
  );

  // Render toasts in a portal to ensure proper z-index stacking
  return createPortal(toastElements, document.body);
};

export default ToastContainer;
