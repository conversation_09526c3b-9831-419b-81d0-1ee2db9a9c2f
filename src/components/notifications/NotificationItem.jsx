import React from 'react';
import Icon from '../AppIcon';
import Button from '../ui/Button';

/**
 * Individual Notification Item Component
 * 
 * Renders a single notification with consistent styling and interactions.
 * Used in both the notification center and toast notifications.
 */
const NotificationItem = ({
  notification,
  onDismiss,
  onClick,
  showDismiss = true,
  variant = 'default', // 'default', 'toast', 'compact'
  className = ''
}) => {
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return { name: 'CheckCircle', color: 'var(--color-success)' };
      case 'warning':
        return { name: 'AlertTriangle', color: 'var(--color-warning)' };
      case 'error':
        return { name: 'AlertCircle', color: 'var(--color-error)' };
      case 'loading':
        return { name: 'Loader2', color: 'var(--color-secondary)' };
      case 'progress':
        return { name: 'Clock', color: 'var(--color-secondary)' };
      case 'info':
      default:
        return { name: 'Info', color: 'var(--color-secondary)' };
    }
  };

  const getNotificationBgColor = (type) => {
    switch (type) {
      case 'success':
        return 'bg-success/10 border-success/20';
      case 'warning':
        return 'bg-warning/10 border-warning/20';
      case 'error':
        return 'bg-error/10 border-error/20';
      case 'loading':
        return 'bg-secondary/10 border-secondary/20';
      case 'progress':
        return 'bg-secondary/10 border-secondary/20';
      case 'info':
      default:
        return 'bg-secondary/10 border-secondary/20';
    }
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const notificationTime = new Date(timestamp);
    const diff = now - notificationTime;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const handleClick = () => {
    onClick?.(notification);
  };

  const handleDismiss = (e) => {
    e.stopPropagation();
    onDismiss?.(notification.id);
  };

  const iconConfig = getNotificationIcon(notification.type);
  const bgColorClass = getNotificationBgColor(notification.type);

  // Variant-specific styling
  const variantClasses = {
    default: 'p-4 hover:bg-background cursor-pointer transition-micro',
    toast: 'p-4 rounded-lg shadow-elevation-3 border max-w-sm bg-surface',
    compact: 'p-3 hover:bg-background cursor-pointer transition-micro'
  };

  const iconContainerClasses = {
    default: 'p-1.5 rounded-lg',
    toast: 'p-1.5 rounded-lg',
    compact: 'p-1 rounded'
  };

  const contentClasses = {
    default: 'flex-1 min-w-0',
    toast: 'flex-1',
    compact: 'flex-1 min-w-0'
  };

  return (
    <div
      className={`
        ${variantClasses[variant]}
        ${!notification.isRead && variant === 'default' ? 'bg-secondary/5' : ''}
        ${variant === 'toast' ? bgColorClass : ''}
        ${className}
      `}
      onClick={variant !== 'toast' ? handleClick : undefined}
      role={variant !== 'toast' ? 'button' : undefined}
      tabIndex={variant !== 'toast' ? 0 : undefined}
      onKeyDown={variant !== 'toast' ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick();
        }
      } : undefined}
    >
      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className={`${iconContainerClasses[variant]} ${variant === 'toast' ? bgColorClass : 'bg-secondary/10'}`}>
          <Icon 
            name={iconConfig.name} 
            size={variant === 'compact' ? 14 : 16} 
            color={iconConfig.color}
            className={notification.type === 'loading' ? 'animate-spin' : ''}
          />
        </div>

        {/* Content */}
        <div className={contentClasses[variant]}>
          <div className="flex items-start justify-between">
            <p className={`
              text-sm text-text-primary
              ${!notification.isRead && variant === 'default' ? 'font-medium' : ''}
              ${variant === 'toast' ? 'font-medium' : ''}
            `}>
              {notification.title}
            </p>
            
            {/* Unread indicator and dismiss button */}
            <div className="flex items-center space-x-1 ml-2">
              {!notification.isRead && variant === 'default' && (
                <div className="w-2 h-2 bg-secondary rounded-full flex-shrink-0"></div>
              )}
              {showDismiss && (
                <Button
                  variant="ghost"
                  onClick={handleDismiss}
                  className={`
                    p-1 flex-shrink-0
                    ${variant === 'default' ? 'opacity-0 group-hover:opacity-100 transition-opacity' : ''}
                  `}
                  aria-label="Dismiss notification"
                >
                  <Icon name="X" size={variant === 'compact' ? 10 : 12} />
                </Button>
              )}
            </div>
          </div>

          {/* Message */}
          {notification.message && (
            <p className={`
              text-xs text-text-secondary mt-1
              ${variant === 'compact' ? 'line-clamp-1' : 'line-clamp-2'}
            `}>
              {notification.message}
            </p>
          )}

          {/* Timestamp and category */}
          {variant !== 'compact' && (
            <div className="flex items-center justify-between mt-2">
              <p className="text-xs text-text-secondary">
                {formatTimestamp(notification.timestamp)}
              </p>
              {notification.category && notification.category !== 'system' && (
                <span className="text-xs text-text-secondary capitalize">
                  {notification.category}
                </span>
              )}
            </div>
          )}

          {/* Actions */}
          {notification.actions && notification.actions.length > 0 && variant !== 'compact' && (
            <div className="flex flex-wrap gap-2 mt-2">
              {notification.actions.slice(0, 2).map((action, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  size="sm"
                  className="text-xs px-2 py-1 h-auto"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Handle action click
                  }}
                >
                  {action}
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationItem;
