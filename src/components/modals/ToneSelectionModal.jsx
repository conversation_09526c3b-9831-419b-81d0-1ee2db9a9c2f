/**
 * Content Preview Modal for DocGenerate
 * Shows preview of content before processing
 */

import React, { useState, useEffect } from 'react';

const ContentPreviewModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  currentText = '',
  operation = 'rewrite',
  title = 'Process Content'
}) => {

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  // Handle clicking outside modal
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleCancel();
    }
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        handleCancel();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-sm font-bold">✨</span>
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
              <p className="text-sm text-gray-600">
                {operation === 'rewrite' && 'Rewrite the selected content'}
                {operation === 'fix-grammar' && 'Fix grammar and spelling issues'}
                {operation === 'reduce' && 'Make the content more concise'}
                {operation === 'expand' && 'Add more detail and context'}
              </p>
            </div>
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close modal"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content Preview */}
        {currentText && (
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Selected text:</h3>
            <div className="bg-gray-50 rounded-lg p-4 text-sm text-gray-700 max-h-32 overflow-y-auto border">
              {currentText.length > 200 
                ? `${currentText.substring(0, 200)}...` 
                : currentText
              }
            </div>
            <p className="text-xs text-gray-500 mt-2">
              {currentText.length} characters selected
            </p>
          </div>
        )}

        {/* Operation Info */}
        <div className="p-6">
          <h3 className="text-sm font-medium text-gray-900 mb-3">What will happen:</h3>
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <div className="flex items-start space-x-3">
              <div className="w-5 h-5 text-blue-500 mt-0.5">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="flex-1">
                <p className="text-sm text-blue-800">
                  {operation === 'rewrite' && 'AI will rewrite your content while preserving the original meaning and improving clarity.'}
                  {operation === 'fix-grammar' && 'AI will correct grammar, spelling, and punctuation errors in your content.'}
                  {operation === 'reduce' && 'AI will make your content more concise by removing unnecessary words while keeping the key points.'}
                  {operation === 'expand' && 'AI will add more detail, examples, and context to make your content more comprehensive.'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={handleCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors"
          >
            {operation === 'rewrite' && 'Rewrite Content'}
            {operation === 'fix-grammar' && 'Fix Grammar'}
            {operation === 'reduce' && 'Make Concise'}
            {operation === 'expand' && 'Expand Content'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContentPreviewModal;
