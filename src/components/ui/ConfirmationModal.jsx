import React, { useEffect } from 'react';
import Icon from '../AppIcon';
import Button from './Button';

/**
 * ConfirmationModal Component
 * 
 * A production-ready modal for confirmation dialogs, specifically designed
 * for destructive actions like deletions. Follows the existing design system
 * and provides proper accessibility features.
 */
const ConfirmationModal = ({
  isOpen = false,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  confirmText = 'Delete',
  cancelText = 'Cancel',
  type = 'danger', // 'danger' | 'warning'
  isLoading = false,
  icon = null, // Custom icon override
  size = 'sm'
}) => {
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && !isLoading) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Focus management - focus the modal when it opens
      const modalElement = document.querySelector('[data-confirmation-modal]');
      if (modalElement) {
        modalElement.focus();
      }
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, isLoading, onClose]);

  if (!isOpen) return null;

  // Get icon based on type
  const getIcon = () => {
    if (icon) return icon;
    
    switch (type) {
      case 'danger':
        return { name: 'AlertTriangle', color: 'text-error', size: 48 };
      case 'warning':
        return { name: 'AlertCircle', color: 'text-warning', size: 48 };
      default:
        return { name: 'AlertTriangle', color: 'text-error', size: 48 };
    }
  };

  // Get button variant based on type
  const getConfirmVariant = () => {
    switch (type) {
      case 'danger':
        return 'danger';
      case 'warning':
        return 'warning';
      default:
        return 'danger';
    }
  };

  const iconConfig = getIcon();

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget && !isLoading) {
      onClose();
    }
  };

  // Handle confirm with loading state
  const handleConfirm = async () => {
    if (onConfirm && !isLoading) {
      await onConfirm();
    }
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'max-w-md';
      case 'md':
        return 'max-w-lg';
      case 'lg':
        return 'max-w-2xl';
      default:
        return 'max-w-md';
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="confirmation-title"
      aria-describedby="confirmation-message"
    >
      <div 
        className={`bg-white rounded-xl shadow-2xl ${getSizeClasses()} w-full transform transition-all duration-300 scale-100`}
        data-confirmation-modal
        tabIndex={-1}
      >
        {/* Content */}
        <div className="p-6">
          <div className="text-center">
            {/* Icon */}
            <div className="mx-auto mb-4">
              <Icon 
                name={iconConfig.name} 
                size={iconConfig.size} 
                className={`${iconConfig.color} mx-auto`}
              />
            </div>

            {/* Title */}
            <h3 
              id="confirmation-title"
              className="text-lg font-semibold text-text-primary mb-2"
            >
              {title}
            </h3>

            {/* Message */}
            <p 
              id="confirmation-message"
              className="text-text-secondary mb-6 leading-relaxed"
            >
              {message}
            </p>

            {/* Action Buttons */}
            <div className="flex gap-3 justify-center sm:justify-end">
              <Button
                variant="secondary"
                onClick={onClose}
                disabled={isLoading}
                className="min-w-[80px]"
              >
                {cancelText}
              </Button>
              <Button
                variant={getConfirmVariant()}
                onClick={handleConfirm}
                loading={isLoading}
                disabled={isLoading}
                className="min-w-[80px]"
                autoFocus
              >
                {confirmText}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
