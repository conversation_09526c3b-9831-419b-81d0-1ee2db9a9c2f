/**
 * Service Status Indicator Component
 * 
 * Displays real-time status of AI services and provides proactive notifications
 * about service availability, quota usage, and potential issues.
 */

import React, { useState, useEffect } from 'react';
import { CheckCircle, AlertCircle, Clock, Zap, Settings, RefreshCw } from 'lucide-react';
import { getAllServiceStatuses, getCriticalServiceStatus } from '../../services/userNotificationService.js';

const ServiceStatusIndicator = ({ 
  showDetails = false, 
  services = ['gemini', 'replicate', 'unsplash'],
  className = ''
}) => {
  const [serviceStatuses, setServiceStatuses] = useState({});
  const [criticalStatus, setCriticalStatus] = useState({ allAvailable: true, unavailableServices: [] });
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    // Initial status check
    updateStatuses();
    
    // Set up periodic status checks
    const interval = setInterval(updateStatuses, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  const updateStatuses = () => {
    const statuses = getAllServiceStatuses();
    const critical = getCriticalServiceStatus();
    
    setServiceStatuses(statuses);
    setCriticalStatus(critical);
  };

  const getServiceIcon = (serviceName, status) => {
    if (!status.available) {
      if (status.quotaExhausted) {
        return <Clock className="w-4 h-4 text-orange-500" />;
      } else if (status.temporaryFailure) {
        return <RefreshCw className="w-4 h-4 text-yellow-500" />;
      } else if (status.reason === 'API key not configured') {
        return <Settings className="w-4 h-4 text-gray-500" />;
      } else {
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      }
    }
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  const getServiceStatusText = (status) => {
    if (!status.available) {
      if (status.quotaExhausted) return 'Quota Exhausted';
      if (status.temporaryFailure) return 'Temporary Issue';
      if (status.reason === 'API key not configured') return 'Not Configured';
      if (status.serviceUnavailable) return 'Service Down';
      return 'Unavailable';
    }
    return 'Available';
  };

  const getServiceStatusColor = (status) => {
    if (!status.available) {
      if (status.quotaExhausted) return 'text-orange-600';
      if (status.temporaryFailure) return 'text-yellow-600';
      if (status.reason === 'API key not configured') return 'text-gray-600';
      return 'text-red-600';
    }
    return 'text-green-600';
  };

  const getOverallStatusColor = () => {
    if (!criticalStatus.allAvailable) {
      if (criticalStatus.partiallyAvailable) return 'bg-yellow-100 border-yellow-300';
      return 'bg-red-100 border-red-300';
    }
    return 'bg-green-100 border-green-300';
  };

  const getOverallStatusIcon = () => {
    if (!criticalStatus.allAvailable) {
      if (criticalStatus.partiallyAvailable) {
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      }
      return <AlertCircle className="w-5 h-5 text-red-600" />;
    }
    return <CheckCircle className="w-5 h-5 text-green-600" />;
  };

  const getOverallStatusText = () => {
    if (!criticalStatus.allAvailable) {
      if (criticalStatus.partiallyAvailable) {
        return 'Some Services Limited';
      }
      return 'Services Unavailable';
    }
    return 'All Services Available';
  };

  const formatServiceName = (serviceName) => {
    const names = {
      gemini: 'Gemini AI',
      replicate: 'Replicate',
      unsplash: 'Unsplash'
    };
    return names[serviceName] || serviceName;
  };

  if (!showDetails) {
    // Compact status indicator
    return (
      <div 
        className={`inline-flex items-center px-3 py-1 rounded-full border cursor-pointer transition-colors ${getOverallStatusColor()} ${className}`}
        onClick={() => setIsExpanded(!isExpanded)}
        title="Click to view service details"
      >
        {getOverallStatusIcon()}
        <span className="ml-2 text-sm font-medium">
          {getOverallStatusText()}
        </span>
        
        {isExpanded && (
          <div className="absolute top-full left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4">
            <h4 className="text-sm font-semibold text-gray-900 mb-3">Service Status</h4>
            <div className="space-y-2">
              {services.map(serviceName => {
                const status = serviceStatuses[serviceName] || { available: false };
                return (
                  <div key={serviceName} className="flex items-center justify-between">
                    <div className="flex items-center">
                      {getServiceIcon(serviceName, status)}
                      <span className="ml-2 text-sm text-gray-700">
                        {formatServiceName(serviceName)}
                      </span>
                    </div>
                    <span className={`text-xs font-medium ${getServiceStatusColor(status)}`}>
                      {getServiceStatusText(status)}
                    </span>
                  </div>
                );
              })}
            </div>
            <div className="mt-3 pt-3 border-t border-gray-200">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  updateStatuses();
                }}
                className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                Refresh Status
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Detailed status display
  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Service Status</h3>
        <button
          onClick={updateStatuses}
          className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
        >
          <RefreshCw className="w-4 h-4 mr-1" />
          Refresh
        </button>
      </div>

      <div className="space-y-4">
        {services.map(serviceName => {
          const status = serviceStatuses[serviceName] || { available: false };
          
          return (
            <div key={serviceName} className="border border-gray-200 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  {getServiceIcon(serviceName, status)}
                  <h4 className="ml-2 text-sm font-medium text-gray-900">
                    {formatServiceName(serviceName)}
                  </h4>
                </div>
                <span className={`text-sm font-medium ${getServiceStatusColor(status)}`}>
                  {getServiceStatusText(status)}
                </span>
              </div>
              
              {status.lastCheck && (
                <p className="text-xs text-gray-500">
                  Last checked: {new Date(status.lastCheck).toLocaleTimeString()}
                </p>
              )}
              
              {!status.available && status.reason && (
                <p className="text-xs text-gray-600 mt-1">
                  Reason: {status.reason}
                </p>
              )}
              
              {!status.available && status.lastError && (
                <p className="text-xs text-red-600 mt-1">
                  Last error: {status.lastError}
                </p>
              )}
            </div>
          );
        })}
      </div>

      {!criticalStatus.allAvailable && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start">
            <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800">
                Limited Functionality
              </h4>
              <p className="text-sm text-yellow-700 mt-1">
                Some AI features may be unavailable. You can still use alternative options like manual input, file uploads, and stock images.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServiceStatusIndicator;
