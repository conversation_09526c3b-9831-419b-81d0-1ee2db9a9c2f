import React, { useState } from 'react';
import Icon from '../AppIcon';
import Button from './Button';

/**
 * Extracted Content Summary Component
 * Shows a compact summary of extracted content throughout the wizard
 */
const ExtractedContentSummary = ({ 
  extractedData, 
  onEdit,
  onRemove,
  className = "",
  showActions = true
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!extractedData || !extractedData.extractedContent) {
    return null;
  }

  const wordCount = extractedData.wordCount || 0;
  const readingTime = Math.ceil(wordCount / 200);
  const contentPreview = extractedData.extractedContent.substring(0, 100) +
    (extractedData.extractedContent.length > 100 ? '...' : '');

  // Determine if this is a URL import or file import
  const isUrlImport = extractedData.sourceUrl && extractedData.sourceUrl.startsWith('http');
  const isFileImport = extractedData.fileName;

  // Get source display information
  const getSourceInfo = () => {
    if (isUrlImport) {
      try {
        return {
          type: 'URL',
          icon: 'Link',
          source: new URL(extractedData.sourceUrl).hostname,
          fullSource: extractedData.sourceUrl
        };
      } catch (error) {
        // Fallback if URL is invalid
        return {
          type: 'URL',
          icon: 'Link',
          source: extractedData.sourceUrl || 'Unknown URL',
          fullSource: extractedData.sourceUrl || ''
        };
      }
    } else if (isFileImport) {
      return {
        type: 'File',
        icon: 'FileText',
        source: extractedData.fileName,
        fullSource: extractedData.fileName
      };
    } else {
      return {
        type: 'Content',
        icon: 'FileText',
        source: 'Imported Content',
        fullSource: 'Imported Content'
      };
    }
  };

  const sourceInfo = getSourceInfo();

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Icon name={sourceInfo.icon} size={16} className="text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900 text-sm">Imported Content</h4>
            <p className="text-blue-700 text-xs">
              From: {sourceInfo.source}
            </p>
          </div>
        </div>
        
        {showActions && (
          <div className="flex items-center space-x-1">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1 hover:bg-blue-100 rounded transition-colors"
              title={isExpanded ? "Collapse" : "Expand"}
            >
              <Icon 
                name={isExpanded ? "ChevronUp" : "ChevronDown"} 
                size={16} 
                className="text-blue-600" 
              />
            </button>
            {onEdit && (
              <button
                onClick={onEdit}
                className="p-1 hover:bg-blue-100 rounded transition-colors"
                title="Edit content"
              >
                <Icon name="Edit3" size={14} className="text-blue-600" />
              </button>
            )}
            {onRemove && (
              <button
                onClick={onRemove}
                className="p-1 hover:bg-red-100 rounded transition-colors"
                title="Remove imported content"
              >
                <Icon name="X" size={14} className="text-red-500" />
              </button>
            )}
          </div>
        )}
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-3 mb-3">
        <div className="bg-white p-2 rounded border text-center">
          <div className="text-xs text-gray-500">Title</div>
          <div className="font-medium text-gray-900 text-sm truncate" title={extractedData.originalTitle}>
            {extractedData.originalTitle || 'Untitled'}
          </div>
        </div>
        <div className="bg-white p-2 rounded border text-center">
          <div className="text-xs text-gray-500">Words</div>
          <div className="font-medium text-gray-900 text-sm">
            {wordCount.toLocaleString()}
          </div>
        </div>
        <div className="bg-white p-2 rounded border text-center">
          <div className="text-xs text-gray-500">Read Time</div>
          <div className="font-medium text-gray-900 text-sm">
            {readingTime} min
          </div>
        </div>
      </div>

      {/* Content Preview */}
      {!isExpanded && (
        <div className="bg-white p-3 rounded border">
          <p className="text-gray-700 text-sm leading-relaxed">
            {contentPreview}
          </p>
        </div>
      )}

      {/* Expanded Content */}
      {isExpanded && (
        <div className="space-y-3">
          <div className="bg-white p-3 rounded border">
            <h5 className="font-medium text-gray-900 mb-2">Full Title</h5>
            <p className="text-gray-700 text-sm">
              {extractedData.originalTitle || 'No title available'}
            </p>
          </div>

          {extractedData.author && (
            <div className="bg-white p-3 rounded border">
              <h5 className="font-medium text-gray-900 mb-2">Author</h5>
              <p className="text-gray-700 text-sm">{extractedData.author}</p>
            </div>
          )}

          {extractedData.publishDate && (
            <div className="bg-white p-3 rounded border">
              <h5 className="font-medium text-gray-900 mb-2">Published</h5>
              <p className="text-gray-700 text-sm">
                {new Date(extractedData.publishDate).toLocaleDateString()}
              </p>
            </div>
          )}

          <div className="bg-white p-3 rounded border">
            <h5 className="font-medium text-gray-900 mb-2">Content Preview</h5>
            <div className="text-gray-700 text-sm leading-relaxed max-h-32 overflow-y-auto">
              {extractedData.extractedContent.length > 500 
                ? `${extractedData.extractedContent.substring(0, 500)}...`
                : extractedData.extractedContent
              }
            </div>
          </div>

          <div className="bg-white p-3 rounded border">
            <h5 className="font-medium text-gray-900 mb-2">
              {sourceInfo.type === 'URL' ? 'Source URL' : 'Source File'}
            </h5>
            {isUrlImport ? (
              <a
                href={sourceInfo.fullSource}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 text-sm break-all"
              >
                {sourceInfo.fullSource}
              </a>
            ) : (
              <div className="text-gray-700 text-sm break-all">
                {sourceInfo.fullSource}
                {extractedData.fileSize && (
                  <span className="text-gray-500 ml-2">
                    ({(extractedData.fileSize / 1024).toFixed(1)} KB)
                  </span>
                )}
              </div>
            )}
          </div>

          <div className="bg-white p-3 rounded border">
            <h5 className="font-medium text-gray-900 mb-2">Extraction Details</h5>
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Word Count:</span>
                <span className="font-medium">{wordCount.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Character Count:</span>
                <span className="font-medium">{extractedData.extractedContent.length.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Reading Time:</span>
                <span className="font-medium">{readingTime} minutes</span>
              </div>
              <div className="flex justify-between">
                <span>Extracted:</span>
                <span className="font-medium">
                  {new Date(extractedData.extractedAt).toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Impact Notice */}
      <div className="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-800">
        <Icon name="Info" size={12} className="inline mr-1" />
        This content will be used to enhance your document generation process.
      </div>
    </div>
  );
};

export default ExtractedContentSummary;
