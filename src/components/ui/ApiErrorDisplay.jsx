/**
 * API Error Display Component
 * 
 * Displays user-friendly error messages for API failures with actionable guidance.
 * Replaces mock content fallbacks with clear error communication.
 */

import React from 'react';
import { AlertCircle, RefreshCw, Settings, ExternalLink, Clock, Zap } from 'lucide-react';

const ApiErrorDisplay = ({
  error,
  onRetry = null,
  onDismiss = null,
  showRetry = true,
  showAlternatives = true,
  className = ''
}) => {
  // Extract error information from standardized API error response
  const errorInfo = error?.apiErrorResponse?.error || {
    type: 'unknown',
    title: 'Something went wrong',
    message: 'An unexpected error occurred. Please try again.',
    actions: ['Try again', 'Contact support']
  };

  const getErrorIcon = () => {
    switch (errorInfo.type) {
      case 'quota_exhausted':
        return <Clock className="w-8 h-8 text-orange-500" />;
      case 'rate_limited':
        return <Zap className="w-8 h-8 text-yellow-500" />;
      case 'authentication':
        return <Settings className="w-8 h-8 text-red-500" />;
      case 'network':
        return <RefreshCw className="w-8 h-8 text-blue-500" />;
      case 'service_unavailable':
        return <ExternalLink className="w-8 h-8 text-purple-500" />;
      default:
        return <AlertCircle className="w-8 h-8 text-red-500" />;
    }
  };

  const getErrorColor = () => {
    switch (errorInfo.type) {
      case 'quota_exhausted':
        return 'border-orange-200 bg-orange-50';
      case 'rate_limited':
        return 'border-yellow-200 bg-yellow-50';
      case 'authentication':
        return 'border-red-200 bg-red-50';
      case 'network':
        return 'border-blue-200 bg-blue-50';
      case 'service_unavailable':
        return 'border-purple-200 bg-purple-50';
      default:
        return 'border-red-200 bg-red-50';
    }
  };

  const getRetryDelay = () => {
    switch (errorInfo.type) {
      case 'rate_limited':
        return 'Please wait 1-2 minutes before trying again.';
      case 'network':
        return 'You can try again immediately.';
      case 'service_unavailable':
        return 'Please wait a few minutes before trying again.';
      default:
        return null;
    }
  };

  const getAlternativeActions = () => {
    const alternatives = [];
    
    if (errorInfo.context?.includes('Image Generation')) {
      alternatives.push(
        { label: 'Upload Image', action: 'upload', description: 'Upload your own image instead' },
        { label: 'Use Stock Images', action: 'stock', description: 'Choose from available stock images' },
        { label: 'Continue Without Image', action: 'skip', description: 'Add an image later' }
      );
    } else if (errorInfo.context?.includes('Content Generation')) {
      alternatives.push(
        { label: 'Write Manually', action: 'manual', description: 'Create content yourself' },
        { label: 'Import Content', action: 'import', description: 'Import from existing document' },
        { label: 'Use Template', action: 'template', description: 'Start with a template' }
      );
    }
    
    return alternatives;
  };

  const retryDelay = getRetryDelay();
  const alternatives = getAlternativeActions();

  return (
    <div className={`border rounded-lg p-6 ${getErrorColor()} ${className}`}>
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          {getErrorIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {errorInfo.title}
          </h3>
          
          <p className="text-gray-700 mb-4">
            {errorInfo.message}
          </p>
          
          {retryDelay && (
            <p className="text-sm text-gray-600 mb-4 italic">
              {retryDelay}
            </p>
          )}
          
          {errorInfo.actions && errorInfo.actions.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                Recommended Actions:
              </h4>
              <ul className="text-sm text-gray-700 space-y-1">
                {errorInfo.actions.map((action, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-2"></span>
                    {action}
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          <div className="flex flex-wrap gap-3">
            {showRetry && onRetry && (
              <button
                onClick={onRetry}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </button>
            )}
            
            {onDismiss && (
              <button
                onClick={onDismiss}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Dismiss
              </button>
            )}
          </div>
          
          {showAlternatives && alternatives.length > 0 && (
            <div className="mt-6 pt-4 border-t border-gray-200">
              <h4 className="text-sm font-medium text-gray-900 mb-3">
                Alternative Options:
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {alternatives.map((alt, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      // Emit custom event for parent components to handle
                      const event = new CustomEvent('alternativeAction', {
                        detail: { action: alt.action, label: alt.label }
                      });
                      window.dispatchEvent(event);
                    }}
                    className="text-left p-3 border border-gray-200 rounded-md hover:border-gray-300 hover:bg-white transition-colors"
                  >
                    <div className="text-sm font-medium text-gray-900">
                      {alt.label}
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {alt.description}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Specialized error displays for common scenarios
export const ServiceUnavailableDisplay = ({ serviceName, feature, onRetry }) => (
  <ApiErrorDisplay
    error={{
      apiErrorResponse: {
        error: {
          type: 'service_unavailable',
          title: `${serviceName} Temporarily Unavailable`,
          message: `${feature} is currently unavailable due to service maintenance or high demand. Please try again in a few minutes.`,
          actions: [
            'Wait a few minutes and try again',
            'Use alternative options below',
            'Contact support if issue persists'
          ],
          context: feature
        }
      }
    }}
    onRetry={onRetry}
    showAlternatives={true}
  />
);

export const QuotaExhaustedDisplay = ({ serviceName, resetTime, onRetry }) => (
  <ApiErrorDisplay
    error={{
      apiErrorResponse: {
        error: {
          type: 'quota_exhausted',
          title: `${serviceName} Daily Quota Reached`,
          message: `The daily usage limit for ${serviceName} has been reached. ${resetTime ? `Quota resets at ${resetTime}.` : 'Quota typically resets daily.'}`,
          actions: [
            'Try again tomorrow',
            'Use alternative options below',
            'Contact support for quota increase'
          ]
        }
      }
    }}
    onRetry={onRetry}
    showRetry={false}
    showAlternatives={true}
  />
);

export const ConfigurationErrorDisplay = ({ serviceName, onRetry }) => (
  <ApiErrorDisplay
    error={{
      apiErrorResponse: {
        error: {
          type: 'authentication',
          title: `${serviceName} Not Configured`,
          message: `${serviceName} requires API key configuration to function. Please contact your administrator to set up the service.`,
          actions: [
            'Contact your administrator',
            'Check API key configuration',
            'Use alternative options below'
          ]
        }
      }
    }}
    onRetry={onRetry}
    showRetry={false}
    showAlternatives={true}
  />
);

export default ApiErrorDisplay;
