import React, { useState, useRef } from 'react';
import Icon from '../AppIcon';
import Button from './Button';
import { validateDocxFile } from '../../services/docxExtractionService';

/**
 * DOCX File Upload Component
 * Provides drag-and-drop and click-to-upload functionality for DOCX files
 * Follows the existing design patterns from UploadArea components
 */
const DocxUpload = ({ 
  onFileSelect, 
  onValidationChange,
  disabled = false,
  className = "",
  showPreview = true
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [validation, setValidation] = useState({ isValid: false });
  const fileInputRef = useRef(null);

  // Handle drag events
  const handleDragOver = (e) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  // Handle file input change
  const handleFileInputChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  // Handle file selection and validation
  const handleFileSelection = (file) => {
    const validationResult = validateFile(file);
    setValidation(validationResult);
    
    if (validationResult.isValid) {
      setSelectedFile(file);
      onFileSelect?.(file);
    } else {
      setSelectedFile(null);
      onFileSelect?.(null);
    }
    
    onValidationChange?.(validationResult);
  };

  // Validate selected file using enhanced validation
  const validateFile = (file) => {
    const result = validateDocxFile(file);
    return {
      ...result,
      file
    };
  };

  // Format file size for display
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Clear selected file
  const clearFile = () => {
    setSelectedFile(null);
    setValidation({ isValid: false });
    onFileSelect?.(null);
    onValidationChange?.({ isValid: false });
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Trigger file input click
  const triggerFileInput = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const getDropzoneClasses = () => {
    let classes = `
      border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 cursor-pointer
      ${className}
    `;
    
    if (disabled) {
      classes += ' opacity-50 cursor-not-allowed bg-gray-50';
    } else if (isDragOver) {
      classes += ' border-primary bg-primary/5 scale-[1.02]';
    } else if (selectedFile && validation.isValid) {
      classes += ' border-green-300 bg-green-50';
    } else if (validation.errors?.length > 0) {
      classes += ' border-red-300 bg-red-50';
    } else {
      classes += ' border-border hover:border-primary/50 hover:bg-gray-50';
    }
    
    return classes;
  };

  return (
    <div className="space-y-4">
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".docx"
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      {/* Drop zone */}
      <div
        className={getDropzoneClasses()}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={triggerFileInput}
      >
        <div className="space-y-4">
          {/* Icon and main message */}
          <div className="space-y-3">
            {selectedFile && validation.isValid ? (
              <Icon name="CheckCircle" size={48} className="mx-auto text-green-500" />
            ) : validation.errors?.length > 0 ? (
              <Icon name="AlertCircle" size={48} className="mx-auto text-red-500" />
            ) : (
              <Icon name="Upload" size={48} className="mx-auto text-text-secondary" />
            )}
            
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">
                {selectedFile && validation.isValid 
                  ? 'DOCX File Selected' 
                  : validation.errors?.length > 0
                  ? 'Invalid File'
                  : 'Upload DOCX Document'
                }
              </h3>
              <p className="text-text-secondary">
                {selectedFile && validation.isValid
                  ? `${selectedFile.name} (${formatFileSize(selectedFile.size)})`
                  : 'Drag and drop your .docx file here, or click to browse'
                }
              </p>
            </div>
          </div>

          {/* File requirements */}
          {!selectedFile && (
            <div className="text-sm text-text-muted space-y-1">
              <p>• Supported format: .docx files only</p>
              <p>• Maximum file size: 10MB</p>
              <p>• Content will be extracted and used to enhance your document</p>
            </div>
          )}

          {/* Action button */}
          {!selectedFile ? (
            <Button
              variant="primary"
              onClick={(e) => {
                e.stopPropagation();
                triggerFileInput();
              }}
              disabled={disabled}
              iconName="FileText"
              iconPosition="left"
              className="mt-4"
            >
              Choose DOCX File
            </Button>
          ) : (
            <Button
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                triggerFileInput();
              }}
              disabled={disabled}
              iconName="RefreshCw"
              iconPosition="left"
              className="mt-4"
            >
              Choose Different File
            </Button>
          )}
        </div>
      </div>

      {/* File preview */}
      {selectedFile && validation.isValid && showPreview && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <Icon name="FileText" size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-gray-900">{selectedFile.name}</h4>
                <p className="text-sm text-gray-600">
                  {formatFileSize(selectedFile.size)} • Ready for processing
                </p>
                {validation.warnings?.length > 0 && (
                  <div className="mt-2 space-y-1">
                    {validation.warnings.map((warning, index) => (
                      <p key={index} className="text-sm text-yellow-600 flex items-center">
                        <Icon name="AlertTriangle" size={14} className="mr-1" />
                        {warning}
                      </p>
                    ))}
                  </div>
                )}
              </div>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                clearFile();
              }}
              className="p-1 hover:bg-green-100 rounded transition-colors"
              disabled={disabled}
            >
              <Icon name="X" size={16} className="text-gray-500" />
            </button>
          </div>
        </div>
      )}

      {/* Error display */}
      {validation.errors?.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Icon name="AlertCircle" size={20} className="text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-gray-900">Upload Error</h4>
              <div className="mt-1 space-y-1">
                {validation.errors.map((error, index) => (
                  <p key={index} className="text-sm text-red-600">{error}</p>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocxUpload;
