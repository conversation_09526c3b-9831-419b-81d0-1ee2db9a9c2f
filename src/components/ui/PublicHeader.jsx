import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import Button from './Button';
import Icon from '../AppIcon';

/**
 * PublicHeader - Simple navigation header for public pages
 * Shows logo, basic navigation, and auth-aware actions
 */
const PublicHeader = ({ 
  showBackButton = false, 
  backButtonText = "Back to Dashboard",
  backButtonPath = "/dashboard" 
}) => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  const handleBackClick = () => {
    if (isAuthenticated) {
      navigate(backButtonPath);
    } else {
      navigate('/auth');
    }
  };

  const handleAuthClick = () => {
    navigate('/auth');
  };

  const handleDashboardClick = () => {
    navigate('/dashboard');
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-surface border-b border-border shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          
          {/* Left Side - Logo and Back Button */}
          <div className="flex items-center space-x-4">
            {/* Logo */}
            <div className="flex items-center cursor-pointer" onClick={() => navigate('/')}>
              <img 
                src="/logo-optimized.svg" 
                alt="RapidDoc AI" 
                className="h-8 w-auto" 
              />
              <span className="ml-2 text-xl font-bold text-text-primary hidden sm:block">
                RapidDoc AI
              </span>
            </div>

            {/* Back Button */}
            {showBackButton && (
              <Button
                variant="ghost"
                onClick={handleBackClick}
                className="flex items-center space-x-2 text-text-secondary hover:text-text-primary"
              >
                <Icon name="ArrowLeft" size={16} />
                <span className="hidden sm:inline">{backButtonText}</span>
                <span className="sm:hidden">Back</span>
              </Button>
            )}
          </div>

          {/* Right Side - Auth Actions */}
          <div className="flex items-center space-x-3">
            {isAuthenticated ? (
              <Button
                onClick={handleDashboardClick}
                className="bg-primary text-white hover:bg-primary-dark"
              >
                <Icon name="LayoutDashboard" size={16} className="mr-2" />
                Dashboard
              </Button>
            ) : (
              <>
                <Button
                  variant="ghost"
                  onClick={handleAuthClick}
                  className="text-text-secondary hover:text-text-primary"
                >
                  Sign In
                </Button>
                <Button
                  onClick={() => navigate('/auth?mode=register')}
                  className="bg-primary text-white hover:bg-primary-dark"
                >
                  Get Started
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default PublicHeader;
