import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useSidebar } from '../../contexts/SidebarContext';
import Icon from '../AppIcon';
import Button from './Button';
import Input from './Input';
import MobileMenuButton from './MobileMenuButton';
import NotificationCenter from '../notifications/NotificationCenter.jsx';

import { prodLogger } from '../../utils/prodLogger.js';
const Header = () => {
  const { user, profile, signOut, loading } = useAuth();
  const { isMobileSidebarOpen } = useSidebar();
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const navigationItems = [
    { label: 'Dashboard', path: '/dashboard', icon: 'LayoutDashboard' },
    { label: 'Create', path: '/document-creator', icon: 'FileText' },
    { label: 'Verify', path: '/plagiarism-checker', icon: 'Shield' },
  ];

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {

      setIsSearchOpen(false);
      setSearchQuery('');
    }
  };

  const handleNavigation = (path) => {
    navigate(path);
  };

  const handleProfileClick = () => {
    setIsProfileOpen(!isProfileOpen);
  };

  const handleAccountSettings = () => {
    navigate('/account-settings');
    setIsProfileOpen(false);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      setIsProfileOpen(false);
      navigate('/auth');
    } catch (error) {
      prodLogger.error('Logout failed:', error);
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-1000 bg-surface border-b border-border shadow-sm">
      <div className="px-4">
        <div className="flex items-center justify-between h-16">

          {/* Logo Section */}
          <div className="p-4 lg:block hidden">
            <div className="flex items-center justify-center">
              <img 
                  src={"/logo-optimized.svg"} 
                  alt="RapidDoc AI" 
                  className={"h-14"} 
                />
            </div>
          </div>

          {/* Mobile Menu Button - Consistent QuickActionSidebar toggle */}
          <MobileMenuButton isOpen={isMobileSidebarOpen} />

          {/* Search Bar - Centered like reference */}
          <div className="flex-1 max-w-2xl mx-auto lg:mx-auto ps-8">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Icon name="Search" size={18} className="text-text-muted" />
              </div>
              <input
                type="search"
                placeholder="Search projects and docs"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-border rounded-md bg-background text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Help Icon */}
            <Button variant="ghost" className="p-2 hidden md:block">
              <Icon name="HelpCircle" size={18} className="text-text-muted" />
            </Button>

            {/* Notifications */}
            <div className="hidden md:block">
              <NotificationCenter />
            </div>

            {/* Create Button */}
            <Button
              onClick={() => navigate('/document-creator')}
              className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors duration-200"
            >
              Create
            </Button>

            {/* Profile Dropdown */}
            <div className="relative">
              <Button
                variant="ghost"
                onClick={handleProfileClick}
                className="p-1.5 rounded-full"
              >
                {profile?.avatar_url ? (
                  <img
                    src={profile.avatar_url}
                    alt={profile.full_name || user?.email}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-8 h-8 bg-gradient-to-r from-primary to-primary/80 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {(profile?.full_name || user?.email || 'U').charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
              </Button>

              {isProfileOpen && (
                <div className="absolute right-0 mt-2 min-w-48 max-w-64 bg-surface rounded-md shadow-elevated border border-border z-1100 overflow-hidden">
                  <div className="py-1">
                    <div className="px-4 py-2 border-b border-border">
                      <p className="text-sm font-medium text-text-primary">
                        {profile?.full_name || user?.email?.split('@')[0] || 'User'}
                      </p>
                      <p className="text-xs text-text-secondary truncate" title={user?.email}>{user?.email}</p>
                      {profile?.user_type && (
                        <p className="text-xs text-text-secondary capitalize">{profile.user_type.replace('_', ' ')}</p>
                      )}
                    </div>
                    <button
                      onClick={handleAccountSettings}
                      className="w-full text-left px-4 py-2 text-sm text-text-primary hover:bg-background flex items-center space-x-2"
                    >
                      <Icon name="Settings" size={16} />
                      <span>Account Settings</span>
                    </button>
                    <button
                      onClick={handleLogout}
                      disabled={loading}
                      className="w-full text-left px-4 py-2 text-sm text-error hover:bg-background flex items-center space-x-2 disabled:opacity-50"
                    >
                      <Icon name="LogOut" size={16} />
                      <span>{loading ? 'Signing out...' : 'Sign Out'}</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;