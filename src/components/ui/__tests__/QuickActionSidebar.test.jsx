import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import QuickActionSidebar from '../QuickActionSidebar';

// Mock dependencies
const mockNavigate = jest.fn();
const mockCloseMobileSidebar = jest.fn();
const mockShowSuccess = jest.fn();
const mockShowError = jest.fn();

// Create a mock sidebar context that can be overridden
let mockSidebarContext = {
  isCollapsed: false,
  isMobile: false,
  isMobileSidebarOpen: false,
  toggleSidebar: jest.fn(),
  toggleMobileSidebar: jest.fn(),
  closeMobileSidebar: mockCloseMobileSidebar,
  sidebarWidth: 'w-64',
};

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: '/dashboard' }),
}));

jest.mock('../../../contexts/SidebarContext', () => ({
  useSidebar: () => mockSidebarContext,
}));

jest.mock('../../../hooks/useMobileSidebarPosition', () => ({
  __esModule: true,
  default: () => {},
}));

jest.mock('../../../hooks/useNotifications', () => ({
  useToasts: () => ({
    showSuccess: mockShowSuccess,
    showError: mockShowError,
  }),
}));

jest.mock('../../AppIcon', () => {
  return function Icon({ name, size, className }) {
    return <span data-testid={`icon-${name}`} data-size={size} className={className}>{name}</span>;
  };
});

// Test wrapper component
const TestWrapper = ({ children }) => {
  return (
    <BrowserRouter>
      {children}
    </BrowserRouter>
  );
};

// Helper function to update mock context
const updateMockSidebarContext = (overrides) => {
  mockSidebarContext = {
    ...mockSidebarContext,
    ...overrides,
  };
};

describe('QuickActionSidebar - Upgrade Button Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset to default expanded state
    updateMockSidebarContext({
      isCollapsed: false,
      isMobile: false,
      isMobileSidebarOpen: false,
    });
  });

  describe('Expanded Sidebar State', () => {
    test('renders upgrade button in expanded state', () => {
      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      expect(screen.getByText('Upgrades')).toBeInTheDocument();
      expect(screen.getByText('Get access to more features and unlimited documents')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /upgrade plan/i })).toBeInTheDocument();
    });

    test('upgrade button navigates to account settings on click', async () => {
      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByRole('button', { name: /upgrade plan/i });
      fireEvent.click(upgradeButton);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/account-settings', { state: { tab: 'billing' } });
      });
    });

    test('shows success toast on successful navigation', async () => {
      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByRole('button', { name: /upgrade plan/i });
      fireEvent.click(upgradeButton);

      await waitFor(() => {
        expect(mockShowSuccess).toHaveBeenCalledWith(
          'Redirecting to upgrade options...',
          'You can manage your subscription and billing from the account settings.'
        );
      });
    });

    test('shows loading state during navigation', async () => {
      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByRole('button', { name: /upgrade plan/i });
      fireEvent.click(upgradeButton);

      // Check for loading state immediately after click
      expect(upgradeButton).toHaveTextContent('Loading...');
      expect(upgradeButton).toBeDisabled();
    });

    test('prevents double-clicks during loading', async () => {
      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByRole('button', { name: /upgrade plan/i });
      
      // Click multiple times rapidly
      fireEvent.click(upgradeButton);
      fireEvent.click(upgradeButton);
      fireEvent.click(upgradeButton);

      // Should only navigate once
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Collapsed Sidebar State', () => {
    beforeEach(() => {
      updateMockSidebarContext({ isCollapsed: true });
    });

    test('renders upgrade button in collapsed state', () => {
      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      // In collapsed state, the button should have title attribute but no visible text
      const upgradeButton = screen.getByTitle('Upgrades');
      expect(upgradeButton).toBeInTheDocument();
      expect(upgradeButton).toHaveAttribute('title', 'Upgrades');
      expect(screen.getByTestId('icon-Zap')).toBeInTheDocument();
    });

    test('collapsed upgrade button navigates correctly', async () => {
      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByTitle('Upgrades');
      fireEvent.click(upgradeButton);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/account-settings', { state: { tab: 'billing' } });
      });
    });

    test('shows loading spinner in collapsed state', async () => {
      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByTitle('Upgrades');
      fireEvent.click(upgradeButton);

      // Check for loading state
      expect(upgradeButton).toBeDisabled();

      await waitFor(() => {
        expect(upgradeButton).toHaveAttribute('title', 'Navigating to upgrades...');
      });
    });
  });

  describe('Mobile Sidebar Support', () => {
    test('closes mobile sidebar on upgrade button click', async () => {
      updateMockSidebarContext({ isMobile: true, isMobileSidebarOpen: true });

      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByRole('button', { name: /upgrade plan/i });
      fireEvent.click(upgradeButton);

      await waitFor(() => {
        expect(mockCloseMobileSidebar).toHaveBeenCalled();
      });
    });

    test('does not close mobile sidebar when not open', async () => {
      updateMockSidebarContext({ isMobile: true, isMobileSidebarOpen: false });

      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByRole('button', { name: /upgrade plan/i });
      fireEvent.click(upgradeButton);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalled();
      });

      // Should not call closeMobileSidebar when sidebar is not open
      expect(mockCloseMobileSidebar).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    test('handles navigation errors gracefully', async () => {
      // Mock navigate to throw an error
      mockNavigate.mockImplementationOnce(() => {
        throw new Error('Navigation failed');
      });

      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByRole('button', { name: /upgrade plan/i });
      fireEvent.click(upgradeButton);

      await waitFor(() => {
        expect(mockShowError).toHaveBeenCalledWith(
          'Navigation Error',
          'Unable to navigate to upgrade page. Redirecting to pricing page instead.'
        );
      });
    });

    test('falls back to pricing page on navigation error', async () => {
      // Mock navigate to throw an error on first call, succeed on second
      mockNavigate
        .mockImplementationOnce(() => {
          throw new Error('Navigation failed');
        })
        .mockImplementationOnce(() => {});

      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByRole('button', { name: /upgrade plan/i });
      fireEvent.click(upgradeButton);

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/pricing');
      });
    });

    test('shows error when both navigation attempts fail', async () => {
      // Mock navigate to throw errors on both calls
      mockNavigate.mockImplementation(() => {
        throw new Error('Navigation failed');
      });

      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByRole('button', { name: /upgrade plan/i });
      fireEvent.click(upgradeButton);

      await waitFor(() => {
        expect(mockShowError).toHaveBeenCalledWith(
          'Navigation Failed',
          'Unable to navigate to upgrade page. Please try refreshing the page.'
        );
      });
    });
  });

  describe('Accessibility', () => {
    test('upgrade buttons have proper accessibility attributes', () => {
      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByRole('button', { name: /upgrade plan/i });
      expect(upgradeButton).toBeInTheDocument();
      // HTML buttons have type="button" by default, but our component doesn't explicitly set it
      expect(upgradeButton.tagName).toBe('BUTTON');
    });

    test('collapsed button has proper title attribute', () => {
      updateMockSidebarContext({ isCollapsed: true });

      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByTitle('Upgrades');
      expect(upgradeButton).toHaveAttribute('title', 'Upgrades');
    });

    test('loading state updates title attribute', async () => {
      updateMockSidebarContext({ isCollapsed: true });

      render(
        <TestWrapper>
          <QuickActionSidebar />
        </TestWrapper>
      );

      const upgradeButton = screen.getByTitle('Upgrades');
      fireEvent.click(upgradeButton);

      await waitFor(() => {
        expect(upgradeButton).toHaveAttribute('title', 'Navigating to upgrades...');
      });
    });
  });
});
