import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '../AppIcon';

const Breadcrumbs = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const routeMap = {
    '/dashboard': { label: 'Dashboard', icon: 'LayoutDashboard' },
    '/documents': { label: 'Documents', icon: 'FolderOpen' },
    '/document-creator': { label: 'Document Creator', icon: 'FileText' },
    '/plagiarism-checker': { label: 'Plagiarism Checker', icon: 'Shield' },

    '/account-settings': { label: 'Account Settings', icon: 'Settings' },
  };

  const generateBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [];

    // Always start with Dashboard as home
    if (location.pathname !== '/dashboard') {
      breadcrumbs.push({
        label: 'Dashboard',
        path: '/dashboard',
        icon: 'LayoutDashboard',
        isActive: false
      });
    }

    // Add current page
    const currentRoute = routeMap[location.pathname];
    if (currentRoute) {
      breadcrumbs.push({
        label: currentRoute.label,
        path: location.pathname,
        icon: currentRoute.icon,
        isActive: true
      });
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  const handleBreadcrumbClick = (path) => {
    if (path !== location.pathname) {
      navigate(path);
    }
  };

  if (breadcrumbs.length <= 1 && location.pathname === '/dashboard') {
    return null;
  }

  return (
    <nav className="flex items-center space-x-2 text-sm text-text-secondary mb-6" aria-label="Breadcrumb">
      <div className="flex items-center space-x-2">
        {breadcrumbs.map((crumb, index) => (
          <React.Fragment key={crumb.path}>
            {index > 0 && (
              <Icon name="ChevronRight" size={14} className="text-text-secondary" />
            )}
            <button
              onClick={() => handleBreadcrumbClick(crumb.path)}
              className={`flex items-center space-x-1.5 px-2 py-1 rounded-md transition-micro ${
                crumb.isActive
                  ? 'text-text-primary bg-background cursor-default' :'text-text-secondary hover:text-text-primary hover:bg-background'
              }`}
              disabled={crumb.isActive}
            >
              <Icon name={crumb.icon} size={14} />
              <span className={crumb.isActive ? 'font-medium' : ''}>{crumb.label}</span>
            </button>
          </React.Fragment>
        ))}
      </div>
    </nav>
  );
};

export default Breadcrumbs;