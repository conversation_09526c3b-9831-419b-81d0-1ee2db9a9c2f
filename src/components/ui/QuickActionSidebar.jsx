import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import useSidebarPosition from '../../hooks/useMobileSidebarPosition';
import { useToasts } from '../../hooks/useNotifications';
import Icon from '../AppIcon';

const QuickActionSidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { showSuccess, showError } = useToasts();
  const [isUpgrading, setIsUpgrading] = useState(false);
  const {
    isCollapsed,
    isMobile,
    isMobileSidebarOpen,
    toggleSidebar,
    toggleMobileSidebar,
    closeMobileSidebar,
    sidebarWidth
  } = useSidebar();

  // Hook to manage sidebar positioning based on page layout (mobile and desktop)
  useSidebarPosition();

  // Main navigation items matching reference design
  const navigationItems = [
    {
      label: 'Home',
      icon: 'Home',
      path: '/dashboard',
      isActive: location.pathname === '/dashboard' || location.pathname === '/'
    },
    {
      label: 'Documents',
      icon: 'FolderOpen',
      path: '/documents',
      isActive: location.pathname === '/documents'
    },
    {
      label: 'Create',
      icon: 'Plus',
      path: '/document-creator',
      isActive: location.pathname === '/document-creator'
    }
  ];

  const handleNavigation = (path) => {
    navigate(path);
    // Close mobile sidebar when navigating
    if (isMobile && isMobileSidebarOpen) {
      closeMobileSidebar();
    }
  };

  const handleUpgradeClick = async () => {
    if (isUpgrading) return; // Prevent double-clicks

    try {
      setIsUpgrading(true);

      // Navigate to account settings with billing tab active
      navigate('/account-settings', { state: { tab: 'billing' } });

      // Close mobile sidebar when navigating
      if (isMobile && isMobileSidebarOpen) {
        closeMobileSidebar();
      }

      // Show success message
      showSuccess('Redirecting to upgrade options...', 'You can manage your subscription and billing from the account settings.');

    } catch (error) {
      console.error('Error navigating to upgrade page:', error);

      // Show error message
      showError('Navigation Error', 'Unable to navigate to upgrade page. Redirecting to pricing page instead.');

      // Fallback to pricing page if account settings navigation fails
      try {
        navigate('/pricing');
        if (isMobile && isMobileSidebarOpen) {
          closeMobileSidebar();
        }
      } catch (fallbackError) {
        console.error('Error navigating to pricing page:', fallbackError);
        showError('Navigation Failed', 'Unable to navigate to upgrade page. Please try refreshing the page.');
      }
    } finally {
      // Reset loading state after a short delay to allow navigation to complete
      setTimeout(() => setIsUpgrading(false), 1000);
    }
  };

  // Reusable sidebar content component
  const SidebarContent = ({ isMobileVersion = false }) => (
    <div className={`flex flex-col h-full ${isMobileVersion ? 'overflow-hidden' : ''}`}>
      {/* Logo Section */}
      <div className="pt-2 px-4 lg:hidden block">
        <div className="">
          <img 
              src={(isCollapsed && !isMobileVersion) ? "/logo-alone.png" : "/logo-optimized.svg"} 
              alt="RapidDoc AI" 
              className={(isCollapsed && !isMobileVersion) ? "h-8 w-8" : "h-12"} 
            />
        </div>
      </div>

      {/* Navigation Items */}
      <nav className={`flex-1 py-4 px-3 ${isMobileVersion ? 'overflow-y-auto' : ''}`}>
        <div className="space-y-2">
          {navigationItems.map((item) => (
            <button
              key={item.path}
              onClick={() => handleNavigation(item.path)}
              className={`w-full flex items-center ${(isCollapsed && !isMobileVersion) ? 'justify-center px-1' : 'space-x-3 px-2'} py-3 rounded-lg text-sm font-medium transition-all duration-200 ${item.isActive
                  ? 'bg-primary text-white shadow-sm'
                  : 'text-text-secondary hover:text-text-primary hover:bg-surface-hover'
                }`}
              title={(isCollapsed && !isMobileVersion) ? item.label : undefined}
            >
              <Icon name={item.icon} size={18} />
              {(!isCollapsed || isMobileVersion) && (
                <span className="sidebar-content-transition">{item.label}</span>
              )}
            </button>
          ))}
        </div>
      </nav>

      {/* Upgrade Section */}
      <div className="p-4 border-t border-border">
        {(isCollapsed && !isMobileVersion) ? (
          <button
            onClick={handleUpgradeClick}
            disabled={isUpgrading}
            className={`w-full p-3 bg-surface-secondary rounded-lg transition-colors duration-200 flex items-center justify-center ${
              isUpgrading
                ? 'opacity-60 cursor-not-allowed'
                : 'hover:bg-surface-hover'
            }`}
            title={isUpgrading ? 'Navigating to upgrades...' : 'Upgrades'}
          >
            {isUpgrading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-warning"></div>
            ) : (
              <Icon name="Zap" size={16} className="text-warning" />
            )}
          </button>
        ) : (
          <div className="bg-surface-secondary rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Zap" size={16} className="text-warning" />
              <span className="text-sm font-medium text-text-primary">Upgrades</span>
            </div>
            <p className="text-xs text-text-secondary mb-3">
              Get access to more features and unlimited documents
            </p>
            <button
              onClick={handleUpgradeClick}
              disabled={isUpgrading}
              className={`w-full bg-primary text-white text-xs py-2 px-3 rounded-lg transition-colors duration-200 flex items-center justify-center ${
                isUpgrading
                  ? 'opacity-60 cursor-not-allowed'
                  : 'hover:bg-primary-dark'
              }`}
            >
              {isUpgrading ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                  Loading...
                </>
              ) : (
                'Upgrade Plan'
              )}
            </button>
          </div>
        )}
      </div>

      {/* Sidebar Toggle Button - Only show on desktop */}
      {!isMobileVersion && (
        <div className="p-4 border-t border-border">
          <button
            onClick={toggleSidebar}
            className={`w-full flex items-center ${isCollapsed ? 'justify-center px-2' : 'justify-center px-4'} py-3 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface-hover transition-all duration-200 group`}
            title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            <Icon
              name={isCollapsed ? "ChevronRight" : "ChevronLeft"}
              size={16}
              className="sidebar-toggle-icon"
            />
            {!isCollapsed && (
              <span className="ml-2 text-xs font-medium sidebar-content-transition">
                Collapse
              </span>
            )}
          </button>
        </div>
      )}
    </div>
  );

  return (
    <>
      {/* Desktop Sidebar */}
      <aside
        className={`fixed left-0 ${sidebarWidth} bg-surface border-r border-border z-1000 shadow-sm hidden lg:block sidebar-transition`}
        style={{
          top: 'var(--desktop-sidebar-top, 0px)',
          height: 'calc(100vh - var(--desktop-sidebar-top, 0px))'
        }}
      >
        <SidebarContent />
      </aside>

      {/* Mobile Sidebar Overlay */}
      {isMobile && (
        <>
          {/* Backdrop */}
          {isMobileSidebarOpen && (
            <div
              className="fixed left-0 right-0 bottom-0 bg-black/50 z-1040 lg:hidden"
              style={{
                top: 'var(--mobile-sidebar-top, 0px)'
              }}
              onClick={closeMobileSidebar}
            />
          )}

          {/* Mobile Sidebar */}
          <aside className={`fixed left-0 w-64 bg-surface border-r border-border z-1050 shadow-xl lg:hidden transform transition-transform duration-300 ease-in-out overflow-hidden ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full'
            }`}
            style={{
              top: 'var(--mobile-sidebar-top, 0px)',
              height: 'var(--mobile-sidebar-height, 100vh)'
            }}>
            <SidebarContent isMobileVersion={true} />
          </aside>
        </>
      )}
    </>
  );
};

export default QuickActionSidebar;