import React, { useState, useEffect } from 'react';
import { useSubscriptionGuard } from '../../hooks/useSubscriptionGuard.jsx';
import Button from '../ui/Button';
import Icon from '../AppIcon';
import { prodLogger } from '../../utils/prodLogger';

const UsageDashboard = () => {
  const {
    usage,
    subscription,
    loading,
    upgradeSuggestions,
    upgradeSubscription,
    openCustomerPortal,
    refresh
  } = useSubscriptionGuard();

  const [upgrading, setUpgrading] = useState(false);

  const handleUpgrade = async (tier) => {
    try {
      setUpgrading(true);
      await upgradeSubscription(tier);
    } catch (error) {
      prodLogger.error('Upgrade failed:', error);
    } finally {
      setUpgrading(false);
    }
  };

  const getUsageColor = (percentage) => {
    if (percentage >= 100) return 'text-red-600';
    if (percentage >= 80) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getProgressBarColor = (percentage) => {
    if (percentage >= 100) return 'bg-red-500';
    if (percentage >= 80) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const formatLimit = (limit) => {
    return limit === -1 ? 'Unlimited' : limit.toLocaleString();
  };

  if (loading) {
    return (
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-border rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-border rounded"></div>
            <div className="h-4 bg-border rounded w-5/6"></div>
            <div className="h-4 bg-border rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!usage || !subscription) {
    return (
      <div className="bg-surface rounded-lg border border-border p-6 text-center">
        <Icon name="AlertCircle" size={24} className="text-yellow-500 mx-auto mb-2" />
        <p className="text-text-secondary">Unable to load usage data</p>
        <Button variant="ghost" onClick={refresh} className="mt-2">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Usage Statistics */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-6">Usage This Month</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Documents */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-text-primary">Documents Created</span>
              <span className={`text-sm font-medium ${getUsageColor(usage.usage_percentages.documents)}`}>
                {usage.documents_created} / {formatLimit(usage.documents_limit)}
              </span>
            </div>
            <div className="w-full bg-border rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(usage.usage_percentages.documents)}`}
                style={{ width: `${Math.min(usage.usage_percentages.documents, 100)}%` }}
              ></div>
            </div>
          </div>

          {/* AI Generations */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-text-primary">AI Requests</span>
              <span className={`text-sm font-medium ${getUsageColor(usage.usage_percentages.ai_generations)}`}>
                {usage.ai_generations_used} / {formatLimit(usage.ai_generations_limit)}
              </span>
            </div>
            <div className="w-full bg-border rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(usage.usage_percentages.ai_generations)}`}
                style={{ width: `${Math.min(usage.usage_percentages.ai_generations, 100)}%` }}
              ></div>
            </div>
          </div>

          {/* AI Images */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-text-primary">AI Images</span>
              <span className={`text-sm font-medium ${getUsageColor(usage.usage_percentages.ai_images)}`}>
                {usage.ai_image_generations_used} / {formatLimit(usage.ai_image_generations_limit)}
              </span>
            </div>
            <div className="w-full bg-border rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(usage.usage_percentages.ai_images)}`}
                style={{ width: `${Math.min(usage.usage_percentages.ai_images, 100)}%` }}
              ></div>
            </div>
          </div>

          {/* Storage */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-text-primary">Storage Used</span>
              <span className={`text-sm font-medium ${getUsageColor(usage.usage_percentages.storage)}`}>
                {usage.storage_used_mb}MB / {formatLimit(usage.storage_limit_mb)}MB
              </span>
            </div>
            <div className="w-full bg-border rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(usage.usage_percentages.storage)}`}
                style={{ width: `${Math.min(usage.usage_percentages.storage, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Upgrade Suggestions */}
      {upgradeSuggestions.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-start">
            <Icon name="AlertTriangle" size={20} className="text-yellow-600 mr-3 mt-0.5" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-yellow-800 mb-2">
                Upgrade Recommended
              </h4>
              <div className="space-y-2">
                {upgradeSuggestions.map((suggestion, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <p className="text-sm text-yellow-700">{suggestion.message}</p>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => handleUpgrade(suggestion.recommendedTier)}
                      loading={upgrading}
                      className="ml-4"
                    >
                      Upgrade to {suggestion.recommendedTier}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reset Information */}
      <div className="bg-surface rounded-lg border border-border p-4">
        <div className="flex items-center text-sm text-text-secondary">
          <Icon name="RotateCcw" size={16} className="mr-2" />
          <span>
            Usage resets on your next billing cycle
            {subscription.days_until_renewal !== null && (
              <span className="font-medium"> ({subscription.days_until_renewal} days)</span>
            )}
          </span>
        </div>
      </div>
    </div>
  );
};

export default UsageDashboard;
