import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../lib/supabase';
import Button from '../ui/Button';
import { prodLogger } from '../../utils/prodLogger';

const DebugSubscription = () => {
  const { user, isAuthenticated } = useAuth();
  const [testResults, setTestResults] = useState(null);
  const [loading, setLoading] = useState(false);

  const testAuthentication = async () => {
    setLoading(true);
    setTestResults(null);

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      
      const results = {
        isAuthenticated,
        hasUser: !!user,
        userEmail: user?.email,
        userId: user?.id,
        hasSession: !!sessionData.session,
        hasAccessToken: !!sessionData.session?.access_token,
        sessionError: sessionError?.message,
        accessTokenPrefix: sessionData.session?.access_token?.substring(0, 20) + '...',
      };

      setTestResults({ type: 'auth', results });
      prodLogger.info('Authentication test results:', results);
    } catch (error) {
      setTestResults({ type: 'error', error: error.message });
      prodLogger.error('Authentication test error:', error);
    } finally {
      setLoading(false);
    }
  };

  const testEdgeFunction = async () => {
    setLoading(true);
    setTestResults(null);

    try {
      if (!user?.id || !user?.email) {
        throw new Error('User not properly authenticated');
      }

      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !sessionData.session) {
        throw new Error('No valid session found');
      }

      prodLogger.info('Testing Edge Function with:', {
        userId: user.id,
        userEmail: user.email,
        hasAccessToken: !!sessionData.session.access_token
      });

      // Use direct fetch instead of supabase.functions.invoke due to client issues
      const functionUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-checkout-session`;

      const response = await fetch(functionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionData.session.access_token}`,
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
        },
        body: JSON.stringify({
          priceId: 'price_1RwhwQRv3cBSjebHxtaaq2sV',
          userId: user.id,
          userEmail: user.email,
          tier: 'basic',
          billingPeriod: 'monthly',
          successUrl: `${window.location.origin}/account-settings?session_id={CHECKOUT_SESSION_ID}`,
          cancelUrl: `${window.location.origin}/pricing`,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { error: errorText };
        }
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      const results = {
        success: true,
        data: data,
        error: null,
        errorDetails: error,
        hasSessionId: !!data?.sessionId,
        hasUrl: !!data?.url,
        fullError: error,
      };

      setTestResults({ type: 'edge-function', results });
      prodLogger.info('Edge Function test results:', results);
      prodLogger.error('Full error object:', error);
    } catch (error) {
      setTestResults({ type: 'error', error: error.message, fullError: error });
      prodLogger.error('Edge Function test error:', error);
    } finally {
      setLoading(false);
    }
  };

  const testEdgeFunctionDirect = async () => {
    setLoading(true);
    setTestResults(null);

    try {
      if (!user?.id || !user?.email) {
        throw new Error('User not properly authenticated');
      }

      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !sessionData.session) {
        throw new Error('No valid session found');
      }

      // Test direct fetch to the Edge Function
      const functionUrl = 'https://mlfojzeyywdxlpbgbtsv.supabase.co/functions/v1/create-checkout-session';

      prodLogger.info('Testing direct fetch to:', functionUrl);

      const response = await fetch(functionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionData.session.access_token}`,
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.XEzo8XCXbPWO2YY5e7naOFhTE1a3slCdVcQs7O9RShs'
        },
        body: JSON.stringify({
          priceId: 'price_1RwhwQRv3cBSjebHxtaaq2sV',
          userId: user.id,
          userEmail: user.email,
          tier: 'basic',
          billingPeriod: 'monthly',
          successUrl: `${window.location.origin}/account-settings?session_id={CHECKOUT_SESSION_ID}`,
          cancelUrl: `${window.location.origin}/pricing`,
        })
      });

      const responseText = await response.text();
      let responseData;

      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = responseText;
      }

      const results = {
        success: response.ok,
        status: response.status,
        statusText: response.statusText,
        responseData: responseData,
        responseText: responseText,
        hasSessionId: responseData?.sessionId,
        hasUrl: responseData?.url,
      };

      setTestResults({ type: 'direct-fetch', results });
      prodLogger.info('Direct fetch test results:', results);
    } catch (error) {
      setTestResults({ type: 'error', error: error.message, fullError: error });
      prodLogger.error('Direct fetch test error:', error);
    } finally {
      setLoading(false);
    }
  };

  const testDirectStripeService = async () => {
    setLoading(true);
    setTestResults(null);

    try {
      if (!user?.id || !user?.email) {
        throw new Error('User not properly authenticated');
      }

      // Import the stripe service dynamically
      const { default: stripeService } = await import('../../services/stripeService');
      
      prodLogger.info('Testing Stripe Service with:', {
        userId: user.id,
        userEmail: user.email
      });

      const result = await stripeService.createCheckoutSession('basic', 'monthly', user.id, user.email);

      const results = {
        success: true,
        sessionId: result.sessionId,
        hasUrl: !!result.url,
        result: result
      };

      setTestResults({ type: 'stripe-service', results });
      prodLogger.info('Stripe Service test results:', results);
    } catch (error) {
      setTestResults({ type: 'error', error: error.message });
      prodLogger.error('Stripe Service test error:', error);
    } finally {
      setLoading(false);
    }
  };

  const testStripeConfig = async () => {
    setLoading(true);
    setTestResults(null);

    try {
      // Test the current environment variables and configuration
      const config = {
        stripePublishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY,
        stripeSecretKey: import.meta.env.STRIPE_SECRET_KEY ? 'SET' : 'NOT_SET',
        supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
        priceIds: {
          basicMonthly: import.meta.env.VITE_STRIPE_BASIC_MONTHLY_PRICE_ID,
          basicYearly: import.meta.env.VITE_STRIPE_BASIC_YEARLY_PRICE_ID,
          standardMonthly: import.meta.env.VITE_STRIPE_STANDARD_MONTHLY_PRICE_ID,
          standardYearly: import.meta.env.VITE_STRIPE_STANDARD_YEARLY_PRICE_ID,
          proMonthly: import.meta.env.VITE_STRIPE_PRO_MONTHLY_PRICE_ID,
          proYearly: import.meta.env.VITE_STRIPE_PRO_YEARLY_PRICE_ID,
        },
        customerPortalConfigId: import.meta.env.VITE_STRIPE_CUSTOMER_PORTAL_CONFIG_ID,
      };

      // Check if publishable key is test or live
      const isTestMode = config.stripePublishableKey?.startsWith('pk_test_');
      const isLiveMode = config.stripePublishableKey?.startsWith('pk_live_');

      // Check if price IDs are test or live
      const priceIdModes = Object.entries(config.priceIds).map(([key, priceId]) => ({
        [key]: {
          priceId,
          isTest: priceId?.startsWith('price_') && priceId.includes('test'),
          isLive: priceId?.startsWith('price_') && !priceId.includes('test'),
        }
      }));

      const results = {
        config,
        isTestMode,
        isLiveMode,
        priceIdModes,
        modeConsistency: {
          allTest: isTestMode && priceIdModes.every(item => Object.values(item)[0].isTest),
          allLive: isLiveMode && priceIdModes.every(item => Object.values(item)[0].isLive),
        },
        recommendations: []
      };

      // Add recommendations
      if (!results.modeConsistency.allTest && !results.modeConsistency.allLive) {
        results.recommendations.push('Mode mismatch detected! Ensure all keys are either test or live mode.');
      }

      if (!config.stripePublishableKey) {
        results.recommendations.push('Missing VITE_STRIPE_PUBLISHABLE_KEY');
      }

      if (Object.values(config.priceIds).some(id => !id)) {
        results.recommendations.push('Some price IDs are missing');
      }

      setTestResults({ type: 'stripe-config', results });
      prodLogger.info('Stripe config test results:', results);
    } catch (error) {
      setTestResults({ type: 'error', error: error.message });
      prodLogger.error('Stripe config test error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">Please log in to test subscription functionality.</p>
      </div>
    );
  }

  return (
    <div className="p-6 bg-surface border border-border rounded-lg space-y-4">
      <h3 className="text-lg font-semibold text-text-primary">Debug Subscription System</h3>
      
      <div className="flex flex-wrap gap-3">
        <Button
          onClick={testAuthentication}
          disabled={loading}
          variant="outline"
        >
          Test Authentication
        </Button>

        <Button
          onClick={testEdgeFunction}
          disabled={loading}
          variant="outline"
        >
          Test Edge Function (Supabase)
        </Button>

        <Button
          onClick={testEdgeFunctionDirect}
          disabled={loading}
          variant="outline"
        >
          Test Edge Function (Direct)
        </Button>

        <Button
          onClick={testDirectStripeService}
          disabled={loading}
          variant="outline"
        >
          Test Stripe Service
        </Button>

        <Button
          onClick={testStripeConfig}
          disabled={loading}
          variant="outline"
        >
          Test Stripe Config
        </Button>

        <Button
          onClick={() => testWithCustomPriceId()}
          disabled={loading}
          variant="outline"
        >
          Test Custom Price ID
        </Button>
      </div>

      {loading && (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded">
          <p className="text-blue-800">Testing...</p>
        </div>
      )}

      {testResults && (
        <div className="p-4 bg-gray-50 border border-gray-200 rounded">
          <h4 className="font-medium text-gray-900 mb-2">
            Test Results ({testResults.type}):
          </h4>
          <pre className="text-sm text-gray-700 overflow-auto">
            {JSON.stringify(testResults.results || testResults.error, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default DebugSubscription;
