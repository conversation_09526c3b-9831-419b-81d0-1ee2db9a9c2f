import React from 'react';
import Button from '../ui/Button';
import { useNotifications } from '../../hooks/useNotifications';
import { NOTIFICATION_TYPES, NOTIFICATION_CATEGORIES } from '../../services/notificationService';

/**
 * Notification Test Panel
 * 
 * Debug component for testing the notification system functionality.
 * This component provides buttons to trigger different types of notifications
 * to verify the system is working correctly.
 */
const NotificationTestPanel = () => {
  const {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,
    showProjectNotification,
    showTemplateNotification,
    showExportNotification,
    showUploadNotification,
    showGenerationNotification,
    showFontNotification,
    notifications,
    unreadCount,
    clearNotifications
  } = useNotifications();

  const testBasicNotifications = () => {
    showSuccess('Success Test', 'This is a success notification test.');
    
    setTimeout(() => {
      showInfo('Info Test', 'This is an info notification test.');
    }, 500);
    
    setTimeout(() => {
      showWarning('Warning Test', 'This is a warning notification test.');
    }, 1000);
    
    setTimeout(() => {
      showError('Error Test', 'This is an error notification test.');
    }, 1500);
  };

  const testCategoryNotifications = () => {
    showProjectNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Project Test',
      'Test project has been created successfully.'
    );
    
    setTimeout(() => {
      showTemplateNotification(
        NOTIFICATION_TYPES.SUCCESS,
        'Template Test',
        'Test template has been saved successfully.'
      );
    }, 500);
    
    setTimeout(() => {
      showExportNotification(
        NOTIFICATION_TYPES.SUCCESS,
        'Export Test',
        'Test document has been exported successfully.'
      );
    }, 1000);
    
    setTimeout(() => {
      showUploadNotification(
        NOTIFICATION_TYPES.SUCCESS,
        'Upload Test',
        'Test file has been uploaded successfully.'
      );
    }, 1500);
    
    setTimeout(() => {
      showGenerationNotification(
        NOTIFICATION_TYPES.SUCCESS,
        'Generation Test',
        'Test content has been generated successfully.'
      );
    }, 2000);
    
    setTimeout(() => {
      showFontNotification(
        NOTIFICATION_TYPES.SUCCESS,
        'Font Test',
        'Test font has been loaded successfully.'
      );
    }, 2500);
  };

  const testLoadingNotification = () => {
    const loadingId = showLoading('Processing', 'Please wait while we process your request...');
    
    setTimeout(() => {
      // In a real scenario, you would remove the loading notification
      // and show a success/error notification
      showSuccess('Processing Complete', 'Your request has been processed successfully.');
    }, 3000);
  };

  const testErrorScenarios = () => {
    showProjectNotification(
      NOTIFICATION_TYPES.ERROR,
      'Project Creation Failed',
      'Failed to create project due to validation errors.',
      { persistent: true }
    );
    
    setTimeout(() => {
      showTemplateNotification(
        NOTIFICATION_TYPES.ERROR,
        'Template Upload Failed',
        'Failed to upload template background image.',
        { persistent: true }
      );
    }, 500);
  };

  const testPersistentNotifications = () => {
    showWarning(
      'Service Unavailable',
      'AI service is temporarily unavailable. Please try again later.',
      { persistent: true }
    );
    
    setTimeout(() => {
      showError(
        'Critical Error',
        'A critical error occurred. Please contact support.',
        { persistent: true }
      );
    }, 500);
  };

  return (
    <div className="p-6 bg-surface rounded-lg border border-border max-w-2xl mx-auto">
      <h2 className="text-xl font-semibold text-text-primary mb-4">
        Notification System Test Panel
      </h2>
      
      <div className="mb-6">
        <p className="text-sm text-text-secondary mb-2">
          Current notifications: {notifications.length} | Unread: {unreadCount}
        </p>
        <Button
          variant="outline"
          size="sm"
          onClick={clearNotifications}
          disabled={notifications.length === 0}
        >
          Clear All Notifications
        </Button>
      </div>

      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-medium text-text-primary mb-2">Basic Notification Types</h3>
          <Button onClick={testBasicNotifications} className="mr-2">
            Test Basic Types
          </Button>
          <Button onClick={testLoadingNotification} variant="outline">
            Test Loading
          </Button>
        </div>

        <div>
          <h3 className="text-lg font-medium text-text-primary mb-2">Category-Specific Notifications</h3>
          <Button onClick={testCategoryNotifications} variant="secondary">
            Test All Categories
          </Button>
        </div>

        <div>
          <h3 className="text-lg font-medium text-text-primary mb-2">Error Scenarios</h3>
          <Button onClick={testErrorScenarios} variant="destructive" className="mr-2">
            Test Error Notifications
          </Button>
          <Button onClick={testPersistentNotifications} variant="outline">
            Test Persistent Notifications
          </Button>
        </div>

        <div>
          <h3 className="text-lg font-medium text-text-primary mb-2">Individual Tests</h3>
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              onClick={() => showSuccess('Quick Success', 'Operation completed!')}
            >
              Success
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => showInfo('Quick Info', 'Here is some information.')}
            >
              Info
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={() => showWarning('Quick Warning', 'Please be careful.')}
            >
              Warning
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={() => showError('Quick Error', 'Something went wrong.')}
            >
              Error
            </Button>
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-background rounded border">
        <h4 className="text-sm font-medium text-text-primary mb-2">Instructions</h4>
        <ul className="text-xs text-text-secondary space-y-1">
          <li>• Click the notification bell in the header to view notifications</li>
          <li>• Toast notifications appear in the top-right corner</li>
          <li>• Error and warning notifications are persistent by default</li>
          <li>• Success and info notifications auto-dismiss after a few seconds</li>
          <li>• Loading notifications remain until manually dismissed</li>
        </ul>
      </div>
    </div>
  );
};

export default NotificationTestPanel;
