import React, { useState, useCallback, useEffect } from 'react';
import { Type } from 'lucide-react';

/**
 * Font Size Editor Component
 * Provides slider and input controls for adjusting font size
 * with real-time preview and mobile-optimized interface
 */
const FontSizeEditor = ({
  value = 16,
  onChange = null,
  min = 1,
  max = 200,
  step = 1,
  isMobile = false,
  className = ''
}) => {
  const [inputValue, setInputValue] = useState(value.toString());

  // Update input when value prop changes
  useEffect(() => {
    setInputValue(value.toString());
  }, [value]);

  // Handle slider change
  const handleSliderChange = useCallback((event) => {
    const newValue = parseInt(event.target.value, 10);
    setInputValue(newValue.toString());
    onChange?.(newValue);
  }, [onChange]);

  // Handle input change
  const handleInputChange = useCallback((event) => {
    const newValue = event.target.value;
    setInputValue(newValue);

    // Validate and apply if valid (only check minimum for input, no maximum limit)
    const numValue = parseInt(newValue, 10);
    if (!isNaN(numValue) && numValue >= min) {
      onChange?.(numValue);
    }
  }, [onChange, min]);

  // Handle input blur (apply value if valid)
  const handleInputBlur = useCallback(() => {
    const numValue = parseInt(inputValue, 10);
    if (isNaN(numValue)) {
      setInputValue(value.toString());
      return;
    }

    // Only enforce minimum value, no maximum limit
    const clampedValue = Math.max(min, numValue);
    setInputValue(clampedValue.toString());

    if (clampedValue !== value) {
      onChange?.(clampedValue);
    }
  }, [inputValue, value, onChange, min]);

  const handlePresetClick = useCallback((presetValue) => {
    setInputValue(presetValue.toString());
    onChange?.(presetValue);
  }, [onChange]);

  return (
    <div className={`font-size-editor ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-2 mb-3">
        <Type className="w-4 h-4 text-gray-600" />
        <label className="block text-sm font-medium text-gray-700">
          Font Size
        </label>
        <span className="text-xs text-gray-500">({value}px)</span>
      </div>

      {/* Slider */}
      <div className="mb-4">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={Math.min(value, max)} // Clamp slider value to max
          onChange={handleSliderChange}
          className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider ${
            isMobile ? 'h-3' : 'h-2'
          }`}
          style={{
            background: `linear-gradient(to right, #3B82F6 0%, #3B82F6 ${
              ((Math.min(value, max) - min) / (max - min)) * 100
            }%, #E5E7EB ${((Math.min(value, max) - min) / (max - min)) * 100}%, #E5E7EB 100%)`
          }}
        />

        {/* Slider labels */}
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>{min}px</span>
          <span>{max}px</span>
        </div>
      </div>

      {/* Direct Input */}
      <div className="mb-4">
        <div className="relative">
          <input
            type="number"
            min={min}
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isMobile ? 'text-base' : 'text-sm'
            }`}
            placeholder="Enter font size"
          />
          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">
            px
          </span>
        </div>
      </div>
    </div>
  );
};

export default FontSizeEditor;
