import React, { useCallback } from 'react';
import { AlignLeft, AlignCenter, AlignRight, AlignJustify } from 'lucide-react';

/**
 * Text Alignment Editor Component
 * Provides button-based interface for selecting text alignment
 * with visual icons and mobile-optimized touch targets
 */
const AlignmentEditor = ({
  value = 'left',
  onChange = null,
  isMobile = false,
  className = ''
}) => {
  // Alignment options with icons and labels
  const alignmentOptions = [
    {
      value: 'left',
      label: 'Left',
      icon: AlignLeft,
      description: 'Align text to the left'
    },
    {
      value: 'center',
      label: 'Center',
      icon: AlignCenter,
      description: 'Center align text'
    },
    {
      value: 'right',
      label: 'Right',
      icon: AlignRight,
      description: 'Align text to the right'
    },
    {
      value: 'justify',
      label: 'Justify',
      icon: AlignJustify,
      description: 'Justify text (full width)'
    }
  ];

  // Handle alignment change
  const handleAlignmentChange = useCallback((newAlignment) => {
    if (onChange && newAlignment !== value) {
      onChange(newAlignment);
    }
  }, [onChange, value]);

  return (
    <div className={`alignment-editor ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-2 mb-3">
        <AlignLeft className="w-4 h-4 text-gray-600" />
        <label className="block text-sm font-medium text-gray-700">
          Text Alignment
        </label>
      </div>

      {/* Alignment Buttons */}
      <div className="space-y-3">
        {/* Button Group */}
        <div className={`grid gap-2 ${isMobile ? 'grid-cols-2' : 'grid-cols-4'}`}>
          {alignmentOptions.map((option) => {
            const IconComponent = option.icon;
            const isSelected = value === option.value;
            
            return (
              <button
                key={option.value}
                onClick={() => handleAlignmentChange(option.value)}
                className={`flex flex-col items-center justify-center p-3 border rounded-lg transition-all ${
                  isSelected
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 bg-white text-gray-600 hover:border-gray-400 hover:bg-gray-50'
                } ${isMobile ? 'p-4' : 'p-3'}`}
                title={option.description}
              >
                <IconComponent className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'} mb-1`} />
                <span className={`text-xs font-medium ${isMobile ? 'text-sm' : 'text-xs'}`}>
                  {option.label}
                </span>
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default AlignmentEditor;
