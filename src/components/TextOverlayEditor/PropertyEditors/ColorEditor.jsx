import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Palette, <PERSON>pette } from 'lucide-react';

/**
 * Color Editor Component
 * Provides color picker with preset swatches and custom color input
 * Optimized for both desktop and mobile interfaces
 */
const ColorEditor = ({
  value = '#000000',
  onChange = null,
  isMobile = false,
  className = ''
}) => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const [customColor, setCustomColor] = useState(value);
  const colorInputRef = useRef(null);
  const pickerRef = useRef(null);

  // Update custom color when value prop changes
  useEffect(() => {
    setCustomColor(value);
  }, [value]);

  // Color presets organized by category
  const colorPresets = {
    'Text Colors': [
      '#000000', // Black
      '#1F2937', // Gray 800
      '#374151', // Gray 700
      '#6B7280', // Gray 500
      '#FFFFFF'  // White
    ],
    'Brand Colors': [
      '#3B82F6', // Blue 500
      '#1D4ED8', // Blue 700
      '#EF4444', // Red 500
      '#DC2626', // Red 600
      '#10B981'  // Green 500
    ],
    'Accent Colors': [
      '#8B5CF6', // Purple 500
      '#F59E0B', // Amber 500
      '#EC4899', // Pink 500
      '#06B6D4', // Cyan 500
      '#84CC16'  // Lime 500
    ]
  };

  // Handle preset color selection
  const handlePresetClick = useCallback((color) => {
    setCustomColor(color);
    onChange?.(color);
    setIsPickerOpen(false);
  }, [onChange]);

  // Handle custom color input change
  const handleCustomColorChange = useCallback((event) => {
    const newColor = event.target.value;
    setCustomColor(newColor);
    onChange?.(newColor);
  }, [onChange]);

  // Handle hex input change
  const handleHexInputChange = useCallback((event) => {
    let hexValue = event.target.value;
    
    // Ensure it starts with #
    if (!hexValue.startsWith('#')) {
      hexValue = '#' + hexValue;
    }
    
    // Validate hex format
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (hexRegex.test(hexValue)) {
      setCustomColor(hexValue);
      onChange?.(hexValue);
    } else if (hexValue.length <= 7) {
      setCustomColor(hexValue);
    }
  }, [onChange]);

  // Toggle color picker
  const handleTogglePicker = useCallback(() => {
    setIsPickerOpen(!isPickerOpen);
  }, [isPickerOpen]);

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target)) {
        setIsPickerOpen(false);
      }
    };

    if (isPickerOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isPickerOpen]);

  // Convert hex to RGB for display
  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  const rgbValues = hexToRgb(value);

  return (
    <div className={`color-editor ${className}`} ref={pickerRef}>
      {/* Header */}
      <div className="flex items-center space-x-2 mb-3">
        <Palette className="w-4 h-4 text-gray-600" />
        <label className="block text-sm font-medium text-gray-700">
          Text Color
        </label>
      </div>

      {/* Current Color Display */}
      <div className="mb-4">
        <button
          onClick={handleTogglePicker}
          className={`w-full flex items-center space-x-3 p-3 border border-gray-300 rounded-lg hover:border-gray-400 transition-colors ${
            isMobile ? 'p-4' : 'p-3'
          }`}
        >
          {/* Color Swatch */}
          <div
            className={`rounded border border-gray-300 ${
              isMobile ? 'w-8 h-8' : 'w-6 h-6'
            }`}
            style={{ backgroundColor: value }}
          />
          
          {/* Color Info */}
          <div className="flex-1 text-left">
            <div className="text-sm font-medium text-gray-900">{value.toUpperCase()}</div>
            {rgbValues && (
              <div className="text-xs text-gray-500">
                RGB({rgbValues.r}, {rgbValues.g}, {rgbValues.b})
              </div>
            )}
          </div>
          
          {/* Picker Icon */}
          <Pipette className="w-4 h-4 text-gray-400" />
        </button>
      </div>

      {/* Color Picker Panel */}
      {isPickerOpen && (
        <div className="mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          {/* Preset Colors */}
          <div className="space-y-4">
            {Object.entries(colorPresets).map(([category, colors]) => (
              <div key={category}>
                <label className="block text-xs font-medium text-gray-600 mb-2">
                  {category}
                </label>
                <div className={`grid gap-2 ${isMobile ? 'grid-cols-5' : 'grid-cols-5'}`}>
                  {colors.map((color) => (
                    <button
                      key={color}
                      onClick={() => handlePresetClick(color)}
                      className={`aspect-square rounded border-2 transition-all hover:scale-110 ${
                        value === color
                          ? 'border-blue-500 ring-2 ring-blue-200'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                      style={{ backgroundColor: color }}
                      title={color}
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Custom Color Input */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <label className="block text-xs font-medium text-gray-600 mb-2">
              Custom Color
            </label>
            <div className="flex space-x-2">
              {/* Native Color Picker */}
              <input
                ref={colorInputRef}
                type="color"
                value={customColor}
                onChange={handleCustomColorChange}
                className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
              />
              
              {/* Hex Input */}
              <input
                type="text"
                value={customColor}
                onChange={handleHexInputChange}
                placeholder="#000000"
                className={`flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono ${
                  isMobile ? 'text-base' : 'text-sm'
                }`}
                maxLength={7}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ColorEditor;
