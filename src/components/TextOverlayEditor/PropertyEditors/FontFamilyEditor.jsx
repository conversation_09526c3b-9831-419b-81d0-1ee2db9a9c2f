import React, { useState, useCallback } from 'react';
import { Type, Plus, Loader } from 'lucide-react';
import fontLoader from '../../../utils/fontLoader.js';

import { prodLogger } from '../../../utils/prodLogger.js';
import { PRELOADED_GOOGLE_FONTS } from '../../../utils/preloadedGoogleFonts.js';

// Use centralized preloaded Google Fonts registry
const GOOGLE_FONTS = PRELOADED_GOOGLE_FONTS;

/**
 * Font Family Editor Component
 * Provides font selection with Google Fonts integration and custom font loading
 */
const FontFamilyEditor = ({
  value = 'Arial',
  onChange = null,
  isMobile = false,
  className = ''
}) => {
  const [isLoadingFont, setIsLoadingFont] = useState(false);
  const [customFontUrl, setCustomFontUrl] = useState('');
  const [customFontName, setCustomFontName] = useState('');
  const [fontLoadingStatus, setFontLoadingStatus] = useState('');
  const [showCustomFontInput, setShowCustomFontInput] = useState(false);
  const [customFonts, setCustomFonts] = useState([]);

  // Handle font selection
  const handleFontChange = useCallback(async (fontFamily) => {
    // Find the selected font
    const allFonts = [...GOOGLE_FONTS, ...customFonts];
    const selectedFont = allFonts.find(font => font.name === fontFamily);

    if (selectedFont?.googleFont) {
      setIsLoadingFont(true);
      try {
        await fontLoader.loadGoogleFont(selectedFont.name);
        prodLogger.debug(`Loaded Google Font: ${selectedFont.name}`);
      } catch (error) {
        prodLogger.warn(`Failed to load font ${selectedFont.name}:`, error);
      } finally {
        setIsLoadingFont(false);
      }
    }

    onChange?.(fontFamily);
  }, [onChange, customFonts]);

  // Load custom Google Font from URL
  const handleLoadCustomFont = useCallback(async () => {
    if (!customFontUrl.trim()) {
      setFontLoadingStatus('Please enter a Google Fonts URL');
      return;
    }

    setIsLoadingFont(true);
    setFontLoadingStatus('Loading font...');

    try {
      // Extract font name from URL (basic extraction)
      const urlParams = new URLSearchParams(new URL(customFontUrl).search);
      const familyParam = urlParams.get('family');
      let extractedName = customFontName;

      if (familyParam && !customFontName) {
        // Try to extract font name from family parameter
        const fontMatch = familyParam.match(/^([^:]+)/);
        if (fontMatch) {
          extractedName = fontMatch[1].replace(/\+/g, ' ');
          setCustomFontName(extractedName);
        }
      }

      // Load the custom font
      await fontLoader.loadCustomFont(customFontUrl, extractedName || 'Custom Font');
      
      setFontLoadingStatus(`✅ Font loaded successfully!`);
      
      // Add to custom fonts list if name is provided
      if (extractedName) {
        const newCustomFont = {
          name: extractedName,
          category: 'Custom',
          fallback: `"${extractedName}", sans-serif`,
          googleFont: true,
          customUrl: customFontUrl
        };

        setCustomFonts(prev => {
          // Check if already exists
          if (prev.some(font => font.name === extractedName)) {
            return prev;
          }
          return [...prev, newCustomFont];
        });

        // Auto-select the loaded font
        onChange?.(extractedName);
      }

    } catch (error) {
      setFontLoadingStatus(`❌ ${error.message}`);
    } finally {
      setIsLoadingFont(false);
    }
  }, [customFontUrl, customFontName, onChange]);

  // Group fonts by category
  const fontsByCategory = [...GOOGLE_FONTS, ...customFonts].reduce((acc, font) => {
    if (!acc[font.category]) {
      acc[font.category] = [];
    }
    acc[font.category].push(font);
    return acc;
  }, {});

  return (
    <div className={`font-family-editor ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Type className="w-4 h-4 text-gray-600" />
          <label className="block text-sm font-medium text-gray-700">
            Font Family
          </label>
          {isLoadingFont && <Loader className="w-4 h-4 animate-spin text-blue-500" />}
        </div>
        <button
          onClick={() => setShowCustomFontInput(!showCustomFontInput)}
          className="text-xs text-blue-600 hover:text-blue-700 flex items-center space-x-1"
        >
          <Plus className="w-3 h-3" />
          <span>Custom</span>
        </button>
      </div>

      {/* Font Selection */}
      <div className="space-y-3">
        <select
          value={value}
          onChange={(e) => handleFontChange(e.target.value)}
          disabled={isLoadingFont}
          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
          style={{ fontFamily: fontLoader.getFontWithFallbacks(value) }}
        >
          {Object.entries(fontsByCategory).map(([category, fonts]) => (
            <optgroup key={category} label={category}>
              {fonts.map(font => (
                <option 
                  key={font.name} 
                  value={font.name}
                  style={{ fontFamily: font.fallback }}
                >
                  {font.name}
                </option>
              ))}
            </optgroup>
          ))}
        </select>

        {/* Custom Font Input */}
        {showCustomFontInput && (
          <div className="p-3 bg-gray-50 rounded-lg border space-y-3">
            <div>
              <label className="block text-xs text-gray-600 mb-1">Google Fonts URL</label>
              <input
                type="url"
                value={customFontUrl}
                onChange={(e) => setCustomFontUrl(e.target.value)}
                placeholder="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap"
                className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 font-mono"
              />
            </div>
            
            <div>
              <label className="block text-xs text-gray-600 mb-1">Font Name (optional)</label>
              <input
                type="text"
                value={customFontName}
                onChange={(e) => setCustomFontName(e.target.value)}
                placeholder="e.g., Roboto"
                className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
              />
            </div>

            <div className="flex space-x-2">
              <button
                onClick={handleLoadCustomFont}
                disabled={isLoadingFont || !customFontUrl.trim()}
                className="flex-1 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoadingFont ? 'Loading...' : 'Load Font'}
              </button>
              <button
                onClick={() => {
                  setShowCustomFontInput(false);
                  setCustomFontUrl('');
                  setCustomFontName('');
                  setFontLoadingStatus('');
                }}
                className="px-3 py-1 text-xs text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>

            {fontLoadingStatus && (
              <p className="text-xs text-gray-600 mt-2">{fontLoadingStatus}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FontFamilyEditor;
