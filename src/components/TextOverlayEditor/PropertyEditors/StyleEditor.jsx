import React, { useCallback } from 'react';
import { Bold, Italic, Underline } from 'lucide-react';

/**
 * Font Style Editor Component
 * Provides toggle controls for font weight, style, and text decoration
 * with visual feedback and mobile-optimized interface
 */
const StyleEditor = ({
  fontWeight = 'normal',
  fontStyle = 'normal',
  textDecoration = 'none',
  onFontWeightChange = null,
  onFontStyleChange = null,
  onTextDecorationChange = null,
  isMobile = false,
  className = ''
}) => {
  // Handle font weight toggle
  const handleFontWeightToggle = useCallback(() => {
    const newWeight = fontWeight === 'bold' ? 'normal' : 'bold';
    onFontWeightChange?.(newWeight);
  }, [fontWeight, onFontWeightChange]);

  // Handle font style toggle
  const handleFontStyleToggle = useCallback(() => {
    const newStyle = fontStyle === 'italic' ? 'normal' : 'italic';
    onFontStyleChange?.(newStyle);
  }, [fontStyle, onFontStyleChange]);

  // Handle text decoration toggle
  const handleTextDecorationToggle = useCallback(() => {
    const newDecoration = textDecoration === 'underline' ? 'none' : 'underline';
    onTextDecorationChange?.(newDecoration);
  }, [textDecoration, onTextDecorationChange]);

  // Style options configuration
  const styleOptions = [
    {
      key: 'bold',
      label: 'Bold',
      icon: Bold,
      isActive: fontWeight === 'bold',
      onClick: handleFontWeightToggle,
      description: 'Make text bold'
    },
    {
      key: 'italic',
      label: 'Italic',
      icon: Italic,
      isActive: fontStyle === 'italic',
      onClick: handleFontStyleToggle,
      description: 'Make text italic'
    },
    {
      key: 'underline',
      label: 'Underline',
      icon: Underline,
      isActive: textDecoration === 'underline',
      onClick: handleTextDecorationToggle,
      description: 'Underline text'
    }
  ];

  return (
    <div className={`style-editor ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-2 mb-3">
        <Bold className="w-4 h-4 text-gray-600" />
        <label className="block text-sm font-medium text-gray-700">
          Text Style
        </label>
      </div>

      {/* Style Toggle Buttons */}
      <div className="space-y-3">
        {/* Button Group */}
        <div className={`grid gap-2 ${isMobile ? 'grid-cols-1' : 'grid-cols-3'}`}>
          {styleOptions.map((option) => {
            const IconComponent = option.icon;
            
            return (
              <button
                key={option.key}
                onClick={option.onClick}
                className={`flex items-center justify-center space-x-2 p-3 border rounded-lg transition-all ${
                  option.isActive
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 bg-white text-gray-600 hover:border-gray-400 hover:bg-gray-50'
                } ${isMobile ? 'p-4 justify-start' : 'p-3 justify-center'}`}
                title={option.description}
              >
                <IconComponent className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
                <span className={`font-medium ${isMobile ? 'text-sm' : 'text-xs'}`}>
                  {option.label}
                </span>
                {isMobile && (
                  <div className="ml-auto">
                    <div className={`w-4 h-4 rounded border-2 ${
                      option.isActive 
                        ? 'bg-blue-500 border-blue-500' 
                        : 'border-gray-300'
                    }`}>
                      {option.isActive && (
                        <svg className="w-full h-full text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                  </div>
                )}
              </button>
            );
          })}
        </div>

        {/* Font Weight Selector (Extended) */}
        <div className="space-y-2">
          <label className="block text-xs font-medium text-gray-600">
            Font Weight
          </label>
          <div className={`grid gap-2 ${isMobile ? 'grid-cols-2' : 'grid-cols-4'}`}>
            {[
              { value: 'normal', label: 'Normal' },
              { value: 'bold', label: 'Bold' },
              { value: '300', label: 'Light' },
              { value: '600', label: 'Semi Bold' }
            ].map((weight) => (
              <button
                key={weight.value}
                onClick={() => onFontWeightChange?.(weight.value)}
                className={`px-3 py-2 text-xs font-medium rounded border transition-colors ${
                  fontWeight === weight.value
                    ? 'bg-blue-100 border-blue-300 text-blue-700'
                    : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                } ${isMobile ? 'py-3' : 'py-2'}`}
                style={{ fontWeight: weight.value }}
              >
                {weight.label}
              </button>
            ))}
          </div>
        </div>

        {/* Current Style Summary */}
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="text-xs text-gray-500 mb-1">Active Styles:</div>
          <div className="flex flex-wrap gap-1">
            {styleOptions.filter(opt => opt.isActive).map((option) => (
              <span
                key={option.key}
                className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded"
              >
                {option.label}
              </span>
            ))}
            {!styleOptions.some(opt => opt.isActive) && (
              <span className="text-xs text-gray-500">None</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StyleEditor;
