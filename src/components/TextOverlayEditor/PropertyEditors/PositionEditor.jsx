import React, { useState, useCallback, useEffect } from 'react';
import { Move, ArrowUp, ArrowDown, ArrowLeft, ArrowRight } from 'lucide-react';

/**
 * Position Editor Component
 * Provides controls for adjusting text overlay position (x, y, width, height)
 * with numeric inputs, directional buttons, and visual feedback
 */
const PositionEditor = ({
  position = { x: 0, y: 0, width: 100, height: 50 },
  onChange = null,
  isMobile = false,
  className = ''
}) => {
  const [localPosition, setLocalPosition] = useState(position);

  // Update local state when position prop changes
  useEffect(() => {
    setLocalPosition(position);
  }, [position]);

  // Handle position property change
  const handlePositionChange = useCallback((property, value) => {
    const numValue = parseInt(value, 10);
    if (isNaN(numValue)) return;

    const newPosition = {
      ...localPosition,
      [property]: numValue
    };

    setLocalPosition(newPosition);
    onChange?.(property, numValue);
  }, [localPosition, onChange]);

  // Handle directional movement
  const handleMove = useCallback((direction, amount = 5) => {
    const movements = {
      up: { property: 'y', delta: -amount },
      down: { property: 'y', delta: amount },
      left: { property: 'x', delta: -amount },
      right: { property: 'x', delta: amount }
    };

    const movement = movements[direction];
    if (movement) {
      const newValue = Math.max(0, localPosition[movement.property] + movement.delta);
      handlePositionChange(movement.property, newValue);
    }
  }, [localPosition, handlePositionChange]);

  // Handle size adjustment
  const handleSizeChange = useCallback((dimension, delta) => {
    const newValue = Math.max(10, localPosition[dimension] + delta);
    handlePositionChange(dimension, newValue);
  }, [localPosition, handlePositionChange]);

  return (
    <div className={`position-editor ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-2 mb-3">
        <Move className="w-4 h-4 text-gray-600" />
        <label className="block text-sm font-medium text-gray-700">
          Position & Size
        </label>
      </div>

      {/* Position Controls */}
      <div className="space-y-4">
        {/* X/Y Position */}
        <div className="space-y-3">
          <label className="block text-xs font-medium text-gray-600">
            Position (X, Y)
          </label>
          
          {/* Directional Controls */}
          <div className="flex items-center justify-center mb-3">
            <div className="grid grid-cols-3 gap-1">
              {/* Top row */}
              <div></div>
              <button
                onClick={() => handleMove('up')}
                className={`p-2 bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors ${
                  isMobile ? 'p-3' : 'p-2'
                }`}
                title="Move up"
              >
                <ArrowUp className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'} text-gray-600`} />
              </button>
              <div></div>
              
              {/* Middle row */}
              <button
                onClick={() => handleMove('left')}
                className={`p-2 bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors ${
                  isMobile ? 'p-3' : 'p-2'
                }`}
                title="Move left"
              >
                <ArrowLeft className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'} text-gray-600`} />
              </button>
              <div className={`p-2 bg-gray-50 border border-gray-200 rounded flex items-center justify-center ${
                isMobile ? 'p-3' : 'p-2'
              }`}>
                <Move className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'} text-gray-400`} />
              </div>
              <button
                onClick={() => handleMove('right')}
                className={`p-2 bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors ${
                  isMobile ? 'p-3' : 'p-2'
                }`}
                title="Move right"
              >
                <ArrowRight className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'} text-gray-600`} />
              </button>
              
              {/* Bottom row */}
              <div></div>
              <button
                onClick={() => handleMove('down')}
                className={`p-2 bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors ${
                  isMobile ? 'p-3' : 'p-2'
                }`}
                title="Move down"
              >
                <ArrowDown className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'} text-gray-600`} />
              </button>
              <div></div>
            </div>
          </div>

          {/* Numeric Inputs */}
          <div className={`grid gap-3 ${isMobile ? 'grid-cols-1' : 'grid-cols-2'}`}>
            <div>
              <label className="block text-xs text-gray-500 mb-1">X Position</label>
              <div className="relative">
                <input
                  type="number"
                  min="0"
                  value={localPosition.x}
                  onChange={(e) => handlePositionChange('x', e.target.value)}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    isMobile ? 'text-base' : 'text-sm'
                  }`}
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">
                  px
                </span>
              </div>
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Y Position</label>
              <div className="relative">
                <input
                  type="number"
                  min="0"
                  value={localPosition.y}
                  onChange={(e) => handlePositionChange('y', e.target.value)}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    isMobile ? 'text-base' : 'text-sm'
                  }`}
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">
                  px
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Width/Height */}
        <div className="space-y-3">
          <label className="block text-xs font-medium text-gray-600">
            Size (Width, Height)
          </label>
          
          {/* Size Controls */}
          <div className={`grid gap-3 ${isMobile ? 'grid-cols-1' : 'grid-cols-2'}`}>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Width</label>
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => handleSizeChange('width', -10)}
                  className="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors"
                  disabled={localPosition.width <= 10}
                >
                  -
                </button>
                <div className="relative flex-1">
                  <input
                    type="number"
                    min="10"
                    value={localPosition.width}
                    onChange={(e) => handlePositionChange('width', e.target.value)}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      isMobile ? 'text-base' : 'text-sm'
                    }`}
                  />
                  <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">
                    px
                  </span>
                </div>
                <button
                  onClick={() => handleSizeChange('width', 10)}
                  className="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors"
                >
                  +
                </button>
              </div>
            </div>
            
            <div>
              <label className="block text-xs text-gray-500 mb-1">Height</label>
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => handleSizeChange('height', -10)}
                  className="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors"
                  disabled={localPosition.height <= 10}
                >
                  -
                </button>
                <div className="relative flex-1">
                  <input
                    type="number"
                    min="10"
                    value={localPosition.height}
                    onChange={(e) => handlePositionChange('height', e.target.value)}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      isMobile ? 'text-base' : 'text-sm'
                    }`}
                  />
                  <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">
                    px
                  </span>
                </div>
                <button
                  onClick={() => handleSizeChange('height', 10)}
                  className="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 transition-colors"
                >
                  +
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PositionEditor;
