import React from 'react'
import Icon from '../AppIcon'

const AuthLoadingSpinner = ({ 
  message = 'Loading...', 
  size = 'md',
  showIcon = true,
  fullScreen = false 
}) => {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }

  const LoadingContent = () => (
    <div className="text-center">
      {showIcon && (
        <div className="flex justify-center mb-4">
          <div className={`${sizeClasses[size]} animate-spin rounded-full border-b-2 border-primary`}></div>
        </div>
      )}
      <p className={`text-text-secondary ${textSizeClasses[size]}`}>
        {message}
      </p>
    </div>
  )

  if (fullScreen) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingContent />
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center p-8">
      <LoadingContent />
    </div>
  )
}

// Specific loading states for different auth operations
export const SignInLoading = () => (
  <AuthLoadingSpinner 
    message="Signing you in..." 
    size="md"
    fullScreen={false}
  />
)

export const SignUpLoading = () => (
  <AuthLoadingSpinner 
    message="Creating your account..." 
    size="md"
    fullScreen={false}
  />
)

export const SignOutLoading = () => (
  <AuthLoadingSpinner 
    message="Signing you out..." 
    size="sm"
    fullScreen={false}
  />
)

export const SessionLoading = () => (
  <AuthLoadingSpinner 
    message="Checking your session..." 
    size="lg"
    fullScreen={true}
  />
)

export const ProfileLoading = () => (
  <AuthLoadingSpinner 
    message="Loading your profile..." 
    size="md"
    fullScreen={false}
  />
)

export const PasswordResetLoading = () => (
  <AuthLoadingSpinner 
    message="Sending reset link..." 
    size="md"
    fullScreen={false}
  />
)

// Skeleton loading for profile sections
export const ProfileSkeleton = () => (
  <div className="bg-surface rounded-lg border border-border p-6">
    <div className="animate-pulse">
      <div className="flex items-center justify-between mb-6">
        <div>
          <div className="h-6 bg-background rounded w-48 mb-2"></div>
          <div className="h-4 bg-background rounded w-64"></div>
        </div>
        <div className="h-10 bg-background rounded w-32"></div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Photo Skeleton */}
        <div className="lg:col-span-1">
          <div className="text-center">
            <div className="w-32 h-32 bg-background rounded-full mx-auto mb-4"></div>
            <div className="h-6 bg-background rounded w-32 mx-auto mb-2"></div>
            <div className="h-4 bg-background rounded w-24 mx-auto"></div>
          </div>
        </div>
        
        {/* Profile Details Skeleton */}
        <div className="lg:col-span-2 space-y-6">
          <div>
            <div className="h-5 bg-background rounded w-32 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="h-4 bg-background rounded w-20 mb-2"></div>
                <div className="h-10 bg-background rounded"></div>
              </div>
              <div>
                <div className="h-4 bg-background rounded w-20 mb-2"></div>
                <div className="h-10 bg-background rounded"></div>
              </div>
            </div>
          </div>
          
          <div>
            <div className="h-5 bg-background rounded w-40 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="h-4 bg-background rounded w-24 mb-2"></div>
                <div className="h-10 bg-background rounded"></div>
              </div>
              <div>
                <div className="h-4 bg-background rounded w-20 mb-2"></div>
                <div className="h-10 bg-background rounded"></div>
              </div>
            </div>
          </div>
          
          <div>
            <div className="h-4 bg-background rounded w-16 mb-2"></div>
            <div className="h-24 bg-background rounded"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

// Dashboard skeleton
export const DashboardSkeleton = () => (
  <div className="px-6 py-8">
    <div className="animate-pulse">
      {/* Welcome Section Skeleton */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 bg-background rounded w-64 mb-2"></div>
            <div className="h-5 bg-background rounded w-80"></div>
          </div>
          <div className="hidden md:flex items-center space-x-4">
            <div className="text-right">
              <div className="h-4 bg-background rounded w-20 mb-1"></div>
              <div className="h-5 bg-background rounded w-16"></div>
            </div>
            <div className="w-12 h-12 bg-background rounded-full"></div>
          </div>
        </div>
      </div>
      
      {/* Quick Actions Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-surface rounded-lg border border-border p-6">
          <div className="h-6 bg-background rounded w-32 mb-2"></div>
          <div className="h-4 bg-background rounded w-48"></div>
        </div>
        <div className="bg-surface rounded-lg border border-border p-6">
          <div className="h-6 bg-background rounded w-32 mb-2"></div>
          <div className="h-4 bg-background rounded w-48"></div>
        </div>
      </div>
      
      {/* Hero Section Skeleton */}
      <div className="mb-12">
        <div className="bg-background rounded-xl p-8 lg:p-12">
          <div className="h-8 bg-surface rounded w-48 mb-4"></div>
          <div className="h-12 bg-surface rounded w-64 mb-6"></div>
          <div className="h-10 bg-surface rounded w-32"></div>
        </div>
      </div>
    </div>
  </div>
)

export default AuthLoadingSpinner
