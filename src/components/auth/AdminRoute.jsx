/**
 * AdminRoute Component
 * Protects routes that require admin privileges
 * Includes comprehensive security checks and proper error handling
 */

import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import adminService from '../../services/adminService';
import { prodLogger } from '../../utils/prodLogger';

const AdminRoute = ({ 
  children, 
  requiredRole = 'moderator',
  fallbackPath = '/dashboard',
  showAccessDenied = true 
}) => {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const location = useLocation();
  
  const [adminCheck, setAdminCheck] = useState({
    loading: true,
    isAdmin: false,
    hasRequiredRole: false,
    sessionValid: false,
    error: null
  });

  // Perform comprehensive admin checks
  useEffect(() => {
    let mounted = true;

    const performAdminChecks = async () => {
      if (!user?.id || !isAuthenticated) {
        if (mounted) {
          setAdminCheck({
            loading: false,
            isAdmin: false,
            hasRequiredRole: false,
            sessionValid: false,
            error: 'User not authenticated'
          });
        }
        return;
      }

      try {
        // Parallel admin checks for better performance
        const [isAdmin, hasRole, sessionValidation] = await Promise.all([
          adminService.isUserAdmin(user.id),
          adminService.userHasAdminRole(user.id, requiredRole),
          adminService.validateAdminSession()
        ]);

        if (mounted) {
          setAdminCheck({
            loading: false,
            isAdmin,
            hasRequiredRole: hasRole,
            sessionValid: sessionValidation.valid,
            error: sessionValidation.valid ? null : sessionValidation.reason,
            requiresReauth: sessionValidation.requiresReauth
          });

          // Log admin access attempt
          if (isAdmin) {
            await adminService.logAdminActivity(
              'admin_route_access',
              'route',
              location.pathname,
              {
                requiredRole,
                hasRequiredRole: hasRole,
                sessionValid: sessionValidation.valid
              }
            );
          }
        }
      } catch (error) {
        prodLogger.error('Admin route check failed:', error);
        if (mounted) {
          setAdminCheck({
            loading: false,
            isAdmin: false,
            hasRequiredRole: false,
            sessionValid: false,
            error: 'Admin verification failed'
          });
        }
      }
    };

    performAdminChecks();

    return () => {
      mounted = false;
    };
  }, [user?.id, isAuthenticated, requiredRole, location.pathname]);

  // Show loading state while checking authentication and admin status
  if (authLoading || adminCheck.loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-text-secondary">Verifying admin access...</p>
          <p className="mt-2 text-xs text-text-muted">Please wait while we check your permissions</p>
        </div>
      </div>
    );
  }

  // Redirect to auth if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // Handle session validation failures
  if (!adminCheck.sessionValid) {
    if (adminCheck.requiresReauth) {
      // Force re-authentication for expired admin sessions
      return <Navigate to="/auth" state={{ 
        from: location, 
        message: 'Admin session expired. Please sign in again.' 
      }} replace />;
    }

    // Log security incident
    prodLogger.warn('Admin access denied - session validation failed', {
      userId: user.id,
      reason: adminCheck.error,
      path: location.pathname
    });
  }

  // Check admin privileges
  if (!adminCheck.isAdmin) {
    if (showAccessDenied) {
      return <AccessDeniedPage reason="Admin privileges required" />;
    }
    return <Navigate to={fallbackPath} replace />;
  }

  // Check specific role requirements
  if (!adminCheck.hasRequiredRole) {
    if (showAccessDenied) {
      return <AccessDeniedPage 
        reason={`${requiredRole} role required`}
        currentRole={adminCheck.currentRole}
      />;
    }
    return <Navigate to={fallbackPath} replace />;
  }

  // All checks passed - render protected content
  return children;
};

// Access Denied Page Component
const AccessDeniedPage = ({ reason, currentRole }) => {
  const { user } = useAuth();

  useEffect(() => {
    // Log access denial for security monitoring
    prodLogger.warn('Admin access denied', {
      userId: user?.id,
      reason,
      currentRole,
      timestamp: new Date().toISOString()
    });
  }, [user?.id, reason, currentRole]);

  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="max-w-md w-full mx-auto p-6">
        <div className="text-center">
          {/* Access Denied Icon */}
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
            <svg 
              className="h-8 w-8 text-red-600" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
              />
            </svg>
          </div>

          {/* Error Message */}
          <h1 className="text-2xl font-bold text-text-primary mb-4">
            Access Denied
          </h1>
          
          <p className="text-text-secondary mb-6">
            {reason || 'You do not have permission to access this page.'}
          </p>

          {currentRole && (
            <p className="text-sm text-text-muted mb-6">
              Current role: <span className="font-medium">{currentRole}</span>
            </p>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={() => window.history.back()}
              className="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary-dark transition-colors"
            >
              Go Back
            </button>
            
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Return to Dashboard
            </button>
          </div>

          {/* Contact Support */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-xs text-text-muted">
              If you believe this is an error, please contact your system administrator.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Higher-order component for admin role checking
export const withAdminRole = (WrappedComponent, requiredRole = 'moderator') => {
  return function AdminProtectedComponent(props) {
    return (
      <AdminRoute requiredRole={requiredRole}>
        <WrappedComponent {...props} />
      </AdminRoute>
    );
  };
};

// Hook for checking admin status in components
export const useAdminAuth = (requiredRole = 'moderator') => {
  const { user, isAuthenticated } = useAuth();
  const [adminStatus, setAdminStatus] = useState({
    loading: true,
    isAdmin: false,
    hasRequiredRole: false,
    error: null
  });

  useEffect(() => {
    let mounted = true;

    const checkAdminStatus = async () => {
      if (!user?.id || !isAuthenticated) {
        if (mounted) {
          setAdminStatus({
            loading: false,
            isAdmin: false,
            hasRequiredRole: false,
            error: 'Not authenticated'
          });
        }
        return;
      }

      try {
        const [isAdmin, hasRole] = await Promise.all([
          adminService.isUserAdmin(user.id),
          adminService.userHasAdminRole(user.id, requiredRole)
        ]);

        if (mounted) {
          setAdminStatus({
            loading: false,
            isAdmin,
            hasRequiredRole: hasRole,
            error: null
          });
        }
      } catch (error) {
        if (mounted) {
          setAdminStatus({
            loading: false,
            isAdmin: false,
            hasRequiredRole: false,
            error: error.message
          });
        }
      }
    };

    checkAdminStatus();

    return () => {
      mounted = false;
    };
  }, [user?.id, isAuthenticated, requiredRole]);

  return adminStatus;
};

export default AdminRoute;
