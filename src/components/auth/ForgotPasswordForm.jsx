import React, { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import Button from '../ui/Button'
import Input from '../ui/Input'
import Icon from '../AppIcon'

import { prodLogger } from '../../utils/prodLogger.js';
const ForgotPasswordForm = ({ onSwitchToLogin }) => {
  const { resetPassword, loading, error, clearError } = useAuth()
  const [email, setEmail] = useState('')
  const [emailError, setEmailError] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleChange = (e) => {
    setEmail(e.target.value)
    
    // Clear field error when user starts typing
    if (emailError) {
      setEmailError('')
    }
    
    // Clear global error
    if (error) {
      clearError()
    }
  }

  const validateEmail = () => {
    if (!email) {
      setEmailError('Email is required')
      return false
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Please enter a valid email address')
      return false
    }
    return true
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateEmail()) {
      return
    }
    
    const result = await resetPassword(email)
    
    if (result.success) {
      setIsSubmitted(true)
    } else {
      prodLogger.error('Password reset failed:', result.error)
    }
  }

  if (isSubmitted) {
    return (
      <div className="w-full max-w-md mx-auto">
        <div className="bg-surface rounded-2xl border border-border p-8 shadow-card">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-success/10 rounded-2xl flex items-center justify-center">
                <Icon name="Mail" size={32} color="var(--color-success)" />
              </div>
            </div>
            <h1 className="text-2xl font-bold text-text-primary mb-4">Check your email</h1>
            <p className="text-text-secondary mb-6">
              We've sent a password reset link to{' '}
              <span className="font-medium text-text-primary">{email}</span>
            </p>
            <p className="text-sm text-text-secondary mb-8">
              Didn't receive the email? Check your spam folder or try again.
            </p>
            
            <div className="space-y-4">
              <Button
                variant="primary"
                size="lg"
                fullWidth
                onClick={() => setIsSubmitted(false)}
              >
                Try again
              </Button>
              
              <Button
                variant="ghost"
                size="lg"
                fullWidth
                onClick={onSwitchToLogin}
              >
                Back to sign in
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-surface rounded-2xl border border-border p-8 shadow-card">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-r from-primary to-primary/80 rounded-2xl flex items-center justify-center">
              <Icon name="Key" size={32} color="white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-text-primary mb-2">Forgot your password?</h1>
          <p className="text-text-secondary">
            No worries! Enter your email address and we'll send you a link to reset your password.
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-error/10 border border-error/20 rounded-lg">
            <div className="flex items-center space-x-2">
              <Icon name="AlertCircle" size={16} color="var(--color-error)" />
              <p className="text-sm text-error">{error}</p>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-text-primary mb-2">
              Email address
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              value={email}
              onChange={handleChange}
              placeholder="Enter your email address"
              className={emailError ? 'border-error focus:ring-error' : ''}
              disabled={loading}
            />
            {emailError && (
              <p className="mt-1 text-sm text-error">{emailError}</p>
            )}
          </div>

          <Button
            type="submit"
            variant="primary"
            size="lg"
            fullWidth
            loading={loading}
            disabled={loading}
          >
            {loading ? 'Sending reset link...' : 'Send reset link'}
          </Button>
        </form>

        <div className="mt-8 text-center">
          <p className="text-text-secondary">
            Remember your password?{' '}
            <button
              onClick={onSwitchToLogin}
              className="text-primary hover:text-primary/80 font-medium transition-colors"
              disabled={loading}
            >
              Back to sign in
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}

export default ForgotPasswordForm
