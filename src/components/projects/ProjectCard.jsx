import React from 'react';
import Icon from '../AppIcon';
import { getDocumentThumbnail } from '../../utils/projectThumbnails';

const ProjectCard = ({
  project,
  onClick,
  onDelete = null,
  showActions = true,
  compact = false,
  disableCardClick = false
}) => {
  const formatDate = (dateString) => {
    if (!dateString) {
      return 'No date';
    }

    const date = new Date(dateString);

    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get the thumbnail from document content, or null if no images
  const thumbnailUrl = getDocumentThumbnail(project);

  return (
    <div
      className={`bg-surface rounded-md border border-border hover:shadow-elevated transition-all duration-300 ${!disableCardClick ? 'cursor-pointer' : ''} group overflow-hidden`}
      onClick={!disableCardClick && onClick ? () => onClick(project) : (e) => e.preventDefault()}
    >
      {/* Project Thumbnail */}
      <div className="aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
        {thumbnailUrl ? (
          <img
            src={thumbnailUrl}
            alt={project.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            onError={(e) => {
              // Hide image if it fails to load
              e.target.style.display = 'none';
            }}
          />
        ) : (
          // Document icon placeholder when no image is available
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
            <Icon name="FileText" size={40} className="text-gray-300 opacity-70" />
          </div>
        )}

        {/* Status Badge */}
        <div className={`absolute top-3 left-3 px-2 py-1 rounded-lg text-xs font-medium shadow-sm ${
          project.status === 'completed' ? 'bg-success text-white' :
          project.status === 'draft' ? 'bg-warning text-white' :
          'bg-primary text-white'
        }`}>
          {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
        </div>

        {/* Hover Actions - Only show if showActions is true */}
        {showActions && (
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex items-center space-x-2">
              <button
                className="bg-white text-text-primary p-2 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  if (onClick) onClick(project);
                }}
                title="Edit"
              >
                <Icon name="Edit" size={16} />
              </button>
              {onDelete && (
                <button
                  className="bg-white text-destructive p-2 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(project);
                  }}
                  title="Delete document"
                  aria-label={`Delete ${project.title}`}
                >
                  <Icon name="Trash2" size={16} />
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Project Info */}
      <div className="p-4">
        <h4 className="font-semibold text-text-primary group-hover:text-primary transition-colors duration-300 line-clamp-2 mb-2">
          {project.title}
        </h4>
        <div className="flex items-center justify-between text-sm text-text-secondary">
          <span className="capitalize">{project.document_type}</span>
          <span>{formatDate(project.updated_at)}</span>
        </div>

        {typeof project.progress === 'number' && project.progress >= 0 && project.progress < 100 && (
          <div className="mt-3">
            <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
              <span>Progress</span>
              <span>{project.progress}%</span>
            </div>
            <div className="w-full bg-surface-secondary rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${project.progress}%` }}
              ></div>
            </div>
          </div>
        )}
        
        {project.progress === 100 && (
          <div className="mt-3 flex items-center text-xs text-success">
            <Icon name="CheckCircle" size={14} className="mr-1" />
            <span>Completed</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectCard;
