import React from 'react';

const ProjectsLoading = ({ count = 4, compact = false }) => {
  return (
    <div className={`grid grid-cols-1 ${
      compact 
        ? 'md:grid-cols-2 lg:grid-cols-4 gap-6' 
        : 'sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6'
    }`}>
      {Array.from({ length: count }).map((_, index) => (
        <div 
          key={index} 
          className="bg-surface rounded-md border border-border overflow-hidden animate-pulse"
        >
          {/* Thumbnail skeleton */}
          <div className="aspect-[4/3] bg-gray-200" />
          
          {/* Content skeleton */}
          <div className="p-4">
            {/* Title skeleton */}
            <div className="h-4 bg-gray-200 rounded mb-2" />
            
            {/* Meta info skeleton */}
            <div className="flex items-center justify-between mb-3">
              <div className="h-3 bg-gray-200 rounded w-16" />
              <div className="h-3 bg-gray-200 rounded w-20" />
            </div>
            
            {/* Progress bar skeleton (randomly show/hide) */}
            {Math.random() > 0.5 && (
              <div className="mt-3">
                <div className="flex items-center justify-between mb-1">
                  <div className="h-2 bg-gray-200 rounded w-12" />
                  <div className="h-2 bg-gray-200 rounded w-8" />
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2" />
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProjectsLoading;
