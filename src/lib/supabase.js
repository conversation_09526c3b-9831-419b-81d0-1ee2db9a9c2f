import { createClient } from '@supabase/supabase-js'

import { prodLogger } from '../utils/prodLogger.js';
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Configure Supabase client with optimized settings
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Disable email confirmation for development
    confirmationUrl: window.location.origin,
    // Set flow type to implicit to skip email confirmation
    flowType: 'implicit',
    // Fix for stuck sessions - reduce storage conflicts
    storageKey: 'sb-supabase-auth-token-v2',
    // Fix for token refresh issues
    autoRefreshToken: true,
    // Disable debug logs
    debug: false
  },
  // Add global error handler
  global: {
    headers: {
      'x-application-name': 'rapiddoc-ai'
    }
  },
  // Add performance monitoring
  realtime: {
    // Disable realtime subscriptions if not needed
    enabled: false
  },
  // Add connection health check
  healthCheck: {
    enabled: true,
    interval: 30000 // 30 seconds
  }
})

// Add connection health check
let isSupabaseHealthy = true;
let lastConnectionCheck = 0;

export const checkSupabaseConnection = async (force = false) => {
  const now = Date.now();
  // Only check every 30 seconds unless forced
  if (!force && now - lastConnectionCheck < 30000) {
    return isSupabaseHealthy;
  }

  try {
    const startTime = performance.now();

    // Simple ping query
    const { data, error } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1)
      .maybeSingle();

    const duration = performance.now() - startTime;
    lastConnectionCheck = now;

    if (error) {
      prodLogger.error('❌ Supabase connection error:', error);
      isSupabaseHealthy = false;
      return false;
    }

    isSupabaseHealthy = true;
    return true;
  } catch (err) {
    prodLogger.error('❌ Supabase connection check failed:', err);
    isSupabaseHealthy = false;
    lastConnectionCheck = now;
    return false;
  }
}

// Auth helper functions
export const authHelpers = {
  // Sign up with email and password
  signUp: async (email, password, userData = {}) => {
    const signUpOptions = {
      email,
      password,
      options: {
        data: userData,
        // Try to skip email confirmation
        emailRedirectTo: undefined
      }
    }

    const { data, error } = await supabase.auth.signUp(signUpOptions)

    // If user is created but not confirmed, try to sign them in immediately
    if (data.user && !data.session && !error) {
      try {
        const signInResult = await supabase.auth.signInWithPassword({
          email,
          password
        })

        if (signInResult.data.session) {
          return { data: signInResult.data, error: null }
        }
      } catch (signInError) {
        // Return original signup result
      }
    }

    return { data, error }
  },

  // Sign in with email and password
  signIn: async (email, password) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  },

  // Sign out
  signOut: async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  // Reset password
  resetPassword: async (email) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`
    })
    return { data, error }
  },

  // Update password
  updatePassword: async (password) => {
    const { data, error } = await supabase.auth.updateUser({
      password
    })
    return { data, error }
  },

  // Get current user
  getCurrentUser: () => {
    return supabase.auth.getUser()
  },

  // Get current session
  getCurrentSession: () => {
    return supabase.auth.getSession()
  },

  // Listen to auth changes
  onAuthStateChange: (callback) => {
    return supabase.auth.onAuthStateChange(callback)
  }
}

// Database helper functions
export const dbHelpers = {
  // Get user profile
  getUserProfile: async (userId) => {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()
    return { data, error }
  },

  // Update user profile
  updateUserProfile: async (userId, updates) => {
    const { data, error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    return { data, error }
  },

  // Create user profile
  createUserProfile: async (profile) => {
    const { data, error } = await supabase
      .from('user_profiles')
      .insert([profile])
      .select()
      .single()
    return { data, error }
  },

  // Update user preferences
  updateUserPreferences: async (userId, preferences) => {
    // Enhanced server-side validation and security
    if (!userId) {
      return { data: null, error: { message: 'User ID is required' } };
    }

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userId)) {
      return { data: null, error: { message: 'Invalid user ID format' } };
    }

    // Validate preferences object
    if (!preferences || typeof preferences !== 'object') {
      return { data: null, error: { message: 'Invalid preferences data' } };
    }

    // Validate required fields (MVP - Essential only, language removed)
    const requiredFields = ['timezone'];
    for (const field of requiredFields) {
      if (!preferences[field]) {
        return { data: null, error: { message: `${field} is required` } };
      }
    }

    // Additional security validations (MVP - Essential fields only, language removed)
    const stringFields = ['timezone'];
    for (const field of stringFields) {
      if (preferences[field]) {
        // Check for basic SQL injection patterns (simplified and safe)
        const dangerousPatterns = [
          "'", '"', ';', '--', '/*', '*/',
          'select', 'insert', 'update', 'delete', 'drop', 'create', 'alter',
          'union', 'or ', 'and ', 'where'
        ];

        const fieldLower = preferences[field].toLowerCase();
        const hasDangerousPattern = dangerousPatterns.some(pattern =>
          fieldLower.includes(pattern.toLowerCase())
        );

        if (hasDangerousPattern) {
          return { data: null, error: { message: `Invalid characters in ${field}` } };
        }

        // Check field length
        if (preferences[field].length > 100) {
          return { data: null, error: { message: `${field} is too long (max 100 characters)` } };
        }
      }
    }

    // Validate boolean fields (MVP - Essential only)
    const booleanFields = ['autoSave', 'emailNotifications'];
    for (const field of booleanFields) {
      if (preferences[field] !== undefined && typeof preferences[field] !== 'boolean') {
        return { data: null, error: { message: `${field} must be a boolean value` } };
      }
    }

    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update({
          // MVP Essential preferences only (language removed - post-MVP feature)
          timezone: preferences.timezone,
          theme: preferences.theme,
          notifications_email: preferences.emailNotifications,
          auto_save: preferences.autoSave
        })
        .eq('id', userId)
        .select()
        .single()

      return { data, error }
    } catch (err) {
      return { data: null, error: { message: 'Database update failed: ' + err.message } };
    }
  },

  // Update user usage statistics
  updateUserUsageStats: async (userId, updates) => {
    const { data, error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    return { data, error }
  },

  // Increment user usage counters
  incrementUsageCounter: async (userId, counterType) => {
    const { data, error } = await supabase.rpc('increment_usage_counter', {
      user_id: userId,
      counter_type: counterType
    })
    return { data, error }
  },

  // Get user activity history (if we have an activities table)
  getUserActivities: async (userId, limit = 10) => {
    const { data, error } = await supabase
      .from('user_activities')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)
    return { data, error }
  },

  // Get user sessions
  getUserSessions: async (userId) => {
    const { data, error } = await supabase.rpc('get_user_sessions', {
      user_uuid: userId
    })
    return { data, error }
  },

  // Get user login history
  getUserLoginHistory: async (userId, limit = 10) => {
    const { data, error } = await supabase.rpc('get_user_login_history', {
      user_uuid: userId,
      limit_count: limit
    })
    return { data, error }
  },

  // Terminate user session
  terminateUserSession: async (sessionId) => {
    const { data, error } = await supabase
      .from('user_sessions')
      .update({ is_active: false })
      .eq('id', sessionId)
      .select()
    return { data, error }
  },

  // Update user password (through Supabase Auth)
  updateUserPassword: async (newPassword) => {
    const { data, error } = await supabase.auth.updateUser({
      password: newPassword
    })
    return { data, error }
  }
}

export default supabase
