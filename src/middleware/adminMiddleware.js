/**
 * Admin Middleware
 * Provides security middleware for admin operations including
 * rate limiting, audit logging, and additional security checks
 */

import adminService from '../services/adminService';
import { prodLogger } from '../utils/prodLogger';
import { sessionManager } from '../utils/sessionManager';
import { supabase } from '../lib/supabase';

// Rate limiting storage (in production, use Redis or similar)
const rateLimitStore = new Map();

/**
 * Rate limiting configuration for different admin actions
 */
const RATE_LIMITS = {
  // General admin actions
  default: { requests: 100, window: 60 * 1000 }, // 100 requests per minute
  
  // Sensitive operations
  grant_admin_role: { requests: 5, window: 60 * 1000 }, // 5 per minute
  revoke_admin_role: { requests: 5, window: 60 * 1000 }, // 5 per minute
  template_delete: { requests: 10, window: 60 * 1000 }, // 10 per minute
  template_create: { requests: 20, window: 60 * 1000 }, // 20 per minute
  
  // Bulk operations
  bulk_operations: { requests: 3, window: 60 * 1000 }, // 3 per minute
  
  // System operations
  system_config: { requests: 10, window: 60 * 1000 } // 10 per minute
};

/**
 * Rate limiting middleware for admin operations
 * @param {string} action - Action type for rate limiting
 * @param {Object} options - Additional options
 */
export const withAdminRateLimit = (action = 'default', options = {}) => {
  return async (userId, actionFn, ...args) => {
    try {
      // Get rate limit config
      const config = RATE_LIMITS[action] || RATE_LIMITS.default;
      const key = `${userId}_${action}`;
      
      // Get current requests for this user/action
      const now = Date.now();
      const userRequests = rateLimitStore.get(key) || [];
      
      // Remove expired requests
      const validRequests = userRequests.filter(
        timestamp => now - timestamp < config.window
      );
      
      // Check if rate limit exceeded
      if (validRequests.length >= config.requests) {
        const error = new Error(`Rate limit exceeded for ${action}`);
        error.code = 'RATE_LIMIT_EXCEEDED';
        error.retryAfter = Math.ceil(config.window / 1000);
        
        // Log rate limit violation
        await adminService.logAdminActivity(
          'rate_limit_exceeded',
          'security',
          action,
          {
            requests: validRequests.length,
            limit: config.requests,
            window: config.window
          }
        );
        
        throw error;
      }
      
      // Add current request
      validRequests.push(now);
      rateLimitStore.set(key, validRequests);
      
      // Execute the action
      const result = await actionFn(...args);
      
      // Log successful action
      await adminService.logAdminActivity(
        action,
        options.resourceType || 'unknown',
        options.resourceId || null,
        {
          success: true,
          ...options.details
        }
      );
      
      return result;
      
    } catch (error) {
      // Log failed action
      await adminService.logAdminActivity(
        `${action}_failed`,
        options.resourceType || 'unknown',
        options.resourceId || null,
        {
          error: error.message,
          ...options.details
        }
      );
      
      throw error;
    }
  };
};

/**
 * Admin session validation middleware
 */
export const withAdminSessionValidation = () => {
  return async (userId, actionFn, ...args) => {
    try {
      // Validate admin session
      const sessionValidation = await adminService.validateAdminSession();
      
      if (!sessionValidation.valid) {
        const error = new Error(`Admin session invalid: ${sessionValidation.reason}`);
        error.code = 'INVALID_ADMIN_SESSION';
        error.requiresReauth = sessionValidation.requiresReauth;
        
        // Log session validation failure
        await adminService.logAdminActivity(
          'session_validation_failed',
          'security',
          'session',
          {
            reason: sessionValidation.reason,
            requiresReauth: sessionValidation.requiresReauth
          }
        );
        
        throw error;
      }
      
      // Execute the action
      return await actionFn(...args);
      
    } catch (error) {
      prodLogger.error('Admin session validation failed:', error);
      throw error;
    }
  };
};

/**
 * Admin privilege verification middleware
 * @param {string} requiredRole - Required admin role
 */
export const withAdminPrivilegeCheck = (requiredRole = 'moderator') => {
  return async (userId, actionFn, ...args) => {
    try {
      // Check if user has required admin role
      const hasRole = await adminService.userHasAdminRole(userId, requiredRole);
      
      if (!hasRole) {
        const error = new Error(`Insufficient privileges: ${requiredRole} role required`);
        error.code = 'INSUFFICIENT_PRIVILEGES';
        
        // Log privilege escalation attempt
        await adminService.logAdminActivity(
          'privilege_escalation_attempt',
          'security',
          'role_check',
          {
            requiredRole,
            hasRole: false
          }
        );
        
        throw error;
      }
      
      // Execute the action
      return await actionFn(...args);
      
    } catch (error) {
      prodLogger.error('Admin privilege check failed:', error);
      throw error;
    }
  };
};

/**
 * Comprehensive admin security middleware
 * Combines multiple security checks
 */
export const withAdminSecurity = (options = {}) => {
  const {
    action = 'default',
    requiredRole = 'moderator',
    skipRateLimit = false,
    skipSessionValidation = false,
    skipPrivilegeCheck = false,
    ...middlewareOptions
  } = options;

  return async (userId, actionFn, ...args) => {
    try {
      // Verify user is admin first
      const isAdmin = await adminService.isUserAdmin(userId);
      if (!isAdmin) {
        throw new Error('User is not an admin');
      }

      // Chain middleware functions
      let wrappedAction = actionFn;

      // Apply privilege check
      if (!skipPrivilegeCheck) {
        const privilegeMiddleware = withAdminPrivilegeCheck(requiredRole);
        wrappedAction = (...args) => privilegeMiddleware(userId, actionFn, ...args);
      }

      // Apply session validation
      if (!skipSessionValidation) {
        const sessionMiddleware = withAdminSessionValidation();
        const currentAction = wrappedAction;
        wrappedAction = (...args) => sessionMiddleware(userId, () => currentAction(...args));
      }

      // Apply rate limiting
      if (!skipRateLimit) {
        const rateLimitMiddleware = withAdminRateLimit(action, middlewareOptions);
        const currentAction = wrappedAction;
        wrappedAction = (...args) => rateLimitMiddleware(userId, () => currentAction(...args));
      }

      // Execute the fully wrapped action
      return await wrappedAction(...args);

    } catch (error) {
      prodLogger.error('Admin security middleware failed:', error);
      throw error;
    }
  };
};

/**
 * React Hook for admin-secured API calls
 */
export const useAdminSecuredAPI = () => {
  const executeSecuredAction = async (actionFn, options = {}) => {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Apply admin security middleware
      const securedAction = withAdminSecurity(options);
      return await securedAction(user.id, actionFn);

    } catch (error) {
      prodLogger.error('Secured admin action failed:', error);
      throw error;
    }
  };

  return {
    // Template management
    createTemplate: (actionFn) => executeSecuredAction(actionFn, {
      action: 'template_create',
      resourceType: 'template',
      requiredRole: 'admin'
    }),

    updateTemplate: (actionFn, templateId) => executeSecuredAction(actionFn, {
      action: 'template_update',
      resourceType: 'template',
      resourceId: templateId,
      requiredRole: 'admin'
    }),

    deleteTemplate: (actionFn, templateId) => executeSecuredAction(actionFn, {
      action: 'template_delete',
      resourceType: 'template',
      resourceId: templateId,
      requiredRole: 'admin'
    }),

    // User management
    grantAdminRole: (actionFn, targetUserId) => executeSecuredAction(actionFn, {
      action: 'grant_admin_role',
      resourceType: 'user',
      resourceId: targetUserId,
      requiredRole: 'super_admin'
    }),

    revokeAdminRole: (actionFn, targetUserId) => executeSecuredAction(actionFn, {
      action: 'revoke_admin_role',
      resourceType: 'user',
      resourceId: targetUserId,
      requiredRole: 'super_admin'
    }),

    // System operations
    updateSystemConfig: (actionFn) => executeSecuredAction(actionFn, {
      action: 'system_config',
      resourceType: 'system',
      requiredRole: 'super_admin'
    }),

    // Bulk operations
    bulkOperation: (actionFn) => executeSecuredAction(actionFn, {
      action: 'bulk_operations',
      resourceType: 'bulk',
      requiredRole: 'admin'
    })
  };
};

/**
 * Utility functions for admin middleware
 */
export const adminMiddlewareUtils = {
  /**
   * Clear rate limit for a user/action
   */
  clearRateLimit(userId, action) {
    const key = `${userId}_${action}`;
    rateLimitStore.delete(key);
  },

  /**
   * Get current rate limit status
   */
  getRateLimitStatus(userId, action = 'default') {
    const config = RATE_LIMITS[action] || RATE_LIMITS.default;
    const key = `${userId}_${action}`;
    const now = Date.now();
    
    const userRequests = rateLimitStore.get(key) || [];
    const validRequests = userRequests.filter(
      timestamp => now - timestamp < config.window
    );

    return {
      requests: validRequests.length,
      limit: config.requests,
      remaining: Math.max(0, config.requests - validRequests.length),
      resetTime: validRequests.length > 0 ? 
        Math.max(...validRequests) + config.window : 
        now
    };
  },

  /**
   * Clean up expired rate limit entries
   */
  cleanupRateLimits() {
    const now = Date.now();
    const maxWindow = Math.max(...Object.values(RATE_LIMITS).map(config => config.window));
    
    for (const [key, requests] of rateLimitStore.entries()) {
      const validRequests = requests.filter(timestamp => now - timestamp < maxWindow);
      if (validRequests.length === 0) {
        rateLimitStore.delete(key);
      } else {
        rateLimitStore.set(key, validRequests);
      }
    }
  }
};

// Cleanup rate limits every 5 minutes
setInterval(() => {
  adminMiddlewareUtils.cleanupRateLimits();
}, 5 * 60 * 1000);

export default {
  withAdminRateLimit,
  withAdminSessionValidation,
  withAdminPrivilegeCheck,
  withAdminSecurity,
  useAdminSecuredAPI,
  adminMiddlewareUtils
};
