import { useAuth } from '../contexts/AuthContext';
import { useSubscriptionGuard } from '../hooks/useSubscriptionGuard.jsx';
import subscriptionService from '../services/subscriptionService';
import usageTrackingService from '../services/usageTrackingService';
import { prodLogger } from '../utils/prodLogger';

/**
 * Subscription Middleware
 * Provides utilities for protecting features and tracking usage across the application
 */

/**
 * Higher-order function to protect API calls with subscription checks
 */
export const withSubscriptionCheck = (actionType, requiredFeature = null) => {
  return async (userId, actionFn, ...args) => {
    try {
      // Check feature access if required
      if (requiredFeature) {
        const hasAccess = await subscriptionService.hasFeatureAccess(userId, requiredFeature);
        if (!hasAccess) {
          throw new Error(`Feature '${requiredFeature}' requires a premium subscription`);
        }
      }

      // Check usage limits
      const canPerform = await usageTrackingService.canPerformAction(userId, actionType);
      if (!canPerform) {
        throw new Error(`Usage limit exceeded for ${actionType}`);
      }

      // Execute the action
      const result = await actionFn(...args);

      // Track usage after successful action
      await usageTrackingService.trackUsage(userId, actionType, 1, {
        action: actionFn.name,
        timestamp: new Date().toISOString(),
      });

      return result;
    } catch (error) {
      prodLogger.error(`Subscription check failed for ${actionType}:`, error);
      throw error;
    }
  };
};

/**
 * Middleware for document creation
 */
export const withDocumentCreationCheck = withSubscriptionCheck('create_document');

/**
 * Middleware for AI generation
 */
export const withAIGenerationCheck = withSubscriptionCheck('ai_generation');

/**
 * Middleware for AI image generation
 */
export const withAIImageGenerationCheck = withSubscriptionCheck('ai_image_generation');

/**
 * Middleware for storage uploads
 */
export const withStorageUploadCheck = withSubscriptionCheck('storage_upload');

/**
 * Middleware for premium features
 */
export const withPremiumFeatureCheck = (feature) => withSubscriptionCheck(null, feature);

/**
 * React Hook for subscription-aware API calls
 */
export const useSubscriptionAwareAPI = () => {
  const { user } = useAuth();
  const { trackUsage, canPerformAction, hasFeatureAccess } = useSubscriptionGuard();

  const executeWithSubscriptionCheck = async (actionType, actionFn, requiredFeature = null) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      // Check feature access if required
      if (requiredFeature) {
        const hasAccess = await hasFeatureAccess(requiredFeature);
        if (!hasAccess) {
          throw new Error(`Feature '${requiredFeature}' requires a premium subscription`);
        }
      }

      // Check usage limits
      const canPerform = await canPerformAction(actionType);
      if (!canPerform) {
        throw new Error(`Usage limit exceeded for ${actionType}`);
      }

      // Execute the action
      const result = await actionFn();

      // Track usage after successful action
      await trackUsage(actionType, 1, {
        action: actionFn.name,
        timestamp: new Date().toISOString(),
      });

      return result;
    } catch (error) {
      prodLogger.error(`Subscription-aware API call failed:`, error);
      throw error;
    }
  };

  return {
    createDocument: (actionFn) => executeWithSubscriptionCheck('create_document', actionFn),
    generateAI: (actionFn) => executeWithSubscriptionCheck('ai_generation', actionFn),
    generateAIImage: (actionFn) => executeWithSubscriptionCheck('ai_image_generation', actionFn),
    uploadToStorage: (actionFn, sizeInMB) => 
      executeWithSubscriptionCheck('storage_upload', actionFn).then(async (result) => {
        // Track additional storage usage
        if (sizeInMB > 1) {
          await trackUsage('storage_upload', sizeInMB - 1);
        }
        return result;
      }),
    usePremiumFeature: (feature, actionFn) => 
      executeWithSubscriptionCheck(null, actionFn, feature),
  };
};

/**
 * Utility functions for subscription checks
 */
export const subscriptionUtils = {
  /**
   * Check if user can perform an action
   */
  async canUserPerformAction(userId, actionType, amount = 1) {
    try {
      return await usageTrackingService.canPerformAction(userId, actionType, amount);
    } catch (error) {
      prodLogger.error('Error checking user action permission:', error);
      return false;
    }
  },

  /**
   * Check if user has feature access
   */
  async hasUserFeatureAccess(userId, feature) {
    try {
      return await subscriptionService.hasFeatureAccess(userId, feature);
    } catch (error) {
      prodLogger.error('Error checking user feature access:', error);
      return false;
    }
  },

  /**
   * Get user's current usage
   */
  async getUserUsage(userId) {
    try {
      return await usageTrackingService.getUserUsage(userId);
    } catch (error) {
      prodLogger.error('Error getting user usage:', error);
      return null;
    }
  },

  /**
   * Get user's subscription details
   */
  async getUserSubscription(userId) {
    try {
      return await subscriptionService.getUserSubscription(userId);
    } catch (error) {
      prodLogger.error('Error getting user subscription:', error);
      return null;
    }
  },

  /**
   * Track usage for a user
   */
  async trackUserUsage(userId, usageType, amount = 1, metadata = {}) {
    try {
      return await usageTrackingService.trackUsage(userId, usageType, amount, metadata);
    } catch (error) {
      prodLogger.error('Error tracking user usage:', error);
      throw error;
    }
  },

  /**
   * Get upgrade suggestions for a user
   */
  async getUpgradeSuggestions(userId) {
    try {
      const usage = await usageTrackingService.getUserUsage(userId);
      const subscription = await subscriptionService.getUserSubscription(userId);
      
      if (!usage || !subscription) return [];

      const suggestions = [];
      const currentTier = subscription.subscription_tier;

      // Check if user is hitting limits
      if (usage.limits_exceeded.documents && currentTier !== 'pro') {
        suggestions.push({
          type: 'documents',
          message: 'You\'ve reached your document limit. Upgrade for more documents.',
          recommendedTier: currentTier === 'free' ? 'basic' : currentTier === 'basic' ? 'standard' : 'pro'
        });
      }

      if (usage.limits_exceeded.ai_generations && currentTier !== 'pro') {
        suggestions.push({
          type: 'ai_generations',
          message: 'You\'ve reached your AI generation limit. Upgrade for more AI requests.',
          recommendedTier: currentTier === 'free' ? 'basic' : currentTier === 'basic' ? 'standard' : 'pro'
        });
      }

      return suggestions;
    } catch (error) {
      prodLogger.error('Error getting upgrade suggestions:', error);
      return [];
    }
  },

  /**
   * Format usage limits for display
   */
  formatUsageLimit(limit) {
    return limit === -1 ? 'Unlimited' : limit.toLocaleString();
  },

  /**
   * Calculate usage percentage
   */
  calculateUsagePercentage(used, limit) {
    if (limit === -1) return 0; // Unlimited
    if (limit === 0) return 100;
    return Math.min(Math.round((used / limit) * 100), 100);
  },

  /**
   * Check if usage limit is exceeded
   */
  isUsageLimitExceeded(used, limit) {
    if (limit === -1) return false; // Unlimited
    return used >= limit;
  },

  /**
   * Get tier hierarchy for comparison
   */
  getTierHierarchy() {
    return ['free', 'basic', 'standard', 'pro'];
  },

  /**
   * Check if tier change is an upgrade
   */
  isUpgrade(currentTier, newTier) {
    const hierarchy = this.getTierHierarchy();
    const currentIndex = hierarchy.indexOf(currentTier);
    const newIndex = hierarchy.indexOf(newTier);
    return newIndex > currentIndex;
  },

  /**
   * Check if tier change is a downgrade
   */
  isDowngrade(currentTier, newTier) {
    const hierarchy = this.getTierHierarchy();
    const currentIndex = hierarchy.indexOf(currentTier);
    const newIndex = hierarchy.indexOf(newTier);
    return newIndex < currentIndex;
  }
};

export default {
  withSubscriptionCheck,
  withDocumentCreationCheck,
  withAIGenerationCheck,
  withAIImageGenerationCheck,
  withStorageUploadCheck,
  withPremiumFeatureCheck,
  useSubscriptionAwareAPI,
  subscriptionUtils,
};
