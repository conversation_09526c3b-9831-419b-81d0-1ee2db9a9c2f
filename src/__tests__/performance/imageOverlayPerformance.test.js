/**
 * Performance Tests for Image Overlay Template System
 * Tests performance characteristics and benchmarks
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import imageOverlayService from '../../services/imageOverlayService.js';

// Mock Canvas API for performance testing
const createMockCanvas = () => ({
  width: 600,
  height: 800,
  getContext: vi.fn(() => ({
    textBaseline: 'top',
    imageSmoothingEnabled: true,
    imageSmoothingQuality: 'high',
    font: '',
    fillStyle: '',
    textAlign: '',
    measureText: vi.fn(() => ({ width: 100 })),
    fillText: vi.fn(),
    drawImage: vi.fn(),
    fillRect: vi.fn(),
    strokeRect: vi.fn(),
    createLinearGradient: vi.fn(() => ({
      addColorStop: vi.fn()
    }))
  })),
  toDataURL: vi.fn(() => 'data:image/png;base64,mockImageData')
});

const createMockImage = () => ({
  width: 600,
  height: 800,
  onload: null,
  onerror: null,
  src: '',
  crossOrigin: ''
});

// Mock DOM
global.document = {
  createElement: vi.fn((tag) => {
    if (tag === 'canvas') return createMockCanvas();
    if (tag === 'img') return createMockImage();
    return {};
  })
};

global.Image = vi.fn(() => createMockImage());

describe('Image Overlay Performance Tests', () => {
  const mockTemplate = {
    id: 'perf-test-template',
    background_image_url: 'https://example.com/bg.jpg',
    background_image_width: 600,
    background_image_height: 800,
    text_overlays: {
      overlays: [
        {
          id: 'title',
          placeholder: '{{title}}',
          position: { x: 50, y: 100, width: 500, height: 60 },
          styling: {
            fontSize: 32,
            fontFamily: 'Arial',
            fontWeight: 'bold',
            color: '#000000',
            textAlign: 'center'
          }
        },
        {
          id: 'author',
          placeholder: 'by {{author}}',
          position: { x: 50, y: 200, width: 500, height: 40 },
          styling: {
            fontSize: 20,
            fontFamily: 'Arial',
            color: '#666666',
            textAlign: 'center'
          }
        },
        {
          id: 'description',
          placeholder: '{{description}}',
          position: { x: 50, y: 300, width: 500, height: 120 },
          styling: {
            fontSize: 14,
            fontFamily: 'Arial',
            color: '#333333',
            textAlign: 'left',
            lineHeight: 1.4,
            maxLines: 6
          }
        }
      ]
    }
  };

  const mockDocumentData = {
    title: 'Performance Test Document',
    author: 'Test Author',
    description: 'This is a longer description text that will be used to test the performance of text wrapping and rendering in the image overlay system. It should be long enough to trigger word wrapping and multiple lines.'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    imageOverlayService.clearCache();
  });

  describe('Canvas Initialization Performance', () => {
    it('should initialize canvas quickly', () => {
      const startTime = performance.now();
      
      for (let i = 0; i < 100; i++) {
        imageOverlayService.initializeCanvas(600, 800);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete 100 initializations in under 100ms
      expect(duration).toBeLessThan(100);
    });

    it('should handle large canvas sizes efficiently', () => {
      const startTime = performance.now();
      
      imageOverlayService.initializeCanvas(2400, 3200); // 4x larger
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should still be fast even with large canvas
      expect(duration).toBeLessThan(50);
    });
  });

  describe('Text Measurement Performance', () => {
    it('should measure text efficiently', () => {
      imageOverlayService.initializeCanvas(600, 800);
      
      const startTime = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        imageOverlayService.measureText(
          'This is a test text for performance measurement',
          400,
          { fontSize: 16, lineHeight: 1.2 }
        );
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete 1000 measurements in under 100ms
      expect(duration).toBeLessThan(100);
    });

    it('should handle long text efficiently', () => {
      imageOverlayService.initializeCanvas(600, 800);
      
      const longText = 'This is a very long text that will require extensive word wrapping and should test the performance of the text measurement algorithm when dealing with large amounts of text content. '.repeat(10);
      
      const startTime = performance.now();
      
      imageOverlayService.measureText(longText, 400, { fontSize: 16, lineHeight: 1.2 });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should handle long text in under 10ms
      expect(duration).toBeLessThan(10);
    });
  });

  describe('Template Rendering Performance', () => {
    it('should render template quickly', async () => {
      // Mock image loading to be instant
      const mockImage = createMockImage();
      setTimeout(() => mockImage.onload(), 0);
      
      const startTime = performance.now();
      
      await imageOverlayService.renderTemplate(mockTemplate, mockDocumentData);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should render in under 100ms
      expect(duration).toBeLessThan(100);
    });

    it('should handle multiple overlays efficiently', async () => {
      // Create template with many overlays
      const manyOverlaysTemplate = {
        ...mockTemplate,
        text_overlays: {
          overlays: Array.from({ length: 20 }, (_, i) => ({
            id: `overlay_${i}`,
            placeholder: `{{title}} ${i}`,
            position: { x: 50 + (i * 10), y: 100 + (i * 20), width: 200, height: 30 },
            styling: {
              fontSize: 16,
              fontFamily: 'Arial',
              color: '#000000'
            }
          }))
        }
      };

      const mockImage = createMockImage();
      setTimeout(() => mockImage.onload(), 0);
      
      const startTime = performance.now();
      
      await imageOverlayService.renderTemplate(manyOverlaysTemplate, mockDocumentData);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should handle 20 overlays in under 200ms
      expect(duration).toBeLessThan(200);
    });
  });

  describe('Image Caching Performance', () => {
    it('should cache images for faster subsequent access', async () => {
      const imageUrl = 'https://example.com/test-bg.jpg';
      const mockImage = createMockImage();
      
      // First load
      setTimeout(() => mockImage.onload(), 0);
      const startTime1 = performance.now();
      await imageOverlayService.loadBackgroundImage(imageUrl);
      const endTime1 = performance.now();
      const firstLoadDuration = endTime1 - startTime1;
      
      // Second load (should use cache)
      const startTime2 = performance.now();
      await imageOverlayService.loadBackgroundImage(imageUrl);
      const endTime2 = performance.now();
      const secondLoadDuration = endTime2 - startTime2;
      
      // Cached load should be significantly faster
      expect(secondLoadDuration).toBeLessThan(firstLoadDuration / 2);
    });

    it('should handle cache with many images', async () => {
      const imageUrls = Array.from({ length: 50 }, (_, i) => `https://example.com/bg-${i}.jpg`);
      
      const startTime = performance.now();
      
      // Load all images
      for (const url of imageUrls) {
        const mockImage = createMockImage();
        setTimeout(() => mockImage.onload(), 0);
        await imageOverlayService.loadBackgroundImage(url);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should handle 50 images in under 500ms
      expect(duration).toBeLessThan(500);
    });
  });

  describe('Export Performance', () => {
    it('should export images quickly', () => {
      const mockCanvas = createMockCanvas();
      
      const startTime = performance.now();
      
      for (let i = 0; i < 100; i++) {
        imageOverlayService.exportAsImage(mockCanvas, 'png', 0.9);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should export 100 images in under 100ms
      expect(duration).toBeLessThan(100);
    });

    it('should handle different export formats efficiently', () => {
      const mockCanvas = createMockCanvas();
      const formats = ['png', 'jpeg', 'webp'];
      
      const startTime = performance.now();
      
      formats.forEach(format => {
        for (let i = 0; i < 50; i++) {
          imageOverlayService.exportAsImage(mockCanvas, format, 0.9);
        }
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should handle all formats efficiently
      expect(duration).toBeLessThan(200);
    });
  });

  describe('Memory Usage', () => {
    it('should not leak memory with repeated operations', async () => {
      const initialMemory = performance.memory?.usedJSHeapSize || 0;
      
      // Perform many operations
      for (let i = 0; i < 100; i++) {
        imageOverlayService.initializeCanvas(600, 800);
        
        const mockImage = createMockImage();
        setTimeout(() => mockImage.onload(), 0);
        await imageOverlayService.renderTemplate(mockTemplate, mockDocumentData);
        
        // Clear cache periodically
        if (i % 20 === 0) {
          imageOverlayService.clearCache();
        }
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = performance.memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });

    it('should clear cache effectively', () => {
      // Add items to cache
      for (let i = 0; i < 50; i++) {
        imageOverlayService.imageCache.set(`test-${i}`, `value-${i}`);
      }
      
      expect(imageOverlayService.imageCache.size).toBe(50);
      
      const startTime = performance.now();
      imageOverlayService.clearCache();
      const endTime = performance.now();
      
      expect(imageOverlayService.imageCache.size).toBe(0);
      expect(endTime - startTime).toBeLessThan(10); // Should clear quickly
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle concurrent template rendering', async () => {
      const concurrentCount = 10;
      const mockImage = createMockImage();
      
      const startTime = performance.now();
      
      const promises = Array.from({ length: concurrentCount }, async () => {
        setTimeout(() => mockImage.onload(), 0);
        return imageOverlayService.renderTemplate(mockTemplate, mockDocumentData);
      });
      
      await Promise.all(promises);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should handle concurrent operations efficiently
      expect(duration).toBeLessThan(500);
    });

    it('should maintain performance under load', async () => {
      const operations = [];
      
      // Mix of different operations
      for (let i = 0; i < 20; i++) {
        operations.push(async () => {
          imageOverlayService.initializeCanvas(600, 800);
          
          const mockImage = createMockImage();
          setTimeout(() => mockImage.onload(), 0);
          
          await imageOverlayService.renderTemplate(mockTemplate, mockDocumentData);
          return imageOverlayService.exportAsImage(createMockCanvas(), 'png', 0.9);
        });
      }
      
      const startTime = performance.now();
      await Promise.all(operations.map(op => op()));
      const endTime = performance.now();
      
      const duration = endTime - startTime;
      
      // Should complete all operations in reasonable time
      expect(duration).toBeLessThan(1000);
    });
  });

  describe('Scalability', () => {
    it('should scale with template complexity', async () => {
      const simpleTemplate = {
        ...mockTemplate,
        text_overlays: {
          overlays: [mockTemplate.text_overlays.overlays[0]] // Only one overlay
        }
      };

      const complexTemplate = {
        ...mockTemplate,
        text_overlays: {
          overlays: Array.from({ length: 10 }, (_, i) => ({
            ...mockTemplate.text_overlays.overlays[0],
            id: `overlay_${i}`,
            position: { x: 50, y: 50 + (i * 50), width: 500, height: 40 }
          }))
        }
      };

      const mockImage = createMockImage();
      setTimeout(() => mockImage.onload(), 0);

      // Test simple template
      const startTime1 = performance.now();
      await imageOverlayService.renderTemplate(simpleTemplate, mockDocumentData);
      const endTime1 = performance.now();
      const simpleDuration = endTime1 - startTime1;

      // Test complex template
      const startTime2 = performance.now();
      await imageOverlayService.renderTemplate(complexTemplate, mockDocumentData);
      const endTime2 = performance.now();
      const complexDuration = endTime2 - startTime2;

      // Complex template should not be more than 5x slower
      expect(complexDuration).toBeLessThan(simpleDuration * 5);
    });

    it('should handle large canvas sizes', () => {
      const sizes = [
        [600, 800],    // Standard
        [1200, 1600],  // 2x
        [2400, 3200],  // 4x
        [4800, 6400]   // 8x
      ];

      const durations = sizes.map(([width, height]) => {
        const startTime = performance.now();
        imageOverlayService.initializeCanvas(width, height);
        const endTime = performance.now();
        return endTime - startTime;
      });

      // Performance should degrade gracefully with size
      durations.forEach((duration, index) => {
        if (index > 0) {
          // Each size increase should not be more than 2x slower
          expect(duration).toBeLessThan(durations[index - 1] * 2);
        }
      });
    });
  });
});
