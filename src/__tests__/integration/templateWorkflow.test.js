/**
 * Integration Tests for Template Workflow
 * Tests the complete flow from template selection to export
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { fetchCoverTemplates } from '../../services/templateService.js';
import { generatePreviewData } from '../../services/previewService.js';
import { exportAsPdf } from '../../services/exportService.js';
import imageOverlayService from '../../services/imageOverlayService.js';

// Mock services
vi.mock('../../services/templateService.js');
vi.mock('../../services/previewService.js');
vi.mock('../../services/exportService.js');
vi.mock('../../services/imageOverlayService.js');

// Mock Supabase
vi.mock('../../lib/supabase.js', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => Promise.resolve({ data: [], error: null }))
        }))
      }))
    }))
  }
}));

describe('Template Workflow Integration', () => {
  const mockTemplate = {
    id: 'test-template-001',
    name: 'Test Business Template',
    description: 'A professional business template',
    category: 'Business',
    background_image_url: 'https://example.com/bg.jpg',
    background_image_width: 600,
    background_image_height: 800,
    text_overlays: {
      overlays: [
        {
          id: 'title',
          placeholder: '{{title}}',
          position: { x: 50, y: 200, width: 500, height: 80 },
          styling: {
            fontSize: 36,
            fontFamily: 'Arial',
            fontWeight: 'bold',
            color: '#000000',
            textAlign: 'center'
          }
        },
        {
          id: 'author',
          placeholder: 'by {{author}}',
          position: { x: 50, y: 320, width: 500, height: 40 },
          styling: {
            fontSize: 20,
            fontFamily: 'Arial',
            color: '#666666',
            textAlign: 'center'
          }
        }
      ]
    },
    usage_count: 15,
    rating: 4.5,
    is_premium: false,
    status: 'active'
  };

  const mockDocumentData = {
    title: 'My Test Document',
    author: 'John Doe',
    description: 'This is a test document for integration testing.'
  };

  const mockGeneratedContent = {
    chapters: [
      {
        title: 'Chapter 1',
        content: 'This is the content of chapter 1.'
      }
    ],
    editorHTML: '<h1>Chapter 1</h1><p>This is the content of chapter 1.</p>'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock implementations
    fetchCoverTemplates.mockResolvedValue({
      success: true,
      data: [mockTemplate],
      total: 1
    });

    generatePreviewData.mockResolvedValue({
      success: true,
      previewHTML: '<div>Mock preview HTML</div>',
      templateData: mockTemplate
    });

    exportAsPdf.mockResolvedValue({
      success: true,
      message: 'PDF exported successfully'
    });

    imageOverlayService.renderTemplate.mockResolvedValue({
      toDataURL: () => 'data:image/png;base64,mockImageData'
    });

    imageOverlayService.exportAsImage.mockReturnValue('data:image/png;base64,mockImageData');
    imageOverlayService.generatePreview.mockResolvedValue('data:image/png;base64,mockPreviewData');
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Template Loading and Selection', () => {
    it('should load templates successfully', async () => {
      const result = await fetchCoverTemplates();
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toEqual(mockTemplate);
    });

    it('should handle template loading errors', async () => {
      fetchCoverTemplates.mockResolvedValueOnce({
        success: false,
        error: 'Failed to fetch templates'
      });

      const result = await fetchCoverTemplates();
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to fetch templates');
    });

    it('should filter templates by category', async () => {
      fetchCoverTemplates.mockResolvedValueOnce({
        success: true,
        data: [mockTemplate],
        total: 1
      });

      const result = await fetchCoverTemplates({
        category: 'Business'
      });
      
      expect(result.success).toBe(true);
      expect(fetchCoverTemplates).toHaveBeenCalledWith({
        category: 'Business'
      });
    });
  });

  describe('Preview Generation', () => {
    it('should generate preview with template and document data', async () => {
      const result = await generatePreviewData(
        mockTemplate,
        mockGeneratedContent,
        mockDocumentData
      );
      
      expect(result.success).toBe(true);
      expect(result.previewHTML).toBeDefined();
      expect(generatePreviewData).toHaveBeenCalledWith(
        mockTemplate,
        mockGeneratedContent,
        mockDocumentData
      );
    });

    it('should handle preview generation errors', async () => {
      generatePreviewData.mockRejectedValueOnce(new Error('Preview generation failed'));

      await expect(
        generatePreviewData(mockTemplate, mockGeneratedContent, mockDocumentData)
      ).rejects.toThrow('Preview generation failed');
    });

    it('should generate image overlay preview', async () => {
      const result = await imageOverlayService.generatePreview(mockTemplate, mockDocumentData);
      
      expect(result).toBe('data:image/png;base64,mockPreviewData');
      expect(imageOverlayService.generatePreview).toHaveBeenCalledWith(
        mockTemplate,
        mockDocumentData
      );
    });
  });

  describe('Template Rendering', () => {
    it('should render template with image overlay service', async () => {
      const result = await imageOverlayService.renderTemplate(mockTemplate, mockDocumentData);
      
      expect(result).toBeDefined();
      expect(imageOverlayService.renderTemplate).toHaveBeenCalledWith(
        mockTemplate,
        mockDocumentData
      );
    });

    it('should export rendered template as image', () => {
      const mockCanvas = { toDataURL: () => 'data:image/png;base64,mockImageData' };
      const result = imageOverlayService.exportAsImage(mockCanvas, 'png', 0.9);
      
      expect(result).toBe('data:image/png;base64,mockImageData');
    });

    it('should handle rendering errors gracefully', async () => {
      imageOverlayService.renderTemplate.mockRejectedValueOnce(
        new Error('Failed to load background image')
      );

      await expect(
        imageOverlayService.renderTemplate(mockTemplate, mockDocumentData)
      ).rejects.toThrow('Failed to load background image');
    });
  });

  describe('Export Workflow', () => {
    it('should export document with template as PDF', async () => {
      const result = await exportAsPdf(
        mockDocumentData,
        mockGeneratedContent,
        { selectedTemplate: mockTemplate }
      );
      
      expect(result.success).toBe(true);
      expect(result.message).toBe('PDF exported successfully');
      expect(exportAsPdf).toHaveBeenCalledWith(
        mockDocumentData,
        mockGeneratedContent,
        { selectedTemplate: mockTemplate }
      );
    });

    it('should handle export errors', async () => {
      exportAsPdf.mockResolvedValueOnce({
        success: false,
        error: 'Export failed'
      });

      const result = await exportAsPdf(
        mockDocumentData,
        mockGeneratedContent,
        { selectedTemplate: mockTemplate }
      );
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Export failed');
    });

    it('should export without template (fallback)', async () => {
      const result = await exportAsPdf(
        mockDocumentData,
        mockGeneratedContent,
        { selectedTemplate: null }
      );
      
      expect(result.success).toBe(true);
      expect(exportAsPdf).toHaveBeenCalledWith(
        mockDocumentData,
        mockGeneratedContent,
        { selectedTemplate: null }
      );
    });
  });

  describe('Template Validation', () => {
    it('should validate template structure', () => {
      const validTemplate = mockTemplate;
      
      expect(validTemplate.background_image_url).toBeDefined();
      expect(validTemplate.text_overlays).toBeDefined();
      expect(validTemplate.text_overlays.overlays).toBeInstanceOf(Array);
      expect(validTemplate.text_overlays.overlays.length).toBeGreaterThan(0);
    });

    it('should handle invalid template structure', async () => {
      const invalidTemplate = {
        ...mockTemplate,
        background_image_url: null,
        text_overlays: null
      };

      imageOverlayService.renderTemplate.mockRejectedValueOnce(
        new Error('Invalid template structure')
      );

      await expect(
        imageOverlayService.renderTemplate(invalidTemplate, mockDocumentData)
      ).rejects.toThrow('Invalid template structure');
    });
  });

  describe('Performance and Quality', () => {
    it('should complete template workflow within acceptable time', async () => {
      const startTime = Date.now();
      
      // Simulate full workflow
      await fetchCoverTemplates();
      await generatePreviewData(mockTemplate, mockGeneratedContent, mockDocumentData);
      await imageOverlayService.renderTemplate(mockTemplate, mockDocumentData);
      await exportAsPdf(mockDocumentData, mockGeneratedContent, { selectedTemplate: mockTemplate });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within 5 seconds (generous for testing)
      expect(duration).toBeLessThan(5000);
    });

    it('should handle multiple concurrent template operations', async () => {
      const promises = Array.from({ length: 5 }, () =>
        imageOverlayService.generatePreview(mockTemplate, mockDocumentData)
      );
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toBe('data:image/png;base64,mockPreviewData');
      });
    });

    it('should maintain image quality in export', () => {
      const mockCanvas = { toDataURL: vi.fn(() => 'data:image/png;base64,highQualityData') };
      
      const result = imageOverlayService.exportAsImage(mockCanvas, 'png', 0.95);
      
      expect(mockCanvas.toDataURL).toHaveBeenCalledWith('image/png', 0.95);
      expect(result).toBe('data:image/png;base64,highQualityData');
    });
  });

  describe('Error Recovery', () => {
    it('should recover from template loading failures', async () => {
      // First call fails
      fetchCoverTemplates.mockRejectedValueOnce(new Error('Network error'));
      
      // Second call succeeds
      fetchCoverTemplates.mockResolvedValueOnce({
        success: true,
        data: [mockTemplate],
        total: 1
      });

      // First attempt should fail
      await expect(fetchCoverTemplates()).rejects.toThrow('Network error');
      
      // Retry should succeed
      const result = await fetchCoverTemplates();
      expect(result.success).toBe(true);
    });

    it('should provide fallback when image overlay fails', async () => {
      imageOverlayService.renderTemplate.mockRejectedValueOnce(
        new Error('Image overlay failed')
      );

      // Export should still work with fallback
      const result = await exportAsPdf(
        mockDocumentData,
        mockGeneratedContent,
        { selectedTemplate: mockTemplate }
      );
      
      expect(result.success).toBe(true);
    });
  });

  describe('Data Consistency', () => {
    it('should maintain template data integrity throughout workflow', async () => {
      const templates = await fetchCoverTemplates();
      const template = templates.data[0];
      
      // Template should maintain its structure
      expect(template.id).toBe(mockTemplate.id);
      expect(template.name).toBe(mockTemplate.name);
      expect(template.text_overlays.overlays).toHaveLength(2);
      
      // Preview should use the same template
      await generatePreviewData(template, mockGeneratedContent, mockDocumentData);
      expect(generatePreviewData).toHaveBeenCalledWith(
        template,
        mockGeneratedContent,
        mockDocumentData
      );
    });

    it('should handle document data changes correctly', async () => {
      const updatedDocumentData = {
        ...mockDocumentData,
        title: 'Updated Title',
        author: 'Updated Author'
      };

      await imageOverlayService.renderTemplate(mockTemplate, updatedDocumentData);
      
      expect(imageOverlayService.renderTemplate).toHaveBeenCalledWith(
        mockTemplate,
        updatedDocumentData
      );
    });
  });
});
