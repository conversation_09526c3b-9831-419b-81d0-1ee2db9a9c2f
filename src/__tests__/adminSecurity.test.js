/**
 * Admin Security Implementation Tests
 * Comprehensive testing of admin authorization, security, and access control
 */

import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import '@testing-library/jest-dom';

// Mock modules
jest.mock('../lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
      onAuthStateChange: jest.fn(() => ({ data: { subscription: { unsubscribe: jest.fn() } } }))
    },
    rpc: jest.fn(),
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            range: jest.fn(() => Promise.resolve({ data: [], error: null }))
          }))
        }))
      }))
    }))
  }
}));

jest.mock('../utils/prodLogger', () => ({
  prodLogger: {
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

jest.mock('../utils/sessionManager', () => ({
  sessionManager: {
    getSessionInfo: jest.fn(() => ({
      sessionId: 'test-session',
      startTime: Date.now() - 1000,
      ipAddress: '127.0.0.1',
      activityCount: 10,
      ipChanges: 0
    })),
    startSessionMonitoring: jest.fn(),
    stopSessionMonitoring: jest.fn()
  },
  sessionStorage: {
    clear: jest.fn()
  }
}));

// Import components after mocking
import AdminRoute from '../components/auth/AdminRoute';
import adminService from '../services/adminService';
import { useAdminSecurity } from '../hooks/useAdminSecurity';
import { AuthProvider } from '../contexts/AuthContext';

// Mock AuthContext
const mockAuthContext = {
  user: { id: 'test-user-id', email: '<EMAIL>' },
  isAuthenticated: true,
  loading: false,
  profile: { full_name: 'Test User' }
};

jest.mock('../contexts/AuthContext', () => ({
  AuthProvider: ({ children }) => children,
  useAuth: () => mockAuthContext
}));

describe('Admin Security Implementation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AdminRoute Component', () => {
    const TestComponent = () => <div>Admin Content</div>;

    test('should show loading state initially', () => {
      // Mock admin service to simulate loading
      jest.spyOn(adminService, 'isUserAdmin').mockImplementation(() => new Promise(() => {}));
      
      render(
        <BrowserRouter>
          <AdminRoute>
            <TestComponent />
          </AdminRoute>
        </BrowserRouter>
      );

      expect(screen.getByText('Verifying admin access...')).toBeInTheDocument();
    });

    test('should redirect to auth if not authenticated', () => {
      const unauthenticatedContext = {
        ...mockAuthContext,
        isAuthenticated: false,
        user: null
      };

      jest.doMock('../contexts/AuthContext', () => ({
        useAuth: () => unauthenticatedContext
      }));

      render(
        <BrowserRouter>
          <AdminRoute>
            <TestComponent />
          </AdminRoute>
        </BrowserRouter>
      );

      // Should not render admin content
      expect(screen.queryByText('Admin Content')).not.toBeInTheDocument();
    });

    test('should show access denied for non-admin users', async () => {
      jest.spyOn(adminService, 'isUserAdmin').mockResolvedValue(false);
      jest.spyOn(adminService, 'userHasAdminRole').mockResolvedValue(false);
      jest.spyOn(adminService, 'validateAdminSession').mockResolvedValue({ valid: true });

      render(
        <BrowserRouter>
          <AdminRoute>
            <TestComponent />
          </AdminRoute>
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(screen.getByText('Access Denied')).toBeInTheDocument();
      });
    });

    test('should render content for valid admin users', async () => {
      jest.spyOn(adminService, 'isUserAdmin').mockResolvedValue(true);
      jest.spyOn(adminService, 'userHasAdminRole').mockResolvedValue(true);
      jest.spyOn(adminService, 'validateAdminSession').mockResolvedValue({ valid: true });
      jest.spyOn(adminService, 'logAdminActivity').mockResolvedValue('log-id');

      render(
        <BrowserRouter>
          <AdminRoute>
            <TestComponent />
          </AdminRoute>
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(screen.getByText('Admin Content')).toBeInTheDocument();
      });
    });

    test('should check required role permissions', async () => {
      jest.spyOn(adminService, 'isUserAdmin').mockResolvedValue(true);
      jest.spyOn(adminService, 'userHasAdminRole').mockResolvedValue(false); // No required role
      jest.spyOn(adminService, 'validateAdminSession').mockResolvedValue({ valid: true });

      render(
        <BrowserRouter>
          <AdminRoute requiredRole="super_admin">
            <TestComponent />
          </AdminRoute>
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(screen.getByText('Access Denied')).toBeInTheDocument();
      });
    });

    test('should handle session validation failures', async () => {
      jest.spyOn(adminService, 'isUserAdmin').mockResolvedValue(true);
      jest.spyOn(adminService, 'userHasAdminRole').mockResolvedValue(true);
      jest.spyOn(adminService, 'validateAdminSession').mockResolvedValue({ 
        valid: false, 
        reason: 'Session expired',
        requiresReauth: true
      });

      render(
        <BrowserRouter>
          <AdminRoute>
            <TestComponent />
          </AdminRoute>
        </BrowserRouter>
      );

      await waitFor(() => {
        // Should redirect to auth for re-authentication
        expect(screen.queryByText('Admin Content')).not.toBeInTheDocument();
      });
    });
  });

  describe('Admin Service', () => {
    test('should check admin status correctly', async () => {
      const { supabase } = require('../lib/supabase');
      supabase.rpc.mockResolvedValue({ data: true, error: null });

      const result = await adminService.isUserAdmin('test-user-id');
      
      expect(result).toBe(true);
      expect(supabase.rpc).toHaveBeenCalledWith('is_user_admin', {
        user_id: 'test-user-id'
      });
    });

    test('should handle admin check errors gracefully', async () => {
      const { supabase } = require('../lib/supabase');
      supabase.rpc.mockResolvedValue({ data: null, error: { message: 'Database error' } });

      const result = await adminService.isUserAdmin('test-user-id');
      
      expect(result).toBe(false);
    });

    test('should validate admin sessions', async () => {
      const { supabase } = require('../lib/supabase');
      supabase.auth.getUser.mockResolvedValue({ 
        data: { user: { id: 'test-user-id' } } 
      });
      
      jest.spyOn(adminService, 'isUserAdmin').mockResolvedValue(true);
      jest.spyOn(adminService, 'checkSuspiciousActivity').mockResolvedValue({ detected: false });

      const result = await adminService.validateAdminSession();
      
      expect(result.valid).toBe(true);
    });

    test('should detect suspicious activity', async () => {
      jest.spyOn(adminService, 'getAdminActivityLogs').mockResolvedValue([
        { action: 'grant_admin_role', ip_address: '***********' },
        { action: 'grant_admin_role', ip_address: '***********' },
        { action: 'revoke_admin_role', ip_address: '***********' },
        { action: 'grant_admin_role', ip_address: '***********' }
      ]);

      const result = await adminService.checkSuspiciousActivity('test-user-id');
      
      expect(result.detected).toBe(true);
      expect(result.details).toContain('Multiple privilege changes');
      expect(result.details).toContain('Multiple IP addresses in short time');
    });

    test('should log admin activities', async () => {
      const { supabase } = require('../lib/supabase');
      supabase.rpc.mockResolvedValue({ data: 'activity-log-id', error: null });

      const result = await adminService.logAdminActivity(
        'template_create',
        'template',
        'template-123',
        { name: 'Test Template' }
      );

      expect(result).toBe('activity-log-id');
      expect(supabase.rpc).toHaveBeenCalledWith('log_admin_activity', expect.objectContaining({
        p_action: 'template_create',
        p_resource_type: 'template',
        p_resource_id: 'template-123'
      }));
    });
  });

  describe('Admin Security Hook', () => {
    test('should provide security status', async () => {
      jest.spyOn(adminService, 'getCurrentUserAdminInfo').mockResolvedValue({
        is_admin: true,
        admin_role: 'admin'
      });
      jest.spyOn(adminService, 'validateAdminSession').mockResolvedValue({ valid: true });
      jest.spyOn(adminService, 'checkSuspiciousActivity').mockResolvedValue({ detected: false });

      const TestComponent = () => {
        const security = useAdminSecurity();
        return (
          <div>
            <div data-testid="is-admin">{security.isAdmin.toString()}</div>
            <div data-testid="is-secure">{security.isSecure.toString()}</div>
          </div>
        );
      };

      render(<TestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('is-admin')).toHaveTextContent('true');
        expect(screen.getByTestId('is-secure')).toHaveTextContent('true');
      });
    });
  });

  describe('Security Edge Cases', () => {
    test('should handle network errors gracefully', async () => {
      jest.spyOn(adminService, 'isUserAdmin').mockRejectedValue(new Error('Network error'));

      const result = await adminService.isUserAdmin('test-user-id');
      expect(result).toBe(false);
    });

    test('should prevent privilege escalation', async () => {
      const { supabase } = require('../lib/supabase');
      supabase.rpc.mockResolvedValue({ 
        data: null, 
        error: { message: 'Access denied: Only super_admin can grant admin roles' } 
      });

      await expect(adminService.grantAdminRole('target-user', 'admin')).rejects.toThrow();
    });

    test('should handle malformed session data', async () => {
      const { sessionManager } = require('../utils/sessionManager');
      sessionManager.getSessionInfo.mockReturnValue({
        sessionId: null,
        startTime: null,
        ipAddress: null
      });

      const result = await adminService.validateAdminSession();
      expect(result.valid).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    test('should enforce rate limits on admin actions', async () => {
      const { withAdminRateLimit } = require('../middleware/adminMiddleware');
      
      const mockAction = jest.fn().mockResolvedValue('success');
      const rateLimitedAction = withAdminRateLimit('test_action');

      // Simulate multiple rapid calls
      const promises = Array(10).fill().map(() => 
        rateLimitedAction('test-user-id', mockAction)
      );

      const results = await Promise.allSettled(promises);
      
      // Some should succeed, some should be rate limited
      const successes = results.filter(r => r.status === 'fulfilled');
      const failures = results.filter(r => r.status === 'rejected');
      
      expect(successes.length).toBeGreaterThan(0);
      expect(failures.length).toBeGreaterThan(0);
    });
  });
});

describe('Integration Tests', () => {
  test('should complete full admin workflow', async () => {
    // Mock successful admin checks
    jest.spyOn(adminService, 'isUserAdmin').mockResolvedValue(true);
    jest.spyOn(adminService, 'userHasAdminRole').mockResolvedValue(true);
    jest.spyOn(adminService, 'validateAdminSession').mockResolvedValue({ valid: true });
    jest.spyOn(adminService, 'logAdminActivity').mockResolvedValue('log-id');

    const AdminTestComponent = () => <div>Admin Dashboard</div>;

    render(
      <BrowserRouter>
        <AdminRoute requiredRole="admin">
          <AdminTestComponent />
        </AdminRoute>
      </BrowserRouter>
    );

    // Should render admin content
    await waitFor(() => {
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
    });

    // Should have logged the access
    expect(adminService.logAdminActivity).toHaveBeenCalledWith(
      'admin_route_access',
      'route',
      expect.any(String),
      expect.any(Object)
    );
  });
});
