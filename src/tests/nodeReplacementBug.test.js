/**
 * Test for Node Replacement Bug Fix
 * Ensures that <PERSON><PERSON><PERSON><PERSON> only replaces the current node, not the entire document
 */

import {
  extractNodeContent,
  replaceNodeContent,
} from "../utils/contentProcessor.js";

// Mock TipTap editor with multiple paragraphs
const createMockEditorWithMultipleParagraphs = () => ({
  state: {
    selection: {
      from: 0,
      to: 0, // No selection
      $from: {
        node: () => ({
          type: { name: "paragraph" },
          textContent: "First paragraph content",
          content: { size: 23 },
          attrs: {},
          nodeSize: 25,
        }),
        start: () => 0, // Start position of first paragraph
      },
    },
    doc: {
      textBetween: () => "", // No selection
      content: {
        size: 100, // Total document size
      },
    },
    tr: {
      replaceWith: jest.fn().mockReturnThis(),
    },
  },
  schema: {
    nodes: {
      paragraph: {
        create: jest.fn((attrs, content) => ({
          type: "paragraph",
          attrs,
          content,
        })),
      },
      heading: {
        create: jest.fn((attrs, content) => ({
          type: "heading",
          attrs,
          content,
        })),
      },
    },
    text: jest.fn((content) => ({ type: "text", text: content })),
  },
  view: {
    dispatch: jest.fn(),
  },
  commands: {
    setTextSelection: jest.fn(),
    focus: jest.fn(),
  },
});

describe("Node Replacement Bug Fix", () => {
  it("should only replace current node, not entire document", () => {
    const editor = createMockEditorWithMultipleParagraphs();

    // Extract content from first paragraph
    const extractionResult = extractNodeContent(editor);

    expect(extractionResult.text).toBe("First paragraph content");
    expect(extractionResult.hasSelection).toBe(false);
    expect(extractionResult.nodeType).toBe("paragraph");

    // Replace content with new text
    const newContent = "Updated paragraph content";

    // This should NOT clear the entire document
    replaceNodeContent(editor, newContent, extractionResult);

    // Verify that replaceWith was called with node-specific positions
    expect(editor.state.tr.replaceWith).toHaveBeenCalledWith(
      0, // Node start position
      23, // Node end position (start + content size)
      expect.objectContaining({
        type: "paragraph",
        content: expect.objectContaining({
          type: "text",
          text: newContent,
        }),
      })
    );

    // Verify that view.dispatch was called (applying the transaction)
    expect(editor.view.dispatch).toHaveBeenCalled();

    // Verify that clearContent was NOT called (this was the bug)
    expect(editor.commands.focus).not.toHaveBeenCalledWith("end");
  });

  it("should preserve node type and attributes when replacing", () => {
    const editor = createMockEditorWithMultipleParagraphs();

    // Mock a heading node
    editor.state.selection.$from.node = () => ({
      type: { name: "heading" },
      textContent: "Original Heading",
      content: { size: 16 },
      attrs: { level: 2 },
      nodeSize: 18,
    });

    const extractionResult = extractNodeContent(editor);
    const newContent = "Updated Heading";

    replaceNodeContent(editor, newContent, extractionResult);

    // Should create a heading node with preserved level
    expect(editor.schema.nodes.heading.create).toHaveBeenCalledWith(
      { level: 2 },
      expect.objectContaining({
        type: "text",
        text: newContent,
      })
    );
  });

  it("should handle text selection correctly (no regression)", () => {
    const editor = createMockEditorWithMultipleParagraphs();

    // Mock a text selection
    editor.state.selection.from = 5;
    editor.state.selection.to = 15;
    editor.state.doc.textBetween = () => "selected";

    const extractionResult = extractNodeContent(editor);
    const newContent = "replaced";

    expect(extractionResult.hasSelection).toBe(true);
    expect(extractionResult.selectedText).toBe("selected");

    replaceNodeContent(editor, newContent, extractionResult);

    // For selections, should use the original replaceWith logic
    expect(editor.state.tr.replaceWith).toHaveBeenCalledWith(
      5, // Selection start
      15, // Selection end
      expect.objectContaining({
        type: "text",
        text: newContent,
      })
    );
  });
});
