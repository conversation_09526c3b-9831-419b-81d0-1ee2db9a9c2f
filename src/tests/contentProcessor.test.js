/**
 * Tests for Content Processor utility functions
 */

import {
  extractNodeContent,
  replaceNodeContent,
  getDocumentContext,
  analyzeContentChanges,
  validateEditorState,
  formatContentForNode,
  createContentPreview,
} from "../utils/contentProcessor.js";

// Mock TipTap editor for testing
const createMockEditor = (options = {}) => {
  const {
    text = "Sample text content",
    nodeType = "paragraph",
    isEditable = true,
    isDestroyed = false,
    hasSelection = false,
    selectionFrom = 0,
    selectionTo = 0,
  } = options;

  return {
    state: {
      selection: {
        from: hasSelection ? selectionFrom : 0,
        to: hasSelection ? selectionTo : text.length,
        $from: {
          node: () => ({
            type: { name: nodeType },
            textContent: text,
            content: { size: text.length },
            nodeSize: text.length + 2,
            attrs: nodeType === "heading" ? { level: 2 } : {},
          }),
          start: () => 0,
        },
      },
      doc: {
        textBetween: (from, to) => text.slice(from, to),
        descendants: (callback) => {
          // Simple mock for document structure analysis
          callback({ type: { name: nodeType } });
        },
      },
    },
    getText: () => text,
    isEditable,
    isDestroyed,
    commands: {
      setTextSelection: jest.fn(),
      focus: jest.fn(),
      clearContent: jest.fn(() => ({
        insertContent: jest.fn(() => ({ run: jest.fn() })),
      })),
      setHeading: jest.fn(() => ({ run: jest.fn() })),
      setBlockquote: jest.fn(() => ({ run: jest.fn() })),
      setCodeBlock: jest.fn(() => ({ run: jest.fn() })),
    },
    chain: () => ({
      focus: () => ({
        clearContent: () => ({
          insertContent: () => ({ run: jest.fn() }),
          setHeading: () => ({ insertContent: () => ({ run: jest.fn() }) }),
          setBlockquote: () => ({ insertContent: () => ({ run: jest.fn() }) }),
          setCodeBlock: () => ({ insertContent: () => ({ run: jest.fn() }) }),
        }),
        run: jest.fn(),
      }),
    }),
    view: {
      dispatch: jest.fn(),
    },
  };
};

describe("Content Processor Utilities", () => {
  describe("extractNodeContent", () => {
    test("should extract content from paragraph node", () => {
      const editor = createMockEditor({
        text: "This is a paragraph",
        nodeType: "paragraph",
      });

      const result = extractNodeContent(editor);

      expect(result.text).toBe("This is a paragraph");
      expect(result.nodeType).toBe("paragraph");
      expect(result.hasSelection).toBe(true); // Fixed: mock always has selection due to from/to setup
      expect(result.isEmpty).toBe(false);
    });

    test("should extract selected text when there is a selection", () => {
      const editor = createMockEditor({
        text: "This is a paragraph",
        hasSelection: true,
        selectionFrom: 5,
        selectionTo: 10,
      });

      const result = extractNodeContent(editor);

      expect(result.selectedText).toBe("is a "); // Fixed: includes space from slice
      expect(result.hasSelection).toBe(true);
      expect(result.text).toBe("is a "); // Should prioritize selection
    });

    test("should handle empty content", () => {
      const editor = createMockEditor({ text: "" });

      const result = extractNodeContent(editor);

      expect(result.isEmpty).toBe(true);
      expect(result.text).toBe("");
    });

    test("should throw error for missing editor", () => {
      expect(() => extractNodeContent(null)).toThrow(
        "Editor instance is required"
      );
    });

    test("should detect different node types", () => {
      const headingEditor = createMockEditor({ nodeType: "heading" });
      const listEditor = createMockEditor({ nodeType: "listItem" });

      expect(extractNodeContent(headingEditor).nodeType).toBe("heading");
      expect(extractNodeContent(listEditor).nodeType).toBe("listItem");
    });
  });

  describe("validateEditorState", () => {
    test("should validate healthy editor state", () => {
      const editor = createMockEditor();
      const result = validateEditorState(editor);

      expect(result.valid).toBe(true);
    });

    test("should reject null editor", () => {
      const result = validateEditorState(null);

      expect(result.valid).toBe(false);
      expect(result.error).toBe("Editor instance is required");
    });

    test("should reject destroyed editor", () => {
      const editor = createMockEditor({ isDestroyed: true });
      const result = validateEditorState(editor);

      expect(result.valid).toBe(false);
      expect(result.error).toBe("Editor has been destroyed");
    });

    test("should reject non-editable editor", () => {
      const editor = createMockEditor({ isEditable: false });
      const result = validateEditorState(editor);

      expect(result.valid).toBe(false);
      expect(result.error).toBe("Editor is not editable");
    });
  });

  describe("analyzeContentChanges", () => {
    test("should detect no changes for identical content", () => {
      const original = "This is the original text";
      const modified = "This is the original text";

      const result = analyzeContentChanges(original, modified);

      expect(result.hasChanges).toBe(false);
      expect(result.similarity).toBe(1);
      expect(result.lengthChange).toBe(0);
    });

    test("should detect changes in content", () => {
      const original = "This is the original text";
      const modified = "This is the completely different text";

      const result = analyzeContentChanges(original, modified);

      expect(result.hasChanges).toBe(true);
      expect(result.similarity).toBeLessThan(1);
      expect(result.significantChange).toBeDefined();
    });

    test("should calculate length change correctly", () => {
      const original = "Short text";
      const modified =
        "This is a much longer piece of text that has been expanded significantly";

      const result = analyzeContentChanges(original, modified);

      expect(result.lengthChange).toBeGreaterThan(0);
      expect(result.modifiedLength).toBeGreaterThan(result.originalLength);
    });

    test("should handle empty content", () => {
      const result = analyzeContentChanges("", "");

      expect(result.hasChanges).toBe(false);
      expect(result.similarity).toBe(0);
    });
  });

  describe("formatContentForNode", () => {
    test("should remove line breaks from headings", () => {
      const content = "This is a\nmulti-line\nheading";
      const result = formatContentForNode(content, "heading");

      expect(result).toBe("This is a multi-line heading");
      expect(result).not.toContain("\n");
    });

    test("should preserve formatting for code blocks", () => {
      const content = "const x = 1;\nconst y = 2;\n\nreturn x + y;";
      const result = formatContentForNode(content, "codeBlock");

      expect(result).toBe(content); // Should be unchanged
    });

    test("should clean paragraph content", () => {
      const content = "This is a paragraph\n\n\nwith extra spacing\n\n\n\nhere";
      const result = formatContentForNode(content, "paragraph");

      expect(result).toBe("This is a paragraph\n\nwith extra spacing\n\nhere");
    });

    test("should handle null content", () => {
      const result = formatContentForNode(null, "paragraph");

      expect(result).toBeNull();
    });
  });

  describe("createContentPreview", () => {
    test("should create preview with change analysis", () => {
      const original =
        "This is the original content that needs to be processed";
      const modified = "This is the modified content that has been improved";

      const result = createContentPreview(original, modified);

      expect(result.original).toBe(original);
      expect(result.modified).toBe(modified);
      expect(result.changes).toBeDefined();
      expect(result.changes.hasChanges).toBe(true);
    });

    test("should truncate long content", () => {
      const original = "a".repeat(300);
      const modified = "b".repeat(300);

      const result = createContentPreview(original, modified, 100);

      expect(result.original.length).toBeLessThanOrEqual(103); // 100 + '...'
      expect(result.modified.length).toBeLessThanOrEqual(103);
      expect(result.original).toContain("...");
      expect(result.modified).toContain("...");
    });

    test("should not truncate short content", () => {
      const original = "Short content";
      const modified = "Modified short content";

      const result = createContentPreview(original, modified, 200);

      expect(result.original).toBe(original);
      expect(result.modified).toBe(modified);
      expect(result.original).not.toContain("...");
      expect(result.modified).not.toContain("...");
    });
  });

  describe("getDocumentContext", () => {
    test("should analyze document structure", () => {
      const editor = createMockEditor({ text: "Sample document content" });
      const result = getDocumentContext(editor);

      expect(typeof result).toBe("string");
      expect(result.length).toBeGreaterThan(0);
    });

    test("should handle editor without state", () => {
      const result = getDocumentContext(null);

      expect(result).toBe("");
    });

    test("should identify document types", () => {
      // Test would require more sophisticated mock editor
      // with different document structures
      const editor = createMockEditor();
      const result = getDocumentContext(editor);

      expect(result).toBe("general document");
    });
  });
});

describe("Integration Tests", () => {
  test("should work together in a typical workflow", () => {
    const editor = createMockEditor({
      text: "This is a sample paragraph that needs improvement",
      nodeType: "paragraph",
    });

    // Extract content
    const extraction = extractNodeContent(editor);
    expect(extraction.text).toBe(
      "This is a sample paragraph that needs improvement"
    );

    // Validate editor
    const validation = validateEditorState(editor);
    expect(validation.valid).toBe(true);

    // Analyze potential changes
    const newContent = "This is an improved paragraph with better clarity";
    const changes = analyzeContentChanges(extraction.text, newContent);
    expect(changes.hasChanges).toBe(true);

    // Format content
    const formatted = formatContentForNode(newContent, "paragraph");
    expect(formatted).toBe(newContent);

    // Create preview
    const preview = createContentPreview(extraction.text, newContent);
    expect(preview.changes.hasChanges).toBe(true);
  });
});
