/**
 * Cover Preview Workflow Tests
 * Tests the new cover-only preview functionality and export integration
 */

import coverPreviewService from '../services/coverPreviewService.js';

// Mock dependencies
jest.mock('../services/imageOverlayService.js', () => ({
  default: {
    renderTemplate: jest.fn().mockResolvedValue({}),
    exportAsImage: jest.fn().mockReturnValue('data:image/png;base64,mockImageData')
  }
}));

jest.mock('../utils/errorMonitor.js', () => ({
  default: {
    logError: jest.fn()
  },
  ErrorSeverity: {
    MEDIUM: 'medium'
  }
}));

describe('Cover Preview Service', () => {
  const mockTemplate = {
    id: 'template-1',
    name: 'Test Template',
    category: 'business',
    background_image_url: 'https://example.com/bg.jpg',
    text_overlays: {
      overlays: [
        {
          placeholder: '{{title}}',
          position: { x: 100, y: 100 },
          styling: { fontSize: 24, color: '#000000' }
        }
      ]
    }
  };

  const mockDocumentData = {
    title: 'Test Document',
    author: 'Test Author',
    description: 'Test Description'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should generate cover preview successfully', async () => {
    const result = await coverPreviewService.generateCoverPreview(mockTemplate, mockDocumentData);

    expect(result).toHaveProperty('coverHTML');
    expect(result).toHaveProperty('coverImageData');
    expect(result).toHaveProperty('metadata');
    expect(result.metadata.previewType).toBe('cover-only');
    expect(result.metadata.templateId).toBe(mockTemplate.id);
  });

  it('should generate cover thumbnail for template cards', async () => {
    const thumbnail = await coverPreviewService.generateCoverThumbnail(mockTemplate);

    expect(thumbnail).toBe('data:image/png;base64,mockImageData');
  });

  it('should update cover preview with new template', async () => {
    const newTemplate = { ...mockTemplate, id: 'template-2', name: 'New Template' };
    
    const result = await coverPreviewService.updateCoverPreview(newTemplate, mockDocumentData);

    expect(result).toHaveProperty('coverHTML');
    expect(result.metadata.templateId).toBe('template-2');
  });

  it('should handle errors gracefully and return fallback', async () => {
    // Mock imageOverlayService to throw error
    const imageOverlayService = await import('../services/imageOverlayService.js');
    imageOverlayService.default.renderTemplate.mockRejectedValueOnce(new Error('Render failed'));

    const result = await coverPreviewService.generateCoverPreview(mockTemplate, mockDocumentData);

    expect(result).toHaveProperty('coverHTML');
    expect(result.metadata.previewType).toBe('cover-only-fallback');
    expect(result.coverHTML).toContain('fallback-cover');
  });

  it('should batch generate previews for multiple templates', async () => {
    const templates = [mockTemplate, { ...mockTemplate, id: 'template-2' }];
    
    const results = await coverPreviewService.batchGenerateCoverPreviews(templates, mockDocumentData);

    expect(results).toHaveLength(2);
    expect(results[0].success).toBe(true);
    expect(results[1].success).toBe(true);
  });
});

describe('Export Integration', () => {
  it('should maintain consistency between cover preview and export', async () => {
    // This test would verify that the cover generated for preview
    // matches the cover generated for export
    const coverPreview = await coverPreviewService.generateCoverPreview(mockTemplate, mockDocumentData);
    
    expect(coverPreview.coverImageData).toBeTruthy();
    expect(coverPreview.metadata.templateId).toBe(mockTemplate.id);
  });
});

describe('Performance Improvements', () => {
  it('should generate cover preview faster than full document preview', async () => {
    const startTime = performance.now();
    
    await coverPreviewService.generateCoverPreview(mockTemplate, mockDocumentData);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Cover preview should be significantly faster (under 100ms for mocked services)
    expect(duration).toBeLessThan(100);
  });

  it('should not process document content during cover preview', async () => {
    const imageOverlayService = await import('../services/imageOverlayService.js');
    
    await coverPreviewService.generateCoverPreview(mockTemplate, mockDocumentData);
    
    // Verify that renderTemplate was called with document metadata only
    expect(imageOverlayService.default.renderTemplate).toHaveBeenCalledWith(
      mockTemplate,
      expect.objectContaining({
        title: mockDocumentData.title,
        author: mockDocumentData.author,
        description: mockDocumentData.description,
        wordCount: 'TBD',
        pageCount: 'TBD'
      })
    );
  });
});
