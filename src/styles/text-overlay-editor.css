/* Text Overlay Editor Styles */

/* Custom slider styling for font size editor */
.slider {
  -webkit-appearance: none;
  appearance: none;
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.slider:hover {
  opacity: 1;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Mobile optimizations for sliders */
@media (max-width: 768px) {
  .slider::-webkit-slider-thumb {
    width: 24px;
    height: 24px;
  }
  
  .slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
  }
}

/* Text overlay editor panel animations */
.text-overlay-editor {
  transition: all 0.3s ease-in-out;
}

/* Color picker custom styling */
.color-picker-swatch {
  transition: transform 0.2s ease;
}

.color-picker-swatch:hover {
  transform: scale(1.1);
}

.color-picker-swatch:active {
  transform: scale(0.95);
}

/* Position editor directional controls */
.position-controls {
  user-select: none;
}

.position-controls button {
  transition: all 0.2s ease;
}

.position-controls button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.position-controls button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Style editor toggle buttons */
.style-toggle {
  transition: all 0.2s ease;
}

.style-toggle.active {
  transform: scale(1.05);
}

/* Alignment editor buttons */
.alignment-button {
  transition: all 0.2s ease;
}

.alignment-button:hover {
  transform: translateY(-1px);
}

.alignment-button.selected {
  box-shadow: 0 0 0 2px #3B82F6;
}

/* Mobile editor modal animations and adaptive bottom sheet */
@media (max-width: 1024px) {
  .mobile-editor-modal {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes slideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Adaptive bottom sheet styling */
  .mobile-bottom-sheet {
    /* Ensure smooth height transitions */
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* Add subtle shadow for depth */
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    /* Ensure proper touch scrolling */
    -webkit-overflow-scrolling: touch;
  }

  /* Drag handle styling */
  .mobile-drag-handle {
    /* Make drag handle more prominent on touch */
    padding: 8px 0;
    cursor: grab;
    touch-action: pan-y;
  }

  .mobile-drag-handle:active {
    cursor: grabbing;
  }

  .mobile-drag-handle::before {
    content: '';
    display: block;
    width: 48px;
    height: 4px;
    background-color: #d1d5db;
    border-radius: 2px;
    margin: 0 auto;
    transition: background-color 0.2s ease;
  }

  .mobile-drag-handle:hover::before,
  .mobile-drag-handle:active::before {
    background-color: #9ca3af;
  }

  /* Compact overlay selector styling */
  .compact-overlay-selector {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
  }

  .compact-overlay-selector select {
    background: transparent;
    border: none;
    outline: none;
    font-weight: 500;
    color: #1f2937;
  }

  /* Progressive disclosure separator */
  .mobile-advanced-separator {
    position: relative;
    margin: 16px 0;
  }

  .mobile-advanced-separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
  }

  .mobile-advanced-separator span {
    position: relative;
    background: white;
    padding: 0 12px;
    font-size: 11px;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  /* Expand hint styling */
  .mobile-expand-hint {
    animation: pulseHint 2s infinite;
  }

  @keyframes pulseHint {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
  }
}

/* Desktop editor panel animations */
@media (min-width: 1024px) {
  .desktop-editor-panel {
    animation: slideLeft 0.3s ease-out;
  }
  
  @keyframes slideLeft {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
}

/* Preview updating indicator */
.preview-updating {
  position: relative;
  overflow: hidden;
}

.preview-updating::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3B82F6, transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Customization indicator dots */
.customization-dot {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Smooth transitions for all interactive elements */
.text-overlay-editor button,
.text-overlay-editor input,
.text-overlay-editor select {
  transition: all 0.2s ease;
}

/* Focus states for accessibility */
.text-overlay-editor button:focus,
.text-overlay-editor input:focus,
.text-overlay-editor select:focus {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Disabled state styling */
.text-overlay-editor button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Loading state for buttons */
.button-loading {
  position: relative;
  color: transparent !important;
}

.button-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error state styling */
.editor-error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Success state styling */
.editor-success {
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Enhanced mobile responsive styling */
@media (max-width: 640px) {
  .text-overlay-editor {
    font-size: 14px;
  }

  .text-overlay-editor h3 {
    font-size: 16px;
    font-weight: 600;
  }

  .text-overlay-editor button {
    min-height: 44px; /* Touch-friendly minimum */
    min-width: 44px;
    border-radius: 8px; /* Consistent with user preference for rounded-lg */
  }

  .text-overlay-editor input,
  .text-overlay-editor select {
    /* min-height: 44px; */
    font-size: 16px; /* Prevent zoom on iOS */
    border-radius: 8px;
    padding: 12px 16px;
  }

  /* Compact spacing for mobile */
  .text-overlay-editor .space-y-4 > * + * {
    margin-top: 16px;
  }

  /* Touch-optimized color picker */
  .color-picker-swatch {
    min-width: 44px;
    min-height: 44px;
    border-radius: 8px;
  }

  /* Mobile-optimized font size controls */
  .font-size-presets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .font-size-presets button {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 6px;
  }

  /* Improved mobile header */
  .mobile-editor-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid #e2e8f0;
  }

  /* Better mobile scrolling */
  .mobile-editor-content {
    /* Improve scroll performance */
    -webkit-overflow-scrolling: touch;
    /* Prevent overscroll bounce */
    overscroll-behavior: contain;
    /* Add momentum scrolling */
    scroll-behavior: smooth;
  }

  /* Mobile-specific button groups */
  .mobile-button-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(44px, 1fr));
    gap: 8px;
  }

  .mobile-button-group button {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Mobile header action buttons */
  .mobile-editor-header button {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Mobile reset button styling */
  .mobile-reset-button {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  .mobile-reset-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .mobile-reset-button:active {
    transform: translateY(0);
  }

  /* Icon sizing for mobile buttons */
  .mobile-editor-header .lucide {
    width: 20px;
    height: 20px;
  }

  .mobile-reset-button .lucide {
    width: 16px;
    height: 16px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .text-overlay-editor {
    border-width: 2px;
  }
  
  .text-overlay-editor button {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .text-overlay-editor *,
  .text-overlay-editor *::before,
  .text-overlay-editor *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
