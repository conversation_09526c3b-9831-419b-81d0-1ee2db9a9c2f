/**
 * Notification System Styles for DocForge AI
 * 
 * Comprehensive styling for the notification system that integrates
 * with the existing design system and animations.
 */

/* ==========================================================================
   Toast Notification Animations
   ========================================================================== */

/* Slide in from right animation for toasts */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-slide-out-right {
  animation: slideOutRight 0.3s ease-in;
}

/* Slide down animation for notification center */
@keyframes slideInDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-in-down {
  animation: slideInDown 0.2s ease-out;
}

/* ==========================================================================
   Notification Badge Animations
   ========================================================================== */

/* Subtle pulse for notification badge */
@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

.animate-badge-pulse {
  animation: badgePulse 2s ease-in-out infinite;
}

/* Badge appearance animation */
@keyframes badgeAppear {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-badge-appear {
  animation: badgeAppear 0.2s ease-out;
}

/* ==========================================================================
   Loading Spinner for Notifications
   ========================================================================== */

/* Smooth rotation for loading notifications */
@keyframes notificationSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.notification-loading-spin {
  animation: notificationSpin 1s linear infinite;
}

/* ==========================================================================
   Notification Container Positioning
   ========================================================================== */

/* Toast container positioning */
.notification-toast-container {
  position: fixed;
  top: 5rem; /* Below header */
  right: 1rem;
  z-index: 1200;
  pointer-events: none;
}

/* Notification center positioning */
.notification-center-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  z-index: 1100;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

/* Mobile adjustments */
@media (max-width: 640px) {
  .notification-toast-container {
    top: 4rem;
    right: 0.5rem;
    left: 0.5rem;
  }
  
  .notification-center-dropdown {
    width: 100vw;
    max-width: calc(100vw - 1rem);
    right: -1rem;
  }
  
  /* Adjust toast width on mobile */
  .notification-toast-mobile {
    max-width: none;
    width: 100%;
  }
  
  /* Compact notification items on mobile */
  .notification-item-mobile {
    padding: 0.75rem;
  }
  
  .notification-item-mobile .notification-content {
    font-size: 0.875rem;
  }
  
  .notification-item-mobile .notification-timestamp {
    font-size: 0.75rem;
  }
}

/* Tablet adjustments */
@media (min-width: 641px) and (max-width: 1024px) {
  .notification-center-dropdown {
    width: 24rem;
  }
}

/* ==========================================================================
   Accessibility Enhancements
   ========================================================================== */

/* High contrast mode support */
@media (prefers-contrast: high) {
  .notification-item {
    border-width: 2px;
  }
  
  .notification-badge {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-slide-in-right,
  .animate-slide-out-right,
  .animate-slide-in-down,
  .animate-badge-pulse,
  .animate-badge-appear,
  .notification-loading-spin {
    animation: none;
  }
  
  /* Provide instant feedback without animation */
  .notification-item {
    transition: none;
  }
  
  .notification-toast {
    transition: opacity 0.1s ease;
  }
}

/* Focus indicators for keyboard navigation */
.notification-item:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.notification-center-button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */

/* Dark mode adjustments are handled by CSS variables in the design system */
/* Additional dark mode specific styles if needed */
@media (prefers-color-scheme: dark) {
  .notification-toast {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }
  
  .notification-center-dropdown {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

/* Hide notifications when printing */
@media print {
  .notification-toast-container,
  .notification-center-dropdown,
  .notification-badge {
    display: none !important;
  }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

/* Notification priority indicators */
.notification-priority-critical {
  border-left: 4px solid var(--color-error);
}

.notification-priority-high {
  border-left: 4px solid var(--color-warning);
}

.notification-priority-normal {
  border-left: 4px solid var(--color-secondary);
}

.notification-priority-low {
  border-left: 4px solid var(--color-muted);
}

/* Notification category colors */
.notification-category-project {
  --notification-accent: var(--color-primary);
}

.notification-category-template {
  --notification-accent: var(--color-secondary);
}

.notification-category-export {
  --notification-accent: var(--color-success);
}

.notification-category-upload {
  --notification-accent: var(--color-info);
}

.notification-category-generation {
  --notification-accent: var(--color-warning);
}

.notification-category-font {
  --notification-accent: var(--color-muted);
}

.notification-category-auth {
  --notification-accent: var(--color-primary);
}

.notification-category-system {
  --notification-accent: var(--color-secondary);
}

/* Smooth transitions for all notification elements */
.notification-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effects */
.notification-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Loading state styles */
.notification-loading {
  position: relative;
  overflow: hidden;
}

.notification-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
