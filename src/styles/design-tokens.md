# RapidDoc AI Design Tokens

## Color Palette

### Primary Colors
```css
--primary: #2563EB           /* Modern blue */
--primary-foreground: #FFFFFF
--primary-light: #DBEAFE     /* Light blue for backgrounds */
```

### Secondary Colors
```css
--secondary: #64748B         /* Slate gray */
--secondary-foreground: #FFFFFF
```

### Accent Colors
```css
--accent: #8B5CF6           /* Purple accent */
--accent-foreground: #FFFFFF
--accent-light: #F3E8FF     /* Light purple */
```

### Background Colors
```css
--background: #FEFEFE       /* Pure white background */
--surface: #FFFFFF          /* White surface */
--surface-secondary: #F8FAFC /* Very light gray */
--surface-hover: #F1F5F9    /* Hover states */
```

### Text Colors
```css
--text-primary: #0F172A     /* Darker for better readability */
--text-secondary: #64748B   /* Softer secondary text */
--text-muted: #94A3B8       /* Even lighter for less important text */
```

### Status Colors
```css
--success: #10B981
--success-light: #D1FAE5
--warning: #F59E0B
--warning-light: #FEF3C7
--error: #EF4444
--error-light: #FEE2E2
```

### Border Colors
```css
--border: #F1F5F9           /* Subtler borders */
--border-strong: #E2E8F0    /* Stronger borders when needed */
```

### Gradient Colors
```css
--gradient-start: #667EEA
--gradient-end: #764BA2
```

## Typography

### Font Families
```css
--font-sans: 'Nunito Sans', 'Noto Color Emoji', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'
--font-heading: 'Nunito Sans', 'Noto Color Emoji', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'
--font-body: 'Nunito Sans', 'Noto Color Emoji', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'
--font-mono: 'JetBrains Mono', monospace
```

### Font Sizes
```css
--text-xs: 0.75rem    /* 12px */
--text-sm: 0.875rem   /* 14px */
--text-base: 1rem     /* 16px */
--text-lg: 1.125rem   /* 18px */
--text-xl: 1.25rem    /* 20px */
--text-2xl: 1.5rem    /* 24px */
--text-3xl: 1.875rem  /* 30px */
--text-4xl: 2.25rem   /* 36px */
```

### Font Weights
```css
--font-light: 300
--font-normal: 400
--font-medium: 500
--font-semibold: 600
--font-bold: 700
--font-extrabold: 800
```

## Shadows

### Modern Shadow System
```css
--shadow-soft: 0 1px 3px rgba(0, 0, 0, 0.05)
--shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03)
--shadow-elevated: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.03)
--shadow-hero: 0 25px 50px -12px rgba(0, 0, 0, 0.15)
--shadow-inner-soft: inset 0 1px 2px rgba(0, 0, 0, 0.05)
```

## Border Radius

### Modern Rounded Corners
```css
--radius-sm: 0.375rem    /* 6px */
--radius-default: 0.5rem /* 8px */
--radius-md: 0.75rem     /* 12px */
--radius-lg: 1rem        /* 16px */
--radius-xl: 1.5rem      /* 24px */
--radius-2xl: 2rem       /* 32px */
--radius-full: 9999px    /* Fully rounded */
```

## Spacing Scale

### Consistent Spacing System
```css
--space-1: 0.25rem   /* 4px */
--space-2: 0.5rem    /* 8px */
--space-3: 0.75rem   /* 12px */
--space-4: 1rem      /* 16px */
--space-5: 1.25rem   /* 20px */
--space-6: 1.5rem    /* 24px */
--space-8: 2rem      /* 32px */
--space-10: 2.5rem   /* 40px */
--space-12: 3rem     /* 48px */
--space-16: 4rem     /* 64px */
--space-20: 5rem     /* 80px */
--space-24: 6rem     /* 96px */
```

## Animation & Transitions

### Timing Functions
```css
--transition-fast: 150ms ease-in-out
--transition-normal: 300ms ease-in-out
--transition-slow: 500ms ease-in-out
```

### Transform Effects
```css
--transform-hover: translateY(-2px)
--transform-active: translateY(0)
--scale-hover: scale(1.05)
--scale-active: scale(0.98)
```

## Usage Guidelines

### Color Usage
- Use `--primary` for main CTAs and important actions
- Use `--accent` for highlights and secondary actions
- Use `--surface` for card backgrounds
- Use `--text-primary` for main content, `--text-secondary` for supporting text

### Typography Usage
- Use `--font-heading` for headings and important text
- Use `--font-body` for regular content
- Maintain consistent line heights for readability

### Shadow Usage
- Use `--shadow-soft` for subtle elevation
- Use `--shadow-card` for standard cards
- Use `--shadow-elevated` for important elements
- Use `--shadow-hero` for hero sections and modals

### Animation Usage
- Use `--transition-normal` for most hover effects
- Use `--transition-fast` for quick feedback
- Always include transform effects for modern feel
