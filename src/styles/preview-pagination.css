/* Preview Pagination Styles */
/* Ensures proper page breaks and content flow in document previews */

/* Page container styles */
.preview-page {
  /* Fixed page dimensions matching standard letter size */
  width: 8.5in;
  height: 11in;
  
  /* Prevent content overflow */
  overflow: hidden;
  
  /* Page styling */
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  
  /* Ensure proper box model */
  box-sizing: border-box;
}

/* Page content container */
.preview-page-content {
  height: 100%;
  padding: 32px;
  overflow: hidden;
  
  /* Typography settings that match final output */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #374151;
}

/* Content area within page */
.preview-content {
  height: 100%;
  overflow: hidden;
  
  /* Ensure content doesn't break page boundaries */
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Page break utilities */
.page-break-before {
  page-break-before: always;
  break-before: page;
}

.page-break-after {
  page-break-after: always;
  break-after: page;
}

.page-break-avoid {
  page-break-inside: avoid;
  break-inside: avoid;
}

/* Content-specific page break rules */
.preview-content h1,
.preview-content h2,
.preview-content h3 {
  /* Prevent headings from appearing at bottom of page */
  page-break-after: avoid;
  break-after: avoid;
  page-break-inside: avoid;
  break-inside: avoid;
}

.preview-content p {
  /* Prevent orphaned lines */
  orphans: 2;
  widows: 2;
}

.preview-content img {
  /* Keep images together */
  page-break-inside: avoid;
  break-inside: avoid;
  max-width: 100%;
  max-height: calc(100% - 2rem);
  object-fit: contain;
}

.preview-content table {
  /* Keep tables together when possible */
  page-break-inside: avoid;
  break-inside: avoid;
  width: 100%;
  table-layout: fixed;
}

.preview-content blockquote {
  /* Keep blockquotes together */
  page-break-inside: avoid;
  break-inside: avoid;
}

.preview-content pre,
.preview-content code {
  /* Keep code blocks together */
  page-break-inside: avoid;
  break-inside: avoid;
}

.preview-content ul,
.preview-content ol {
  /* Prevent list breaks when possible */
  page-break-inside: avoid;
  break-inside: avoid;
}

/* Responsive adjustments for different screen sizes */
@media (max-width: 768px) {
  .preview-page {
    max-width: calc(100vw - 2rem);
    transform-origin: center top;
  }
  
  .preview-page-content {
    padding: 16px;
    font-size: 14px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .preview-page-content {
    padding: 24px;
    font-size: 15px;
  }
}

/* Print media styles for accurate preview-to-print matching */
@media print {
  .preview-page {
    width: 8.5in;
    height: 11in;
    margin: 0;
    box-shadow: none;
    border: none;
    page-break-after: always;
  }
  
  .preview-page-content {
    padding: 0.5in;
    font-size: 12pt;
    line-height: 1.5;
  }
  
  /* Hide UI elements in print */
  .preview-page .absolute {
    display: none !important;
  }
}

/* Continuous view adjustments - REMOVED: Container now handles its own sizing */
/* .continuous-view .preview-page-container rules removed - spacing now handled by component */

/* Zoom level adjustments - REMOVED: Conflicted with dynamic container sizing */
/* .preview-page[style*="scale"] margin rule removed - container now properly sized */

/* Template page specific styles */
.template-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.template-content img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Document content specific styles */
.document-content {
  height: 100%;
  overflow: hidden;
}

/* Ensure proper text flow */
.document-content * {
  max-width: 100%;
}

/* Handle long URLs and text */
.document-content a {
  word-break: break-all;
}

/* ENHANCED List styling within pages */
.document-content ul,
.document-content ol {
  list-style: initial !important;
  margin: 1rem 0 !important;
  padding-left: 1.5rem !important;
}

.document-content ul {
  list-style-type: disc !important;
}

.document-content ol {
  list-style-type: decimal !important;
}

.document-content li {
  display: list-item !important;
  margin-bottom: 0.25rem !important;
  line-height: 1.6 !important;
}

/* Nested list styling */
.document-content ul ul {
  list-style-type: circle !important;
  margin: 0.5rem 0 !important;
}

.document-content ul ul ul {
  list-style-type: square !important;
}

.document-content ol ol {
  list-style-type: lower-alpha !important;
  margin: 0.5rem 0 !important;
}

.document-content ol ol ol {
  list-style-type: lower-roman !important;
}

/* Table responsive handling */
.document-content table {
  font-size: 0.9em;
  border-collapse: collapse;
}

.document-content th,
.document-content td {
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  word-wrap: break-word;
}

/* Code block handling */
.document-content pre {
  font-size: 0.875rem;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Blockquote styling */
.document-content blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
}

/* Image captions and credits */
.document-content .image-caption {
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
  margin-top: 0.5rem;
}

/* Ensure proper spacing between elements */
.document-content > * + * {
  margin-top: 1rem;
}

.document-content h1 + *,
.document-content h2 + *,
.document-content h3 + * {
  margin-top: 0.5rem;
}
