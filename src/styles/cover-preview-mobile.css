/* Cover Preview Mobile Optimizations */

/* Base cover preview interface - prevent horizontal overflow */
.cover-preview-interface {
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Cover preview header - mobile responsive */
.cover-preview-header {
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Base cover preview container */
.cover-preview-container {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  flex: 1;
  min-height: 0;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Cover preview page container - prevent overflow */
.cover-preview-page {
  /* Ensure containers don't exceed viewport on mobile */
  max-width: 100vw;
  overflow: visible !important; /* CRITICAL: Prevent internal scrollbars */
  /* Ensure proper centering on mobile */
  margin-left: auto;
  margin-right: auto;
}

/* Scaled cover page positioning */
.cover-page-scaled {
  /* Ensure the scaled page doesn't create overflow */
  overflow: hidden !important;
}

/* Small mobile phones (up to 480px) */
@media (max-width: 480px) {
  .cover-preview-interface {
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  .cover-preview-header {
    padding: 8px !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  .cover-preview-container {
    padding: 8px !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  .cover-preview-page {
    max-width: calc(100vw - 16px);
  }

  /* Ensure text remains readable on small screens */
  .cover-preview-wrapper h3 {
    font-size: 16px !important;
  }

  .cover-preview-wrapper p {
    font-size: 12px !important;
  }

  /* Header specific mobile fixes */
  .cover-preview-header .flex {
    flex-wrap: wrap !important;
    gap: 8px !important;
  }

  .cover-preview-header select {
    max-width: calc(100vw - 120px) !important;
    min-width: 0 !important;
  }

  .cover-preview-header button {
    white-space: nowrap !important;
  }
}

/* Large mobile phones (481px to 640px) */
@media (min-width: 481px) and (max-width: 640px) {
  .cover-preview-interface {
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  .cover-preview-header {
    padding: 12px !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  .cover-preview-container {
    padding: 12px !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  .cover-preview-page {
    max-width: calc(100vw - 24px);
  }

  .cover-preview-wrapper h3 {
    font-size: 18px !important;
  }

  .cover-preview-wrapper p {
    font-size: 13px !important;
  }

  /* Header specific mobile fixes */
  .cover-preview-header select {
    max-width: calc(100vw - 140px) !important;
  }
}

/* Tablets in portrait (641px to 768px) */
@media (min-width: 641px) and (max-width: 768px) {
  .cover-preview-container {
    padding: 16px !important;
  }

  .cover-preview-page {
    max-width: calc(100vw - 32px);
  }
}

/* All mobile devices - common fixes */
@media (max-width: 768px) {
  /* CRITICAL: Prevent horizontal overflow at all levels */
  .cover-preview-interface {
    max-width: 100vw !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  .cover-preview-header {
    max-width: 100vw !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  /* Ensure proper touch scrolling ONLY on main container */
  .cover-preview-container {
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden !important;
    overflow-y: auto;
    max-width: 100vw !important;
    box-sizing: border-box !important;
  }

  /* CRITICAL: Prevent dual scrollbars */
  .cover-preview-page {
    overflow: visible !important; /* No internal scrolling */
    max-width: 100vw !important;
  }

  /* Ensure cover content doesn't create overflow */
  .cover-page-scaled {
    overflow: hidden !important; /* Content stays within page bounds */
  }

  /* Ensure content is visible and properly positioned */
  .cover-content {
    overflow: hidden !important; /* Prevent content overflow */
  }

  /* Header layout fixes for mobile */
  .cover-preview-header .flex {
    flex-wrap: wrap !important;
  }

  .cover-preview-header select {
    min-width: 0 !important;
    flex-shrink: 1 !important;
    max-width: 100% !important;
  }

  .cover-preview-header button {
    flex-shrink: 0 !important;
    white-space: nowrap !important;
  }

  /* Ensure all text elements wrap properly */
  .cover-preview-header span {
    word-break: break-word !important;
    overflow-wrap: break-word !important;
  }

  /* Touch and scroll optimizations */
  .cover-preview-interface {
    /* Improve touch scrolling performance */
    -webkit-overflow-scrolling: touch;
    /* Prevent zoom on double tap */
    touch-action: pan-y;
  }

  /* Prevent text selection issues on touch */
  .cover-preview-page {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    /* Prevent touch callouts */
    -webkit-touch-callout: none;
    /* Prevent tap highlight */
    -webkit-tap-highlight-color: transparent;
  }

  /* Ensure smooth scrolling */
  .cover-preview-container {
    scroll-behavior: smooth;
  }

  /* Fix for iOS Safari viewport issues */
  @supports (-webkit-touch-callout: none) {
    .cover-preview-page {
      /* Account for iOS Safari's dynamic viewport */
      max-width: calc(100vw - 16px) !important;
    }
  }
}

/* Landscape orientation optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .cover-preview-container {
    padding: 8px !important;
  }

  /* In landscape, prioritize width over height */
  .cover-preview-wrapper {
    max-height: calc(100vh - 120px); /* Account for header */
    overflow-y: auto;
  }

  /* Reduce info section spacing in landscape */
  .cover-preview-wrapper .mt-4 {
    margin-top: 8px !important;
  }

  .cover-preview-wrapper .mt-6 {
    margin-top: 12px !important;
  }
}

/* High DPI displays - ensure crisp rendering */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .cover-content img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode support for cover preview */
@media (prefers-color-scheme: dark) {
  .cover-preview-page {
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.1) !important;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .cover-preview-container {
    scroll-behavior: auto;
  }

  .cover-page-scaled {
    transition: none !important;
  }
}

/* Print styles - hide mobile-specific elements */
@media print {
  .cover-preview-container {
    padding: 0 !important;
    overflow: visible !important;
  }

  .cover-preview-page {
    box-shadow: none !important;
    max-width: none !important;
  }

  .cover-page-scaled {
    transform: none !important;
    position: static !important;
    margin: 0 !important;
  }

  /* Hide mobile debug info */
  .cover-preview-wrapper .text-xs {
    display: none !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: no-preference) {
  .cover-page-scaled {
    transition: transform 0.2s ease-in-out;
  }
}

/* Focus states for keyboard navigation */
.cover-preview-page:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Ensure proper contrast for text overlays */
.cover-content {
  /* Ensure text remains readable */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
