/* Landing Page Integration Styles */

/* Plan selection banner for auth page */
.plan-selection-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 20px;
  text-align: center;
  border-radius: 8px;
  margin-bottom: 20px;
}

.plan-selection-banner p {
  margin: 0;
  font-size: 14px;
}

.plan-selection-banner small {
  opacity: 0.8;
  font-size: 12px;
}

/* Auto-checkout loading overlay */
.auto-checkout-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.auto-checkout-loading h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.5rem;
  font-weight: 600;
}

.auto-checkout-loading p {
  color: #666;
  font-size: 14px;
  margin-bottom: 1rem;
}

/* Landing page button enhancements */
.btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .auto-checkout-loading {
    background: rgba(0, 0, 0, 0.95);
  }
  
  .auto-checkout-loading h3 {
    color: #fff;
  }
  
  .auto-checkout-loading p {
    color: #ccc;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .plan-selection-banner {
    padding: 10px 16px;
    margin-bottom: 16px;
  }
  
  .plan-selection-banner p {
    font-size: 13px;
  }
  
  .plan-selection-banner small {
    font-size: 11px;
  }
  
  .auto-checkout-loading h3 {
    font-size: 1.25rem;
  }
  
  .auto-checkout-loading p {
    font-size: 13px;
    padding: 0 20px;
    text-align: center;
  }
}
