/* Template Selection Mobile Optimizations */

/* Force smaller card dimensions with !important to override Tailwind */
.template-card-grid {
  max-width: 140px !important;
  width: 100% !important;
}

@media (min-width: 640px) {
  .template-card-grid {
    max-width: 160px !important;
  }
}

@media (min-width: 768px) {
  .template-card-grid {
    max-width: 180px !important;
  }
}

/* Force smaller preview heights */
.template-card-grid .aspect-\[4\/5\] {
  height: 96px !important; /* h-24 */
}

@media (min-width: 640px) {
  .template-card-grid .aspect-\[4\/5\] {
    height: 112px !important; /* h-28 */
  }
}

@media (min-width: 768px) {
  .template-card-grid .aspect-\[4\/5\] {
    height: 128px !important; /* h-32 */
  }
}

/* Enhanced touch interactions for template cards */
@media (hover: none) and (pointer: coarse) {
  .template-card-grid:hover {
    transform: none;
    box-shadow: none;
  }

  .template-card-grid:active {
    transform: scale(0.98);
    transition: transform 150ms ease-out;
  }

  .template-card-list:hover {
    transform: none;
    box-shadow: none;
  }

  .template-card-list:active {
    transform: scale(0.99);
    transition: transform 150ms ease-out;
  }
}

/* Force smaller grid gaps */
.template-grid {
  gap: 8px !important; /* Override gap-2 sm:gap-3 */
  padding: 0 8px !important;
}

@media (min-width: 640px) {
  .template-grid {
    gap: 12px !important;
  }
}

/* Mobile-specific template grid optimizations */
@media (max-width: 640px) {
  .template-grid {
    padding: 0 4px !important; /* Add slight padding to prevent edge clipping */
  }

  /* Ensure minimum touch target size but keep cards small */
  .template-card-grid {
    min-height: 140px !important; /* Much smaller than before */
    max-height: 160px !important;
  }

  .template-card-list {
    min-height: 60px !important; /* Smaller list items */
    max-height: 80px !important;
  }

  /* Optimize template preview aspect ratio for mobile */
  .template-card-grid .aspect-\[3\/4\] {
    aspect-ratio: 3/4; /* Maintain aspect ratio but allow flexibility */
    min-height: 140px; /* Prevent cards from being too small */
    max-height: 180px; /* Prevent cards from being too large */
  }

  /* Enhanced selection indicators for touch */
  .template-card-grid .absolute.top-2.right-2 {
    top: 8px;
    right: 8px;
    min-width: 28px;
    min-height: 28px;
  }

  /* Improved text readability on mobile */
  .template-card-grid h4,
  .template-card-list h4 {
    line-height: 1.3;
    font-weight: 500;
  }

  /* Better tag spacing on mobile */
  .template-card-grid .flex.flex-wrap.gap-1,
  .template-card-list .flex.flex-wrap.gap-1 {
    gap: 4px;
    margin-top: 6px;
  }

  /* Optimize premium badge positioning */
  .template-card-grid .absolute.top-2.left-2 {
    top: 8px;
    left: 8px;
  }

  .template-card-grid .absolute.top-2.left-2 span {
    font-size: 10px;
    padding: 3px 6px;
    font-weight: 600;
  }
}

/* Tablet-specific optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  .template-grid {
    gap: 16px; /* Balanced spacing for tablets */
  }

  .template-card-grid {
    min-height: 240px;
  }

  /* Slightly larger touch targets for tablets */
  .template-card-grid .absolute.top-2.right-2 {
    width: 26px;
    height: 26px;
  }
}

/* Improved focus states for keyboard navigation */
.template-card-grid:focus,
.template-card-list:focus {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Loading state optimizations for mobile */
@media (max-width: 640px) {
  .template-grid.loading {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .template-grid.loading .skeleton-card {
    height: 200px;
    border-radius: 8px;
  }
}

/* Enhanced visual feedback for selection */
.template-card-grid.selected,
.template-card-list.selected {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* Smooth transitions for all interactions */
.template-card-grid,
.template-card-list {
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prevent text selection on template cards for better touch experience */
.template-card-grid,
.template-card-list {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Ensure proper spacing in template info sections */
@media (max-width: 640px) {
  .template-card-grid .p-3,
  .template-card-list .ml-3 {
    padding: 12px;
  }

  .template-card-list .ml-3 {
    margin-left: 12px;
    padding: 0;
  }
}

/* Optimize for very small screens (iPhone SE, etc.) */
@media (max-width: 375px) {
  .template-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 0 8px;
  }

  .template-card-grid {
    min-height: 180px;
  }

  .template-card-grid .aspect-\[3\/4\] {
    min-height: 120px;
    max-height: 140px;
  }

  .template-card-grid .p-3 {
    padding: 8px;
  }

  .template-card-grid h4 {
    font-size: 13px;
  }

  .template-card-grid p {
    font-size: 11px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .template-card-grid,
  .template-card-list {
    border-width: 3px;
  }

  .template-card-grid.selected,
  .template-card-list.selected {
    border-color: #1E40AF;
    background-color: #EFF6FF;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .template-card-grid,
  .template-card-list {
    transition: none;
  }

  .template-card-grid:active,
  .template-card-list:active {
    transform: none;
  }
}
