/* Document Preview Mobile Optimizations */

/* Fix scrolling issues in continuous view */
.preview-content-container {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  flex: 1;
  min-height: 0;
}

/* Optimize continuous view spacing */
.continuous-view {
  padding: 16px;
  max-width: 100%;
  overflow-x: hidden;
}

@media (min-width: 640px) {
  .continuous-view {
    padding: 24px;
  }
}

@media (min-width: 768px) {
  .continuous-view {
    padding: 32px;
  }
}

/* Mobile-specific container fixes for proper content display */
.preview-page-container {
  /* Ensure containers don't exceed viewport on mobile */
  max-width: 100vw;
  overflow: visible !important; /* CRITICAL: Prevent internal scrollbars */
  /* Ensure proper centering on mobile */
  margin-left: auto;
  margin-right: auto;
}

/* Small mobile phones (up to 480px) */
@media (max-width: 480px) {
  .preview-page {
    max-width: calc(100vw - 16px);
    /* Remove conflicting positioning - let container handle centering */
  }

  .continuous-view {
    padding: 8px !important;
  }
}

/* Large mobile phones (481px to 640px) */
@media (min-width: 481px) and (max-width: 640px) {
  .preview-page {
    max-width: calc(100vw - 24px);
    /* Remove conflicting positioning - let container handle centering */
  }

  .continuous-view {
    padding: 12px !important;
  }
}

/* Tablets in portrait (641px to 767px) */
@media (min-width: 641px) and (max-width: 767px) {
  .preview-page {
    max-width: calc(100vw - 32px);
    /* Remove conflicting positioning - let container handle centering */
  }

  .continuous-view {
    padding: 16px !important;
  }
}

/* All mobile devices - common fixes */
@media (max-width: 767px) {
  /* Ensure proper touch scrolling ONLY on main container */
  .continuous-view {
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
    overflow-y: auto;
  }

  /* CRITICAL: Prevent dual scrollbars */
  .preview-page-container {
    overflow: visible !important; /* No internal scrolling */
    max-width: 100vw !important;
  }

  /* Ensure page content doesn't create overflow */
  .preview-page {
    overflow: hidden !important; /* Content stays within page bounds */
  }

  /* Ensure content is visible and properly positioned */
  .preview-page-content {
    overflow: hidden !important; /* Prevent content overflow */
  }

  /* Touch and scroll optimizations */
  .document-preview-interface {
    /* Improve touch scrolling performance */
    -webkit-overflow-scrolling: touch;
    /* Prevent zoom on double tap */
    touch-action: pan-y;
  }

  /* Prevent text selection issues on touch */
  .preview-page {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    /* Prevent touch callouts */
    -webkit-touch-callout: none;
    /* Prevent tap highlight */
    -webkit-tap-highlight-color: transparent;
  }

  /* Ensure smooth scrolling */
  .continuous-view {
    scroll-behavior: smooth;
  }

  /* Fix for iOS Safari viewport issues */
  @supports (-webkit-touch-callout: none) {
    .preview-page-container {
      /* Account for iOS Safari's dynamic viewport */
      max-width: calc(100vw - 16px) !important;
    }
  }
}

/* Mobile-responsive page sizing */
.preview-page {
  max-width: calc(100vw - 32px);
  margin: 0 auto;
}

@media (max-width: 640px) {
  .preview-page {
    max-width: calc(100vw - 24px);
  }
}

/* Responsive page content padding */
.preview-page-content {
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
}

@media (min-width: 640px) {
  .preview-page-content {
    padding: 24px;
    font-size: 15px;
  }
}

@media (min-width: 768px) {
  .preview-page-content {
    padding: 32px;
    font-size: 16px;
  }
}
