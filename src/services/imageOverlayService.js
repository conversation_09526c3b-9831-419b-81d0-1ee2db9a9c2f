/**
 * Image Overlay Template Service for DocForge AI
 * Renders text overlays on background images for cover template generation
 *
 * Features:
 * - Canvas-based text rendering with precise positioning
 * - Dynamic text styling (font, size, color, alignment)
 * - Text overflow handling (ellipsis, word wrapping)
 * - High-quality image export for PDF generation
 * - Real-time preview generation
 * - Dynamic font loading for custom fonts
 */

import errorMonitor, { ErrorSeverity } from "../utils/errorMonitor.js";
import fontLoader from "../utils/fontLoader.js";
import {
  templateHasLogoOverlays,
  getLogoOverlays,
  renderLogoOverlay,
  applyLogoCustomizations,
  getLogoDataForTemplate
} from "./logoOverlayService.js";

import { prodLogger } from '../utils/prodLogger.js';
/**
 * Image Overlay Service Class
 * Handles all image overlay template rendering operations
 */
class ImageOverlayService {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.imageCache = new Map();
  }

  /**
   * Initialize canvas context
   * @param {number} width - Canvas width
   * @param {number} height - Canvas height
   * @returns {Object} Canvas and context
   */
  initializeCanvas(width, height) {
    try {
      // Create canvas element
      this.canvas = document.createElement("canvas");
      this.canvas.width = width;
      this.canvas.height = height;

      // Get 2D context with high quality settings
      this.ctx = this.canvas.getContext("2d", {
        alpha: true,
        desynchronized: false,
      });

      if (!this.ctx) {
        throw new Error("Failed to get 2D context from canvas");
      }

      // Enable high-quality text rendering
      this.ctx.textBaseline = "top";
      this.ctx.imageSmoothingEnabled = true;

      // Set image smoothing quality if supported
      if ("imageSmoothingQuality" in this.ctx) {
        this.ctx.imageSmoothingQuality = "high";
      }

      prodLogger.debug(`✅ Canvas initialized: ${width}x${height}`);
      return { canvas: this.canvas, ctx: this.ctx };
    } catch (error) {
      prodLogger.error("Failed to initialize canvas:", error);
      throw new Error(`Canvas initialization failed: ${error.message}`);
    }
  }

  /**
   * Load and cache background image
   * @param {string} imageUrl - URL of the background image
   * @returns {Promise<HTMLImageElement>} Loaded image element
   */
  async loadBackgroundImage(imageUrl) {
    try {
      // Check cache first
      if (this.imageCache.has(imageUrl)) {
        return this.imageCache.get(imageUrl);
      }

      return new Promise((resolve, reject) => {
        const img = new Image();

        // Only set crossOrigin for external URLs, not for placeholder URLs
        if (imageUrl.includes("supabase") || imageUrl.includes("storage")) {
          img.crossOrigin = "anonymous";
        }

        img.onload = () => {
          // Cache the loaded image
          this.imageCache.set(imageUrl, img);
          prodLogger.debug(`✅ Image loaded successfully: ${imageUrl}`);
          resolve(img);
        };

        img.onerror = (error) => {
          prodLogger.error(`❌ Failed to load image: ${imageUrl}`, error);

          // Create a fallback colored rectangle
          const fallbackCanvas = document.createElement("canvas");
          fallbackCanvas.width = 600;
          fallbackCanvas.height = 800;
          const fallbackCtx = fallbackCanvas.getContext("2d");

          // Create a gradient background as fallback
          const gradient = fallbackCtx.createLinearGradient(0, 0, 0, 800);
          gradient.addColorStop(0, "#3498db");
          gradient.addColorStop(1, "#2c3e50");
          fallbackCtx.fillStyle = gradient;
          fallbackCtx.fillRect(0, 0, 600, 800);

          // Convert canvas to image
          const fallbackImg = new Image();
          fallbackImg.onload = () => {
            this.imageCache.set(imageUrl, fallbackImg);
            prodLogger.debug(`✅ Using fallback image for: ${imageUrl}`);
            resolve(fallbackImg);
          };
          fallbackImg.src = fallbackCanvas.toDataURL();
        };

        img.src = imageUrl;
      });
    } catch (error) {
      prodLogger.error("Error loading background image:", error);
      throw error;
    }
  }

  /**
   * Apply text styling to canvas context with font loading
   * @param {Object} styling - Text styling configuration
   * @returns {Promise<void>} Promise that resolves when styling is applied
   */
  async applyTextStyling(styling) {
    const {
      fontSize = 16,
      fontFamily = "Arial",
      fontWeight = "normal",
      fontStyle = "normal",
      color = "#000000",
      textAlign = "left",
      letterSpacing = 0,
    } = styling;

    // Ensure font is loaded before applying
    const loadedFontFamily = await fontLoader.ensureFontLoaded(fontFamily, {
      fallback: "Arial",
      loadGoogleFonts: true,
    });

    // Get font with proper fallbacks
    const fontWithFallbacks = fontLoader.getFontWithFallbacks(loadedFontFamily);

    // Set font with style and weight
    this.ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontWithFallbacks}`;

    // Set color
    this.ctx.fillStyle = color;

    // Set text alignment
    this.ctx.textAlign = textAlign;

    // Set letter spacing (if supported)
    if ("letterSpacing" in this.ctx) {
      this.ctx.letterSpacing = `${letterSpacing}px`;
    }
  }

  /**
   * Measure text dimensions with word wrapping
   * @param {string} text - Text to measure
   * @param {number} maxWidth - Maximum width for wrapping
   * @param {Object} styling - Text styling
   * @returns {Object} Text metrics and wrapped lines
   */
  measureText(text, maxWidth, styling) {
    this.applyTextStyling(styling);

    const words = text.split(" ");
    const lines = [];
    let currentLine = "";

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const metrics = this.ctx.measureText(testLine);

      if (metrics.width > maxWidth && currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine = testLine;
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    const lineHeight = styling.lineHeight || 1.2;
    const totalHeight = lines.length * styling.fontSize * lineHeight;

    return {
      lines,
      totalHeight,
      lineHeight: styling.fontSize * lineHeight,
      maxWidth: Math.max(
        ...lines.map((line) => this.ctx.measureText(line).width)
      ),
    };
  }

  /**
   * Render text overlay with positioning and styling
   * @param {string} text - Text content to render
   * @param {Object} position - Position configuration
   * @param {Object} styling - Text styling configuration
   * @returns {Promise<void>} Promise that resolves when rendering is complete
   */
  async renderTextOverlay(text, position, styling) {
    try {
      const { x, y, width, height } = position;
      const { textTransform = "none" } = styling;

      // Apply text transformation
      const transformedText = this.applyTextTransform(text, textTransform);

      // Apply styling (now async for font loading)
      await this.applyTextStyling(styling);

      // Measure text and handle wrapping (using transformed text)
      const textMetrics = this.measureText(transformedText, width, styling);
      let { lines } = textMetrics;

      // No max lines constraint - allow unlimited text lines

      // Calculate vertical positioning
      const lineHeight = textMetrics.lineHeight;
      const totalTextHeight = lines.length * lineHeight;
      let startY = y;

      // Vertical alignment within the bounds
      if (styling.verticalAlign === "center") {
        startY = y + (height - totalTextHeight) / 2;
      } else if (styling.verticalAlign === "bottom") {
        startY = y + height - totalTextHeight;
      }

      // Render each line
      lines.forEach((line, index) => {
        const lineY = startY + index * lineHeight;

        // Calculate horizontal position based on alignment
        let lineX = x;
        if (styling.textAlign === "center") {
          lineX = x + width / 2;
        } else if (styling.textAlign === "right") {
          lineX = x + width;
        }

        // Render the text
        this.ctx.fillText(line, lineX, lineY);

        // Handle text decorations
        if (styling.textDecoration && styling.textDecoration !== "none") {
          const textWidth = this.ctx.measureText(line).width;
          let decorationX = lineX;

          // Adjust decoration position based on text alignment
          if (styling.textAlign === "center") {
            decorationX = lineX - textWidth / 2;
          } else if (styling.textAlign === "right") {
            decorationX = lineX - textWidth;
          }

          // Draw underline
          if (styling.textDecoration.includes("underline")) {
            const underlineY = lineY + styling.fontSize * 0.1;
            this.ctx.beginPath();
            this.ctx.moveTo(decorationX, underlineY);
            this.ctx.lineTo(decorationX + textWidth, underlineY);
            this.ctx.strokeStyle = styling.color;
            this.ctx.lineWidth = Math.max(1, styling.fontSize * 0.05);
            this.ctx.stroke();
          }

          // Draw strikethrough
          if (styling.textDecoration.includes("line-through")) {
            const strikeY = lineY - styling.fontSize * 0.3;
            this.ctx.beginPath();
            this.ctx.moveTo(decorationX, strikeY);
            this.ctx.lineTo(decorationX + textWidth, strikeY);
            this.ctx.strokeStyle = styling.color;
            this.ctx.lineWidth = Math.max(1, styling.fontSize * 0.05);
            this.ctx.stroke();
          }
        }
      });
    } catch (error) {
      prodLogger.error("Error rendering text overlay:", error);
      throw error;
    }
  }

  /**
   * Apply text transformation based on textTransform property
   * @param {string} text - Text to transform
   * @param {string} textTransform - Transform type (none, uppercase, lowercase, capitalize, capitalize-first)
   * @returns {string} Transformed text
   */
  applyTextTransform(text, textTransform) {
    if (!text || !textTransform || textTransform === "none") {
      return text;
    }

    switch (textTransform) {
      case "uppercase":
        return text.toUpperCase();

      case "lowercase":
        return text.toLowerCase();

      case "capitalize":
        // Capitalize each word
        return text.replace(/\b\w/g, (char) => char.toUpperCase());

      case "capitalize-first":
        // Capitalize only the first letter of the entire text
        return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();

      default:
        return text;
    }
  }

  /**
   * Populate placeholder text with document data
   * @param {string} placeholder - Placeholder text with {{variables}}
   * @param {Object} documentData - Document metadata
   * @returns {string} Populated text
   */
  populatePlaceholder(placeholder, documentData) {
    if (!placeholder || typeof placeholder !== "string") {
      return "";
    }

    let populatedText = placeholder;

    // Replace common placeholders
    const replacements = {
      "{{title}}": documentData.title || "Untitled Document",
      "{{author}}": documentData.author || "Unknown Author",
      "{{description}}": documentData.description || "",
      "{{date}}": new Date().toLocaleDateString(),
      "{{year}}": new Date().getFullYear().toString(),
    };

    Object.entries(replacements).forEach(([placeholder, value]) => {
      populatedText = populatedText.replace(
        new RegExp(placeholder, "g"),
        value
      );
    });

    return populatedText;
  }

  /**
   * Render complete template with background image and text overlays
   * @param {Object} template - Template configuration
   * @param {Object} documentData - Document metadata
   * @param {Object} options - Rendering options
   * @returns {Promise<HTMLCanvasElement>} Rendered canvas
   */
  async renderTemplate(template, documentData, options = {}) {
    try {
      const {
        background_image_url,
        background_image_width,
        background_image_height,
        text_overlays,
      } = template;

      // Preload fonts for better performance
      await this.preloadTemplateFonts(template);

      // Initialize canvas
      this.initializeCanvas(background_image_width, background_image_height);

      // Load background image
      const backgroundImage = await this.loadBackgroundImage(
        background_image_url
      );

      // Draw background image
      this.ctx.drawImage(
        backgroundImage,
        0,
        0,
        background_image_width,
        background_image_height
      );

      // Render text overlays (now async for font loading)
      if (text_overlays && text_overlays.overlays) {
        for (const overlay of text_overlays.overlays) {
          const populatedText = this.populatePlaceholder(
            overlay.placeholder,
            documentData
          );

          if (populatedText.trim()) {
            await this.renderTextOverlay(
              populatedText,
              overlay.position,
              overlay.styling
            );
          }
        }
      }

      // Render logo overlays if template supports them
      if (templateHasLogoOverlays(template)) {
        const logoOverlays = getLogoOverlays(template);
        const logoData = await getLogoDataForTemplate(documentData.userId, template);

        for (const overlay of logoOverlays) {
          const logoForOverlay = logoData[overlay.id];
          if (logoForOverlay) {
            await renderLogoOverlay(
              this.ctx,
              logoForOverlay,
              overlay.position,
              overlay.styling
            );
          }
        }
      }

      return this.canvas;
    } catch (error) {
      prodLogger.error("Error rendering template:", error);
      errorMonitor.captureError(
        error,
        { template: template?.id },
        ErrorSeverity.MEDIUM
      );
      throw error;
    }
  }

  /**
   * Render template with customizations applied
   * @param {Object} template - Template configuration
   * @param {Object} documentData - Document metadata
   * @param {Object} customizations - User customizations for overlays
   * @param {Object} options - Rendering options
   * @returns {Promise<HTMLCanvasElement>} Rendered canvas
   */
  async renderTemplateWithCustomizations(
    template,
    documentData,
    customizations = {},
    options = {}
  ) {
    try {
      const {
        background_image_url,
        background_image_width,
        background_image_height,
        text_overlays,
      } = template;

      // Preload fonts for better performance (including customized fonts)
      await this.preloadTemplateFonts(template);
      await this.preloadCustomizationFonts(customizations);

      // Initialize canvas
      this.initializeCanvas(background_image_width, background_image_height);

      // Load background image
      const backgroundImage = await this.loadBackgroundImage(
        background_image_url
      );

      // Draw background image
      this.ctx.drawImage(
        backgroundImage,
        0,
        0,
        background_image_width,
        background_image_height
      );

      // Render text overlays with customizations (now async for font loading)
      if (text_overlays && text_overlays.overlays) {
        for (const overlay of text_overlays.overlays) {
          const populatedText = this.populatePlaceholder(
            overlay.placeholder,
            documentData
          );

          if (populatedText.trim()) {
            // Apply customizations to overlay
            const customizedOverlay = this.applyCustomizations(
              overlay,
              customizations[overlay.id] || {}
            );
            await this.renderTextOverlay(
              populatedText,
              customizedOverlay.position,
              customizedOverlay.styling
            );
          }
        }
      }

      // Render logo overlays with customizations if template supports them
      if (templateHasLogoOverlays(template)) {
        const logoOverlays = getLogoOverlays(template);
        const logoData = await getLogoDataForTemplate(documentData.userId, template, customizations);

        for (const overlay of logoOverlays) {
          const logoForOverlay = logoData[overlay.id];
          if (logoForOverlay) {
            // Apply customizations to logo overlay
            const customizedOverlay = applyLogoCustomizations(
              overlay,
              customizations[overlay.id] || {}
            );
            await renderLogoOverlay(
              this.ctx,
              logoForOverlay,
              customizedOverlay.position,
              customizedOverlay.styling
            );
          }
        }
      }

      return this.canvas;
    } catch (error) {
      prodLogger.error("Error rendering template with customizations:", error);
      errorMonitor.captureError(
        error,
        { template: template?.id, customizations },
        ErrorSeverity.MEDIUM
      );
      throw error;
    }
  }

  /**
   * Apply user customizations to an overlay configuration
   * @param {Object} originalOverlay - Original overlay configuration
   * @param {Object} customizations - User customizations
   * @returns {Object} Customized overlay configuration
   */
  applyCustomizations(originalOverlay, customizations) {
    return {
      ...originalOverlay,
      position: {
        ...originalOverlay.position,
        ...customizations.position,
      },
      styling: {
        ...originalOverlay.styling,
        ...customizations.styling,
      },
    };
  }

  /**
   * Preload fonts used in template for better performance
   * @param {Object} template - Template configuration
   * @returns {Promise<void>} Promise that resolves when all fonts are loaded
   */
  async preloadTemplateFonts(template) {
    try {
      if (!template?.text_overlays?.overlays) {
        return;
      }

      const fontFamilies = new Set();

      // Collect all unique font families from overlays
      template.text_overlays.overlays.forEach((overlay) => {
        if (overlay.styling?.fontFamily) {
          fontFamilies.add(overlay.styling.fontFamily);
        }
      });

      // Load all fonts in parallel
      const loadPromises = Array.from(fontFamilies).map((fontFamily) =>
        fontLoader
          .ensureFontLoaded(fontFamily, {
            fallback: "Arial",
            loadGoogleFonts: true,
          })
          .catch((error) => {
            prodLogger.warn(`Failed to preload font ${fontFamily}:`, error);
            // Don't fail the entire operation if one font fails
          })
      );

      await Promise.all(loadPromises);
      prodLogger.debug(
        `Preloaded ${fontFamilies.size} fonts for template ${template.id}`
      );
    } catch (error) {
      prodLogger.warn("Error preloading template fonts:", error);
      // Don't throw - this is an optimization, not critical
    }
  }

  /**
   * Preload fonts used in customizations for better performance
   * @param {Object} customizations - User customizations
   * @returns {Promise<void>} Promise that resolves when all fonts are loaded
   */
  async preloadCustomizationFonts(customizations) {
    try {
      if (!customizations || typeof customizations !== "object") {
        return;
      }

      const fontFamilies = new Set();

      // Collect all unique font families from customizations
      Object.values(customizations).forEach((customization) => {
        if (customization?.styling?.fontFamily) {
          fontFamilies.add(customization.styling.fontFamily);
        }
      });

      if (fontFamilies.size === 0) {
        return;
      }

      // Load all fonts in parallel
      const loadPromises = Array.from(fontFamilies).map((fontFamily) =>
        fontLoader
          .ensureFontLoaded(fontFamily, {
            fallback: "Arial",
            loadGoogleFonts: true,
          })
          .catch((error) => {
            prodLogger.warn(
              `Failed to preload customization font ${fontFamily}:`,
              error
            );
            // Don't fail the entire operation if one font fails
          })
      );

      await Promise.all(loadPromises);
      prodLogger.debug(`Preloaded ${fontFamilies.size} customization fonts`);
    } catch (error) {
      prodLogger.warn("Error preloading customization fonts:", error);
      // Don't throw - this is an optimization, not critical
    }
  }

  /**
   * Export rendered template as image data
   * @param {HTMLCanvasElement} canvas - Rendered canvas
   * @param {string} format - Export format ('png', 'jpeg', 'webp')
   * @param {number} quality - Export quality (0-1)
   * @returns {string} Base64 image data URL
   */
  exportAsImage(canvas = this.canvas, format = "png", quality = 0.9) {
    try {
      if (!canvas) {
        throw new Error("No canvas available for export");
      }

      const mimeType = `image/${format}`;
      return canvas.toDataURL(mimeType, quality);
    } catch (error) {
      prodLogger.error("Error exporting image:", error);
      throw error;
    }
  }

  /**
   * Generate preview with sample data
   * @param {Object} template - Template configuration
   * @param {Object} sampleData - Sample document data for preview
   * @returns {Promise<string>} Base64 image data URL
   */
  async generatePreview(template, sampleData = null) {
    try {
      const defaultSampleData = {
        title: "Sample Document Title",
        author: "John Doe",
        description:
          "This is a sample description to show how the template will look with your content.",
      };

      const previewData = sampleData || defaultSampleData;
      const canvas = await this.renderTemplate(template, previewData);

      return this.exportAsImage(canvas, "png", 0.8);
    } catch (error) {
      prodLogger.error("Error generating preview:", error);
      throw error;
    }
  }

  /**
   * Clear image cache
   */
  clearCache() {
    this.imageCache.clear();
  }
}

// Create singleton instance
const imageOverlayService = new ImageOverlayService();

export default imageOverlayService;

// Export individual methods for convenience
export const { renderTemplate, generatePreview, exportAsImage, clearCache } =
  imageOverlayService;
