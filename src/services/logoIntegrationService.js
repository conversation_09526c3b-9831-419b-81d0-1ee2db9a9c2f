/**
 * Logo Integration Service
 * Handles logo integration into document generation workflow
 * Manages logo data preparation and document type-specific settings
 */

import { supabase } from "../lib/supabase.js";
import { getUserLogos, incrementLogoUsage } from "./logoService.js";
import { prodLogger } from "../utils/prodLogger.js";

// Document type-specific logo settings
const DOCUMENT_TYPE_LOGO_SETTINGS = {
  business: {
    position: "top-right",
    size: "medium",
    opacity: 1.0,
    enabled: true,
    subtypes: {
      "proposal": { position: "top-right", size: "medium" },
      "report": { position: "top-right", size: "medium" },
      "business-plan": { position: "top-center", size: "large" },
      "executive-summary": { position: "top-right", size: "small" },
      "memo": { position: "top-left", size: "small" },
      "white-paper": { position: "top-center", size: "medium" }
    }
  },
  academic: {
    position: "top-center",
    size: "small",
    opacity: 0.8,
    enabled: true,
    subtypes: {
      "research-paper": { position: "top-center", size: "small" },
      "thesis": { position: "top-center", size: "medium" },
      "dissertation": { position: "top-center", size: "medium" },
      "literature-review": { position: "top-center", size: "small" },
      "case-study": { position: "top-right", size: "small" },
      "essay": { position: "top-center", size: "small" }
    }
  },
  ebook: {
    position: "top-left",
    size: "small",
    opacity: 0.7,
    enabled: false, // Optional for eBooks
    subtypes: {
      "fiction": { position: "top-left", size: "small", enabled: false },
      "non-fiction": { position: "top-right", size: "small", enabled: true },
      "educational": { position: "top-center", size: "medium", enabled: true },
      "business": { position: "top-right", size: "medium", enabled: true }
    }
  }
};

/**
 * Get logo settings for a specific document type and subtype
 * @param {string} documentType - Primary document type
 * @param {string} subType - Document subtype (optional)
 * @returns {Object} Logo settings for the document type
 */
export const getLogoSettingsForDocumentType = (documentType, subType = null) => {
  const typeSettings = DOCUMENT_TYPE_LOGO_SETTINGS[documentType];
  
  if (!typeSettings) {
    // Default settings for unknown document types
    return {
      position: "top-right",
      size: "medium",
      opacity: 1.0,
      enabled: true
    };
  }

  // Check for subtype-specific settings
  if (subType && typeSettings.subtypes && typeSettings.subtypes[subType]) {
    return {
      ...typeSettings,
      ...typeSettings.subtypes[subType]
    };
  }

  return typeSettings;
};

/**
 * Prepare logo data for document generation
 * @param {string} userId - User ID
 * @param {string} logoId - Specific logo ID (optional, uses default if not provided)
 * @param {string} documentType - Document type for settings
 * @param {string} subType - Document subtype (optional)
 * @returns {Promise<Object>} Logo data ready for document generation
 */
export const prepareLogoForDocument = async (userId, logoId = null, documentType = 'business', subType = null) => {
  try {
    prodLogger.debug('🎨 Preparing logo for document generation', {
      userId,
      logoId,
      documentType,
      subType
    });

    let logoData = null;

    if (logoId) {
      // Get specific logo
      const { data: logo, error } = await supabase
        .from('user_logos')
        .select('*')
        .eq('id', logoId)
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (error) {
        prodLogger.warn('⚠️ Failed to fetch specific logo, falling back to default:', error);
      } else {
        logoData = logo;
      }
    }

    if (!logoData) {
      // Get user's default logo
      const { data: defaultLogo, error } = await supabase
        .from('user_logos')
        .select('*')
        .eq('user_id', userId)
        .eq('is_default', true)
        .eq('is_active', true)
        .single();

      if (error) {
        prodLogger.debug('ℹ️ No default logo found for user:', userId);
        return null;
      }

      logoData = defaultLogo;
    }

    if (!logoData) {
      return null;
    }

    // Get logo settings for document type
    const logoSettings = getLogoSettingsForDocumentType(documentType, subType);

    // Check if logo should be included for this document type
    if (!logoSettings.enabled) {
      prodLogger.debug('🚫 Logo disabled for document type:', documentType, subType);
      return null;
    }

    // Download logo data from storage
    const { data: logoBuffer, error: downloadError } = await supabase.storage
      .from('user-logos')
      .download(logoData.storage_path);

    if (downloadError) {
      prodLogger.error('❌ Failed to download logo data:', downloadError);
      return null;
    }

    // Convert blob to array buffer
    const arrayBuffer = await logoBuffer.arrayBuffer();

    // Increment usage count (async, don't wait)
    incrementLogoUsage(logoData.id).catch(error => {
      prodLogger.warn('⚠️ Failed to increment logo usage:', error);
    });

    const result = {
      id: logoData.id,
      name: logoData.name,
      description: logoData.description,
      data: arrayBuffer,
      width: logoData.width,
      height: logoData.height,
      settings: logoSettings,
      metadata: {
        fileSize: logoData.file_size,
        fileType: logoData.file_type,
        aspectRatio: logoData.aspect_ratio
      }
    };

    prodLogger.debug('✅ Logo prepared successfully for document generation', {
      logoId: logoData.id,
      logoName: logoData.name,
      settings: logoSettings
    });

    return result;
  } catch (error) {
    prodLogger.error('❌ Error preparing logo for document:', error);
    return null;
  }
};

/**
 * Get user's available logos for document creation UI
 * @param {string} userId - User ID
 * @returns {Promise<Object>} User's logos with metadata
 */
export const getLogosForDocumentCreation = async (userId) => {
  try {
    const result = await getUserLogos(userId, { 
      activeOnly: true, 
      includeDefault: true 
    });

    if (!result.success) {
      return { success: false, error: result.error };
    }

    // Add preview URLs and format for UI
    const formattedLogos = result.logos.map(logo => ({
      id: logo.id,
      name: logo.name,
      description: logo.description,
      previewUrl: logo.public_url,
      dimensions: `${logo.width}x${logo.height}`,
      fileSize: `${(logo.file_size / (1024 * 1024)).toFixed(2)} MB`,
      usageCount: logo.usage_count,
      lastUsed: logo.last_used_at,
      isDefault: logo.is_default
    }));

    return {
      success: true,
      logos: formattedLogos,
      defaultLogo: result.defaultLogo ? {
        id: result.defaultLogo.id,
        name: result.defaultLogo.name,
        description: result.defaultLogo.description,
        previewUrl: result.defaultLogo.public_url,
        dimensions: `${result.defaultLogo.width}x${result.defaultLogo.height}`,
        fileSize: `${(result.defaultLogo.file_size / (1024 * 1024)).toFixed(2)} MB`,
        usageCount: result.defaultLogo.usage_count,
        lastUsed: result.defaultLogo.last_used_at,
        isDefault: true
      } : null,
      total: result.total
    };
  } catch (error) {
    prodLogger.error('❌ Error fetching logos for document creation:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch logos'
    };
  }
};

/**
 * Validate logo compatibility with document type
 * @param {Object} logo - Logo data
 * @param {string} documentType - Document type
 * @param {string} subType - Document subtype (optional)
 * @returns {Object} Validation result with recommendations
 */
export const validateLogoForDocumentType = (logo, documentType, subType = null) => {
  const settings = getLogoSettingsForDocumentType(documentType, subType);
  const warnings = [];
  const recommendations = [];

  // Check if logo is enabled for this document type
  if (!settings.enabled) {
    return {
      compatible: false,
      reason: `Logos are typically not used in ${documentType} documents`,
      warnings: [],
      recommendations: ['Consider using a watermark instead', 'Check if branding is required for your use case']
    };
  }

  // Check aspect ratio for different positions
  const aspectRatio = logo.width / logo.height;
  
  if (settings.position.includes('center') && aspectRatio > 3) {
    warnings.push('Logo is very wide for center positioning');
    recommendations.push('Consider using left or right positioning');
  }

  if (settings.position.includes('left') || settings.position.includes('right')) {
    if (aspectRatio < 0.5) {
      warnings.push('Logo is very tall for side positioning');
      recommendations.push('Consider using center positioning');
    }
  }

  // Check size recommendations
  const totalPixels = logo.width * logo.height;
  if (totalPixels > 640000) { // 800x800
    warnings.push('Logo resolution is higher than recommended');
    recommendations.push('Consider optimizing logo size for better performance');
  }

  return {
    compatible: true,
    settings,
    warnings,
    recommendations,
    suggestedPosition: settings.position,
    suggestedSize: settings.size
  };
};

/**
 * Get logo positioning preview for UI
 * @param {string} position - Logo position
 * @returns {Object} CSS classes and preview info
 */
export const getLogoPositionPreview = (position) => {
  const positions = {
    'top-left': {
      label: 'Top Left',
      description: 'Logo appears in the upper left corner',
      cssClass: 'justify-start items-start',
      icon: 'CornerUpLeft'
    },
    'top-center': {
      label: 'Top Center',
      description: 'Logo appears centered at the top',
      cssClass: 'justify-center items-start',
      icon: 'ArrowUp'
    },
    'top-right': {
      label: 'Top Right',
      description: 'Logo appears in the upper right corner',
      cssClass: 'justify-end items-start',
      icon: 'CornerUpRight'
    }
  };

  return positions[position] || positions['top-right'];
};

export { DOCUMENT_TYPE_LOGO_SETTINGS };
