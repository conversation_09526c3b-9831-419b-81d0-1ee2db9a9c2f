/**
 * Tests for Gemini Image Generation Service
 * 
 * These tests verify the AI image generation functionality using Gemini,
 * error handling, and fallback mechanisms.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  isGeminiImageConfigured,
  generateImage,
  validateGenerationOptions,
  getGenerationCostEstimate,
  checkServiceHealth,
  getFallbackOptions,
  IMAGE_STYLES,
  IMAGE_SIZES
} from '../geminiImageService';

// Mock environment variables
vi.mock('import.meta', () => ({
  env: {
    VITE_GEMINI_API_KEY: 'test-api-key',
    DEV: true
  }
}));

// Mock GoogleGenerativeAI
const mockGenerateContent = vi.fn();
vi.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: vi.fn(() => ({
    getGenerativeModel: vi.fn(() => ({
      generateContent: mockGenerateContent
    }))
  }))
}));

// Mock error monitor
vi.mock('../../utils/errorMonitor', () => ({
  default: {
    createContextLogger: vi.fn(() => ({
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn()
    }))
  }
}));

describe('Gemini Image Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Configuration', () => {
    it('should check if Gemini is configured', () => {
      const isConfigured = isGeminiImageConfigured();
      expect(typeof isConfigured).toBe('boolean');
    });

    it('should have proper image styles configuration', () => {
      expect(IMAGE_STYLES).toBeDefined();
      expect(IMAGE_STYLES.photorealistic).toBeDefined();
      expect(IMAGE_STYLES.artistic).toBeDefined();
      expect(IMAGE_STYLES.illustration).toBeDefined();
      expect(IMAGE_STYLES.professional).toBeDefined();
      
      // Check structure
      expect(IMAGE_STYLES.photorealistic.prompt_suffix).toBeDefined();
      expect(IMAGE_STYLES.photorealistic.negative_prompt).toBeDefined();
    });

    it('should have proper image sizes configuration', () => {
      expect(IMAGE_SIZES).toBeDefined();
      expect(IMAGE_SIZES.landscape).toBeDefined();
      expect(IMAGE_SIZES.portrait).toBeDefined();
      expect(IMAGE_SIZES.square).toBeDefined();
      
      // Check structure
      expect(IMAGE_SIZES.landscape.width).toBeDefined();
      expect(IMAGE_SIZES.landscape.height).toBeDefined();
      expect(IMAGE_SIZES.landscape.label).toBeDefined();
    });
  });

  describe('Option Validation', () => {
    it('should validate and set defaults for generation options', () => {
      const options = validateGenerationOptions({
        style: 'photorealistic',
        size: 'landscape'
      });

      expect(options.style).toBe('photorealistic');
      expect(options.size).toBe('landscape');
    });

    it('should fallback to defaults for invalid options', () => {
      const options = validateGenerationOptions({
        style: 'invalid-style',
        size: 'invalid-size'
      });

      expect(options.style).toBe('photorealistic');
      expect(options.size).toBe('landscape');
    });

    it('should set defaults when no options provided', () => {
      const options = validateGenerationOptions();

      expect(options.style).toBe('photorealistic');
      expect(options.size).toBe('landscape');
    });
  });

  describe('Cost Estimation', () => {
    it('should return zero cost for Gemini', () => {
      const cost = getGenerationCostEstimate();
      
      expect(cost.estimatedCost).toBe(0);
      expect(cost.currency).toBe('USD');
      expect(cost.breakdown).toBeDefined();
      expect(cost.note).toContain('free');
    });
  });

  describe('Fallback Options', () => {
    it('should provide fallback configuration', () => {
      const fallback = getFallbackOptions();
      
      expect(fallback.useMockImages).toBe(true);
      expect(fallback.supportedStyles).toEqual(Object.keys(IMAGE_STYLES));
      expect(fallback.supportedSizes).toEqual(Object.keys(IMAGE_SIZES));
    });
  });

  describe('Image Generation', () => {
    it('should generate mock images in development', async () => {
      const result = await generateImage('A beautiful sunset', {
        style: 'photorealistic',
        size: 'landscape'
      });

      expect(result.success).toBe(true);
      expect(result.imageUrl).toBeTruthy();
      expect(result.prompt).toBe('A beautiful sunset');
      expect(result.metadata.isMock).toBe(true);
      expect(result.metadata.provider).toBe('mock');
    });

    it('should handle empty prompts', async () => {
      await expect(generateImage('')).rejects.toThrow('Valid prompt is required');
    });

    it('should handle invalid prompts', async () => {
      await expect(generateImage(null)).rejects.toThrow('Valid prompt is required');
      await expect(generateImage('   ')).rejects.toThrow('Valid prompt is required');
    });

    it('should call progress callback during generation', async () => {
      const progressCallback = vi.fn();
      
      await generateImage('Test prompt', {}, progressCallback);
      
      expect(progressCallback).toHaveBeenCalled();
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: expect.any(String),
          progress: expect.any(Number),
          elapsed: expect.any(Number)
        })
      );
    });

    it('should generate images with different styles', async () => {
      const styles = ['photorealistic', 'artistic', 'illustration', 'professional'];
      
      for (const style of styles) {
        const result = await generateImage('Test prompt', { style });
        expect(result.success).toBe(true);
        expect(result.metadata.style).toBe(style);
      }
    });

    it('should generate images with different sizes', async () => {
      const sizes = ['landscape', 'portrait', 'square', 'wide'];
      
      for (const size of sizes) {
        const result = await generateImage('Test prompt', { size });
        expect(result.success).toBe(true);
        expect(result.metadata.size).toBe(size);
      }
    });
  });

  describe('Service Health', () => {
    it('should check service health when not configured', async () => {
      // Mock unconfigured state
      vi.mocked(isGeminiImageConfigured).mockReturnValue(false);
      
      const health = await checkServiceHealth();
      
      expect(health.status).toBe('unavailable');
      expect(health.configured).toBe(false);
    });
  });
});

describe('Integration Tests', () => {
  it('should handle complete generation workflow', async () => {
    const prompt = 'A professional office workspace';
    const options = {
      style: 'professional',
      size: 'landscape'
    };

    // Test the complete workflow
    const validatedOptions = validateGenerationOptions(options);
    const costEstimate = getGenerationCostEstimate(validatedOptions);
    const result = await generateImage(prompt, validatedOptions);

    expect(validatedOptions.style).toBe('professional');
    expect(costEstimate.estimatedCost).toBe(0);
    expect(result.success).toBe(true);
    expect(result.imageUrl).toBeTruthy();
    expect(result.metadata.provider).toBeDefined();
  });

  it('should maintain interface compatibility with existing code', async () => {
    const result = await generateImage('Test prompt');
    
    // Check that the response has the expected structure
    expect(result).toHaveProperty('success');
    expect(result).toHaveProperty('imageUrl');
    expect(result).toHaveProperty('prompt');
    expect(result).toHaveProperty('predictionId');
    expect(result).toHaveProperty('metadata');
    
    expect(result.metadata).toHaveProperty('model');
    expect(result.metadata).toHaveProperty('style');
    expect(result.metadata).toHaveProperty('size');
    expect(result.metadata).toHaveProperty('generatedAt');
  });
});
