/**
 * Notification Service Tests
 * 
 * Tests for the core notification service functionality
 */

import notificationService, {
  createSuccessNotification,
  createErrorNotification,
  createWarningNotification,
  createInfoNotification,
  NOTIFICATION_TYPES_EXTENDED,
  NOTIFICATION_CATEGORIES,
  NOTIFICATION_PRIORITIES
} from '../notificationService.js';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock errorMonitor
jest.mock('../../utils/errorMonitor.js', () => ({
  createContextLogger: () => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  })
}));

// Mock notificationConfig
jest.mock('../notificationConfig.js', () => ({
  isTypeEnabled: jest.fn(() => true),
  isCategoryEnabled: jest.fn(() => true),
  shouldShowToast: jest.fn(() => true),
  getDuration: jest.fn(() => 5000),
  subscribe: jest.fn(() => () => {})
}));

describe('NotificationService', () => {
  beforeEach(() => {
    // Clear all notifications before each test
    notificationService.notifications = [];
    notificationService.toasts = [];
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
  });

  describe('Basic Functionality', () => {
    test('should add a notification', () => {
      const notification = createSuccessNotification('Test', 'Test message');
      const id = notificationService.add(notification);

      expect(id).toBeTruthy();
      expect(notificationService.notifications).toHaveLength(1);
      expect(notificationService.notifications[0].title).toBe('Test');
      expect(notificationService.notifications[0].message).toBe('Test message');
      expect(notificationService.notifications[0].type).toBe('success');
    });

    test('should remove a notification', () => {
      const notification = createSuccessNotification('Test', 'Test message');
      const id = notificationService.add(notification);

      const removed = notificationService.remove(id);
      expect(removed).toBe(true);
      expect(notificationService.notifications).toHaveLength(0);
    });

    test('should mark notification as read', () => {
      const notification = createSuccessNotification('Test', 'Test message');
      const id = notificationService.add(notification);

      const marked = notificationService.markAsRead(id);
      expect(marked).toBe(true);
      expect(notificationService.notifications[0].isRead).toBe(true);
    });

    test('should mark all notifications as read', () => {
      notificationService.add(createSuccessNotification('Test 1', 'Message 1'));
      notificationService.add(createInfoNotification('Test 2', 'Message 2'));

      const count = notificationService.markAllAsRead();
      expect(count).toBe(2);
      expect(notificationService.notifications.every(n => n.isRead)).toBe(true);
    });

    test('should clear non-persistent notifications', () => {
      notificationService.add(createSuccessNotification('Success', 'Message'));
      notificationService.add(createErrorNotification('Error', 'Message')); // Persistent by default

      const cleared = notificationService.clear();
      expect(cleared).toBe(1);
      expect(notificationService.notifications).toHaveLength(1);
      expect(notificationService.notifications[0].type).toBe('error');
    });
  });

  describe('State Management', () => {
    test('should return correct state', () => {
      notificationService.add(createSuccessNotification('Test 1', 'Message 1'));
      notificationService.add(createInfoNotification('Test 2', 'Message 2'));
      notificationService.markAsRead(notificationService.notifications[0].id);

      const state = notificationService.getState();
      expect(state.notifications).toHaveLength(2);
      expect(state.totalCount).toBe(2);
      expect(state.unreadCount).toBe(1);
    });

    test('should filter notifications by category', () => {
      notificationService.add({
        ...createSuccessNotification('Project', 'Message'),
        category: NOTIFICATION_CATEGORIES.PROJECT
      });
      notificationService.add({
        ...createInfoNotification('Template', 'Message'),
        category: NOTIFICATION_CATEGORIES.TEMPLATE
      });

      const projectNotifications = notificationService.getByCategory(NOTIFICATION_CATEGORIES.PROJECT);
      const templateNotifications = notificationService.getByCategory(NOTIFICATION_CATEGORIES.TEMPLATE);

      expect(projectNotifications).toHaveLength(1);
      expect(templateNotifications).toHaveLength(1);
      expect(projectNotifications[0].title).toBe('Project');
      expect(templateNotifications[0].title).toBe('Template');
    });

    test('should filter notifications by type', () => {
      notificationService.add(createSuccessNotification('Success', 'Message'));
      notificationService.add(createErrorNotification('Error', 'Message'));
      notificationService.add(createSuccessNotification('Success 2', 'Message'));

      const successNotifications = notificationService.getByType('success');
      const errorNotifications = notificationService.getByType('error');

      expect(successNotifications).toHaveLength(2);
      expect(errorNotifications).toHaveLength(1);
    });
  });

  describe('Priority Sorting', () => {
    test('should sort notifications by priority', () => {
      const lowPriority = {
        ...createInfoNotification('Low', 'Message'),
        priority: NOTIFICATION_PRIORITIES.LOW
      };
      const highPriority = {
        ...createErrorNotification('High', 'Message'),
        priority: NOTIFICATION_PRIORITIES.HIGH
      };
      const normalPriority = {
        ...createSuccessNotification('Normal', 'Message'),
        priority: NOTIFICATION_PRIORITIES.NORMAL
      };

      notificationService.add(lowPriority);
      notificationService.add(highPriority);
      notificationService.add(normalPriority);

      expect(notificationService.notifications[0].title).toBe('High');
      expect(notificationService.notifications[1].title).toBe('Normal');
      expect(notificationService.notifications[2].title).toBe('Low');
    });
  });

  describe('Toast Management', () => {
    test('should add non-persistent notifications to toast queue', () => {
      const notification = createSuccessNotification('Toast Test', 'Message');
      notificationService.add(notification);

      expect(notificationService.toasts).toHaveLength(1);
      expect(notificationService.toasts[0].title).toBe('Toast Test');
    });

    test('should not add persistent notifications to toast queue', () => {
      const notification = createErrorNotification('Error Test', 'Message');
      notificationService.add(notification);

      expect(notificationService.toasts).toHaveLength(0);
    });

    test('should remove toast from queue', () => {
      const notification = createSuccessNotification('Toast Test', 'Message');
      const id = notificationService.add(notification);

      notificationService.removeToast(id);
      expect(notificationService.toasts).toHaveLength(0);
    });
  });

  describe('Event Listeners', () => {
    test('should notify listeners on add', () => {
      const listener = jest.fn();
      const unsubscribe = notificationService.subscribe(listener);

      const notification = createSuccessNotification('Test', 'Message');
      notificationService.add(notification);

      expect(listener).toHaveBeenCalledWith('add', expect.any(Object), expect.any(Object));

      unsubscribe();
    });

    test('should notify listeners on remove', () => {
      const listener = jest.fn();
      const unsubscribe = notificationService.subscribe(listener);

      const notification = createSuccessNotification('Test', 'Message');
      const id = notificationService.add(notification);
      
      listener.mockClear(); // Clear previous calls
      notificationService.remove(id);

      expect(listener).toHaveBeenCalledWith('remove', { id }, expect.any(Object));

      unsubscribe();
    });
  });

  describe('Validation', () => {
    test('should validate notification type', () => {
      const invalidNotification = {
        type: 'invalid-type',
        title: 'Test',
        message: 'Message'
      };

      const id = notificationService.add(invalidNotification);
      expect(id).toBeNull();
      expect(notificationService.notifications).toHaveLength(0);
    });

    test('should validate notification title', () => {
      const invalidNotification = {
        type: 'success',
        message: 'Message'
        // Missing title
      };

      const id = notificationService.add(invalidNotification);
      expect(id).toBeNull();
      expect(notificationService.notifications).toHaveLength(0);
    });
  });
});

describe('Notification Creators', () => {
  test('should create success notification', () => {
    const notification = createSuccessNotification('Success', 'Message', { duration: 3000 });
    
    expect(notification.type).toBe(NOTIFICATION_TYPES_EXTENDED.SUCCESS);
    expect(notification.title).toBe('Success');
    expect(notification.message).toBe('Message');
    expect(notification.duration).toBe(3000);
    expect(notification.category).toBe(NOTIFICATION_CATEGORIES.SYSTEM);
    expect(notification.priority).toBe(NOTIFICATION_PRIORITIES.NORMAL);
  });

  test('should create error notification', () => {
    const notification = createErrorNotification('Error', 'Message');
    
    expect(notification.type).toBe(NOTIFICATION_TYPES_EXTENDED.ERROR);
    expect(notification.title).toBe('Error');
    expect(notification.message).toBe('Message');
    expect(notification.persistent).toBe(true);
    expect(notification.priority).toBe(NOTIFICATION_PRIORITIES.HIGH);
  });

  test('should create warning notification', () => {
    const notification = createWarningNotification('Warning', 'Message');
    
    expect(notification.type).toBe(NOTIFICATION_TYPES_EXTENDED.WARNING);
    expect(notification.title).toBe('Warning');
    expect(notification.message).toBe('Message');
    expect(notification.persistent).toBe(true);
  });

  test('should create info notification', () => {
    const notification = createInfoNotification('Info', 'Message');
    
    expect(notification.type).toBe(NOTIFICATION_TYPES_EXTENDED.INFO);
    expect(notification.title).toBe('Info');
    expect(notification.message).toBe('Message');
  });
});
