/**
 * Tests for Replicate Service
 * 
 * These tests verify the AI image generation functionality,
 * error handling, and fallback mechanisms.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  isReplicateConfigured,
  generateImage,
  validateGenerationOptions,
  getGenerationCostEstimate,
  checkServiceHealth,
  getFallbackOptions,
  IMAGE_STYLES,
  IMAGE_SIZES
} from '../replicateService';

// Mock environment variables
vi.mock('import.meta', () => ({
  env: {
    VITE_REPLICATE_API_KEY: 'test-api-key',
    DEV: true
  }
}));

// Mock fetch for API calls
global.fetch = vi.fn();

describe('Replicate Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Configuration', () => {
    it('should detect when Replicate is configured', () => {
      expect(isReplicateConfigured()).toBe(true);
    });

    it('should provide available image styles', () => {
      expect(IMAGE_STYLES).toHaveProperty('photorealistic');
      expect(IMAGE_STYLES).toHaveProperty('artistic');
      expect(IMAGE_STYLES).toHaveProperty('illustration');
      expect(IMAGE_STYLES).toHaveProperty('professional');
    });

    it('should provide available image sizes', () => {
      expect(IMAGE_SIZES).toHaveProperty('square');
      expect(IMAGE_SIZES).toHaveProperty('landscape');
      expect(IMAGE_SIZES).toHaveProperty('portrait');
      expect(IMAGE_SIZES).toHaveProperty('wide');
    });
  });

  describe('Option Validation', () => {
    it('should validate and set defaults for generation options', () => {
      const options = validateGenerationOptions({
        style: 'photorealistic',
        size: 'landscape'
      });

      expect(options.style).toBe('photorealistic');
      expect(options.size).toBe('landscape');
    });

    it('should fallback to defaults for invalid options', () => {
      const options = validateGenerationOptions({
        style: 'invalid-style',
        size: 'invalid-size'
      });

      expect(options.style).toBe('photorealistic');
      expect(options.size).toBe('landscape');
    });

    it('should clamp numeric parameters to valid ranges', () => {
      const options = validateGenerationOptions({
        num_inference_steps: 100, // Should be clamped to 50
        guidance_scale: 25 // Should be clamped to 20
      });

      expect(options.num_inference_steps).toBe(50);
      expect(options.guidance_scale).toBe(20);
    });
  });

  describe('Cost Estimation', () => {
    it('should calculate cost estimates based on options', () => {
      const estimate = getGenerationCostEstimate({
        size: 'landscape',
        num_inference_steps: 20
      });

      expect(estimate).toHaveProperty('estimatedCost');
      expect(estimate).toHaveProperty('currency', 'USD');
      expect(estimate).toHaveProperty('factors');
      expect(typeof estimate.estimatedCost).toBe('number');
    });

    it('should adjust cost for different sizes and steps', () => {
      const smallEstimate = getGenerationCostEstimate({
        size: 'square',
        num_inference_steps: 10
      });

      const largeEstimate = getGenerationCostEstimate({
        size: 'wide',
        num_inference_steps: 30
      });

      expect(largeEstimate.estimatedCost).toBeGreaterThan(smallEstimate.estimatedCost);
    });
  });

  describe('Image Generation', () => {
    it('should generate mock images in development', async () => {
      const result = await generateImage('A beautiful sunset', {
        style: 'photorealistic',
        size: 'landscape'
      });

      expect(result.success).toBe(true);
      expect(result.imageUrl).toBeTruthy();
      expect(result.prompt).toBe('A beautiful sunset');
      expect(result.metadata.isMock).toBe(true);
    });

    it('should handle empty prompts', async () => {
      await expect(generateImage('')).rejects.toThrow('Valid prompt is required');
    });

    it('should handle invalid prompts', async () => {
      await expect(generateImage(null)).rejects.toThrow('Valid prompt is required');
      await expect(generateImage('   ')).rejects.toThrow('Valid prompt is required');
    });

    it('should include generation metadata', async () => {
      const result = await generateImage('Test prompt', {
        style: 'artistic',
        size: 'portrait'
      });

      expect(result.metadata).toHaveProperty('style', 'artistic');
      expect(result.metadata).toHaveProperty('size', 'portrait');
      expect(result.metadata).toHaveProperty('generatedAt');
    });
  });

  describe('Service Health', () => {
    it('should check service health when configured', async () => {
      // Mock successful API response
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ results: [{ id: 'model1' }, { id: 'model2' }] })
      });

      const health = await checkServiceHealth();

      expect(health.healthy).toBe(true);
      expect(health.configured).toBe(true);
      expect(health.modelsAvailable).toBe(2);
    });

    it('should handle API errors gracefully', async () => {
      // Mock API error
      fetch.mockRejectedValueOnce(new Error('Network error'));

      const health = await checkServiceHealth();

      expect(health.healthy).toBe(false);
      expect(health.configured).toBe(true);
      expect(health.recommendation).toBeTruthy();
    });
  });

  describe('Fallback Options', () => {
    it('should provide fallback options when service is unavailable', () => {
      const options = getFallbackOptions();

      expect(Array.isArray(options)).toBe(true);
      expect(options.length).toBeGreaterThan(0);
      
      const mockOption = options.find(opt => opt.type === 'mock');
      expect(mockOption).toBeTruthy();
      expect(mockOption.available).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should provide appropriate error messages for different failure types', async () => {
      // Test rate limiting error
      fetch.mockRejectedValueOnce(new Error('rate limit'));
      
      try {
        await generateImage('test prompt');
      } catch (error) {
        // Should fallback to mock in development
        expect(error).toBeFalsy(); // Should not throw in dev mode
      }
    });

    it('should retry on transient failures', async () => {
      // Mock transient failure followed by success
      fetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ id: 'prediction-123' })
        });

      // This test would need more complex mocking to test retry logic
      // For now, just verify the service handles errors gracefully
      const result = await generateImage('test prompt');
      expect(result.success).toBe(true);
    });
  });
});

describe('Integration Tests', () => {
  it('should handle complete generation workflow', async () => {
    const prompt = 'A professional office workspace';
    const options = {
      style: 'professional',
      size: 'landscape'
    };

    // Test the complete workflow
    const validatedOptions = validateGenerationOptions(options);
    const costEstimate = getGenerationCostEstimate(validatedOptions);
    const result = await generateImage(prompt, validatedOptions);

    expect(validatedOptions.style).toBe('professional');
    expect(costEstimate.estimatedCost).toBeGreaterThan(0);
    expect(result.success).toBe(true);
    expect(result.imageUrl).toBeTruthy();
  });

  it('should handle service unavailability gracefully', async () => {
    // Mock service unavailable
    fetch.mockRejectedValueOnce(new Error('503 Service Unavailable'));

    const result = await generateImage('test prompt');
    
    // Should fallback to mock image
    expect(result.success).toBe(true);
    expect(result.metadata.isMock).toBe(true);
    expect(result.metadata.mockReason).toBeTruthy();
  });
});
