/**
 * Unit tests for DOCX chapter organization and formatting functionality
 * Tests the implementation of task 6: chapter structure, page breaks, and list formatting
 */

import {
    createChapterContent,
    processChapterContentString,
    createDocumentStructure,
    createUnifiedDocumentStructure,
    createDocumentNumbering,
    createDocumentStyles
} from '../docxGenerationService.js';

import {
    createListParagraphs,
    createEnhancedListParagraphs,
    processList,
    processHeading,
    processParagraph,
    parseHTMLContent,
    convertMarkdownToHTML,
    CONTENT_TYPES
} from '../contentProcessingService.js';

// Mock the docx library
jest.mock('docx', () => ({
    Document: jest.fn(),
    Packer: {
        toBlob: jest.fn()
    },
    Paragraph: jest.fn((config) => ({ type: 'paragraph', ...config })),
    TextRun: jest.fn((config) => ({ type: 'textrun', ...config })),
    HeadingLevel: {
        HEADING_1: 'heading1',
        HEADING_2: 'heading2',
        HEADING_3: 'heading3',
        HEADING_4: 'heading4',
        HEADING_5: 'heading5',
        HEADING_6: 'heading6'
    },
    AlignmentType: {
        CENTER: 'center',
        LEFT: 'left',
        RIGHT: 'right'
    },
    ImageRun: jest.fn(),
    LevelFormat: {
        BULLET: 'bullet',
        DECIMAL: 'decimal'
    }
}));

describe('Chapter Organization and Formatting', () => {
    describe('createChapterContent', () => {
        test('should create chapter with proper title and page break', () => {
            const chapter = {
                number: 1,
                title: 'Introduction',
                content: 'This is the introduction chapter.'
            };

            const result = createChapterContent(chapter, 0);

            expect(result.length).toBeGreaterThanOrEqual(1); // At least title, possibly more with content
            expect(result[0]).toMatchObject({
                type: 'paragraph',
                heading: 'heading1',
                spacing: {
                    before: 400,
                    after: 300
                },
                pageBreakBefore: false // First chapter should not have page break
            });
        });

        test('should add page break for chapters after the first', () => {
            const chapter = {
                number: 2,
                title: 'Chapter Two',
                content: 'Second chapter content.'
            };

            const result = createChapterContent(chapter, 1);

            expect(result[0]).toMatchObject({
                pageBreakBefore: true // Second chapter should have page break
            });
        });

        test('should handle chapter with HTML content', () => {
            const chapter = {
                number: 1,
                title: 'HTML Chapter',
                content: '<h2>Subheading</h2><p>Paragraph content</p><ul><li>List item 1</li><li>List item 2</li></ul>'
            };

            const result = createChapterContent(chapter, 0);

            expect(result.length).toBeGreaterThan(1); // Should have title + processed content
        });

        test('should handle chapter with markdown content', () => {
            const chapter = {
                number: 1,
                title: 'Markdown Chapter',
                content: '## Subheading\n\nParagraph content\n\n- List item 1\n- List item 2'
            };

            const result = createChapterContent(chapter, 0);

            expect(result.length).toBeGreaterThan(1); // Should have title + processed content
        });

        test('should handle chapter with array content', () => {
            const chapter = {
                number: 1,
                title: 'Array Chapter',
                content: [
                    { type: CONTENT_TYPES.PARAGRAPH, text: 'First paragraph' },
                    { type: CONTENT_TYPES.HEADING, level: 2, text: 'Subheading' }
                ]
            };

            const result = createChapterContent(chapter, 0);

            expect(result.length).toBeGreaterThan(1); // Should have title + processed content
        });
    });

    describe('processChapterContentString', () => {
        test('should process HTML content string', () => {
            const htmlContent = '<h2>Heading</h2><p>Paragraph</p>';
            const result = processChapterContentString(htmlContent);

            expect(Array.isArray(result)).toBe(true);
            expect(result.length).toBeGreaterThan(0);
        });

        test('should process markdown content string', () => {
            const markdownContent = '## Heading\n\nParagraph content';
            const result = processChapterContentString(markdownContent);

            expect(Array.isArray(result)).toBe(true);
            expect(result.length).toBeGreaterThan(0);
        });

        test('should handle processing errors gracefully', () => {
            const invalidContent = null;
            const result = processChapterContentString(invalidContent);

            expect(Array.isArray(result)).toBe(true);
            expect(result.length).toBeGreaterThanOrEqual(1); // Should return fallback paragraph
        });
    });

    describe('createDocumentStructure', () => {
        test('should create document structure with chapters', () => {
            const documentData = {
                title: 'Test Document',
                author: 'Test Author',
                description: 'Test Description'
            };

            const processedContent = {
                chapters: [
                    { number: 1, title: 'Chapter 1', content: 'Content 1' },
                    { number: 2, title: 'Chapter 2', content: 'Content 2' }
                ]
            };

            const result = createDocumentStructure(documentData, processedContent);

            expect(result.title).toBe('Test Document');
            expect(result.creator).toBe('Test Author');
            expect(result.sections).toHaveLength(3); // Title page + 2 chapters
        });

        test('should handle empty chapters', () => {
            const documentData = {
                title: 'Test Document',
                author: 'Test Author'
            };

            const processedContent = { chapters: [] };

            const result = createDocumentStructure(documentData, processedContent);

            expect(result.sections).toHaveLength(1); // Only title page
        });
    });

    describe('createUnifiedDocumentStructure', () => {
        test('should create unified document structure', () => {
            const documentData = {
                title: 'Test Document',
                author: 'Test Author'
            };

            const processedContent = [
                { type: CONTENT_TYPES.HEADING, level: 1, text: 'Main Heading' },
                { type: CONTENT_TYPES.PARAGRAPH, text: 'Content paragraph' }
            ];

            const result = createUnifiedDocumentStructure(documentData, processedContent);

            expect(result.sections).toHaveLength(2); // Title page + content
        });

        test('should handle empty content', () => {
            const documentData = {
                title: 'Test Document',
                author: 'Test Author'
            };

            const result = createUnifiedDocumentStructure(documentData, []);

            expect(result.sections).toHaveLength(1); // Only title page
        });
    });

    describe('createDocumentNumbering', () => {
        test('should create numbering configuration', () => {
            const result = createDocumentNumbering();

            expect(result).toHaveProperty('config');
            expect(result.config).toHaveLength(2); // bullet-list and ordered-list

            const bulletList = result.config.find(c => c.reference === 'bullet-list');
            const orderedList = result.config.find(c => c.reference === 'ordered-list');

            expect(bulletList).toBeDefined();
            expect(orderedList).toBeDefined();
            expect(bulletList.levels[0].format).toBe('bullet');
            expect(orderedList.levels[0].format).toBe('decimal');
        });
    });

    describe('createDocumentStyles', () => {
        test('should create document styles configuration', () => {
            const result = createDocumentStyles();

            expect(result).toHaveProperty('paragraphStyles');
            expect(result.paragraphStyles).toHaveLength(4); // Normal, Heading1, Heading2, Heading3

            const normalStyle = result.paragraphStyles.find(s => s.id === 'Normal');
            const heading1Style = result.paragraphStyles.find(s => s.id === 'Heading1');

            expect(normalStyle).toBeDefined();
            expect(heading1Style).toBeDefined();
            expect(heading1Style.run.size).toBe(32); // 16pt
            expect(heading1Style.run.bold).toBe(true);
        });
    });
});

describe('List Formatting', () => {
    describe('createListParagraphs', () => {
        test('should create ordered list paragraphs', () => {
            const listContent = {
                type: CONTENT_TYPES.LIST,
                ordered: true,
                items: [
                    { text: 'First item', textRuns: [{ text: 'First item', bold: false }] },
                    { text: 'Second item', textRuns: [{ text: 'Second item', bold: false }] }
                ]
            };

            const result = createListParagraphs(listContent);

            expect(result).toHaveLength(2);
            expect(result[0]).toMatchObject({
                numbering: {
                    reference: 'ordered-list',
                    level: 0
                },
                indent: {
                    left: 720,
                    hanging: 360
                }
            });
        });

        test('should create unordered list paragraphs', () => {
            const listContent = {
                type: CONTENT_TYPES.LIST,
                ordered: false,
                items: [
                    { text: 'First item', textRuns: [{ text: 'First item', bold: false }] },
                    { text: 'Second item', textRuns: [{ text: 'Second item', bold: false }] }
                ]
            };

            const result = createListParagraphs(listContent);

            expect(result).toHaveLength(2);
            expect(result[0]).toMatchObject({
                numbering: {
                    reference: 'bullet-list',
                    level: 0
                }
            });
        });

        test('should handle empty list items', () => {
            const listContent = {
                type: CONTENT_TYPES.LIST,
                ordered: false,
                items: []
            };

            const result = createListParagraphs(listContent);

            expect(result).toHaveLength(0);
        });
    });

    describe('createEnhancedListParagraphs', () => {
        test('should create enhanced list with fallback formatting', () => {
            const listContent = {
                type: CONTENT_TYPES.LIST,
                ordered: true,
                items: [
                    { text: 'First item', textRuns: [{ text: 'First item', bold: false }] }
                ]
            };

            const result = createEnhancedListParagraphs(listContent);

            expect(result).toHaveLength(1);
            expect(result[0]).toMatchObject({
                indent: {
                    left: 720,
                    firstLine: -360
                }
            });
            // Should have prefix text run
            expect(result[0].children[0]).toMatchObject({
                text: '1. '
            });
        });

        test('should create bullet list with fallback formatting', () => {
            const listContent = {
                type: CONTENT_TYPES.LIST,
                ordered: false,
                items: [
                    { text: 'First item', textRuns: [{ text: 'First item', bold: false }] }
                ]
            };

            const result = createEnhancedListParagraphs(listContent);

            expect(result).toHaveLength(1);
            // Should have bullet prefix
            expect(result[0].children[0]).toMatchObject({
                text: '• '
            });
        });
    });

    describe('processList', () => {
        test('should process HTML unordered list', () => {
            // Create a mock DOM element
            const mockElement = {
                tagName: 'UL',
                querySelectorAll: jest.fn(() => [
                    {
                        textContent: 'Item 1',
                        children: [],
                        childNodes: [{ nodeType: 3, textContent: 'Item 1' }]
                    },
                    {
                        textContent: 'Item 2',
                        children: [],
                        childNodes: [{ nodeType: 3, textContent: 'Item 2' }]
                    }
                ])
            };

            const result = processList(mockElement);

            expect(result).toMatchObject({
                type: CONTENT_TYPES.LIST,
                ordered: false,
                items: expect.arrayContaining([
                    expect.objectContaining({ text: 'Item 1' }),
                    expect.objectContaining({ text: 'Item 2' })
                ])
            });
        });

        test('should process HTML ordered list', () => {
            const mockElement = {
                tagName: 'OL',
                querySelectorAll: jest.fn(() => [
                    {
                        textContent: 'First item',
                        children: [],
                        childNodes: [{ nodeType: 3, textContent: 'First item' }]
                    }
                ])
            };

            const result = processList(mockElement);

            expect(result).toMatchObject({
                type: CONTENT_TYPES.LIST,
                ordered: true,
                items: expect.arrayContaining([
                    expect.objectContaining({ text: 'First item' })
                ])
            });
        });
    });
});

describe('Content Processing Integration', () => {
    describe('parseHTMLContent', () => {
        test('should parse HTML with headings, paragraphs, and lists', () => {
            const htmlContent = `
                <h1>Main Heading</h1>
                <p>Introduction paragraph</p>
                <h2>Subheading</h2>
                <ul>
                    <li>First item</li>
                    <li>Second item</li>
                </ul>
                <ol>
                    <li>Numbered item 1</li>
                    <li>Numbered item 2</li>
                </ol>
            `;

            const result = parseHTMLContent(htmlContent);

            expect(result.length).toBeGreaterThan(0);

            // Should contain different content types
            const headings = result.filter(item => item.type === CONTENT_TYPES.HEADING);
            const paragraphs = result.filter(item => item.type === CONTENT_TYPES.PARAGRAPH);
            const lists = result.filter(item => item.type === CONTENT_TYPES.LIST);

            expect(headings.length).toBeGreaterThan(0);
            expect(paragraphs.length).toBeGreaterThan(0);
            expect(lists.length).toBeGreaterThan(0);
        });
    });

    describe('convertMarkdownToHTML', () => {
        test('should convert markdown lists to HTML', () => {
            const markdownContent = `
# Main Heading

Introduction paragraph

## Subheading

- First item
- Second item

1. Numbered item 1
2. Numbered item 2
            `;

            const result = convertMarkdownToHTML(markdownContent);

            expect(result).toContain('<h1>Main Heading</h1>');
            expect(result).toContain('<h2>Subheading</h2>');
            expect(result).toContain('<li>First item</li>');
            expect(result).toContain('<li>Numbered item 1</li>');
        });

        test('should handle complex markdown formatting', () => {
            const markdownContent = `
## Chapter Title

This is a paragraph with **bold text** and *italic text*.

- Bullet point with **bold**
- Another bullet point

1. First numbered item
2. Second numbered item with *emphasis*
            `;

            const result = convertMarkdownToHTML(markdownContent);

            expect(result).toContain('<strong>bold text</strong>');
            expect(result).toContain('<em>italic text</em>');
            expect(result).toContain('<li>Bullet point with <strong>bold</strong></li>');
        });
    });
});