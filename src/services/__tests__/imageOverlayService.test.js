/**
 * Tests for Image Overlay Service
 * Comprehensive testing of image overlay template rendering functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import imageOverlayService from '../imageOverlayService.js';

// Mock Canvas API for testing
const mockCanvas = {
  width: 600,
  height: 800,
  getContext: vi.fn(() => ({
    textBaseline: 'top',
    imageSmoothingEnabled: true,
    imageSmoothingQuality: 'high',
    font: '',
    fillStyle: '',
    textAlign: '',
    measureText: vi.fn(() => ({ width: 100 })),
    fillText: vi.fn(),
    drawImage: vi.fn(),
    fillRect: vi.fn(),
    strokeRect: vi.fn(),
    createLinearGradient: vi.fn(() => ({
      addColorStop: vi.fn()
    }))
  })),
  toDataURL: vi.fn(() => 'data:image/png;base64,mockImageData')
};

const mockImage = {
  width: 600,
  height: 800,
  onload: null,
  onerror: null,
  src: '',
  crossOrigin: ''
};

// Mock DOM methods
global.document = {
  createElement: vi.fn((tag) => {
    if (tag === 'canvas') return mockCanvas;
    if (tag === 'img') return mockImage;
    return {};
  })
};

global.Image = vi.fn(() => mockImage);

describe('ImageOverlayService', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
    imageOverlayService.clearCache();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Canvas Initialization', () => {
    it('should initialize canvas with correct dimensions', () => {
      const result = imageOverlayService.initializeCanvas(600, 800);
      
      expect(document.createElement).toHaveBeenCalledWith('canvas');
      expect(mockCanvas.width).toBe(600);
      expect(mockCanvas.height).toBe(800);
      expect(result.canvas).toBe(mockCanvas);
      expect(result.ctx).toBeDefined();
    });

    it('should handle canvas initialization errors', () => {
      document.createElement.mockImplementationOnce(() => {
        throw new Error('Canvas not supported');
      });

      expect(() => {
        imageOverlayService.initializeCanvas(600, 800);
      }).toThrow('Canvas initialization failed');
    });
  });

  describe('Image Loading', () => {
    it('should load background image successfully', async () => {
      const imageUrl = 'https://example.com/background.jpg';
      
      // Simulate successful image load
      setTimeout(() => {
        mockImage.onload();
      }, 0);

      const result = await imageOverlayService.loadBackgroundImage(imageUrl);
      
      expect(result).toBe(mockImage);
      expect(mockImage.src).toBe(imageUrl);
    });

    it('should handle image loading errors with fallback', async () => {
      const imageUrl = 'https://example.com/invalid.jpg';
      
      // Simulate image load error
      setTimeout(() => {
        mockImage.onerror();
      }, 0);

      const result = await imageOverlayService.loadBackgroundImage(imageUrl);
      
      expect(result).toBeDefined();
      // Should create fallback image
      expect(document.createElement).toHaveBeenCalledWith('canvas');
    });

    it('should cache loaded images', async () => {
      const imageUrl = 'https://example.com/background.jpg';
      
      // First load
      setTimeout(() => mockImage.onload(), 0);
      await imageOverlayService.loadBackgroundImage(imageUrl);
      
      // Second load should use cache
      const result = await imageOverlayService.loadBackgroundImage(imageUrl);
      expect(result).toBe(mockImage);
    });
  });

  describe('Text Styling', () => {
    it('should apply text styling correctly', () => {
      const ctx = mockCanvas.getContext('2d');
      const styling = {
        fontSize: 24,
        fontFamily: 'Arial',
        fontWeight: 'bold',
        color: '#FF0000',
        textAlign: 'center'
      };

      imageOverlayService.applyTextStyling(styling);

      expect(ctx.font).toBe('bold 24px Arial');
      expect(ctx.fillStyle).toBe('#FF0000');
      expect(ctx.textAlign).toBe('center');
    });

    it('should use default styling values', () => {
      const ctx = mockCanvas.getContext('2d');
      
      imageOverlayService.applyTextStyling({});

      expect(ctx.font).toBe('normal 16px Arial');
      expect(ctx.fillStyle).toBe('#000000');
      expect(ctx.textAlign).toBe('left');
    });
  });

  describe('Text Measurement', () => {
    it('should measure text with word wrapping', () => {
      const text = 'This is a long text that should wrap';
      const maxWidth = 200;
      const styling = { fontSize: 16, lineHeight: 1.2 };

      const ctx = mockCanvas.getContext('2d');
      ctx.measureText.mockReturnValue({ width: 150 });

      const result = imageOverlayService.measureText(text, maxWidth, styling);

      expect(result.lines).toBeDefined();
      expect(result.totalHeight).toBeDefined();
      expect(result.lineHeight).toBe(16 * 1.2);
    });
  });

  describe('Placeholder Population', () => {
    it('should populate placeholders with document data', () => {
      const placeholder = 'Title: {{title}} by {{author}}';
      const documentData = {
        title: 'Test Document',
        author: 'John Doe'
      };

      const result = imageOverlayService.populatePlaceholder(placeholder, documentData);
      
      expect(result).toBe('Title: Test Document by John Doe');
    });

    it('should handle missing document data', () => {
      const placeholder = '{{title}} by {{author}}';
      const documentData = {};

      const result = imageOverlayService.populatePlaceholder(placeholder, documentData);
      
      expect(result).toBe('Untitled Document by Unknown Author');
    });

    it('should handle invalid placeholder input', () => {
      expect(imageOverlayService.populatePlaceholder(null, {})).toBe('');
      expect(imageOverlayService.populatePlaceholder(undefined, {})).toBe('');
      expect(imageOverlayService.populatePlaceholder(123, {})).toBe('');
    });
  });

  describe('Template Rendering', () => {
    const mockTemplate = {
      id: 'test-template',
      background_image_url: 'https://example.com/bg.jpg',
      background_image_width: 600,
      background_image_height: 800,
      text_overlays: {
        overlays: [
          {
            id: 'title',
            placeholder: '{{title}}',
            position: { x: 50, y: 100, width: 500, height: 60 },
            styling: {
              fontSize: 32,
              fontFamily: 'Arial',
              fontWeight: 'bold',
              color: '#000000',
              textAlign: 'center'
            }
          }
        ]
      }
    };

    const mockDocumentData = {
      title: 'Test Document',
      author: 'Test Author',
      description: 'Test Description'
    };

    it('should render template successfully', async () => {
      // Mock image loading
      setTimeout(() => mockImage.onload(), 0);

      const result = await imageOverlayService.renderTemplate(mockTemplate, mockDocumentData);
      
      expect(result).toBe(mockCanvas);
      expect(mockCanvas.getContext('2d').drawImage).toHaveBeenCalled();
      expect(mockCanvas.getContext('2d').fillText).toHaveBeenCalled();
    });

    it('should handle template rendering errors', async () => {
      const invalidTemplate = {
        ...mockTemplate,
        background_image_url: null
      };

      await expect(
        imageOverlayService.renderTemplate(invalidTemplate, mockDocumentData)
      ).rejects.toThrow();
    });
  });

  describe('Image Export', () => {
    it('should export canvas as image data URL', () => {
      const result = imageOverlayService.exportAsImage(mockCanvas, 'png', 0.9);
      
      expect(mockCanvas.toDataURL).toHaveBeenCalledWith('image/png', 0.9);
      expect(result).toBe('data:image/png;base64,mockImageData');
    });

    it('should handle export errors', () => {
      mockCanvas.toDataURL.mockImplementationOnce(() => {
        throw new Error('Export failed');
      });

      expect(() => {
        imageOverlayService.exportAsImage(mockCanvas);
      }).toThrow('Export failed');
    });

    it('should handle missing canvas', () => {
      expect(() => {
        imageOverlayService.exportAsImage(null);
      }).toThrow('No canvas available for export');
    });
  });

  describe('Preview Generation', () => {
    const mockTemplate = {
      background_image_url: 'https://example.com/bg.jpg',
      background_image_width: 600,
      background_image_height: 800,
      text_overlays: {
        overlays: [
          {
            id: 'title',
            placeholder: '{{title}}',
            position: { x: 50, y: 100, width: 500, height: 60 },
            styling: { fontSize: 32, fontFamily: 'Arial' }
          }
        ]
      }
    };

    it('should generate preview with sample data', async () => {
      setTimeout(() => mockImage.onload(), 0);

      const result = await imageOverlayService.generatePreview(mockTemplate);
      
      expect(result).toBe('data:image/png;base64,mockImageData');
    });

    it('should use custom sample data', async () => {
      const customSampleData = {
        title: 'Custom Title',
        author: 'Custom Author'
      };

      setTimeout(() => mockImage.onload(), 0);

      const result = await imageOverlayService.generatePreview(mockTemplate, customSampleData);
      
      expect(result).toBe('data:image/png;base64,mockImageData');
    });
  });

  describe('Cache Management', () => {
    it('should clear image cache', () => {
      // Add something to cache first
      imageOverlayService.imageCache.set('test', 'value');
      
      imageOverlayService.clearCache();
      
      expect(imageOverlayService.imageCache.size).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle canvas context errors gracefully', () => {
      mockCanvas.getContext.mockReturnValueOnce(null);

      expect(() => {
        imageOverlayService.initializeCanvas(600, 800);
      }).toThrow('Failed to get 2D context from canvas');
    });

    it('should handle text rendering errors', () => {
      const ctx = mockCanvas.getContext('2d');
      ctx.fillText.mockImplementationOnce(() => {
        throw new Error('Text rendering failed');
      });

      expect(() => {
        imageOverlayService.renderTextOverlay('Test', { x: 0, y: 0, width: 100, height: 50 }, {});
      }).toThrow('Text rendering failed');
    });
  });

  describe('Performance', () => {
    it('should reuse canvas context efficiently', () => {
      imageOverlayService.initializeCanvas(600, 800);
      imageOverlayService.initializeCanvas(600, 800);
      
      // Should create canvas twice but context should be reused efficiently
      expect(document.createElement).toHaveBeenCalledTimes(2);
    });

    it('should cache images to avoid repeated loading', async () => {
      const imageUrl = 'https://example.com/bg.jpg';
      
      setTimeout(() => mockImage.onload(), 0);
      await imageOverlayService.loadBackgroundImage(imageUrl);
      
      // Second call should use cache
      const result = await imageOverlayService.loadBackgroundImage(imageUrl);
      expect(result).toBe(mockImage);
    });
  });
});
