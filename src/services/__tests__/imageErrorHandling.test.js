/**
 * Unit tests for image processing error handling and retry logic
 */

import { jest } from '@jest/globals';

// Mock fetch globally
global.fetch = jest.fn();

// Mock DOMParser
global.DOMParser = jest.fn().mockImplementation(() => ({
    parseFromString: jest.fn().mockReturnValue({
        body: {
            children: [],
            querySelectorAll: jest.fn().mockReturnValue([])
        }
    })
}));

// Mock DOMPurify
jest.mock('dompurify', () => ({
    default: {
        sanitize: jest.fn((html) => html)
    }
}));

import {
    validateImageUrl,
    validateImageFormat,
    downloadImage,
    shouldRetryImageDownload,
    extractAndProcessImages,
    processImageWithErrorHandling,
    createImageErrorSummary,
    generateImageErrorMessage,
    IMAGE_CONFIG
} from '../contentProcessingService.js';

describe('Image Error Handling', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        global.fetch.mockClear();
    });

    describe('validateImageUrl', () => {
        test('should validate correct HTTP URLs', () => {
            const result = validateImageUrl('https://example.com/image.jpg');
            expect(result.isValid).toBe(true);
            expect(result.url).toBe('https://example.com/image.jpg');
        });

        test('should validate correct HTTPS URLs', () => {
            const result = validateImageUrl('http://example.com/image.png');
            expect(result.isValid).toBe(true);
        });

        test('should reject empty URLs', () => {
            const result = validateImageUrl('');
            expect(result.isValid).toBe(false);
            expect(result.error).toContain('empty');
        });

        test('should reject null/undefined URLs', () => {
            expect(validateImageUrl(null).isValid).toBe(false);
            expect(validateImageUrl(undefined).isValid).toBe(false);
        });

        test('should reject non-HTTP protocols', () => {
            const result = validateImageUrl('ftp://example.com/image.jpg');
            expect(result.isValid).toBe(false);
            expect(result.error).toContain('HTTP and HTTPS');
        });

        test('should reject malformed URLs', () => {
            const result = validateImageUrl('not-a-url');
            expect(result.isValid).toBe(false);
            expect(result.error).toContain('Invalid URL format');
        });

        test('should handle URLs without image extensions', () => {
            const result = validateImageUrl('https://example.com/api/image');
            expect(result.isValid).toBe(true);
            expect(result.hasImageExtension).toBe(false);
        });
    });

    describe('validateImageFormat', () => {
        test('should validate supported image formats', () => {
            const formats = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

            formats.forEach(format => {
                const result = validateImageFormat(format);
                expect(result.isValid).toBe(true);
                expect(result.format).toBe(format);
            });
        });

        test('should reject unsupported formats', () => {
            const result = validateImageFormat('image/bmp');
            expect(result.isValid).toBe(false);
            expect(result.error).toContain('Unsupported image format');
        });

        test('should handle content type with charset', () => {
            const result = validateImageFormat('image/jpeg; charset=utf-8');
            expect(result.isValid).toBe(true);
            expect(result.format).toBe('image/jpeg');
        });

        test('should handle missing content type', () => {
            const result = validateImageFormat('');
            expect(result.isValid).toBe(false);
            expect(result.error).toContain('No content type');
        });
    });

    describe('shouldRetryImageDownload', () => {
        test('should not retry client errors (4xx)', () => {
            const error = new Error('Not found');
            error.status = 404;
            expect(shouldRetryImageDownload(error)).toBe(false);
        });

        test('should retry specific client errors', () => {
            const timeoutError = new Error('Timeout');
            timeoutError.status = 408;
            expect(shouldRetryImageDownload(timeoutError)).toBe(true);

            const rateLimitError = new Error('Rate limited');
            rateLimitError.status = 429;
            expect(shouldRetryImageDownload(rateLimitError)).toBe(true);
        });

        test('should retry server errors (5xx)', () => {
            const error = new Error('Server error');
            error.status = 500;
            expect(shouldRetryImageDownload(error)).toBe(true);
        });

        test('should retry network errors', () => {
            const abortError = new Error('Aborted');
            abortError.name = 'AbortError';
            expect(shouldRetryImageDownload(abortError)).toBe(true);

            const networkError = new Error('Network error');
            expect(shouldRetryImageDownload(networkError)).toBe(true);
        });

        test('should not retry validation errors', () => {
            const formatError = new Error('Unsupported format');
            expect(shouldRetryImageDownload(formatError)).toBe(false);

            const sizeError = new Error('Image too large');
            expect(shouldRetryImageDownload(sizeError)).toBe(false);
        });

        test('should not retry when explicitly marked non-retryable', () => {
            const error = new Error('Non-retryable error');
            error.retryable = false;
            expect(shouldRetryImageDownload(error)).toBe(false);
        });
    });

    describe('downloadImage', () => {
        test('should successfully download valid image', async () => {
            const mockArrayBuffer = new ArrayBuffer(1024);
            global.fetch.mockResolvedValue({
                ok: true,
                headers: {
                    get: jest.fn().mockImplementation((header) => {
                        if (header === 'content-type') return 'image/jpeg';
                        if (header === 'content-length') return '1024';
                        return null;
                    })
                },
                arrayBuffer: jest.fn().mockResolvedValue(mockArrayBuffer)
            });

            const result = await downloadImage('https://example.com/image.jpg');

            expect(result.success).toBe(true);
            expect(result.data).toBe(mockArrayBuffer);
            expect(result.contentType).toBe('image/jpeg');
            expect(result.size).toBe(1024);
        });

        test('should handle HTTP errors', async () => {
            global.fetch.mockResolvedValue({
                ok: false,
                status: 404,
                statusText: 'Not Found'
            });

            const result = await downloadImage('https://example.com/missing.jpg');

            expect(result.success).toBe(false);
            expect(result.error).toContain('404');
            expect(result.retryable).toBe(false);
        });

        test('should handle network timeouts', async () => {
            global.fetch.mockImplementation(() =>
                new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('AbortError')), 100);
                })
            );

            const result = await downloadImage('https://example.com/slow-image.jpg');

            expect(result.success).toBe(false);
            expect(result.retryable).toBe(true);
        });

        test('should reject oversized images', async () => {
            const largeSize = IMAGE_CONFIG.MAX_SIZE + 1000;
            global.fetch.mockResolvedValue({
                ok: true,
                headers: {
                    get: jest.fn().mockImplementation((header) => {
                        if (header === 'content-type') return 'image/jpeg';
                        if (header === 'content-length') return largeSize.toString();
                        return null;
                    })
                }
            });

            const result = await downloadImage('https://example.com/large-image.jpg');

            expect(result.success).toBe(false);
            expect(result.error).toContain('too large');
            expect(result.retryable).toBe(false);
        });

        test('should retry on retryable errors', async () => {
            let callCount = 0;
            global.fetch.mockImplementation(() => {
                callCount++;
                if (callCount < 3) {
                    return Promise.reject(new Error('Network error'));
                }
                return Promise.resolve({
                    ok: true,
                    headers: {
                        get: jest.fn().mockImplementation((header) => {
                            if (header === 'content-type') return 'image/jpeg';
                            return null;
                        })
                    },
                    arrayBuffer: jest.fn().mockResolvedValue(new ArrayBuffer(1024))
                });
            });

            const result = await downloadImage('https://example.com/retry-image.jpg');

            expect(result.success).toBe(true);
            expect(result.attempts).toBe(3);
            expect(callCount).toBe(3);
        });

        test('should fail after max retries', async () => {
            global.fetch.mockRejectedValue(new Error('Persistent network error'));

            const result = await downloadImage('https://example.com/failing-image.jpg');

            expect(result.success).toBe(false);
            expect(result.finalError).toBe(true);
            expect(result.attempts).toBe(IMAGE_CONFIG.MAX_RETRIES);
        });
    });

    describe('processImageWithErrorHandling', () => {
        test('should handle missing image source', async () => {
            const image = { alt: 'Test image' };
            const result = await processImageWithErrorHandling(image, 0);

            expect(result.success).toBe(false);
            expect(result.error).toContain('missing');
        });

        test('should process successful image download', async () => {
            const mockArrayBuffer = new ArrayBuffer(1024);
            global.fetch.mockResolvedValue({
                ok: true,
                headers: {
                    get: jest.fn().mockImplementation((header) => {
                        if (header === 'content-type') return 'image/png';
                        return null;
                    })
                },
                arrayBuffer: jest.fn().mockResolvedValue(mockArrayBuffer)
            });

            const image = { src: 'https://example.com/test.png', alt: 'Test image' };
            const result = await processImageWithErrorHandling(image, 0);

            expect(result.success).toBe(true);
            expect(result.downloadSuccess).toBe(true);
            expect(result.data).toBe(mockArrayBuffer);
            expect(result.contentType).toBe('image/png');
        });

        test('should handle download failures gracefully', async () => {
            global.fetch.mockRejectedValue(new Error('Network error'));

            const image = { src: 'https://example.com/fail.jpg', alt: 'Failing image' };
            const result = await processImageWithErrorHandling(image, 0);

            expect(result.success).toBe(false);
            expect(result.downloadSuccess).toBe(false);
            expect(result.error).toBeDefined();
        });
    });

    describe('extractAndProcessImages', () => {
        test('should handle content with no images', async () => {
            const content = '<p>No images here</p>';
            const result = await extractAndProcessImages(content, 'html');

            expect(result.totalImages).toBe(0);
            expect(result.successCount).toBe(0);
            expect(result.failureCount).toBe(0);
            expect(result.processedImages).toHaveLength(0);
        });

        test('should process multiple images with mixed results', async () => {
            // Mock DOM parsing to return images
            global.DOMParser = jest.fn().mockImplementation(() => ({
                parseFromString: jest.fn().mockReturnValue({
                    body: {
                        children: [],
                        querySelectorAll: jest.fn().mockReturnValue([
                            {
                                getAttribute: jest.fn().mockImplementation((attr) => {
                                    if (attr === 'src') return 'https://example.com/success.jpg';
                                    if (attr === 'alt') return 'Success image';
                                    return null;
                                })
                            },
                            {
                                getAttribute: jest.fn().mockImplementation((attr) => {
                                    if (attr === 'src') return 'https://example.com/fail.jpg';
                                    if (attr === 'alt') return 'Fail image';
                                    return null;
                                })
                            }
                        ])
                    }
                })
            }));

            // Mock fetch responses
            let callCount = 0;
            global.fetch.mockImplementation((url) => {
                callCount++;
                if (url.includes('success')) {
                    return Promise.resolve({
                        ok: true,
                        headers: {
                            get: jest.fn().mockImplementation((header) => {
                                if (header === 'content-type') return 'image/jpeg';
                                return null;
                            })
                        },
                        arrayBuffer: jest.fn().mockResolvedValue(new ArrayBuffer(1024))
                    });
                } else {
                    return Promise.reject(new Error('Network error'));
                }
            });

            const content = '<img src="https://example.com/success.jpg" alt="Success image"><img src="https://example.com/fail.jpg" alt="Fail image">';
            const result = await extractAndProcessImages(content, 'html');

            expect(result.totalImages).toBe(2);
            expect(result.successCount).toBe(1);
            expect(result.failureCount).toBe(1);
            expect(result.processedImages).toHaveLength(1);
            expect(result.failedImages).toHaveLength(1);
        });
    });

    describe('createImageErrorSummary', () => {
        test('should categorize different error types', () => {
            const failedImages = [
                { error: 'Network timeout occurred' },
                { error: 'Unsupported image format: image/bmp' },
                { error: 'Image too large: 15MB' },
                { error: 'CORS policy violation' },
                { error: 'Unknown error occurred' }
            ];

            const summary = createImageErrorSummary(failedImages);

            expect(summary.totalFailed).toBe(5);
            expect(summary.networkErrors).toHaveLength(2); // timeout + CORS
            expect(summary.formatErrors).toHaveLength(1);
            expect(summary.sizeErrors).toHaveLength(1);
            expect(summary.unknownErrors).toHaveLength(1);
        });
    });

    describe('generateImageErrorMessage', () => {
        test('should generate message for partial failures', () => {
            const imageStats = {
                totalImages: 5,
                successCount: 3,
                failureCount: 2,
                failedImages: [
                    { error: 'Network timeout' },
                    { error: 'Unsupported format' }
                ]
            };

            const message = generateImageErrorMessage(imageStats);

            expect(message).toContain('2 of 5 images could not be processed');
            expect(message).toContain('3 images');
            expect(message).toContain('network issues');
            expect(message).toContain('unsupported formats');
        });

        test('should generate message for complete failure', () => {
            const imageStats = {
                totalImages: 3,
                successCount: 0,
                failureCount: 3,
                failedImages: [
                    { error: 'Network error' },
                    { error: 'Network error' },
                    { error: 'Network error' }
                ]
            };

            const message = generateImageErrorMessage(imageStats);

            expect(message).toContain('All 3 images failed to process');
            expect(message).toContain('without images');
        });

        test('should return empty string for no failures', () => {
            const imageStats = {
                totalImages: 2,
                successCount: 2,
                failureCount: 0,
                failedImages: []
            };

            const message = generateImageErrorMessage(imageStats);

            expect(message).toBe('');
        });
    });
});

describe('Image Error Handling Integration', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        global.fetch.mockClear();
    });

    test('should handle complex image processing scenario', async () => {
        // Mock DOM parsing
        global.DOMParser = jest.fn().mockImplementation(() => ({
            parseFromString: jest.fn().mockReturnValue({
                body: {
                    children: [],
                    querySelectorAll: jest.fn().mockReturnValue([
                        {
                            getAttribute: jest.fn().mockImplementation((attr) => {
                                if (attr === 'src') return 'https://example.com/good.jpg';
                                if (attr === 'alt') return 'Good image';
                                return null;
                            })
                        },
                        {
                            getAttribute: jest.fn().mockImplementation((attr) => {
                                if (attr === 'src') return 'https://example.com/retry.jpg';
                                if (attr === 'alt') return 'Retry image';
                                return null;
                            })
                        },
                        {
                            getAttribute: jest.fn().mockImplementation((attr) => {
                                if (attr === 'src') return 'https://example.com/fail.jpg';
                                if (attr === 'alt') return 'Fail image';
                                return null;
                            })
                        }
                    ])
                }
            })
        }));

        // Mock different fetch behaviors
        let retryCount = 0;
        global.fetch.mockImplementation((url) => {
            if (url.includes('good')) {
                return Promise.resolve({
                    ok: true,
                    headers: {
                        get: jest.fn().mockImplementation((header) => {
                            if (header === 'content-type') return 'image/jpeg';
                            return null;
                        })
                    },
                    arrayBuffer: jest.fn().mockResolvedValue(new ArrayBuffer(1024))
                });
            } else if (url.includes('retry')) {
                retryCount++;
                if (retryCount < 2) {
                    return Promise.reject(new Error('Temporary network error'));
                } else {
                    return Promise.resolve({
                        ok: true,
                        headers: {
                            get: jest.fn().mockImplementation((header) => {
                                if (header === 'content-type') return 'image/png';
                                return null;
                            })
                        },
                        arrayBuffer: jest.fn().mockResolvedValue(new ArrayBuffer(2048))
                    });
                }
            } else {
                return Promise.resolve({
                    ok: false,
                    status: 404,
                    statusText: 'Not Found'
                });
            }
        });

        const content = `
            <img src="https://example.com/good.jpg" alt="Good image">
            <img src="https://example.com/retry.jpg" alt="Retry image">
            <img src="https://example.com/fail.jpg" alt="Fail image">
        `;

        const result = await extractAndProcessImages(content, 'html');

        expect(result.totalImages).toBe(3);
        expect(result.successCount).toBe(2); // good + retry after retry
        expect(result.failureCount).toBe(1); // fail
        expect(result.processedImages).toHaveLength(2);
        expect(result.failedImages).toHaveLength(1);

        // Check that retry worked
        const retryImage = result.processedImages.find(img => img.src.includes('retry'));
        expect(retryImage).toBeDefined();
        expect(retryImage.attempts).toBeGreaterThan(1);
    });
});