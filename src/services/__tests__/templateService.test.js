/**
 * Template Service Tests
 * Tests for template fetching, caching, and search functionality
 */

import { 
  fetchCoverTemplates, 
  fetchTemplateById, 
  fetchTemplateCategories,
  searchTemplates,
  clearTemplateCache 
} from '../templateService.js';

// Mock Supabase
jest.mock('../../lib/supabase.js', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            range: jest.fn(() => ({
              then: jest.fn()
            }))
          }))
        }))
      }))
    }))
  }
}));

// Mock error monitor
jest.mock('../../utils/errorMonitor.js', () => ({
  __esModule: true,
  default: {
    logError: jest.fn()
  },
  ErrorSeverity: {
    HIGH: 'HIGH',
    MEDIUM: 'MEDIUM',
    LOW: 'LOW'
  }
}));

describe('Template Service', () => {
  beforeEach(() => {
    // Clear cache before each test
    clearTemplateCache();
    jest.clearAllMocks();
  });

  describe('fetchCoverTemplates', () => {
    it('should fetch templates successfully', async () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'Modern Business',
          category: 'business',
          template_definition: { templateId: 'template-1' }
        },
        {
          id: 'template-2',
          name: 'Creative Design',
          category: 'creative',
          template_definition: { templateId: 'template-2' }
        }
      ];

      // Mock successful response
      const { supabase } = require('../../lib/supabase.js');
      const mockQuery = {
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        range: jest.fn().mockResolvedValue({
          data: mockTemplates,
          error: null,
          count: mockTemplates.length
        }),
        overlaps: jest.fn().mockReturnThis()
      };
      
      supabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue(mockQuery)
      });

      const result = await fetchCoverTemplates();

      expect(result.success).toBe(true);
      expect(result.templates).toEqual(mockTemplates);
      expect(result.total).toBe(mockTemplates.length);
    });

    it('should handle fetch errors gracefully', async () => {
      const { supabase } = require('../../lib/supabase.js');
      const mockQuery = {
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        range: jest.fn().mockResolvedValue({
          data: null,
          error: { message: 'Database connection failed' },
          count: 0
        }),
        overlaps: jest.fn().mockReturnThis()
      };
      
      supabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue(mockQuery)
      });

      const result = await fetchCoverTemplates();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Database connection failed');
      expect(result.templates).toEqual([]);
    });

    it('should apply category filter', async () => {
      const { supabase } = require('../../lib/supabase.js');
      const mockQuery = {
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        range: jest.fn().mockResolvedValue({
          data: [],
          error: null,
          count: 0
        }),
        overlaps: jest.fn().mockReturnThis()
      };
      
      supabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue(mockQuery)
      });

      await fetchCoverTemplates({ category: 'business' });

      expect(mockQuery.eq).toHaveBeenCalledWith('category', 'business');
    });
  });

  describe('fetchTemplateById', () => {
    it('should fetch single template successfully', async () => {
      const mockTemplate = {
        id: 'template-1',
        name: 'Modern Business',
        template_definition: { templateId: 'template-1' }
      };

      const { supabase } = require('../../lib/supabase.js');
      const mockQuery = {
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockTemplate,
          error: null
        })
      };
      
      supabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue(mockQuery)
      });

      const result = await fetchTemplateById('template-1');

      expect(result.success).toBe(true);
      expect(result.template).toEqual(mockTemplate);
    });

    it('should handle template not found', async () => {
      const { supabase } = require('../../lib/supabase.js');
      const mockQuery = {
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: null
        })
      };
      
      supabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue(mockQuery)
      });

      const result = await fetchTemplateById('nonexistent');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Template not found');
      expect(result.template).toBe(null);
    });
  });

  describe('fetchTemplateCategories', () => {
    it('should fetch unique categories', async () => {
      const mockTemplates = [
        { category: 'business' },
        { category: 'creative' },
        { category: 'business' }, // duplicate
        { category: 'academic' }
      ];

      const { supabase } = require('../../lib/supabase.js');
      const mockQuery = {
        eq: jest.fn().mockResolvedValue({
          data: mockTemplates,
          error: null
        })
      };
      
      supabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue(mockQuery)
      });

      const result = await fetchTemplateCategories();

      expect(result.success).toBe(true);
      expect(result.categories).toEqual(['business', 'creative', 'academic']);
      expect(result.categories.length).toBe(3); // No duplicates
    });
  });

  describe('searchTemplates', () => {
    it('should search templates by name and description', async () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'Modern Business Report',
          description: 'Professional business template'
        }
      ];

      const { supabase } = require('../../lib/supabase.js');
      const mockQuery = {
        eq: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        overlaps: jest.fn().mockResolvedValue({
          data: mockTemplates,
          error: null
        })
      };
      
      supabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue(mockQuery)
      });

      const result = await searchTemplates('business');

      expect(result.success).toBe(true);
      expect(result.templates).toEqual(mockTemplates);
      expect(mockQuery.or).toHaveBeenCalledWith('name.ilike.%business%,description.ilike.%business%');
    });
  });
});
