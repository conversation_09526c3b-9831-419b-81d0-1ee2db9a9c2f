/**
 * Integration Tests for DOCX Generation Service with Content Processing
 * 
 * Tests the integration between content processing and DOCX generation
 */

import {
    generateDocxFromContent,
    generateAndDownloadDocxFromContent
} from '../docxGenerationService';

// Mock the docx library
jest.mock('docx', () => ({
    Document: jest.fn().mockImplementation((config) => ({ config })),
    Packer: {
        toBlob: jest.fn().mockResolvedValue(new Blob(['mock docx content'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }))
    },
    Paragraph: jest.fn().mockImplementation((config) => ({ type: 'paragraph', config })),
    TextRun: jest.fn().mockImplementation((config) => ({ type: 'textrun', config })),
    HeadingLevel: {
        HEADING_1: 'HEADING_1',
        HEADING_2: 'HEADING_2',
        HEADING_3: 'HEADING_3'
    },
    AlignmentType: {
        CENTER: 'CENTER'
    }
}));

// Mock DOMPurify
jest.mock('dompurify', () => ({
    sanitize: jest.fn((html) => html)
}));

// Mock DOM APIs
global.DOMParser = jest.fn(() => ({
    parseFromString: jest.fn((html) => ({
        body: {
            children: [
                {
                    tagName: 'H1',
                    textContent: 'Test Title',
                    children: { length: 0 },
                    childNodes: []
                },
                {
                    tagName: 'P',
                    textContent: 'Test paragraph content',
                    children: { length: 0 },
                    childNodes: []
                }
            ]
        }
    }))
}));

global.URL = {
    createObjectURL: jest.fn(() => 'mock-url'),
    revokeObjectURL: jest.fn()
};

const mockCreateElement = jest.fn(() => ({
    href: '',
    download: '',
    click: jest.fn()
}));

global.document = {
    createElement: mockCreateElement,
    body: {
        appendChild: jest.fn(),
        removeChild: jest.fn()
    }
};

describe('DOCX Generation Service Integration', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('generateDocxFromContent', () => {
        test('should generate DOCX from HTML content', async () => {
            const documentData = {
                title: 'Test Document',
                author: 'Test Author',
                description: 'Test Description'
            };

            const htmlContent = '<h1>Test Title</h1><p>This is a <strong>test</strong> paragraph.</p>';

            const result = await generateDocxFromContent(documentData, htmlContent, 'html');

            expect(result.success).toBe(true);
            expect(result.blob).toBeDefined();
            expect(result.contentType).toBe('html');
            expect(result.processedContent).toBeDefined();
            expect(result.processedContent.processedContent).toHaveLength(2); // H1 and P
        });

        test('should generate DOCX from markdown content', async () => {
            const documentData = {
                title: 'Markdown Document',
                author: 'Test Author',
                description: 'Test Description'
            };

            const markdownContent = '# Test Title\n\nThis is a **test** paragraph with *italic* text.';

            const result = await generateDocxFromContent(documentData, markdownContent, 'markdown');

            expect(result.success).toBe(true);
            expect(result.blob).toBeDefined();
            expect(result.contentType).toBe('markdown');
            expect(result.processedContent).toBeDefined();
        });

        test('should auto-detect content type', async () => {
            const documentData = {
                title: 'Auto-detect Document',
                author: 'Test Author'
            };

            const htmlContent = '<h1>HTML Title</h1><p>HTML content</p>';

            const result = await generateDocxFromContent(documentData, htmlContent);

            expect(result.success).toBe(true);
            expect(result.contentType).toBe('html');
        });

        test('should handle empty content', async () => {
            const documentData = {
                title: 'Empty Document',
                author: 'Test Author'
            };

            const result = await generateDocxFromContent(documentData, '');

            expect(result.success).toBe(true);
            expect(result.blob).toBeDefined();
        });

        test('should handle content processing errors gracefully', async () => {
            // Mock DOMParser to throw error
            global.DOMParser = jest.fn(() => ({
                parseFromString: jest.fn(() => {
                    throw new Error('Parse error');
                })
            }));

            const documentData = {
                title: 'Error Document',
                author: 'Test Author'
            };

            const result = await generateDocxFromContent(documentData, '<invalid>html');

            // Should still succeed but with error content
            expect(result.success).toBe(true);
            expect(result.blob).toBeDefined();
        });
    });

    describe('generateAndDownloadDocxFromContent', () => {
        test('should generate and trigger download', async () => {
            const documentData = {
                title: 'Download Test',
                author: 'Test Author'
            };

            const htmlContent = '<h1>Download Test</h1><p>Content for download test.</p>';

            const result = await generateAndDownloadDocxFromContent(documentData, htmlContent);

            expect(result.success).toBe(true);
            expect(result.message).toBe('DOCX document generated and downloaded successfully');
            expect(result.processedContent).toBeDefined();
            expect(result.contentType).toBe('html');

            // Verify download function completed successfully
            // (The actual download mechanism is tested separately)
        });

        test('should handle generation errors', async () => {
            // Mock Packer to throw error
            const { Packer } = require('docx');
            Packer.toBlob.mockRejectedValueOnce(new Error('DOCX generation failed'));

            const documentData = {
                title: 'Error Test',
                author: 'Test Author'
            };

            const result = await generateAndDownloadDocxFromContent(documentData, '<h1>Test</h1>');

            expect(result.success).toBe(false);
            expect(result.error).toBeDefined();
        });
    });

    describe('Content Processing Integration', () => {
        test('should process complex HTML with multiple elements', async () => {
            const documentData = {
                title: 'Complex Document',
                author: 'Test Author'
            };

            const complexHtml = `
                <h1>Main Title</h1>
                <h2>Subtitle</h2>
                <p>This is a paragraph with <strong>bold</strong> and <em>italic</em> text.</p>
                <ul>
                    <li>List item 1</li>
                    <li>List item 2</li>
                </ul>
                <img src="test.jpg" alt="Test image" />
            `;

            // Mock more complex DOM structure
            global.DOMParser = jest.fn(() => ({
                parseFromString: jest.fn(() => ({
                    body: {
                        children: [
                            { tagName: 'H1', textContent: 'Main Title', children: { length: 0 }, childNodes: [] },
                            { tagName: 'H2', textContent: 'Subtitle', children: { length: 0 }, childNodes: [] },
                            { tagName: 'P', textContent: 'This is a paragraph with bold and italic text.', children: { length: 0 }, childNodes: [] },
                            {
                                tagName: 'UL',
                                querySelectorAll: jest.fn(() => [
                                    { textContent: 'List item 1', children: { length: 0 }, childNodes: [] },
                                    { textContent: 'List item 2', children: { length: 0 }, childNodes: [] }
                                ])
                            },
                            {
                                tagName: 'IMG',
                                getAttribute: jest.fn((attr) => {
                                    return attr === 'src' ? 'test.jpg' : (attr === 'alt' ? 'Test image' : null);
                                }),
                                outerHTML: '<img src="test.jpg" alt="Test image" />'
                            }
                        ]
                    }
                }))
            }));

            const result = await generateDocxFromContent(documentData, complexHtml, 'html');

            expect(result.success).toBe(true);
            expect(result.processedContent.processedContent.length).toBeGreaterThan(3);
        });

        test('should process markdown with various elements', async () => {
            const documentData = {
                title: 'Markdown Test',
                author: 'Test Author'
            };

            const markdownContent = `
# Main Title

## Subtitle

This is a paragraph with **bold** and *italic* text.

- List item 1
- List item 2

![Test image](test.jpg)

[Link text](http://example.com)
            `.trim();

            const result = await generateDocxFromContent(documentData, markdownContent, 'markdown');

            expect(result.success).toBe(true);
            expect(result.contentType).toBe('markdown');
            expect(result.processedContent).toBeDefined();
        });
    });
});