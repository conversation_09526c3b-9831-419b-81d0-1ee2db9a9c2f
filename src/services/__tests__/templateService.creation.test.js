/**
 * Template Service Creation Tests
 * Tests for template creation, validation, and image upload functionality
 */

import {
  validateTemplateData,
  uploadBackgroundImage,
  createTemplate,
  getDefaultTextOverlays
} from '../templateService';

// Mock Supabase
jest.mock('../../lib/supabase.js', () => ({
  supabase: {
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn(),
        getPublicUrl: jest.fn()
      }))
    },
    from: jest.fn(() => ({
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn()
        }))
      }))
    }))
  }
}));

// Mock error monitor
jest.mock('../../utils/errorMonitor.js', () => ({
  __esModule: true,
  default: {
    captureError: jest.fn()
  },
  ErrorSeverity: {
    HIGH: 'HIGH',
    MEDIUM: 'MEDIUM',
    LOW: 'LOW'
  }
}));

describe('Template Service - Creation Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateTemplateData', () => {
    const validTemplateData = {
      name: 'Test Template',
      description: 'A test template',
      category: 'Business',
      tags: ['test'],
      background_image_url: 'https://example.com/image.jpg',
      background_image_width: 1200,
      background_image_height: 1600,
      text_overlays: {
        overlays: [{
          id: 'title',
          type: 'text',
          placeholder: '{{title}}',
          position: { x: 50, y: 100, width: 400, height: 60 },
          styling: {
            fontSize: 24,
            fontFamily: 'Arial',
            color: '#000000'
          }
        }]
      }
    };

    test('should validate valid template data', () => {
      const result = validateTemplateData(validTemplateData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should reject template data without required fields', () => {
      const invalidData = { ...validTemplateData };
      delete invalidData.name;
      delete invalidData.category;

      const result = validateTemplateData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Template name is required');
      expect(result.errors).toContain('Template category is required');
    });

    test('should validate text overlays structure', () => {
      const invalidData = {
        ...validTemplateData,
        text_overlays: {
          overlays: [{
            id: 'title',
            // Missing required fields
          }]
        }
      };

      const result = validateTemplateData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Position configuration'))).toBe(true);
    });
  });

  describe('uploadBackgroundImage', () => {
    const mockFile = {
      name: 'test-image.jpg',
      type: 'image/jpeg',
      size: 1024 * 1024 // 1MB
    };

    test('should upload valid image files', async () => {
      const { supabase } = require('../../lib/supabase.js');
      
      // Mock successful upload
      supabase.storage.from().upload.mockResolvedValue({
        data: { path: 'test-path' },
        error: null
      });
      
      supabase.storage.from().getPublicUrl.mockReturnValue({
        data: { publicUrl: 'https://example.com/test-image.jpg' }
      });

      // Mock image dimensions
      global.Image = class {
        constructor() {
          setTimeout(() => {
            this.naturalWidth = 1200;
            this.naturalHeight = 1600;
            this.onload();
          }, 0);
        }
      };
      global.URL = {
        createObjectURL: jest.fn(() => 'blob:test')
      };

      const result = await uploadBackgroundImage(mockFile, 'test-template-id');
      
      expect(result.success).toBe(true);
      expect(result.url).toBe('https://example.com/test-image.jpg');
      expect(result.dimensions).toEqual({ width: 1200, height: 1600 });
    });

    test('should reject invalid file types', async () => {
      const invalidFile = {
        ...mockFile,
        type: 'text/plain'
      };

      const result = await uploadBackgroundImage(invalidFile, 'test-template-id');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid file type');
    });

    test('should reject files that are too large', async () => {
      const largeFile = {
        ...mockFile,
        size: 11 * 1024 * 1024 // 11MB
      };

      const result = await uploadBackgroundImage(largeFile, 'test-template-id');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('too large');
    });

    test('should handle upload errors', async () => {
      const { supabase } = require('../../lib/supabase.js');
      
      // Mock upload error
      supabase.storage.from().upload.mockResolvedValue({
        data: null,
        error: { message: 'Upload failed' }
      });

      const result = await uploadBackgroundImage(mockFile, 'test-template-id');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Upload failed');
    });
  });

  describe('createTemplate', () => {
    const validTemplateData = {
      name: 'Test Template',
      description: 'A test template',
      category: 'Business',
      tags: ['test'],
      background_image_url: 'https://example.com/image.jpg',
      background_image_width: 1200,
      background_image_height: 1600,
      text_overlays: {
        overlays: [{
          id: 'title',
          type: 'text',
          placeholder: '{{title}}',
          position: { x: 50, y: 100, width: 400, height: 60 },
          styling: {
            fontSize: 24,
            fontFamily: 'Arial',
            color: '#000000'
          }
        }]
      }
    };

    test('should create valid templates', async () => {
      const { supabase } = require('../../lib/supabase.js');
      
      const mockCreatedTemplate = {
        id: 'template-123',
        ...validTemplateData,
        created_at: new Date().toISOString()
      };

      // Mock successful database insert
      supabase.from().insert().select().single.mockResolvedValue({
        data: mockCreatedTemplate,
        error: null
      });

      const result = await createTemplate(validTemplateData);
      
      expect(result.success).toBe(true);
      expect(result.template).toEqual(mockCreatedTemplate);
      expect(supabase.from).toHaveBeenCalledWith('cover_templates');
    });

    test('should reject invalid template data', async () => {
      const invalidData = { ...validTemplateData };
      delete invalidData.name; // Remove required field

      const result = await createTemplate(invalidData);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Validation failed');
      expect(result.errors).toContain('Template name is required');
    });

    test('should handle database errors', async () => {
      const { supabase } = require('../../lib/supabase.js');
      
      // Mock database error
      supabase.from().insert().select().single.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      });

      const result = await createTemplate(validTemplateData);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Database error');
    });
  });

  describe('getDefaultTextOverlays', () => {
    test('should return standard layout by default', () => {
      const overlays = getDefaultTextOverlays();
      expect(overlays.overlays).toHaveLength(3); // title, author, description
      expect(overlays.overlays[0].id).toBe('title');
      expect(overlays.overlays[1].id).toBe('author');
      expect(overlays.overlays[2].id).toBe('description');
    });

    test('should return minimal layout', () => {
      const overlays = getDefaultTextOverlays('minimal');
      expect(overlays.overlays).toHaveLength(1); // title only
      expect(overlays.overlays[0].id).toBe('title');
    });

    test('should return professional layout', () => {
      const overlays = getDefaultTextOverlays('professional');
      expect(overlays.overlays).toHaveLength(3); // title, author, subtitle
      expect(overlays.overlays[0].id).toBe('title');
      expect(overlays.overlays[1].id).toBe('author');
      expect(overlays.overlays[2].id).toBe('subtitle');
    });

    test('should return custom layout', () => {
      const overlays = getDefaultTextOverlays('custom');
      expect(overlays.overlays).toHaveLength(2); // title, author
      expect(overlays.overlays[0].id).toBe('title');
      expect(overlays.overlays[1].id).toBe('author');
    });

    test('should fallback to standard for invalid layout types', () => {
      const overlays = getDefaultTextOverlays('invalid-layout');
      expect(overlays.overlays).toHaveLength(3); // standard layout
    });

    test('should have proper overlay structure', () => {
      const overlays = getDefaultTextOverlays('standard');
      const titleOverlay = overlays.overlays[0];
      
      expect(titleOverlay).toHaveProperty('id');
      expect(titleOverlay).toHaveProperty('type', 'text');
      expect(titleOverlay).toHaveProperty('placeholder');
      expect(titleOverlay).toHaveProperty('position');
      expect(titleOverlay).toHaveProperty('styling');
      
      expect(titleOverlay.position).toHaveProperty('x');
      expect(titleOverlay.position).toHaveProperty('y');
      expect(titleOverlay.position).toHaveProperty('width');
      expect(titleOverlay.position).toHaveProperty('height');
      
      expect(titleOverlay.styling).toHaveProperty('fontSize');
      expect(titleOverlay.styling).toHaveProperty('fontFamily');
      expect(titleOverlay.styling).toHaveProperty('color');
    });
  });
});
