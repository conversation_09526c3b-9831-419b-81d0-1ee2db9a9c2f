/**
 * Test suite for Export Service
 * Verifies that exports only include block-based images, not legacy placedImages
 */

// Import the internal function for testing
import { exportAsHtml, exportAsPdf } from '../exportService';

// Mock browser APIs for testing
global.URL = {
  createObjectURL: jest.fn(() => 'mock-blob-url')
};

global.window = {
  open: jest.fn(() => ({
    document: {
      write: jest.fn(),
      close: jest.fn()
    },
    print: jest.fn(),
    close: jest.fn()
  }))
};

// Mock document data with both legacy placedImages and block-based images
const mockDocumentData = {
  title: 'Test Document',
  author: 'Test Author',
  description: 'Test Description'
};

const mockGeneratedContentWithLegacyImages = {
  chapters: [
    {
      id: 'chapter-1',
      number: 1,
      title: 'Chapter 1',
      content: '# Chapter 1\n\nThis is chapter content without images.'
    },
    {
      id: 'chapter-2', 
      number: 2,
      title: 'Chapter 2',
      content: '# Chapter 2\n\nThis chapter has a block-based image:\n\n![Test Image](https://example.com/test-image.jpg)\n\nMore content here.'
    }
  ],
  // Legacy placedImages that should NOT appear in exports
  placedImages: {
    'chapter-1': [
      {
        id: 'legacy-image-1',
        url: 'https://example.com/legacy-image.jpg',
        description: 'Legacy Image',
        position: 'top',
        photographer: 'Legacy Photographer'
      }
    ]
  }
};

const mockGeneratedContentBlockOnly = {
  chapters: [
    {
      id: 'chapter-1',
      number: 1,
      title: 'Chapter 1',
      content: '# Chapter 1\n\nThis chapter has a block-based image:\n\n![Block Image](https://example.com/block-image.jpg)\n\nMore content here.'
    }
  ]
  // No placedImages - clean block-based content
};

// Test the markdown to HTML conversion directly
const markdownToHtml = (markdown) => {
  if (!markdown) return '';

  return markdown
    // Images - convert to HTML img tags
    .replace(/!\[(.*?)\]\((.*?)\)/gim, '<img src="$2" alt="$1" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin: 20px 0;" />')
    // Headers
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    // Bold
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    // Italic
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    // Lists
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/gims, '<ul>$1</ul>')
    // Line breaks
    .replace(/\n/gim, '<br>');
};

describe('Export Service', () => {
  describe('Markdown to HTML Conversion', () => {
    test('should convert block-based images to HTML img tags', () => {
      const markdownWithImage = '# Chapter 1\n\nThis chapter has an image:\n\n![Test Image](https://example.com/test-image.jpg)\n\nMore content here.';

      const htmlOutput = markdownToHtml(markdownWithImage);

      expect(htmlOutput).toContain('<img src="https://example.com/test-image.jpg"');
      expect(htmlOutput).toContain('alt="Test Image"');
      expect(htmlOutput).toContain('style="max-width: 100%');
    });

    test('should handle content without images', () => {
      const markdownWithoutImages = '# Chapter 1\n\nThis is just text content.';

      const htmlOutput = markdownToHtml(markdownWithoutImages);

      expect(htmlOutput).toContain('<h1>Chapter 1</h1>');
      expect(htmlOutput).toContain('This is just text content');
      expect(htmlOutput).not.toContain('<img');
    });
  });

  describe('HTML Export', () => {
    test('should include block-based images in HTML output', async () => {
      // Mock the downloadFile function
      const mockDownloadFile = jest.fn();
      global.downloadFile = mockDownloadFile;

      const result = await exportAsHtml(mockDocumentData, mockGeneratedContentBlockOnly);

      expect(result.success).toBe(true);
      expect(mockDownloadFile).toHaveBeenCalled();

      const htmlContent = mockDownloadFile.mock.calls[0][0];
      expect(htmlContent).toContain('<img src="https://example.com/block-image.jpg"');
      expect(htmlContent).toContain('alt="Block Image"');
    });

    test('should NOT include legacy placedImages in HTML output', async () => {
      const mockDownloadFile = jest.fn();
      global.downloadFile = mockDownloadFile;

      const result = await exportAsHtml(mockDocumentData, mockGeneratedContentWithLegacyImages);
      
      expect(result.success).toBe(true);
      
      const htmlContent = mockDownloadFile.mock.calls[0][0];
      // Should include block-based image
      expect(htmlContent).toContain('<img src="https://example.com/test-image.jpg"');
      // Should NOT include legacy image
      expect(htmlContent).not.toContain('https://example.com/legacy-image.jpg');
      expect(htmlContent).not.toContain('Legacy Image');
    });

    test('should handle content without any images', async () => {
      const mockDownloadFile = jest.fn();
      global.downloadFile = mockDownloadFile;

      const contentWithoutImages = {
        chapters: [
          {
            id: 'chapter-1',
            number: 1,
            title: 'Chapter 1',
            content: '# Chapter 1\n\nThis is just text content.'
          }
        ]
      };

      const result = await exportAsHtml(mockDocumentData, contentWithoutImages);
      
      expect(result.success).toBe(true);
      
      const htmlContent = mockDownloadFile.mock.calls[0][0];
      expect(htmlContent).toContain('This is just text content');
      expect(htmlContent).not.toContain('<img');
    });
  });

  describe('PDF Export', () => {
    test('should use block-based images for PDF generation', async () => {
      // Mock window.open and related functions
      const mockPrintWindow = {
        document: {
          write: jest.fn(),
          close: jest.fn()
        },
        print: jest.fn(),
        close: jest.fn(),
        onload: null
      };
      
      global.window = {
        open: jest.fn(() => mockPrintWindow)
      };

      const result = await exportAsPdf(mockDocumentData, mockGeneratedContentBlockOnly);
      
      expect(result.success).toBe(true);
      expect(global.window.open).toHaveBeenCalledWith('', '_blank');
      expect(mockPrintWindow.document.write).toHaveBeenCalled();
      
      const htmlContent = mockPrintWindow.document.write.mock.calls[0][0];
      expect(htmlContent).toContain('<img src="https://example.com/block-image.jpg"');
    });
  });
});
