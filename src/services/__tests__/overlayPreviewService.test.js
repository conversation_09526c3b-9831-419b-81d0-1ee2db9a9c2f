import overlayPreviewService from '../overlayPreviewService';
import imageOverlayService from '../imageOverlayService';

// Mock the image overlay service
jest.mock('../imageOverlayService', () => ({
  renderTemplateWithCustomizations: jest.fn(),
  exportAsImage: jest.fn()
}));

// Mock error monitor
jest.mock('../../utils/errorMonitor', () => ({
  captureError: jest.fn(),
  ErrorSeverity: {
    MEDIUM: 'medium'
  }
}));

describe('OverlayPreviewService', () => {
  const mockTemplate = {
    id: 'test-template',
    name: 'Test Template',
    category: 'Business',
    text_overlays: {
      overlays: [
        {
          id: 'title',
          placeholder: '{{title}}',
          position: { x: 50, y: 100, width: 400, height: 60 },
          styling: {
            fontSize: 32,
            color: '#000000',
            textAlign: 'center'
          }
        }
      ]
    }
  };

  const mockDocumentData = {
    title: 'Test Document',
    author: 'Test Author',
    description: 'Test Description'
  };

  const mockCustomizations = {
    title: {
      styling: {
        fontSize: 48,
        color: '#FF0000'
      },
      position: {
        x: 100
      }
    }
  };

  const mockCanvas = {
    toDataURL: jest.fn(() => 'data:image/png;base64,mock-image-data')
  };

  beforeEach(() => {
    jest.clearAllMocks();
    imageOverlayService.renderTemplateWithCustomizations.mockResolvedValue(mockCanvas);
    imageOverlayService.exportAsImage.mockReturnValue('data:image/png;base64,mock-image-data');
  });

  afterEach(() => {
    overlayPreviewService.clearCache();
    overlayPreviewService.cancelPendingUpdates();
  });

  test('generates preview with customizations', async () => {
    const result = await overlayPreviewService.generatePreviewWithCustomizations(
      mockTemplate,
      mockDocumentData,
      mockCustomizations
    );

    expect(imageOverlayService.renderTemplateWithCustomizations).toHaveBeenCalledWith(
      mockTemplate,
      expect.objectContaining({
        title: 'Test Document',
        author: 'Test Author',
        description: 'Test Description'
      }),
      mockCustomizations
    );

    expect(result).toEqual({
      coverHTML: expect.stringContaining('data:image/png;base64,mock-image-data'),
      coverImageData: 'data:image/png;base64,mock-image-data',
      metadata: {
        templateId: 'test-template',
        templateName: 'Test Template',
        templateCategory: 'Business',
        documentData: expect.any(Object),
        customizations: mockCustomizations,
        previewType: 'cover-with-customizations',
        generatedAt: expect.any(String)
      }
    });
  });

  test('uses cache for repeated requests', async () => {
    // First request
    const result1 = await overlayPreviewService.generatePreviewWithCustomizations(
      mockTemplate,
      mockDocumentData,
      mockCustomizations
    );

    // Second request with same parameters
    const result2 = await overlayPreviewService.generatePreviewWithCustomizations(
      mockTemplate,
      mockDocumentData,
      mockCustomizations
    );

    // Should only call render service once
    expect(imageOverlayService.renderTemplateWithCustomizations).toHaveBeenCalledTimes(1);
    expect(result1).toEqual(result2);
  });

  test('bypasses cache when useCache is false', async () => {
    // First request
    await overlayPreviewService.generatePreviewWithCustomizations(
      mockTemplate,
      mockDocumentData,
      mockCustomizations,
      { useCache: false }
    );

    // Second request with same parameters but cache disabled
    await overlayPreviewService.generatePreviewWithCustomizations(
      mockTemplate,
      mockDocumentData,
      mockCustomizations,
      { useCache: false }
    );

    // Should call render service twice
    expect(imageOverlayService.renderTemplateWithCustomizations).toHaveBeenCalledTimes(2);
  });

  test('queues preview updates with debouncing', async () => {
    jest.useFakeTimers();

    const updateFunction = jest.fn().mockResolvedValue('update-result');

    // Queue multiple updates quickly
    const promise1 = overlayPreviewService.queuePreviewUpdate('test-update', updateFunction, 300);
    const promise2 = overlayPreviewService.queuePreviewUpdate('test-update', updateFunction, 300);
    const promise3 = overlayPreviewService.queuePreviewUpdate('test-update', updateFunction, 300);

    // Update function should not be called yet
    expect(updateFunction).not.toHaveBeenCalled();

    // Fast forward time
    jest.advanceTimersByTime(300);

    // Wait for promises to resolve
    await Promise.all([promise1, promise2, promise3]);

    // Update function should only be called once (debounced)
    expect(updateFunction).toHaveBeenCalledTimes(1);

    jest.useRealTimers();
  });

  test('generates cache key correctly', () => {
    const key1 = overlayPreviewService.generateCacheKey(
      mockTemplate,
      mockDocumentData,
      mockCustomizations
    );

    const key2 = overlayPreviewService.generateCacheKey(
      mockTemplate,
      mockDocumentData,
      mockCustomizations
    );

    // Same parameters should generate same key
    expect(key1).toBe(key2);

    // Different customizations should generate different key
    const differentCustomizations = {
      title: {
        styling: {
          fontSize: 36
        }
      }
    };

    const key3 = overlayPreviewService.generateCacheKey(
      mockTemplate,
      mockDocumentData,
      differentCustomizations
    );

    expect(key1).not.toBe(key3);
  });

  test('clears cache correctly', async () => {
    // Generate some cached previews
    await overlayPreviewService.generatePreviewWithCustomizations(
      mockTemplate,
      mockDocumentData,
      mockCustomizations
    );

    const stats1 = overlayPreviewService.getCacheStats();
    expect(stats1.cacheSize).toBe(1);

    // Clear cache
    overlayPreviewService.clearCache();

    const stats2 = overlayPreviewService.getCacheStats();
    expect(stats2.cacheSize).toBe(0);
  });

  test('clears cache for specific template', async () => {
    const template2 = { ...mockTemplate, id: 'template-2' };

    // Generate previews for two templates
    await overlayPreviewService.generatePreviewWithCustomizations(
      mockTemplate,
      mockDocumentData,
      mockCustomizations
    );

    await overlayPreviewService.generatePreviewWithCustomizations(
      template2,
      mockDocumentData,
      mockCustomizations
    );

    const stats1 = overlayPreviewService.getCacheStats();
    expect(stats1.cacheSize).toBe(2);

    // Clear cache for specific template
    overlayPreviewService.clearCache('test-template');

    const stats2 = overlayPreviewService.getCacheStats();
    expect(stats2.cacheSize).toBe(1);
  });

  test('handles errors gracefully', async () => {
    const error = new Error('Render failed');
    imageOverlayService.renderTemplateWithCustomizations.mockRejectedValue(error);

    await expect(
      overlayPreviewService.generatePreviewWithCustomizations(
        mockTemplate,
        mockDocumentData,
        mockCustomizations
      )
    ).rejects.toThrow('Render failed');
  });

  test('cancels pending updates', async () => {
    jest.useFakeTimers();

    const updateFunction = jest.fn().mockResolvedValue('update-result');

    // Queue an update
    const promise = overlayPreviewService.queuePreviewUpdate('test-update', updateFunction, 300);

    // Cancel pending updates
    overlayPreviewService.cancelPendingUpdates();

    // Fast forward time
    jest.advanceTimersByTime(300);

    // Promise should be rejected
    await expect(promise).rejects.toThrow('Update cancelled');

    // Update function should not be called
    expect(updateFunction).not.toHaveBeenCalled();

    jest.useRealTimers();
  });

  test('generates enhanced document data', () => {
    const basicData = {
      title: 'Basic Title',
      author: 'Basic Author'
    };

    const enhancedData = overlayPreviewService.generateCoverDocumentData(basicData);

    expect(enhancedData).toEqual({
      title: 'Basic Title',
      author: 'Basic Author',
      description: 'This is a sample description for the document cover.',
      subtitle: '',
      category: '',
      tags: [],
      publishDate: expect.any(String)
    });
  });

  test('provides cache statistics', async () => {
    const initialStats = overlayPreviewService.getCacheStats();
    expect(initialStats).toEqual({
      cacheSize: 0,
      maxCacheSize: 50,
      pendingUpdates: 0,
      cacheKeys: []
    });

    // Add some cache entries
    await overlayPreviewService.generatePreviewWithCustomizations(
      mockTemplate,
      mockDocumentData,
      mockCustomizations
    );

    const updatedStats = overlayPreviewService.getCacheStats();
    expect(updatedStats.cacheSize).toBe(1);
    expect(updatedStats.cacheKeys).toHaveLength(1);
  });

  test('implements LRU cache behavior', async () => {
    // Mock a smaller cache size for testing
    overlayPreviewService.maxCacheSize = 2;

    const template1 = { ...mockTemplate, id: 'template-1' };
    const template2 = { ...mockTemplate, id: 'template-2' };
    const template3 = { ...mockTemplate, id: 'template-3' };

    // Fill cache to capacity
    await overlayPreviewService.generatePreviewWithCustomizations(
      template1,
      mockDocumentData,
      mockCustomizations
    );

    await overlayPreviewService.generatePreviewWithCustomizations(
      template2,
      mockDocumentData,
      mockCustomizations
    );

    expect(overlayPreviewService.getCacheStats().cacheSize).toBe(2);

    // Add one more (should evict oldest)
    await overlayPreviewService.generatePreviewWithCustomizations(
      template3,
      mockDocumentData,
      mockCustomizations
    );

    expect(overlayPreviewService.getCacheStats().cacheSize).toBe(2);

    // Reset cache size
    overlayPreviewService.maxCacheSize = 50;
  });
});
