import { prodLogger } from '../utils/prodLogger.js';

/**
 * Document Optimization Service
 * Provides utilities for optimizing document size and performance
 */

/**
 * Analyze document for optimization opportunities
 * @param {Object} documentData - Document data to analyze
 * @returns {Object} Analysis result with optimization recommendations
 */
export const analyzeDocumentSize = (documentData) => {
  try {
    const totalSize = JSON.stringify(documentData).length;
    const editorHTML = documentData.generatedContent?.editorHTML || '';
    
    // Count images in the document
    const base64ImageRegex = /<img[^>]*src="data:image\/[^"]*"[^>]*>/gi;
    const imageMatches = [...editorHTML.matchAll(base64ImageRegex)];
    
    let totalImageSize = 0;
    let largeImageCount = 0;
    let uncompressedImageCount = 0;
    
    imageMatches.forEach(match => {
      const imgTag = match[0];
      const base64Match = imgTag.match(/src="data:image\/([^;]+);base64,([A-Za-z0-9+/=]+)"/);
      
      if (base64Match) {
        const [, imageType, base64Data] = base64Match;
        const imageSize = Math.ceil(base64Data.length * 0.75); // Approximate binary size
        totalImageSize += imageSize;
        
        // Large image (>500KB)
        if (imageSize > 500 * 1024) {
          largeImageCount++;
        }
        
        // Likely uncompressed (PNG/JPEG format and large size)
        if ((imageType === 'png' || imageType === 'jpeg') && imageSize > 200 * 1024) {
          uncompressedImageCount++;
        }
      }
    });
    
    const textSize = totalSize - totalImageSize;
    const compressionPotential = uncompressedImageCount * 0.7; // Estimate 70% compression
    
    return {
      totalSize,
      totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
      textSize,
      imageSize: totalImageSize,
      imageSizeMB: (totalImageSize / 1024 / 1024).toFixed(2),
      imageCount: imageMatches.length,
      largeImageCount,
      uncompressedImageCount,
      compressionPotential: Math.round(compressionPotential * 100),
      needsOptimization: totalSize > 1.5 * 1024 * 1024 || uncompressedImageCount > 0,
      recommendations: generateOptimizationRecommendations(totalSize, imageMatches.length, uncompressedImageCount, largeImageCount)
    };
    
  } catch (error) {
    prodLogger.error('Error analyzing document size:', error);
    return {
      error: error.message,
      totalSize: 0,
      needsOptimization: false,
      recommendations: []
    };
  }
};

/**
 * Generate optimization recommendations based on analysis
 */
const generateOptimizationRecommendations = (totalSize, imageCount, uncompressedCount, largeCount) => {
  const recommendations = [];
  
  if (totalSize > 2 * 1024 * 1024) {
    recommendations.push({
      type: 'critical',
      title: 'Document Too Large',
      description: `Document is ${(totalSize / 1024 / 1024).toFixed(2)}MB. This may cause save timeouts.`,
      action: 'Compress images immediately'
    });
  } else if (totalSize > 1 * 1024 * 1024) {
    recommendations.push({
      type: 'warning',
      title: 'Large Document',
      description: `Document is ${(totalSize / 1024 / 1024).toFixed(2)}MB. Consider optimization.`,
      action: 'Compress images for better performance'
    });
  }
  
  if (uncompressedCount > 0) {
    recommendations.push({
      type: 'optimization',
      title: 'Uncompressed Images Found',
      description: `${uncompressedCount} images can be compressed to reduce size by ~70%.`,
      action: 'Run image compression'
    });
  }
  
  if (largeCount > 0) {
    recommendations.push({
      type: 'performance',
      title: 'Large Images Detected',
      description: `${largeCount} images are larger than 500KB each.`,
      action: 'Consider resizing or compressing large images'
    });
  }
  
  if (imageCount > 10) {
    recommendations.push({
      type: 'info',
      title: 'Many Images',
      description: `Document contains ${imageCount} images.`,
      action: 'Consider splitting into multiple documents if performance issues occur'
    });
  }
  
  return recommendations;
};

/**
 * Optimize document by compressing images and reducing size
 * @param {Object} documentData - Document data to optimize
 * @param {Function} onProgress - Progress callback (optional)
 * @returns {Promise<Object>} Optimization result
 */
export const optimizeDocument = async (documentData, onProgress = null) => {
  try {
    const analysis = analyzeDocumentSize(documentData);
    
    if (!analysis.needsOptimization) {
      return {
        success: true,
        optimized: false,
        message: 'Document is already optimized',
        analysis
      };
    }
    
    // Import compression utility
    const { compressBase64Image } = await import('../utils/imageOptimization.js');
    
    const editorHTML = documentData.generatedContent?.editorHTML || '';
    let optimizedHTML = editorHTML;
    let compressionCount = 0;
    let totalSizeSaved = 0;
    
    // Find all base64 images
    const imageRegex = /<img([^>]*?)src="data:image\/([^;]+);base64,([A-Za-z0-9+/=]+)"([^>]*?)>/gi;
    const imageMatches = [...editorHTML.matchAll(imageRegex)];
    
    if (onProgress) {
      onProgress({ stage: 'analyzing', total: imageMatches.length, current: 0 });
    }
    
    for (let i = 0; i < imageMatches.length; i++) {
      const match = imageMatches[i];
      
      try {
        const [fullMatch, beforeSrc, imageType, base64Data, afterSrc] = match;
        const originalImageSize = Math.ceil(base64Data.length * 0.75);
        
        // Skip small images or already compressed WebP images
        if (originalImageSize < 100 * 1024 || (imageType === 'webp' && originalImageSize < 300 * 1024)) {
          continue;
        }
        
        if (onProgress) {
          onProgress({ 
            stage: 'compressing', 
            total: imageMatches.length, 
            current: i + 1,
            currentImage: { type: imageType, size: originalImageSize }
          });
        }
        
        // Compress the image
        const compressionResult = await compressBase64Image(base64Data, {
          format: 'webp',
          quality: 0.75,
          maxWidth: 1024,
          maxHeight: 1024
        });
        
        // Replace in HTML
        const newImageTag = `<img${beforeSrc}src="data:${compressionResult.mimeType};base64,${compressionResult.base64Data}"${afterSrc}>`;
        optimizedHTML = optimizedHTML.replace(fullMatch, newImageTag);
        
        compressionCount++;
        totalSizeSaved += (originalImageSize - compressionResult.compressedSize);
        
      } catch (compressionError) {
        prodLogger.warn(`Failed to compress image ${i + 1}:`, compressionError.message);
      }
    }
    
    if (compressionCount === 0) {
      return {
        success: true,
        optimized: false,
        message: 'No images needed compression',
        analysis
      };
    }
    
    // Create optimized document data
    const optimizedData = {
      ...documentData,
      generatedContent: {
        ...documentData.generatedContent,
        editorHTML: optimizedHTML
      }
    };
    
    const originalSize = JSON.stringify(documentData).length;
    const newSize = JSON.stringify(optimizedData).length;
    const sizeSaved = originalSize - newSize;
    
    if (onProgress) {
      onProgress({ stage: 'complete', compressionCount, sizeSaved });
    }
    
    return {
      success: true,
      optimized: true,
      originalSize,
      newSize,
      sizeSaved,
      sizeSavedMB: (sizeSaved / 1024 / 1024).toFixed(2),
      compressionCount,
      compressionRatio: ((sizeSaved / originalSize) * 100).toFixed(1),
      optimizedData,
      analysis
    };
    
  } catch (error) {
    prodLogger.error('Document optimization failed:', error);
    return {
      success: false,
      error: error.message,
      analysis: analyzeDocumentSize(documentData)
    };
  }
};

/**
 * Check if document needs optimization
 * @param {Object} documentData - Document data to check
 * @returns {boolean} True if optimization is recommended
 */
export const needsOptimization = (documentData) => {
  const analysis = analyzeDocumentSize(documentData);
  return analysis.needsOptimization;
};

/**
 * Get human-readable size information
 * @param {number} bytes - Size in bytes
 * @returns {string} Formatted size string
 */
export const formatSize = (bytes) => {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / 1024 / 1024).toFixed(2)} MB`;
};
