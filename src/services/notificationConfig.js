/**
 * Notification Configuration Service for DocForge AI
 * 
 * Manages user preferences and system configuration for notifications
 * including duration, positioning, filtering, and behavior settings.
 */

import errorMonitor from '../utils/errorMonitor.js';

// Default notification configuration
export const DEFAULT_NOTIFICATION_CONFIG = {
  // General settings
  enabled: true,
  maxNotifications: 50,
  maxToasts: 3,
  enablePersistence: true,
  enableAnalytics: true,
  
  // Duration settings (in milliseconds)
  durations: {
    success: 4000,
    info: 5000,
    warning: 8000,
    error: 0, // Persistent by default
    loading: 0, // Persistent by default
    progress: 0 // Persistent by default
  },
  
  // Toast positioning
  position: {
    vertical: 'top', // 'top', 'bottom'
    horizontal: 'right', // 'left', 'right', 'center'
    offset: {
      top: 80, // pixels from top (below header)
      right: 16, // pixels from right
      bottom: 16, // pixels from bottom
      left: 16 // pixels from left
    }
  },
  
  // Category preferences
  categories: {
    system: { enabled: true, priority: 'normal' },
    project: { enabled: true, priority: 'normal' },
    template: { enabled: true, priority: 'normal' },
    export: { enabled: true, priority: 'normal' },
    upload: { enabled: true, priority: 'normal' },
    generation: { enabled: true, priority: 'normal' },
    font: { enabled: true, priority: 'low' },
    auth: { enabled: true, priority: 'high' }
  },
  
  // Type preferences
  types: {
    success: { enabled: true, showToast: true, playSound: false },
    info: { enabled: true, showToast: true, playSound: false },
    warning: { enabled: true, showToast: true, playSound: false },
    error: { enabled: true, showToast: true, playSound: true },
    loading: { enabled: true, showToast: false, playSound: false },
    progress: { enabled: true, showToast: false, playSound: false }
  },
  
  // Accessibility settings
  accessibility: {
    reduceMotion: false, // Auto-detected from system preference
    highContrast: false, // Auto-detected from system preference
    announceToScreenReader: true,
    keyboardNavigation: true
  },
  
  // Advanced settings
  advanced: {
    groupSimilar: true, // Group similar notifications
    autoMarkAsRead: false, // Auto-mark as read after viewing
    clearOnPageChange: false, // Clear non-persistent notifications on navigation
    debugMode: false // Enable debug logging
  }
};

/**
 * Notification Configuration Manager
 */
class NotificationConfigManager {
  constructor() {
    this.config = { ...DEFAULT_NOTIFICATION_CONFIG };
    this.storageKey = 'docforge_notification_config';
    this.listeners = new Set();
    
    // Load saved configuration
    this.loadConfig();
    
    // Detect system preferences
    this.detectSystemPreferences();
    
    // Create logger
    this.logger = errorMonitor.createContextLogger('NotificationConfig');
  }

  /**
   * Load configuration from storage
   */
  loadConfig() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const savedConfig = JSON.parse(stored);
        this.config = this.mergeConfig(this.config, savedConfig);
      }
    } catch (error) {
      this.logger.error('Failed to load notification config', { error: error.message });
    }
  }

  /**
   * Save configuration to storage
   */
  saveConfig() {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.config));
      this.notifyListeners('configSaved', this.config);
    } catch (error) {
      this.logger.error('Failed to save notification config', { error: error.message });
    }
  }

  /**
   * Merge configuration objects deeply
   * @param {Object} target - Target configuration
   * @param {Object} source - Source configuration
   * @returns {Object} Merged configuration
   */
  mergeConfig(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.mergeConfig(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * Detect system accessibility preferences
   */
  detectSystemPreferences() {
    try {
      // Detect reduced motion preference
      if (window.matchMedia) {
        const reduceMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        this.config.accessibility.reduceMotion = reduceMotionQuery.matches;
        
        // Listen for changes
        reduceMotionQuery.addEventListener('change', (e) => {
          this.updateConfig({
            accessibility: {
              ...this.config.accessibility,
              reduceMotion: e.matches
            }
          });
        });

        // Detect high contrast preference
        const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
        this.config.accessibility.highContrast = highContrastQuery.matches;
        
        highContrastQuery.addEventListener('change', (e) => {
          this.updateConfig({
            accessibility: {
              ...this.config.accessibility,
              highContrast: e.matches
            }
          });
        });
      }
    } catch (error) {
      this.logger.error('Failed to detect system preferences', { error: error.message });
    }
  }

  /**
   * Get current configuration
   * @returns {Object} Current configuration
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * Update configuration
   * @param {Object} updates - Configuration updates
   */
  updateConfig(updates) {
    const oldConfig = { ...this.config };
    this.config = this.mergeConfig(this.config, updates);
    this.saveConfig();
    this.notifyListeners('configUpdated', this.config, oldConfig);
  }

  /**
   * Reset configuration to defaults
   */
  resetConfig() {
    const oldConfig = { ...this.config };
    this.config = { ...DEFAULT_NOTIFICATION_CONFIG };
    this.detectSystemPreferences();
    this.saveConfig();
    this.notifyListeners('configReset', this.config, oldConfig);
  }

  /**
   * Get duration for notification type
   * @param {string} type - Notification type
   * @returns {number} Duration in milliseconds
   */
  getDuration(type) {
    return this.config.durations[type] ?? this.config.durations.info;
  }

  /**
   * Check if notification type is enabled
   * @param {string} type - Notification type
   * @returns {boolean} Whether type is enabled
   */
  isTypeEnabled(type) {
    return this.config.types[type]?.enabled ?? true;
  }

  /**
   * Check if notification category is enabled
   * @param {string} category - Notification category
   * @returns {boolean} Whether category is enabled
   */
  isCategoryEnabled(category) {
    return this.config.categories[category]?.enabled ?? true;
  }

  /**
   * Check if toast should be shown for type
   * @param {string} type - Notification type
   * @returns {boolean} Whether to show toast
   */
  shouldShowToast(type) {
    return this.config.types[type]?.showToast ?? true;
  }

  /**
   * Get toast position configuration
   * @returns {Object} Position configuration
   */
  getToastPosition() {
    return { ...this.config.position };
  }

  /**
   * Subscribe to configuration changes
   * @param {Function} listener - Listener function
   * @returns {Function} Unsubscribe function
   */
  subscribe(listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Notify listeners of configuration changes
   * @param {string} event - Event type
   * @param {*} data - Event data
   */
  notifyListeners(event, ...data) {
    this.listeners.forEach(listener => {
      try {
        listener(event, ...data);
      } catch (error) {
        this.logger.error('Config listener error', { error: error.message, event });
      }
    });
  }

  /**
   * Export configuration for backup
   * @returns {string} JSON string of configuration
   */
  exportConfig() {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * Import configuration from backup
   * @param {string} configJson - JSON string of configuration
   * @returns {boolean} Success status
   */
  importConfig(configJson) {
    try {
      const importedConfig = JSON.parse(configJson);
      this.updateConfig(importedConfig);
      return true;
    } catch (error) {
      this.logger.error('Failed to import config', { error: error.message });
      return false;
    }
  }
}

// Create singleton instance
const notificationConfig = new NotificationConfigManager();

export default notificationConfig;
