/**
 * Logo Service
 * Handles upload, storage, and management of user logos
 * Follows patterns established by customCoverImageService.js
 */

import { supabase } from "../lib/supabase.js";
import errorMonitor, { ErrorSeverity } from "../utils/errorMonitor.js";
import { prodLogger } from "../utils/prodLogger.js";

// Configuration for user logos
const LOGO_CONFIG = {
  // Storage bucket for user logos
  STORAGE_BUCKET: "user-logos",

  // File size limits (in bytes)
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  RECOMMENDED_MAX_SIZE: 2 * 1024 * 1024, // 2MB recommended

  // Supported image formats
  SUPPORTED_FORMATS: ["image/jpeg", "image/jpg", "image/png", "image/webp"],

  // Image optimization settings
  OPTIMIZATION: {
    maxWidth: 800, // Smaller than cover images since logos are typically smaller
    maxHeight: 800,
    quality: 0.9, // High quality for logos
    format: "png", // PNG preserves transparency for logos
  },

  // File naming convention
  FILE_PREFIX: "logo-",
  
  // Logo positioning options for documents
  POSITIONS: {
    TOP_LEFT: "top-left",
    TOP_CENTER: "top-center", 
    TOP_RIGHT: "top-right",
    CENTER: "center",
    BOTTOM_LEFT: "bottom-left",
    BOTTOM_CENTER: "bottom-center",
    BOTTOM_RIGHT: "bottom-right"
  },

  // Default logo settings
  DEFAULTS: {
    position: "top-right",
    size: "medium", // small, medium, large
    opacity: 1.0
  }
};

/**
 * Validate uploaded logo file
 * @param {File} file - The uploaded file
 * @returns {Object} Validation result with success flag and error message
 */
export const validateLogo = (file) => {
  try {
    // Check if file exists
    if (!file) {
      return { success: false, error: "No file provided" };
    }

    // Check file size
    if (file.size > LOGO_CONFIG.MAX_FILE_SIZE) {
      const maxSizeMB = LOGO_CONFIG.MAX_FILE_SIZE / (1024 * 1024);
      return {
        success: false,
        error: `File size exceeds ${maxSizeMB}MB limit. Current size: ${(
          file.size / (1024 * 1024)
        ).toFixed(2)}MB`,
      };
    }

    // Check file type
    if (!LOGO_CONFIG.SUPPORTED_FORMATS.includes(file.type)) {
      return {
        success: false,
        error: `Unsupported file format. Supported formats: ${LOGO_CONFIG.SUPPORTED_FORMATS.join(", ")}`,
      };
    }

    // Warn if file is larger than recommended
    const warnings = [];
    if (file.size > LOGO_CONFIG.RECOMMENDED_MAX_SIZE) {
      const recommendedMB = LOGO_CONFIG.RECOMMENDED_MAX_SIZE / (1024 * 1024);
      warnings.push(`File size is larger than recommended ${recommendedMB}MB. Consider optimizing for better performance.`);
    }

    return { success: true, warnings };
  } catch (error) {
    prodLogger.error("❌ Error validating logo:", error);
    return { success: false, error: "Failed to validate logo file" };
  }
};

/**
 * Optimize logo for storage and use
 * @param {File} file - The original logo file
 * @returns {Promise<Blob>} Optimized logo blob
 */
export const optimizeLogo = async (file) => {
  return new Promise((resolve, reject) => {
    try {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new Image();

      img.onload = () => {
        try {
          const { maxWidth, maxHeight, quality } = LOGO_CONFIG.OPTIMIZATION;

          // Calculate optimal dimensions while maintaining aspect ratio
          let { width, height } = img;

          if (width > maxWidth || height > maxHeight) {
            const aspectRatio = width / height;

            if (width > height) {
              width = maxWidth;
              height = width / aspectRatio;
            } else {
              height = maxHeight;
              width = height * aspectRatio;
            }
          }

          // Set canvas dimensions
          canvas.width = width;
          canvas.height = height;

          // Draw and optimize image
          ctx.drawImage(img, 0, 0, width, height);

          // Convert to blob with optimization
          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve(blob);
              } else {
                reject(new Error("Failed to optimize logo"));
              }
            },
            `image/${LOGO_CONFIG.OPTIMIZATION.format}`,
            quality
          );
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error("Failed to load image for optimization"));
      };

      img.src = URL.createObjectURL(file);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Get image dimensions from blob
 * @param {Blob} blob - Image blob
 * @returns {Promise<Object>} Dimensions object with width and height
 */
export const getImageDimensions = (blob) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
      URL.revokeObjectURL(img.src);
    };
    img.onerror = () => {
      reject(new Error("Failed to get image dimensions"));
      URL.revokeObjectURL(img.src);
    };
    img.src = URL.createObjectURL(blob);
  });
};

/**
 * Upload logo to Supabase storage
 * @param {File} file - The logo file to upload
 * @param {string} userId - User ID for file organization
 * @param {Object} metadata - Additional metadata (name, description)
 * @returns {Promise<Object>} Upload result with logo data
 */
export const uploadLogo = async (file, userId, metadata = {}) => {
  try {
    prodLogger.debug("🔄 Starting logo upload", {
      fileName: file.name,
      fileSize: file.size,
      userId,
      metadata,
    });

    // Validate the logo
    const validation = validateLogo(file);
    if (!validation.success) {
      throw new Error(validation.error);
    }

    // Optimize the logo
    const optimizedBlob = await optimizeLogo(file);

    // Generate unique filename
    const timestamp = Date.now();
    const fileExtension = LOGO_CONFIG.OPTIMIZATION.format;
    const fileName = `${LOGO_CONFIG.FILE_PREFIX}${timestamp}.${fileExtension}`;
    const filePath = `${userId}/${fileName}`;

    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from(LOGO_CONFIG.STORAGE_BUCKET)
      .upload(filePath, optimizedBlob, {
        contentType: `image/${fileExtension}`,
        upsert: false, // Don't overwrite existing files
        cacheControl: "3600",
      });

    if (error) {
      throw error;
    }

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage
      .from(LOGO_CONFIG.STORAGE_BUCKET)
      .getPublicUrl(filePath);

    // Get image dimensions
    const dimensions = await getImageDimensions(optimizedBlob);

    // Save logo metadata to database
    const logoData = {
      user_id: userId,
      name: metadata.name || file.name.replace(/\.[^/.]+$/, ""), // Remove extension
      description: metadata.description || null,
      file_name: file.name,
      file_size: optimizedBlob.size,
      file_type: `image/${fileExtension}`,
      storage_path: filePath,
      public_url: publicUrl,
      width: dimensions.width,
      height: dimensions.height,
      is_default: metadata.isDefault || false,
    };

    const { data: savedLogo, error: dbError } = await supabase
      .from("user_logos")
      .insert([logoData])
      .select()
      .single();

    if (dbError) {
      // Clean up uploaded file if database save fails
      await supabase.storage
        .from(LOGO_CONFIG.STORAGE_BUCKET)
        .remove([filePath]);
      throw dbError;
    }

    const result = {
      success: true,
      logo: savedLogo,
      warnings: validation.warnings || [],
    };

    prodLogger.debug("✅ Logo uploaded successfully", result);
    return result;
  } catch (error) {
    prodLogger.error("❌ Error uploading logo:", error);

    errorMonitor.captureError(
      error,
      {
        userId,
        fileName: file?.name,
        fileSize: file?.size,
        metadata,
      },
      ErrorSeverity.MEDIUM
    );

    return {
      success: false,
      error: error.message || "Failed to upload logo",
    };
  }
};

/**
 * Get user's logos
 * @param {string} userId - User ID
 * @param {Object} options - Query options (activeOnly, includeDefault)
 * @returns {Promise<Object>} User's logos
 */
export const getUserLogos = async (userId, options = {}) => {
  try {
    const { activeOnly = true, includeDefault = true } = options;

    let query = supabase
      .from("user_logos")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    if (activeOnly) {
      query = query.eq("is_active", true);
    }

    const { data: logos, error } = await query;

    if (error) {
      throw error;
    }

    // Separate default logo if requested
    let defaultLogo = null;
    let otherLogos = logos;

    if (includeDefault) {
      defaultLogo = logos.find(logo => logo.is_default);
      otherLogos = logos.filter(logo => !logo.is_default);
    }

    return {
      success: true,
      logos: otherLogos,
      defaultLogo,
      total: logos.length,
    };
  } catch (error) {
    prodLogger.error("❌ Error fetching user logos:", error);
    return {
      success: false,
      error: error.message || "Failed to fetch logos",
    };
  }
};

/**
 * Set logo as default for user
 * @param {string} logoId - Logo ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Update result
 */
export const setDefaultLogo = async (logoId, userId) => {
  try {
    // The database trigger will handle unsetting other default logos
    const { data, error } = await supabase
      .from("user_logos")
      .update({ is_default: true })
      .eq("id", logoId)
      .eq("user_id", userId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      logo: data,
    };
  } catch (error) {
    prodLogger.error("❌ Error setting default logo:", error);
    return {
      success: false,
      error: error.message || "Failed to set default logo",
    };
  }
};

/**
 * Delete logo
 * @param {string} logoId - Logo ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Delete result
 */
export const deleteLogo = async (logoId, userId) => {
  try {
    // Get logo data first
    const { data: logo, error: fetchError } = await supabase
      .from("user_logos")
      .select("*")
      .eq("id", logoId)
      .eq("user_id", userId)
      .single();

    if (fetchError) {
      throw fetchError;
    }

    // Delete from storage
    const { error: storageError } = await supabase.storage
      .from(LOGO_CONFIG.STORAGE_BUCKET)
      .remove([logo.storage_path]);

    if (storageError) {
      prodLogger.warn("⚠️ Failed to delete logo from storage:", storageError);
      // Continue with database deletion even if storage deletion fails
    }

    // Delete from database
    const { error: dbError } = await supabase
      .from("user_logos")
      .delete()
      .eq("id", logoId)
      .eq("user_id", userId);

    if (dbError) {
      throw dbError;
    }

    return {
      success: true,
      message: "Logo deleted successfully",
    };
  } catch (error) {
    prodLogger.error("❌ Error deleting logo:", error);
    return {
      success: false,
      error: error.message || "Failed to delete logo",
    };
  }
};

/**
 * Increment logo usage count (called when logo is used in document generation)
 * @param {string} logoId - Logo ID
 * @returns {Promise<void>}
 */
export const incrementLogoUsage = async (logoId) => {
  try {
    await supabase.rpc("increment_logo_usage", { logo_uuid: logoId });
  } catch (error) {
    prodLogger.warn("⚠️ Failed to increment logo usage:", error);
    // Don't throw error as this is not critical
  }
};

// Export configuration for use in components
export { LOGO_CONFIG };
