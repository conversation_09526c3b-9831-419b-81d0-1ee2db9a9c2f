/**
 * Comprehensive Notification Service for DocForge AI
 * 
 * Centralized notification management system that handles:
 * - Notification queue management with priority
 * - Persistence and state management
 * - Event-driven architecture
 * - Integration with existing services
 * - Performance optimization
 */

import { NOTIFICATION_TYPES } from './userNotificationService.js';
import errorMonitor, { ErrorSeverity } from '../utils/errorMonitor.js';
import notificationConfig from './notificationConfig.js';

// Extended notification types for comprehensive coverage
export const NOTIFICATION_TYPES_EXTENDED = {
  ...NOTIFICATION_TYPES,
  LOADING: 'loading',
  PROGRESS: 'progress'
};

// Notification categories for organization
export const NOTIFICATION_CATEGORIES = {
  SYSTEM: 'system',           // API errors, service status
  PROJECT: 'project',         // Project CRUD operations
  TEMPLATE: 'template',       // Template operations
  EXPORT: 'export',           // Document export status
  UPLOAD: 'upload',           // File upload results
  GENERATION: 'generation',   // AI content/image generation
  FONT: 'font',              // Font loading operations
  AUTH: 'auth'               // Authentication operations
};

// Notification priorities for queue management
export const NOTIFICATION_PRIORITIES = {
  LOW: 1,
  NORMAL: 2,
  HIGH: 3,
  CRITICAL: 4
};

// Default configuration
const DEFAULT_CONFIG = {
  maxNotifications: 50,
  maxToasts: 3,
  defaultDuration: 5000,
  persistentTypes: ['error', 'warning'],
  enablePersistence: true,
  storageKey: 'docforge_notifications',
  enableAnalytics: true
};

/**
 * Core Notification Service Class
 */
class NotificationService {
  constructor(config = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.notifications = [];
    this.toasts = [];
    this.listeners = new Set();
    this.nextId = 1;

    // Initialize from persistence
    this.loadFromStorage();

    // Subscribe to configuration changes
    this.configUnsubscribe = notificationConfig.subscribe((event, newConfig) => {
      if (event === 'configUpdated') {
        this.handleConfigUpdate(newConfig);
      }
    });

    // Bind methods
    this.add = this.add.bind(this);
    this.remove = this.remove.bind(this);
    this.clear = this.clear.bind(this);
    this.markAsRead = this.markAsRead.bind(this);
    this.subscribe = this.subscribe.bind(this);
    this.unsubscribe = this.unsubscribe.bind(this);

    // Create logger
    this.logger = errorMonitor.createContextLogger('NotificationService');
  }

  /**
   * Generate unique notification ID
   * @returns {string} Unique ID
   */
  generateId() {
    return `notification_${Date.now()}_${this.nextId++}`;
  }

  /**
   * Validate notification object
   * @param {Object} notification - Notification to validate
   * @throws {Error} If notification is invalid
   */
  validateNotification(notification) {
    if (!notification.type || !Object.values(NOTIFICATION_TYPES_EXTENDED).includes(notification.type)) {
      throw new Error(`Invalid notification type: ${notification.type}`);
    }
    
    if (!notification.title || typeof notification.title !== 'string') {
      throw new Error('Notification title is required and must be a string');
    }
    
    if (notification.message && typeof notification.message !== 'string') {
      throw new Error('Notification message must be a string');
    }
  }

  /**
   * Sort notifications by priority (highest first)
   */
  sortNotificationsByPriority() {
    this.notifications.sort((a, b) => {
      // First sort by priority (highest first)
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }
      // Then by timestamp (newest first)
      return new Date(b.timestamp) - new Date(a.timestamp);
    });
  }

  /**
   * Add notification to toast queue
   * @param {Object} notification - Notification to add to toast queue
   */
  addToToastQueue(notification) {
    // Remove oldest toast if at limit
    if (this.toasts.length >= this.config.maxToasts) {
      this.toasts.shift();
    }
    
    this.toasts.push({
      ...notification,
      showTime: Date.now()
    });
    
    this.notifyListeners('toastAdd', notification);
  }

  /**
   * Remove toast from queue
   * @param {string} id - Toast ID to remove
   */
  removeToast(id) {
    const initialLength = this.toasts.length;
    this.toasts = this.toasts.filter(t => t.id !== id);
    
    if (this.toasts.length < initialLength) {
      this.notifyListeners('toastRemove', { id });
    }
  }

  /**
   * Load notifications from storage
   */
  loadFromStorage() {
    if (!this.config.enablePersistence) return;
    
    try {
      const stored = localStorage.getItem(this.config.storageKey);
      if (stored) {
        const data = JSON.parse(stored);
        this.notifications = Array.isArray(data.notifications) ? data.notifications : [];
        this.nextId = data.nextId || 1;
        
        // Clean up old notifications (older than 7 days)
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        this.notifications = this.notifications.filter(n => 
          new Date(n.timestamp) > weekAgo || n.persistent
        );
      }
    } catch (error) {
      this.logger.error('Failed to load notifications from storage', { error: error.message });
      this.notifications = [];
    }
  }

  /**
   * Save notifications to storage
   */
  saveToStorage() {
    if (!this.config.enablePersistence) return;
    
    try {
      const data = {
        notifications: this.notifications,
        nextId: this.nextId,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem(this.config.storageKey, JSON.stringify(data));
    } catch (error) {
      this.logger.error('Failed to save notifications to storage', { error: error.message });
    }
  }

  /**
   * Notify all listeners of changes
   * @param {string} event - Event type
   * @param {Object} data - Event data
   */
  notifyListeners(event, data) {
    this.listeners.forEach(listener => {
      try {
        listener(event, data, this.getState());
      } catch (error) {
        this.logger.error('Listener error', { error: error.message, event });
      }
    });
  }

  /**
   * Subscribe to notification changes
   * @param {Function} listener - Listener function
   * @returns {Function} Unsubscribe function
   */
  subscribe(listener) {
    this.listeners.add(listener);
    return () => this.unsubscribe(listener);
  }

  /**
   * Unsubscribe from notification changes
   * @param {Function} listener - Listener function
   */
  unsubscribe(listener) {
    this.listeners.delete(listener);
  }

  /**
   * Handle configuration updates
   * @param {Object} newConfig - Updated configuration
   */
  handleConfigUpdate(newConfig) {
    // Update internal config
    this.config.maxNotifications = newConfig.maxNotifications || this.config.maxNotifications;
    this.config.maxToasts = newConfig.maxToasts || this.config.maxToasts;
    this.config.enableAnalytics = newConfig.enableAnalytics ?? this.config.enableAnalytics;

    // Trim notifications if max count reduced
    if (this.notifications.length > this.config.maxNotifications) {
      this.notifications = this.notifications.slice(0, this.config.maxNotifications);
    }

    // Trim toasts if max count reduced
    if (this.toasts.length > this.config.maxToasts) {
      this.toasts = this.toasts.slice(0, this.config.maxToasts);
    }

    this.notifyListeners('configUpdated', newConfig);
  }

  /**
   * Add a new notification to the system
   * @param {Object} notification - Notification data
   * @returns {string} Notification ID
   */
  add(notification) {
    try {
      // Check if notification type/category is enabled
      if (!notificationConfig.isTypeEnabled(notification.type) ||
          !notificationConfig.isCategoryEnabled(notification.category || 'system')) {
        return null;
      }

      const id = this.generateId();
      const timestamp = new Date().toISOString();

      // Get duration from configuration
      const configDuration = notificationConfig.getDuration(notification.type);
      const duration = notification.duration !== undefined ? notification.duration : configDuration;

      const fullNotification = {
        id,
        timestamp,
        isRead: false,
        priority: NOTIFICATION_PRIORITIES.NORMAL,
        category: NOTIFICATION_CATEGORIES.SYSTEM,
        duration,
        persistent: duration === 0 || this.config.persistentTypes.includes(notification.type),
        ...notification
      };

      // Validate notification
      this.validateNotification(fullNotification);

      // Add to notifications array with priority sorting
      this.notifications.unshift(fullNotification);
      this.sortNotificationsByPriority();

      // Manage queue size
      if (this.notifications.length > this.config.maxNotifications) {
        this.notifications = this.notifications.slice(0, this.config.maxNotifications);
      }

      // Add to toast queue if should show toast and not persistent
      if (notificationConfig.shouldShowToast(fullNotification.type) &&
          !fullNotification.persistent &&
          fullNotification.type !== 'loading') {
        this.addToToastQueue(fullNotification);
      }

      // Persist to storage
      this.saveToStorage();

      // Notify listeners
      this.notifyListeners('add', fullNotification);

      // Log for analytics
      if (this.config.enableAnalytics) {
        this.logger.info('Notification added', {
          id,
          type: fullNotification.type,
          category: fullNotification.category,
          priority: fullNotification.priority
        });
      }

      return id;
    } catch (error) {
      this.logger.error('Failed to add notification', { error: error.message, notification });
      return null;
    }
  }

  /**
   * Remove a notification by ID
   * @param {string} id - Notification ID
   * @returns {boolean} Success status
   */
  remove(id) {
    try {
      const initialLength = this.notifications.length;
      this.notifications = this.notifications.filter(n => n.id !== id);

      // Remove from toasts as well
      this.toasts = this.toasts.filter(t => t.id !== id);

      const removed = this.notifications.length < initialLength;

      if (removed) {
        this.saveToStorage();
        this.notifyListeners('remove', { id });

        if (this.config.enableAnalytics) {
          this.logger.info('Notification removed', { id });
        }
      }

      return removed;
    } catch (error) {
      this.logger.error('Failed to remove notification', { error: error.message, id });
      return false;
    }
  }

  /**
   * Mark notification as read
   * @param {string} id - Notification ID
   * @returns {boolean} Success status
   */
  markAsRead(id) {
    try {
      const notification = this.notifications.find(n => n.id === id);
      if (notification && !notification.isRead) {
        notification.isRead = true;
        this.saveToStorage();
        this.notifyListeners('markAsRead', notification);

        if (this.config.enableAnalytics) {
          this.logger.info('Notification marked as read', { id });
        }

        return true;
      }
      return false;
    } catch (error) {
      this.logger.error('Failed to mark notification as read', { error: error.message, id });
      return false;
    }
  }

  /**
   * Mark all notifications as read
   * @returns {number} Number of notifications marked as read
   */
  markAllAsRead() {
    try {
      let count = 0;
      this.notifications.forEach(notification => {
        if (!notification.isRead) {
          notification.isRead = true;
          count++;
        }
      });

      if (count > 0) {
        this.saveToStorage();
        this.notifyListeners('markAllAsRead', { count });

        if (this.config.enableAnalytics) {
          this.logger.info('All notifications marked as read', { count });
        }
      }

      return count;
    } catch (error) {
      this.logger.error('Failed to mark all notifications as read', { error: error.message });
      return 0;
    }
  }

  /**
   * Clear all non-persistent notifications
   * @returns {number} Number of notifications cleared
   */
  clear() {
    try {
      const initialLength = this.notifications.length;
      this.notifications = this.notifications.filter(n => n.persistent);
      this.toasts = [];

      const cleared = initialLength - this.notifications.length;

      if (cleared > 0) {
        this.saveToStorage();
        this.notifyListeners('clear', { count: cleared });

        if (this.config.enableAnalytics) {
          this.logger.info('Notifications cleared', { count: cleared });
        }
      }

      return cleared;
    } catch (error) {
      this.logger.error('Failed to clear notifications', { error: error.message });
      return 0;
    }
  }

  /**
   * Get current notification state
   * @returns {Object} Current state
   */
  getState() {
    return {
      notifications: [...this.notifications],
      toasts: [...this.toasts],
      unreadCount: this.notifications.filter(n => !n.isRead).length,
      totalCount: this.notifications.length
    };
  }

  /**
   * Get notifications by category
   * @param {string} category - Notification category
   * @returns {Array} Filtered notifications
   */
  getByCategory(category) {
    return this.notifications.filter(n => n.category === category);
  }

  /**
   * Get notifications by type
   * @param {string} type - Notification type
   * @returns {Array} Filtered notifications
   */
  getByType(type) {
    return this.notifications.filter(n => n.type === type);
  }

  /**
   * Update notification configuration
   * @param {Object} newConfig - New configuration options
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.saveToStorage();
  }
}

// Convenience methods for common notification types
export const createSuccessNotification = (title, message, options = {}) => ({
  type: NOTIFICATION_TYPES.SUCCESS,
  title,
  message,
  category: NOTIFICATION_CATEGORIES.SYSTEM,
  priority: NOTIFICATION_PRIORITIES.NORMAL,
  ...options
});

export const createErrorNotification = (title, message, options = {}) => ({
  type: NOTIFICATION_TYPES.ERROR,
  title,
  message,
  category: NOTIFICATION_CATEGORIES.SYSTEM,
  priority: NOTIFICATION_PRIORITIES.HIGH,
  persistent: true,
  ...options
});

export const createWarningNotification = (title, message, options = {}) => ({
  type: NOTIFICATION_TYPES.WARNING,
  title,
  message,
  category: NOTIFICATION_CATEGORIES.SYSTEM,
  priority: NOTIFICATION_PRIORITIES.NORMAL,
  persistent: true,
  ...options
});

export const createInfoNotification = (title, message, options = {}) => ({
  type: NOTIFICATION_TYPES.INFO,
  title,
  message,
  category: NOTIFICATION_CATEGORIES.SYSTEM,
  priority: NOTIFICATION_PRIORITIES.NORMAL,
  ...options
});

export const createLoadingNotification = (title, message, options = {}) => ({
  type: NOTIFICATION_TYPES_EXTENDED.LOADING,
  title,
  message,
  category: NOTIFICATION_CATEGORIES.SYSTEM,
  priority: NOTIFICATION_PRIORITIES.NORMAL,
  persistent: true,
  ...options
});

// Project-specific notification creators
export const createProjectNotification = (type, title, message, options = {}) => ({
  type,
  title,
  message,
  category: NOTIFICATION_CATEGORIES.PROJECT,
  priority: type === NOTIFICATION_TYPES.ERROR ? NOTIFICATION_PRIORITIES.HIGH : NOTIFICATION_PRIORITIES.NORMAL,
  ...options
});

export const createTemplateNotification = (type, title, message, options = {}) => ({
  type,
  title,
  message,
  category: NOTIFICATION_CATEGORIES.TEMPLATE,
  priority: NOTIFICATION_PRIORITIES.NORMAL,
  ...options
});

export const createExportNotification = (type, title, message, options = {}) => ({
  type,
  title,
  message,
  category: NOTIFICATION_CATEGORIES.EXPORT,
  priority: NOTIFICATION_PRIORITIES.NORMAL,
  ...options
});

export const createUploadNotification = (type, title, message, options = {}) => ({
  type,
  title,
  message,
  category: NOTIFICATION_CATEGORIES.UPLOAD,
  priority: type === NOTIFICATION_TYPES.ERROR ? NOTIFICATION_PRIORITIES.HIGH : NOTIFICATION_PRIORITIES.NORMAL,
  ...options
});

export const createGenerationNotification = (type, title, message, options = {}) => ({
  type,
  title,
  message,
  category: NOTIFICATION_CATEGORIES.GENERATION,
  priority: NOTIFICATION_PRIORITIES.NORMAL,
  ...options
});

export const createFontNotification = (type, title, message, options = {}) => ({
  type,
  title,
  message,
  category: NOTIFICATION_CATEGORIES.FONT,
  priority: NOTIFICATION_PRIORITIES.LOW,
  ...options
});

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
