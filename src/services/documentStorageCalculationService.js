/**
 * Document Storage Calculation Service
 * Calculates actual database storage used by user documents
 */

import { supabase } from "../lib/supabase.js";
import { prodLogger } from "../utils/prodLogger.js";

export const documentStorageCalculationService = {
  /**
   * Calculate the storage size of a single document in bytes
   * @param {Object} document - Document data from database
   * @returns {number} Size in bytes
   */
  calculateDocumentSize(document) {
    try {
      // Convert the entire document record to JSON string to get accurate size
      const documentJson = JSON.stringify(document);
      
      // Calculate size in bytes (UTF-8 encoding)
      const sizeInBytes = new Blob([documentJson]).size;
      
      return sizeInBytes;
    } catch (error) {
      prodLogger.error('Error calculating document size:', error);
      return 0;
    }
  },

  /**
   * Calculate total storage usage for a user in MB
   * @param {string} userId - User ID
   * @returns {Promise<number>} Total storage usage in MB
   */
  async calculateUserStorageUsage(userId) {
    try {
      // Get all documents for the user
      const { data: documents, error } = await supabase
        .from('documents')
        .select('*')
        .eq('user_id', userId);

      if (error) {
        prodLogger.error('Error fetching user documents for storage calculation:', error);
        throw error;
      }

      if (!documents || documents.length === 0) {
        return 0;
      }

      // Calculate total size
      let totalSizeBytes = 0;
      const documentSizes = [];

      documents.forEach(document => {
        const docSize = this.calculateDocumentSize(document);
        totalSizeBytes += docSize;
        
        documentSizes.push({
          id: document.id,
          title: document.title || 'Untitled',
          sizeBytes: docSize,
          sizeMB: Math.ceil(docSize / (1024 * 1024)) || 1, // At least 1MB per document
          lastModified: document.updated_at
        });
      });

      // Convert to MB and round up
      const totalSizeMB = Math.ceil(totalSizeBytes / (1024 * 1024)) || 0;

      prodLogger.debug(`Storage calculation for user ${userId}:`, {
        documentCount: documents.length,
        totalSizeBytes,
        totalSizeMB,
        documentSizes
      });

      return totalSizeMB;
    } catch (error) {
      prodLogger.error('Error calculating user storage usage:', error);
      throw error;
    }
  },

  /**
   * Update user's storage usage in the database
   * @param {string} userId - User ID
   * @returns {Promise<number>} Updated storage usage in MB
   */
  async updateUserStorageUsage(userId) {
    try {
      // Calculate current storage usage
      const storageUsageMB = await this.calculateUserStorageUsage(userId);

      // Update user profile with new storage usage
      const { error } = await supabase
        .from('user_profiles')
        .update({ 
          storage_used_mb: storageUsageMB,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        prodLogger.error('Error updating user storage usage:', error);
        throw error;
      }

      prodLogger.debug(`✅ Updated storage usage for user ${userId}: ${storageUsageMB}MB`);
      return storageUsageMB;
    } catch (error) {
      prodLogger.error('Error updating user storage usage:', error);
      throw error;
    }
  },

  /**
   * Get detailed storage breakdown for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Detailed storage information
   */
  async getUserStorageBreakdown(userId) {
    try {
      const { data: documents, error } = await supabase
        .from('documents')
        .select('*')
        .eq('user_id', userId);

      if (error) {
        throw error;
      }

      if (!documents || documents.length === 0) {
        return {
          totalDocuments: 0,
          totalSizeBytes: 0,
          totalSizeMB: 0,
          documents: [],
          averageDocumentSize: 0
        };
      }

      let totalSizeBytes = 0;
      const documentBreakdown = [];

      documents.forEach(document => {
        const docSize = this.calculateDocumentSize(document);
        totalSizeBytes += docSize;

        // Analyze document content
        const contentAnalysis = this.analyzeDocumentContent(document);

        documentBreakdown.push({
          id: document.id,
          title: document.title || 'Untitled',
          sizeBytes: docSize,
          sizeMB: Math.ceil(docSize / (1024 * 1024)) || 1,
          createdAt: document.created_at,
          updatedAt: document.updated_at,
          contentAnalysis
        });
      });

      const totalSizeMB = Math.ceil(totalSizeBytes / (1024 * 1024)) || 0;
      const averageDocumentSize = Math.round(totalSizeBytes / documents.length);

      return {
        totalDocuments: documents.length,
        totalSizeBytes,
        totalSizeMB,
        documents: documentBreakdown.sort((a, b) => b.sizeBytes - a.sizeBytes), // Largest first
        averageDocumentSize,
        averageDocumentSizeMB: Math.ceil(averageDocumentSize / (1024 * 1024)) || 1
      };
    } catch (error) {
      prodLogger.error('Error getting user storage breakdown:', error);
      throw error;
    }
  },

  /**
   * Analyze document content to understand what's taking up space
   * @param {Object} document - Document data
   * @returns {Object} Content analysis
   */
  analyzeDocumentContent(document) {
    try {
      const analysis = {
        hasGeneratedContent: !!document.generated_content,
        hasQuestionnaireData: !!document.questionnaire_data,
        wordCount: document.word_count || 0,
        chapterCount: document.chapter_count || 0,
        contentTypes: []
      };

      // Analyze generated content
      if (document.generated_content) {
        const contentSize = new Blob([JSON.stringify(document.generated_content)]).size;
        analysis.contentTypes.push({
          type: 'generated_content',
          sizeBytes: contentSize,
          sizeMB: Math.ceil(contentSize / (1024 * 1024)) || 1
        });
      }

      // Analyze questionnaire data
      if (document.questionnaire_data) {
        const questionnaireSize = new Blob([JSON.stringify(document.questionnaire_data)]).size;
        analysis.contentTypes.push({
          type: 'questionnaire_data',
          sizeBytes: questionnaireSize,
          sizeMB: Math.ceil(questionnaireSize / (1024 * 1024)) || 1
        });
      }

      // Check for embedded images in content
      const documentString = JSON.stringify(document);
      const base64ImageMatches = documentString.match(/data:image\/[^;]+;base64,[A-Za-z0-9+/=]+/g);
      
      if (base64ImageMatches) {
        let totalImageSize = 0;
        base64ImageMatches.forEach(match => {
          // Estimate image size from base64 string
          const base64Data = match.split(',')[1];
          const imageSize = Math.ceil(base64Data.length * 0.75); // Approximate binary size
          totalImageSize += imageSize;
        });

        analysis.contentTypes.push({
          type: 'embedded_images',
          count: base64ImageMatches.length,
          sizeBytes: totalImageSize,
          sizeMB: Math.ceil(totalImageSize / (1024 * 1024)) || 1
        });
      }

      return analysis;
    } catch (error) {
      prodLogger.error('Error analyzing document content:', error);
      return { error: 'Analysis failed' };
    }
  },

  /**
   * Recalculate storage for all users (admin function)
   * @returns {Promise<Object>} Recalculation results
   */
  async recalculateAllUsersStorage() {
    try {
      // Get all users with documents
      const { data: users, error } = await supabase
        .from('user_profiles')
        .select('id, email');

      if (error) {
        throw error;
      }

      const results = {
        totalUsers: users.length,
        updated: 0,
        errors: 0,
        details: []
      };

      for (const user of users) {
        try {
          const storageUsage = await this.updateUserStorageUsage(user.id);
          results.updated++;
          results.details.push({
            userId: user.id,
            email: user.email,
            storageUsageMB: storageUsage,
            status: 'success'
          });
        } catch (error) {
          results.errors++;
          results.details.push({
            userId: user.id,
            email: user.email,
            error: error.message,
            status: 'error'
          });
        }
      }

      prodLogger.info('Storage recalculation completed:', results);
      return results;
    } catch (error) {
      prodLogger.error('Error recalculating all users storage:', error);
      throw error;
    }
  }
};
