import { prodLogger } from '../utils/prodLogger.js';

/**
 * Error Handling Service for DOCX Export
 *
 * Provides comprehensive error handling, retry logic, and user-friendly error messages
 * for the DOCX export functionality.
 *
 * Note: Core validation and error handling functions have been moved to utils/
 * This service now focuses on DOCX-specific error handling and image processing.
 */

import {
  validateDocumentData,
  validateImageUrl,
  validateImageFormat,
  IMAGE_CONFIG,
} from "../utils/validation.js";
import {
  downloadImage,
  downloadImageWithRetry,
  shouldRetryImageDownload,
  categorizeImageError,
} from "../utils/imageProcessing.js";
import {
  categorizeError,
  createUserFriendlyErrorMessage,
  createErrorSummary,
  generateImageErrorMessage,
} from "../utils/errorHandling.js";

// IMAGE_CONFIG is now imported from utils/validation.js

/**
 * Sleep utility for retry delays
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise} Promise that resolves after the delay
 */
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// categorizeError is now imported from utils/errorHandling.js

// createUserFriendlyErrorMessage is now imported from utils/errorHandling.js

/**
 * Determine if an error is retryable based on comprehensive error analysis
 * @param {Error} error - Error to analyze
 * @returns {boolean} Whether the error should be retried
 */
export const isRetryableError = (error) => {
  const errorType = categorizeError(error);

  // Never retry validation or size errors
  if (errorType === "validation_error" || errorType === "size_error") {
    return false;
  }

  // Don't retry most client errors except specific ones
  if (errorType === "client_error") {
    return [408, 429, 499].includes(error.status);
  }

  // Retry server errors, network errors, and timeouts
  return ["server_error", "network_error", "timeout", "unknown_error"].includes(
    errorType
  );
};

// validateImageUrl is now imported from utils/validation.js

// validateImageFormat is now imported from utils/validation.js

/**
 * Get file extension from content type
 * @param {string} contentType - Content type
 * @returns {string} File extension
 */
export const getExtensionFromContentType = (contentType) => {
  const typeMap = {
    "image/jpeg": ".jpg",
    "image/jpg": ".jpg",
    "image/png": ".png",
    "image/gif": ".gif",
    "image/webp": ".webp",
  };
  return typeMap[contentType] || ".jpg";
};

/**
 * Download image with basic error handling (internal use only)
 * @param {string} url - Image URL to download
 * @param {Object} options - Download options
 * @returns {Promise<Object>} Download result with success status and data/error
 * @private
 */
const downloadImage_errorService = async (url, options = {}) => {
  const { timeout = IMAGE_CONFIG.TIMEOUT } = options;

  // Validate URL first
  const urlValidation = validateImageUrl(url);
  if (!urlValidation.isValid) {
    return {
      success: false,
      error: urlValidation.error,
      url: url,
      retryable: false,
      errorType: "validation_error",
    };
  }

  let timeoutId;
  try {
    // Create AbortController for timeout
    const controller = new AbortController();
    timeoutId = setTimeout(() => {
      controller.abort();
    }, timeout);

    const response = await fetch(urlValidation.url, {
      method: "GET",
      signal: controller.signal,
      headers: {
        Accept: "image/*",
        "User-Agent": "RapidDoc-AI/1.0",
        "Cache-Control": "no-cache",
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const error = new Error(
        `HTTP ${response.status}: ${response.statusText}`
      );
      error.status = response.status;
      error.statusText = response.statusText;
      throw error;
    }

    // Validate content type
    const contentType = response.headers.get("content-type");
    const formatValidation = validateImageFormat(contentType);

    if (!formatValidation.isValid) {
      const error = new Error(formatValidation.error);
      error.retryable = false;
      throw error;
    }

    // Check content length
    const contentLength = response.headers.get("content-length");
    if (contentLength && parseInt(contentLength) > IMAGE_CONFIG.MAX_SIZE) {
      const error = new Error(
        `Image too large: ${contentLength} bytes (max: ${IMAGE_CONFIG.MAX_SIZE} bytes)`
      );
      error.retryable = false;
      throw error;
    }

    // Download the image data
    const arrayBuffer = await response.arrayBuffer();

    // Final size check
    if (arrayBuffer.byteLength > IMAGE_CONFIG.MAX_SIZE) {
      const error = new Error(
        `Image too large: ${arrayBuffer.byteLength} bytes (max: ${IMAGE_CONFIG.MAX_SIZE} bytes)`
      );
      error.retryable = false;
      throw error;
    }

    return {
      success: true,
      data: arrayBuffer,
      contentType: formatValidation.format,
      extension: formatValidation.extension,
      size: arrayBuffer.byteLength,
      url: urlValidation.url,
    };
  } catch (error) {
    // Clear timeout if it exists
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // Determine if error is retryable
    const isRetryable = isRetryableError(error);
    const errorType = categorizeError(error);

    // Return detailed error information
    return {
      success: false,
      error: createUserFriendlyErrorMessage(error),
      originalError: error.message,
      url: url,
      retryable: isRetryable,
      errorType: errorType,
    };
  }
};

/**
 * Download image with enhanced retry logic and exponential backoff
 * @param {string} url - Image URL to download
 * @param {Object} options - Download options
 * @returns {Promise<Object>} Download result with comprehensive error information
 */
const downloadImageWithRetry_errorService = async (url, options = {}) => {
  const {
    maxRetries = IMAGE_CONFIG.MAX_RETRIES,
    timeout = IMAGE_CONFIG.TIMEOUT,
    retryDelayBase = IMAGE_CONFIG.RETRY_DELAY_BASE,
    logRetries = true,
  } = options;

  let lastError = null;
  let attempts = 0;

  // Try initial download plus retries
  for (let retryCount = 0; retryCount <= maxRetries; retryCount++) {
    attempts++;

    try {
      // Attempt to download the image
      const result = await downloadImage(url, { timeout });

      if (result.success) {
        return {
          ...result,
          attempts,
          retryCount,
        };
      }

      lastError = result;

      // Don't retry if explicitly marked as non-retryable
      if (!result.retryable) {
        if (logRetries) {
          prodLogger.warn(
            `Image download failed with non-retryable error: ${result.error}`
          );
        }
        break;
      }

      // Don't retry on last attempt
      if (retryCount === maxRetries) {
        if (logRetries) {
          prodLogger.warn(
            `Image download failed after ${attempts} attempts: ${result.error}`
          );
        }
        break;
      }

      // Calculate delay with exponential backoff and jitter
      const delay = retryDelayBase * Math.pow(2, retryCount);
      const jitter = Math.random() * 0.1 * delay; // Add 10% jitter
      const totalDelay = Math.min(delay + jitter, 10000); // Cap at 10 seconds

      if (logRetries) {
        prodLogger.debug(
          `Retrying image download (${retryCount + 1
          }/${maxRetries}) in ${Math.round(totalDelay)}ms: ${url}`
        );
      }

      // Wait before retrying
      await sleep(totalDelay);
    } catch (error) {
      lastError = {
        success: false,
        error: error.message,
        errorType: categorizeError(error),
        retryable: isRetryableError(error),
        url,
      };

      // Don't retry on last attempt
      if (retryCount === maxRetries) {
        break;
      }

      // Calculate delay with exponential backoff for unexpected errors
      const delay = retryDelayBase * Math.pow(2, retryCount);
      const jitter = Math.random() * 0.1 * delay;
      const totalDelay = Math.min(delay + jitter, 10000);

      if (logRetries) {
        prodLogger.warn(
          `Unexpected error during image download, retrying in ${Math.round(
            totalDelay
          )}ms:`,
          error
        );
      }

      await sleep(totalDelay);
    }
  }

  // If we got here, all attempts failed
  return {
    success: false,
    error: lastError?.error || "Image download failed after multiple attempts",
    originalError: lastError?.originalError,
    errorType: lastError?.errorType || "unknown_error",
    url,
    attempts,
    retryable: false,
  };
};

/**
 * Validate and sanitize document data for DOCX generation
 * @param {Object} documentData - Document metadata to validate
 * @returns {Object} Validation result with sanitized data and errors
 */
const validateDocumentData_errorService = (documentData) => {
  const result = {
    isValid: true,
    sanitizedData: {},
    errors: [],
    warnings: [],
  };

  try {
    if (!documentData || typeof documentData !== "object") {
      result.isValid = false;
      result.errors.push("Document data must be an object");
      return result;
    }

    // Sanitize and validate title
    const title = documentData.title;
    if (title && typeof title === "string") {
      const sanitizedTitle = title.trim().substring(0, 255);
      if (sanitizedTitle.length === 0) {
        result.warnings.push("Document title is empty after sanitization");
        result.sanitizedData.title = "Untitled Document";
      } else {
        result.sanitizedData.title = sanitizedTitle;
        if (sanitizedTitle.length < title.length) {
          result.warnings.push(
            "Document title was truncated to 255 characters"
          );
        }
      }
    } else {
      result.sanitizedData.title = "Untitled Document";
      if (title) {
        result.warnings.push("Invalid title format, using default");
      }
    }

    // Sanitize and validate author
    const author = documentData.author;
    if (author && typeof author === "string") {
      const sanitizedAuthor = author.trim().substring(0, 100);
      if (sanitizedAuthor.length > 0) {
        result.sanitizedData.author = sanitizedAuthor;
        if (sanitizedAuthor.length < author.length) {
          result.warnings.push("Author name was truncated to 100 characters");
        }
      } else {
        result.sanitizedData.author = "Unknown Author";
        result.warnings.push("Author name is empty after sanitization");
      }
    } else {
      result.sanitizedData.author = "Unknown Author";
      if (author) {
        result.warnings.push("Invalid author format, using default");
      }
    }

    // Sanitize and validate description
    const description = documentData.description;
    if (description && typeof description === "string") {
      const sanitizedDescription = description.trim().substring(0, 500);
      if (sanitizedDescription.length > 0) {
        result.sanitizedData.description = sanitizedDescription;
        if (sanitizedDescription.length < description.length) {
          result.warnings.push("Description was truncated to 500 characters");
        }
      } else {
        result.sanitizedData.description = "";
      }
    } else {
      result.sanitizedData.description = "";
      if (description) {
        result.warnings.push("Invalid description format, using empty string");
      }
    }

    return result;
  } catch (error) {
    // Log validation errors - these are important for debugging
    if (import.meta.env.DEV) {
      prodLogger.error("Error validating document data:", error);
    }
    result.isValid = false;
    result.errors.push(`Validation failed: ${error.message}`);
    return result;
  }
};

/**
 * Create comprehensive error summary for user display
 * @param {Array} errors - Array of error messages
 * @param {Array} warnings - Array of warning messages
 * @param {Object} context - Additional context information
 * @returns {Object} Error summary with user-friendly messages
 */
const createErrorSummary_errorService = (
  errors = [],
  warnings = [],
  context = {}
) => {
  const summary = {
    hasErrors: errors.length > 0,
    hasWarnings: warnings.length > 0,
    errorCount: errors.length,
    warningCount: warnings.length,
    userMessage: "",
    technicalDetails: {
      errors,
      warnings,
      context,
    },
  };

  // Create user-friendly message
  if (summary.hasErrors) {
    if (errors.length === 1) {
      summary.userMessage = `Export failed: ${errors[0]}`;
    } else {
      summary.userMessage = `Export failed with ${errors.length} errors. Please check the document and try again.`;
    }
  } else if (summary.hasWarnings) {
    if (warnings.length === 1) {
      summary.userMessage = `Export completed with a warning: ${warnings[0]}`;
    } else {
      summary.userMessage = `Export completed with ${warnings.length} warnings. The document was generated but some issues were encountered.`;
    }
  } else {
    summary.userMessage = "Export completed successfully.";
  }

  return summary;
};

/**
 * Process multiple images with enhanced concurrent downloads and error handling
 * @param {Array} images - Array of image objects with src, alt, etc.
 * @param {Object} options - Processing options
 * @returns {Promise<Object>} Processing result with successful and failed downloads
 */
export const processImages = async (images, options = {}) => {
  const {
    concurrency = 3,
    maxRetries = IMAGE_CONFIG.MAX_RETRIES,
    timeout = IMAGE_CONFIG.TIMEOUT,
    skipOnError = true,
    logProgress = true,
  } = options;

  if (!Array.isArray(images) || images.length === 0) {
    return {
      success: true,
      processedImages: [],
      failedImages: [],
      totalImages: 0,
      successCount: 0,
      failureCount: 0,
      processingTime: 0,
    };
  }

  const startTime = Date.now();
  const processedImages = [];
  const failedImages = [];
  const errors = [];
  const warnings = [];

  if (logProgress && images.length > 0) {
    prodLogger.debug(
      `Processing ${images.length} images with concurrency ${concurrency} and max retries ${maxRetries}`
    );
  }

  // Validate all image URLs first
  const validImages = [];
  const invalidImages = [];

  images.forEach((image, index) => {
    const validation = validateImageUrl(image.src);
    if (validation.isValid) {
      validImages.push({ ...image, index });
    } else {
      invalidImages.push({
        ...image,
        index,
        error: validation.error,
        errorType: "validation_error",
        downloadSuccess: false,
      });
    }
  });

  // Add invalid images to failed list
  failedImages.push(...invalidImages);

  if (invalidImages.length > 0) {
    warnings.push(
      `${invalidImages.length} images have invalid URLs and will be skipped`
    );
    if (logProgress && import.meta.env.DEV) {
      prodLogger.warn(`Skipping ${invalidImages.length} images with invalid URLs`);
    }
  }

  if (validImages.length === 0) {
    return {
      success: skipOnError,
      processedImages: [],
      failedImages,
      totalImages: images.length,
      successCount: 0,
      failureCount: failedImages.length,
      processingTime: Date.now() - startTime,
      errors,
      warnings,
    };
  }

  // Process valid images in batches with concurrency control
  for (let i = 0; i < validImages.length; i += concurrency) {
    const batch = validImages.slice(i, i + concurrency);

    if (logProgress) {
      prodLogger.debug(
        `Processing batch ${Math.floor(i / concurrency) + 1}/${Math.ceil(
          validImages.length / concurrency
        )} (${batch.length} images)`
      );
    }

    const batchPromises = batch.map(async (image) => {
      try {
        const downloadResult = await downloadImageWithRetry(image.src, {
          maxRetries,
          timeout,
          logRetries: logProgress,
        });

        if (downloadResult.success) {
          processedImages.push({
            ...image,
            data: downloadResult.data,
            contentType: downloadResult.contentType,
            extension: downloadResult.extension,
            size: downloadResult.size,
            downloadSuccess: true,
            attempts: downloadResult.attempts,
          });

          if (logProgress && downloadResult.attempts > 1) {
            prodLogger.debug(
              `Successfully downloaded image after ${downloadResult.attempts} attempts: ${image.src}`
            );
          }
        } else {
          failedImages.push({
            ...image,
            error: downloadResult.error,
            errorType: downloadResult.errorType,
            downloadSuccess: false,
            attempts: downloadResult.attempts,
          });

          if (logProgress) {
            prodLogger.warn(
              `Failed to download image after ${downloadResult.attempts || 1
              } attempts: ${image.src} - ${downloadResult.error}`
            );
          }
        }
      } catch (error) {
        failedImages.push({
          ...image,
          error: `Unexpected error: ${error.message}`,
          errorType: "unknown_error",
          downloadSuccess: false,
        });

        if (logProgress && import.meta.env.DEV) {
          prodLogger.error(
            `Unexpected error processing image ${image.src}:`,
            error
          );
        }
      }
    });

    // Wait for current batch to complete before starting next batch
    await Promise.all(batchPromises);
  }

  const processingTime = Date.now() - startTime;

  if (logProgress && import.meta.env.DEV) {
    prodLogger.debug(
      `Image processing completed in ${processingTime}ms: ${processedImages.length} successful, ${failedImages.length} failed`
    );
  }

  // Determine overall success based on skipOnError flag
  const success = skipOnError
    ? processedImages.length > 0
    : failedImages.length === 0;

  if (failedImages.length > 0) {
    const message = `${failedImages.length} of ${images.length} images failed to download`;
    if (skipOnError) {
      warnings.push(message);
    } else {
      errors.push(message);
    }
  }

  return {
    success,
    processedImages,
    failedImages,
    totalImages: images.length,
    successCount: processedImages.length,
    failureCount: failedImages.length,
    processingTime,
    errors,
    warnings,
  };
};

/**
 * Create graceful error messages for different failure scenarios
 * @param {Array} failedImages - Array of failed image processing results
 * @returns {Object} Categorized error messages
 */
export const createImageErrorSummary = (failedImages) => {
  const summary = {
    networkErrors: [],
    formatErrors: [],
    sizeErrors: [],
    accessErrors: [],
    timeoutErrors: [],
    unknownErrors: [],
    totalFailed: failedImages.length,
  };

  failedImages.forEach((failed) => {
    const errorType =
      failed.errorType || categorizeError({ message: failed.error });

    switch (errorType) {
      case "timeout":
        summary.timeoutErrors.push(failed);
        break;
      case "network_error":
        summary.networkErrors.push(failed);
        break;
      case "validation_error":
      case "format_error":
        summary.formatErrors.push(failed);
        break;
      case "size_error":
        summary.sizeErrors.push(failed);
        break;
      case "client_error":
        summary.accessErrors.push(failed);
        break;
      default:
        summary.unknownErrors.push(failed);
        break;
    }
  });

  return summary;
};

/**
 * Generate user-friendly error message for image processing failures
 * @param {Object} imageStats - Image processing statistics
 * @returns {string} User-friendly error message
 */
const generateImageErrorMessage_errorService = (imageStats) => {
  if (!imageStats || imageStats.failureCount === 0) {
    return "";
  }

  const { totalImages, successCount, failureCount, failedImages } = imageStats;

  if (failureCount === totalImages) {
    return `All ${totalImages} images failed to process. The document will be generated without images.`;
  }

  const errorSummary = createImageErrorSummary(failedImages);
  let message = `${failureCount} of ${totalImages} images could not be processed. `;

  const errorDetails = [];
  if (errorSummary.networkErrors.length > 0) {
    errorDetails.push(
      `${errorSummary.networkErrors.length} due to network issues`
    );
  }
  if (errorSummary.timeoutErrors.length > 0) {
    errorDetails.push(`${errorSummary.timeoutErrors.length} due to timeouts`);
  }
  if (errorSummary.formatErrors.length > 0) {
    errorDetails.push(
      `${errorSummary.formatErrors.length} due to unsupported formats`
    );
  }
  if (errorSummary.sizeErrors.length > 0) {
    errorDetails.push(
      `${errorSummary.sizeErrors.length} due to size limitations`
    );
  }
  if (errorSummary.accessErrors.length > 0) {
    errorDetails.push(
      `${errorSummary.accessErrors.length} due to access restrictions`
    );
  }
  if (errorSummary.unknownErrors.length > 0) {
    errorDetails.push(
      `${errorSummary.unknownErrors.length} due to other issues`
    );
  }

  if (errorDetails.length > 0) {
    message += `Failures: ${errorDetails.join(", ")}.`;
  }

  message += ` The document will be generated with ${successCount} images and placeholders for failed images.`;

  return message;
};

/**
 * Create a detailed error report for logging or debugging
 * @param {Object} imageStats - Image processing statistics
 * @returns {Object} Detailed error report
 */
export const createDetailedErrorReport = (imageStats) => {
  if (!imageStats || !imageStats.failedImages) {
    return { hasErrors: false };
  }

  const errorSummary = createImageErrorSummary(imageStats.failedImages);

  return {
    hasErrors: imageStats.failureCount > 0,
    totalImages: imageStats.totalImages,
    successCount: imageStats.successCount,
    failureCount: imageStats.failureCount,
    processingTime: imageStats.processingTime,
    errorSummary,
    userMessage: generateImageErrorMessage(imageStats),
    failedUrls: imageStats.failedImages.map((img) => img.src),
  };
};
