/**
 * Replicate Service for RapidDoc AI
 * Handles AI image generation using Replicate's API
 * 
 * Features:
 * - Image generation with various models
 * - Progress tracking and status polling
 * - Error handling and retry logic
 * - Mock fallbacks for development
 */

import errorMonitor from "../utils/errorMonitor";
import { handleApiError, createApiErrorResponse } from './apiErrorHandler.js';
import { updateServiceStatus, createServiceUnavailableNotification } from './userNotificationService.js';

import { prodLogger } from '../utils/prodLogger.js';
// Environment variables for Replicate API
const REPLICATE_API_KEY = import.meta.env.VITE_REPLICATE_API_KEY;

// Create a logger for the Replicate service
const replicateLogger = errorMonitor.createContextLogger("ReplicateService");

// Replicate API configuration
const REPLICATE_CONFIG = {
  BASE_URL: 'https://api.replicate.com/v1',
  DEFAULT_MODEL: 'stability-ai/stable-diffusion:27b93a2413e7f36cd83da926f3656280b2931564ff050bf9575f1fdf9bcd7478',
  TIMEOUT: 300000, // 5 minutes for image generation
  POLL_INTERVAL: 2000, // 2 seconds between status checks
  MAX_RETRIES: 3,
  RETRY_DELAY_BASE: 1000, // 1 second base delay
};

// Available image generation models
export const IMAGE_MODELS = {
  'stable-diffusion': {
    id: 'stability-ai/stable-diffusion:27b93a2413e7f36cd83da926f3656280b2931564ff050bf9575f1fdf9bcd7478',
    name: 'Stable Diffusion',
    description: 'High-quality general purpose image generation',
    category: 'general'
  },
  'sdxl': {
    id: 'stability-ai/sdxl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b',
    name: 'SDXL',
    description: 'Latest Stable Diffusion XL for enhanced quality',
    category: 'general'
  },
  'realistic': {
    id: 'lucataco/realistic-vision-v5:eab4d8c6e0b8b5b8e0b8b5b8e0b8b5b8e0b8b5b8',
    name: 'Realistic Vision',
    description: 'Photorealistic image generation',
    category: 'photorealistic'
  }
};

// Image generation styles and presets
export const IMAGE_STYLES = {
  'photorealistic': {
    name: 'Photorealistic',
    prompt_suffix: ', photorealistic, high quality, detailed',
    negative_prompt: 'cartoon, anime, painting, drawing, sketch'
  },
  'artistic': {
    name: 'Artistic',
    prompt_suffix: ', artistic, creative, beautiful composition',
    negative_prompt: 'low quality, blurry, distorted'
  },
  'illustration': {
    name: 'Illustration',
    prompt_suffix: ', digital illustration, clean lines, vibrant colors',
    negative_prompt: 'photograph, realistic, low quality'
  },
  'professional': {
    name: 'Professional',
    prompt_suffix: ', professional, clean, business appropriate',
    negative_prompt: 'casual, messy, inappropriate, low quality'
  }
};

// Image size presets
export const IMAGE_SIZES = {
  'square': { width: 512, height: 512, label: 'Square (512×512)' },
  'landscape': { width: 768, height: 512, label: 'Landscape (768×512)' },
  'portrait': { width: 512, height: 768, label: 'Portrait (512×768)' },
  'wide': { width: 1024, height: 512, label: 'Wide (1024×512)' }
};

/**
 * Check if Replicate is configured and available
 * @returns {boolean} True if Replicate API key is available
 */
export const isReplicateConfigured = () => {
  return !!(REPLICATE_API_KEY && REPLICATE_API_KEY !== 'your-replicate-api-key-here');
};

// Lazy initialization - will be called when service is first used
let isReplicateInitialized = false;
const ensureReplicateInitialized = () => {
  if (isReplicateInitialized) return;

  if (isReplicateConfigured()) {
    try {
      updateServiceStatus('replicate', true);
      replicateLogger.info("Replicate service initialized successfully");
    } catch (statusError) {
      prodLogger.warn('Could not update service status:', statusError.message);
    }
  } else {
    try {
      updateServiceStatus('replicate', false, { reason: 'API key not configured' });
      replicateLogger.warn("Replicate API key not configured - image generation will not be available");
    } catch (statusError) {
      prodLogger.warn('Could not update service status:', statusError.message);
    }
  }

  isReplicateInitialized = true;
};

/**
 * Get available image generation providers
 * @returns {Array} Array of available providers
 */
export const getAvailableProviders = () => {
  const providers = [];
  if (isReplicateConfigured()) {
    providers.push('replicate');
  }
  return providers;
};

/**
 * Create headers for Replicate API requests
 * @returns {Object} Headers object
 */
const createHeaders = () => {
  return {
    'Authorization': `Token ${REPLICATE_API_KEY}`,
    'Content-Type': 'application/json',
  };
};

/**
 * Make a request to Replicate API with error handling and retry logic
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Request options
 * @param {number} retryCount - Current retry attempt
 * @returns {Promise<Object>} API response
 */
const makeReplicateRequest = async (endpoint, options = {}, retryCount = 0) => {
  const url = `${REPLICATE_CONFIG.BASE_URL}${endpoint}`;

  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        ...createHeaders(),
        ...options.headers
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const error = new Error(`Replicate API error: ${response.status} - ${errorData.detail || response.statusText}`);

      // Check if this is a retryable error
      const isRetryable = response.status >= 500 || response.status === 429; // Server errors or rate limiting

      if (isRetryable && retryCount < REPLICATE_CONFIG.MAX_RETRIES) {
        const delay = REPLICATE_CONFIG.RETRY_DELAY_BASE * Math.pow(2, retryCount); // Exponential backoff

        replicateLogger.warn(`Retrying request after ${delay}ms`, {
          endpoint,
          retryCount: retryCount + 1,
          status: response.status,
          error: errorData.detail || response.statusText
        });

        await new Promise(resolve => setTimeout(resolve, delay));
        return makeReplicateRequest(endpoint, options, retryCount + 1);
      }

      throw error;
    }

    return await response.json();
  } catch (error) {
    // Network errors are also retryable
    if (error.name === 'TypeError' && retryCount < REPLICATE_CONFIG.MAX_RETRIES) {
      const delay = REPLICATE_CONFIG.RETRY_DELAY_BASE * Math.pow(2, retryCount);

      replicateLogger.warn(`Retrying request after network error, delay: ${delay}ms`, {
        endpoint,
        retryCount: retryCount + 1,
        error: error.message
      });

      await new Promise(resolve => setTimeout(resolve, delay));
      return makeReplicateRequest(endpoint, options, retryCount + 1);
    }

    replicateLogger.error(error, {
      action: 'api_request',
      endpoint,
      retryCount,
      message: 'Failed to make Replicate API request'
    });
    throw error;
  }
};

/**
 * Start image generation prediction
 * @param {string} prompt - Text prompt for image generation
 * @param {Object} options - Generation options
 * @returns {Promise<Object>} Prediction object with ID for polling
 */
const startPrediction = async (prompt, options = {}) => {
  const {
    model = REPLICATE_CONFIG.DEFAULT_MODEL,
    style = 'photorealistic',
    size = 'landscape',
    negative_prompt = '',
    num_inference_steps = 20,
    guidance_scale = 7.5
  } = options;

  // Apply style modifications to prompt
  const styleConfig = IMAGE_STYLES[style] || IMAGE_STYLES.photorealistic;
  const enhancedPrompt = prompt + styleConfig.prompt_suffix;
  const finalNegativePrompt = negative_prompt || styleConfig.negative_prompt;

  // Get size configuration
  const sizeConfig = IMAGE_SIZES[size] || IMAGE_SIZES.landscape;

  const input = {
    prompt: enhancedPrompt,
    negative_prompt: finalNegativePrompt,
    width: sizeConfig.width,
    height: sizeConfig.height,
    num_inference_steps,
    guidance_scale,
    scheduler: "K_EULER"
  };

  replicateLogger.info("Starting image generation", {
    model,
    prompt: prompt.substring(0, 100),
    style,
    size: `${sizeConfig.width}x${sizeConfig.height}`
  });

  return await makeReplicateRequest('/predictions', {
    method: 'POST',
    body: JSON.stringify({
      version: model,
      input
    })
  });
};

/**
 * Poll prediction status until completion
 * @param {string} predictionId - Prediction ID to poll
 * @param {Function} onProgress - Progress callback function
 * @returns {Promise<Object>} Final prediction result
 */
const pollPrediction = async (predictionId, onProgress = null) => {
  const startTime = Date.now();
  let pollCount = 0;

  while (true) {
    try {
      const prediction = await makeReplicateRequest(`/predictions/${predictionId}`);
      pollCount++;

      // Calculate estimated progress based on status and time elapsed
      let estimatedProgress = prediction.progress || 0;

      // If API doesn't provide progress, estimate based on status and time
      if (estimatedProgress === 0 && prediction.status) {
        switch (prediction.status) {
          case 'starting':
            estimatedProgress = 0.1; // 10%
            break;
          case 'processing':
            // Estimate progress based on elapsed time (most generations take 10-60 seconds)
            const elapsed = Date.now() - startTime;
            const estimatedDuration = 30000; // 30 seconds average
            estimatedProgress = Math.min(0.9, 0.1 + (elapsed / estimatedDuration) * 0.8); // 10% to 90%
            break;
          case 'succeeded':
            estimatedProgress = 1.0; // 100%
            break;
          default:
            estimatedProgress = Math.min(0.8, pollCount * 0.1); // Gradual increase based on poll count
        }
      }

      // Call progress callback if provided
      if (onProgress) {
        onProgress({
          status: prediction.status,
          progress: estimatedProgress,
          elapsed: Date.now() - startTime
        });
      }

      // Check if prediction is complete
      if (prediction.status === 'succeeded') {
        replicateLogger.info("Image generation completed successfully", {
          predictionId,
          duration: Date.now() - startTime
        });
        return prediction;
      }

      if (prediction.status === 'failed') {
        const error = new Error(`Image generation failed: ${prediction.error || 'Unknown error'}`);
        replicateLogger.error(error, {
          predictionId,
          duration: Date.now() - startTime
        });
        throw error;
      }

      if (prediction.status === 'canceled') {
        const error = new Error('Image generation was canceled');
        replicateLogger.warn(error.message, { predictionId });
        throw error;
      }

      // Check timeout
      if (Date.now() - startTime > REPLICATE_CONFIG.TIMEOUT) {
        const error = new Error('Image generation timed out');
        replicateLogger.error(error, {
          predictionId,
          timeout: REPLICATE_CONFIG.TIMEOUT
        });
        throw error;
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, REPLICATE_CONFIG.POLL_INTERVAL));
      
    } catch (error) {
      if (error.message.includes('timed out') || error.message.includes('canceled')) {
        throw error;
      }
      
      // For other errors, wait and retry
      replicateLogger.warn("Error polling prediction, retrying", {
        predictionId,
        error: error.message
      });
      await new Promise(resolve => setTimeout(resolve, REPLICATE_CONFIG.POLL_INTERVAL));
    }
  }
};

/**
 * Generate image using Replicate API
 * @param {string} prompt - Text prompt for image generation
 * @param {Object} options - Generation options
 * @param {Function} onProgress - Progress callback function
 * @returns {Promise<Object>} Generated image data
 */
export const generateImage = async (prompt, options = {}, onProgress = null) => {
  ensureReplicateInitialized();
  try {
    // Validate inputs
    if (!prompt || typeof prompt !== 'string' || prompt.trim().length === 0) {
      throw new Error('Valid prompt is required for image generation');
    }

    // Check if Replicate is configured
    if (!isReplicateConfigured()) {
      const error = new Error('Replicate image generation is not available. Please configure your API key.');
      replicateLogger.error(error, {
        action: 'generate_image',
        prompt: prompt.substring(0, 50),
        reason: 'service_not_configured'
      });
      handleApiError(error, { service: 'Replicate Image Generation' });
    }

    replicateLogger.info("Starting image generation request", {
      prompt: prompt.substring(0, 100),
      options
    });

    // Start prediction
    const prediction = await startPrediction(prompt, options);
    
    // Poll for completion
    const result = await pollPrediction(prediction.id, onProgress);
    
    // Process result
    if (result.output && result.output.length > 0) {
      const imageUrl = Array.isArray(result.output) ? result.output[0] : result.output;
      
      return {
        success: true,
        imageUrl,
        prompt,
        predictionId: prediction.id,
        metadata: {
          model: options.model || REPLICATE_CONFIG.DEFAULT_MODEL,
          style: options.style || 'photorealistic',
          size: options.size || 'landscape',
          generatedAt: new Date().toISOString()
        }
      };
    } else {
      throw new Error('No image generated in response');
    }

  } catch (error) {
    replicateLogger.error(error, {
      action: 'generate_image',
      prompt: prompt.substring(0, 50),
      message: 'Image generation failed'
    });

    // Update service status based on error type
    if (error.message.includes('rate limit') || error.message.includes('quota')) {
      updateServiceStatus('replicate', false, { quotaExhausted: true });
    } else if (error.message.includes('timeout') || error.message.includes('network')) {
      updateServiceStatus('replicate', false, { temporaryFailure: true });
    } else if (error.message.includes('503') || error.message.includes('502')) {
      updateServiceStatus('replicate', false, { serviceUnavailable: true });
    }

    // Handle the error with proper user messaging
    handleApiError(error, { service: 'Replicate Image Generation' });
  }
};

/**
 * Cancel an ongoing image generation
 * @param {string} predictionId - Prediction ID to cancel
 * @returns {Promise<Object>} Cancellation result
 */
export const cancelGeneration = async (predictionId) => {
  try {
    if (!isReplicateConfigured()) {
      throw new Error('Replicate not configured');
    }

    replicateLogger.info("Canceling image generation", { predictionId });

    return await makeReplicateRequest(`/predictions/${predictionId}/cancel`, {
      method: 'POST'
    });
  } catch (error) {
    replicateLogger.error(error, {
      action: 'cancel_generation',
      predictionId,
      message: 'Failed to cancel image generation'
    });
    throw error;
  }
};



/**
 * Validate image generation options
 * @param {Object} options - Options to validate
 * @returns {Object} Validated options with defaults
 */
export const validateGenerationOptions = (options = {}) => {
  const validated = { ...options };

  // Validate model
  if (validated.model && !Object.values(IMAGE_MODELS).some(m => m.id === validated.model)) {
    validated.model = REPLICATE_CONFIG.DEFAULT_MODEL;
  }

  // Validate style
  if (validated.style && !IMAGE_STYLES[validated.style]) {
    validated.style = 'photorealistic';
  }

  // Validate size
  if (validated.size && !IMAGE_SIZES[validated.size]) {
    validated.size = 'landscape';
  }

  // Validate numeric parameters
  if (validated.num_inference_steps) {
    validated.num_inference_steps = Math.max(1, Math.min(50, parseInt(validated.num_inference_steps)));
  }

  if (validated.guidance_scale) {
    validated.guidance_scale = Math.max(1, Math.min(20, parseFloat(validated.guidance_scale)));
  }

  return validated;
};

/**
 * Get generation cost estimate (if available from Replicate)
 * @param {Object} options - Generation options
 * @returns {Object} Cost estimate information
 */
export const getGenerationCostEstimate = (options = {}) => {
  const sizeConfig = IMAGE_SIZES[options.size || 'landscape'];
  const steps = options.num_inference_steps || 20;

  // Rough cost estimation (actual costs may vary)
  const basePixels = 512 * 512;
  const actualPixels = sizeConfig.width * sizeConfig.height;
  const pixelMultiplier = actualPixels / basePixels;
  const stepMultiplier = steps / 20;

  const estimatedCost = 0.0023 * pixelMultiplier * stepMultiplier; // Approximate cost in USD

  return {
    estimatedCost: Math.round(estimatedCost * 10000) / 10000, // Round to 4 decimal places
    currency: 'USD',
    factors: {
      resolution: `${sizeConfig.width}×${sizeConfig.height}`,
      steps,
      pixelMultiplier: Math.round(pixelMultiplier * 100) / 100,
      stepMultiplier: Math.round(stepMultiplier * 100) / 100
    }
  };
};

/**
 * Check Replicate service health and availability
 * @returns {Promise<Object>} Service health status
 */
export const checkServiceHealth = async () => {
  try {
    if (!isReplicateConfigured()) {
      return {
        healthy: false,
        configured: false,
        message: 'API key not configured',
        recommendation: 'Add VITE_REPLICATE_API_KEY to environment variables'
      };
    }

    // Try a simple API call to check connectivity
    const response = await makeReplicateRequest('/models', { method: 'GET' });

    return {
      healthy: true,
      configured: true,
      message: 'Service is available',
      modelsAvailable: response?.results?.length || 0
    };

  } catch (error) {
    let recommendation = 'Check your internet connection and try again';

    if (error.message.includes('401') || error.message.includes('403')) {
      recommendation = 'Check your API key configuration';
    } else if (error.message.includes('429')) {
      recommendation = 'Rate limit reached, wait before trying again';
    } else if (error.message.includes('5')) {
      recommendation = 'Replicate service is temporarily unavailable';
    }

    return {
      healthy: false,
      configured: true,
      message: error.message,
      recommendation,
      canUseFallback: true
    };
  }
};

/**
 * Get alternative options when service is unavailable
 * @returns {Array} Array of alternative options
 */
export const getAlternativeOptions = () => {
  return [
    {
      type: 'upload',
      name: 'Upload Image',
      description: 'Upload your own image instead of generating one',
      available: true,
      recommended: true
    },
    {
      type: 'stock',
      name: 'Stock Images',
      description: 'Choose from available stock images',
      available: true,
      recommended: true
    },
    {
      type: 'manual',
      name: 'Add Later',
      description: 'Continue without an image and add one manually later',
      available: true,
      recommended: false
    }
  ];
};

// Export configuration objects for use in components
export { REPLICATE_CONFIG };
