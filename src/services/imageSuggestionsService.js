/**
 * Image Suggestions Service
 * Handles persistence, caching, and management of AI-generated image suggestions
 */

import { prodLogger } from "../utils/prodLogger.js";

class ImageSuggestionsService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours
  }

  /**
   * Cache key generator for image suggestions
   */
  generateCacheKey(documentId) {
    return `image_suggestions_${documentId}`;
  }

  /**
   * Store image suggestions in cache
   */
  cacheImageSuggestions(documentId, suggestions) {
    const cacheKey = this.generateCacheKey(documentId);
    const cacheData = {
      suggestions,
      timestamp: Date.now(),
      expiresAt: Date.now() + this.cacheExpiry,
    };

    this.cache.set(cacheKey, cacheData);
    prodLogger.debug(`📸 Image suggestions cached for document ${documentId}`);
  }

  /**
   * Retrieve image suggestions from cache
   */
  getCachedImageSuggestions(documentId) {
    const cacheKey = this.generateCacheKey(documentId);
    const cacheData = this.cache.get(cacheKey);

    if (!cacheData) {
      return null;
    }

    // Check if cache has expired
    if (Date.now() > cacheData.expiresAt) {
      this.cache.delete(cacheKey);
      prodLogger.debug(
        `🗑️ Expired image suggestions cache removed for document ${documentId}`
      );
      return null;
    }

    prodLogger.debug(
      `✅ Image suggestions retrieved from cache for document ${documentId}`
    );
    return cacheData.suggestions;
  }

  /**
   * Extract image suggestions from document data
   */
  extractImageSuggestions(documentData) {
    if (!documentData) {
      return {};
    }

    // First, try to get from questionnaire_data.imageAddition.suggestions (database format)
    if (documentData.questionnaire_data?.imageAddition?.suggestions) {
      return documentData.questionnaire_data.imageAddition.suggestions;
    }

    // Second, try to get from imageAddition.suggestions (component format)
    if (documentData.imageAddition?.suggestions) {
      return documentData.imageAddition.suggestions;
    }

    // Third, try to get from generated_content.imageAddition.suggestions (alternative format)
    if (documentData.generated_content?.imageAddition?.suggestions) {
      return documentData.generated_content.imageAddition.suggestions;
    }

    // Fallback: check if it's stored directly in the document (legacy)
    if (documentData.imageSuggestions) {
      return documentData.imageSuggestions;
    }

    return {};
  }

  /**
   * Validate image suggestions structure
   */
  validateImageSuggestions(suggestions) {
    if (!suggestions || typeof suggestions !== "object") {
      return false;
    }

    // Check if it has chapter keys and valid structure
    const chapterKeys = Object.keys(suggestions);
    if (chapterKeys.length === 0) {
      return false;
    }

    // Validate at least one chapter has valid image data
    return chapterKeys.some((key) => {
      const chapter = suggestions[key];
      return (
        chapter &&
        chapter.images &&
        Array.isArray(chapter.images) &&
        chapter.images.length > 0
      );
    });
  }

  /**
   * Merge document data with image suggestions
   */
  mergeImageSuggestionsWithDocument(documentData, imageSuggestions) {
    if (!this.validateImageSuggestions(imageSuggestions)) {
      return documentData;
    }

    return {
      ...documentData,
      imageAddition: documentData.imageAddition?.enabled
        ? {
            ...documentData.imageAddition,
            suggestions: imageSuggestions,
          }
        : documentData.imageAddition,
    };
  }

  /**
   * Clean up expired cache entries
   */
  cleanupCache() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, data] of this.cache.entries()) {
      if (now > data.expiresAt) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      prodLogger.debug(
        `🧹 Cleaned up ${cleanedCount} expired image suggestions cache entries`
      );
    }
  }

  /**
   * Get statistics about cached suggestions
   */
  getCacheStats() {
    const totalEntries = this.cache.size;
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const [key, data] of this.cache.entries()) {
      if (now > data.expiresAt) {
        expiredEntries++;
      } else {
        validEntries++;
      }
    }

    return {
      totalEntries,
      validEntries,
      expiredEntries,
      cacheHitRate: totalEntries > 0 ? (validEntries / totalEntries) * 100 : 0,
    };
  }

  /**
   * Force regeneration of image suggestions if they're missing or invalid
   */
  async regenerateImageSuggestionsIfNeeded(
    documentData,
    generateImageSuggestions
  ) {
    const existingSuggestions = this.extractImageSuggestions(documentData);

    if (this.validateImageSuggestions(existingSuggestions)) {
      prodLogger.debug(
        "✅ Valid image suggestions found, no regeneration needed"
      );
      return existingSuggestions;
    }

    prodLogger.warn(
      "⚠️ Invalid or missing image suggestions, attempting regeneration"
    );

    try {
      // Check if we have the necessary data for regeneration
      if (!documentData.documentOutline?.generatedOutline) {
        prodLogger.warn(
          "Cannot regenerate image suggestions: missing document outline"
        );
        return {};
      }

      const regeneratedSuggestions = await generateImageSuggestions(
        documentData,
        documentData.documentOutline.generatedOutline
      );

      if (this.validateImageSuggestions(regeneratedSuggestions)) {
        prodLogger.info("✅ Image suggestions successfully regenerated");
        this.cacheImageSuggestions(
          documentData.id || "unknown",
          regeneratedSuggestions
        );
        return regeneratedSuggestions;
      }
    } catch (error) {
      prodLogger.error("❌ Failed to regenerate image suggestions:", error);
    }

    return {};
  }
}

// Create singleton instance
export const imageSuggestionsService = new ImageSuggestionsService();

// Clean up cache periodically (every 30 minutes)
if (typeof window !== "undefined") {
  setInterval(() => {
    imageSuggestionsService.cleanupCache();
  }, 30 * 60 * 1000);
}

export default imageSuggestionsService;
