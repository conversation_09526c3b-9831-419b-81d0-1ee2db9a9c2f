/**
 * Overlay Preview Service for DocForge AI
 * Handles real-time preview updates for text overlay customizations
 * with debouncing, caching, and performance optimization
 */

import imageOverlayService from "./imageOverlayService.js";
import errorMonitor, { ErrorSeverity } from "../utils/errorMonitor.js";

import { prodLogger } from '../utils/prodLogger.js';
/**
 * Overlay Preview Service Class
 * Manages real-time preview generation with customizations
 */
class OverlayPreviewService {
  constructor() {
    this.updateQueue = new Map();
    this.previewCache = new Map();
    this.isProcessing = false;
    this.maxCacheSize = 50;
  }

  /**
   * Generate preview with customizations
   * @param {Object} template - Template configuration
   * @param {Object} documentData - Document metadata
   * @param {Object} customizations - User customizations
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Preview data
   */
  async generatePreviewWithCustomizations(
    template,
    documentData,
    customizations = {},
    options = {}
  ) {
    try {
      const { quality = 0.9, format = "png", useCache = true } = options;

      // Generate cache key
      const cacheKey = this.generateCacheKey(
        template,
        documentData,
        customizations
      );

      // Check cache first
      if (useCache && this.previewCache.has(cacheKey)) {
        prodLogger.debug("🎯 Using cached preview for customizations");
        return this.previewCache.get(cacheKey);
      }

      prodLogger.debug("🎨 Generating preview with customizations", {
        templateId: template.id,
        customizationCount: Object.keys(customizations).length,
      });

      // Use the document data as provided (should already be enhanced by cover preview service)
      const enhancedDocumentData = documentData;

      // Generate cover using image overlay service with customizations
      const templateCanvas =
        await imageOverlayService.renderTemplateWithCustomizations(
          template,
          enhancedDocumentData,
          customizations
        );

      const coverImageData = imageOverlayService.exportAsImage(
        templateCanvas,
        format,
        quality
      );

      // Create cover HTML for preview display
      const coverHTML = `<div style="width: 100%; height: 100%; margin: 0; padding: 0; background: white; overflow: hidden; display: flex; align-items: center; justify-content: center;">
        <img src="${coverImageData}" alt="Document Cover" style="width: 100%; height: 100%; object-fit: cover; display: block; margin: 0; padding: 0;" />
      </div>`;

      const previewData = {
        coverHTML,
        coverImageData,
        metadata: {
          templateId: template.id,
          templateName: template.name,
          templateCategory: template.category,
          documentData: enhancedDocumentData,
          customizations,
          previewType: "cover-with-customizations",
          generatedAt: new Date().toISOString(),
        },
      };

      // Cache the result
      if (useCache) {
        this.cachePreview(cacheKey, previewData);
      }

      prodLogger.debug("✅ Preview with customizations generated successfully");
      return previewData;
    } catch (error) {
      prodLogger.error("Error generating preview with customizations:", error);
      errorMonitor.captureError(
        error,
        { template: template?.id, customizations },
        ErrorSeverity.MEDIUM
      );
      throw error;
    }
  }

  /**
   * Queue preview update with debouncing
   * @param {string} updateId - Unique identifier for the update
   * @param {Function} updateFunction - Function to execute the update
   * @param {number} debounceMs - Debounce delay in milliseconds
   * @returns {Promise} Update promise
   */
  queuePreviewUpdate(updateId, updateFunction, debounceMs = 300) {
    return new Promise((resolve, reject) => {
      // Clear existing timeout for this update
      if (this.updateQueue.has(updateId)) {
        clearTimeout(this.updateQueue.get(updateId).timeout);
      }

      // Set new timeout
      const timeout = setTimeout(async () => {
        try {
          this.updateQueue.delete(updateId);
          const result = await updateFunction();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, debounceMs);

      // Store in queue
      this.updateQueue.set(updateId, {
        timeout,
        resolve,
        reject,
        updateFunction,
      });
    });
  }

  /**
   * Generate cache key for preview
   * @param {Object} template - Template configuration
   * @param {Object} documentData - Document metadata
   * @param {Object} customizations - User customizations
   * @returns {string} Cache key
   */
  generateCacheKey(template, documentData, customizations) {
    const templateKey = template?.id || "no-template";
    const dataKey = JSON.stringify({
      title: documentData?.title,
      author: documentData?.author,
      description: documentData?.description,
    });
    const customKey = JSON.stringify(customizations);

    // Create hash of the combined data
    return `${templateKey}-${this.hashString(dataKey + customKey)}`;
  }

  /**
   * Simple string hash function
   * @param {string} str - String to hash
   * @returns {string} Hash value
   */
  hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Cache preview data
   * @param {string} key - Cache key
   * @param {Object} data - Preview data to cache
   */
  cachePreview(key, data) {
    // Implement LRU cache behavior
    if (this.previewCache.size >= this.maxCacheSize) {
      const firstKey = this.previewCache.keys().next().value;
      this.previewCache.delete(firstKey);
    }

    this.previewCache.set(key, data);
  }

  /**
   * Clear preview cache
   * @param {string} templateId - Optional template ID to clear specific cache
   */
  clearCache(templateId = null) {
    if (templateId) {
      // Clear cache entries for specific template
      for (const [key, value] of this.previewCache.entries()) {
        if (value.metadata?.templateId === templateId) {
          this.previewCache.delete(key);
        }
      }
    } else {
      // Clear all cache
      this.previewCache.clear();
    }
  }

  /**
   * Generate enhanced document data for cover rendering
   * @param {Object} documentData - Original document data
   * @returns {Object} Enhanced document data
   */
  generateCoverDocumentData(documentData) {
    // Preserve original document data and only add missing optional fields
    return {
      // Preserve all original data first
      ...documentData,
      // Only add defaults for truly missing optional fields (not core content)
      subtitle: documentData?.subtitle || "",
      category: documentData?.category || "",
      tags: documentData?.tags || [],
      publishDate:
        documentData?.publishDate || new Date().toISOString().split("T")[0],
      // Note: title, author, description are preserved from original documentData
      // and should not be overridden with placeholder values
    };
  }

  /**
   * Cancel all pending updates
   */
  cancelPendingUpdates() {
    for (const [updateId, update] of this.updateQueue.entries()) {
      clearTimeout(update.timeout);
      update.reject(new Error("Update cancelled"));
    }
    this.updateQueue.clear();
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      cacheSize: this.previewCache.size,
      maxCacheSize: this.maxCacheSize,
      pendingUpdates: this.updateQueue.size,
      cacheKeys: Array.from(this.previewCache.keys()),
    };
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.cancelPendingUpdates();
    this.clearCache();
  }
}

// Create and export singleton instance
const overlayPreviewService = new OverlayPreviewService();
export default overlayPreviewService;
