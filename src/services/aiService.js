/**
 * AI Service for RapidDoc AI
 * Handles all AI-powered content generation using Google Gemini API
 *
 * Features:
 * - Sub-niche generation based on topics
 * - Document title generation
 * - Document outline creation
 * - Full content generation
 */

import { GoogleGenerativeAI } from "@google/generative-ai";
import { createDocumentTypePrompt } from "./documentTypePrompts.js";
import errorMonitor, { ErrorSeverity } from "../utils/errorMonitor";
import { handleApiError, createApiErrorResponse } from './apiErrorHandler.js';
import { updateServiceStatus, createServiceUnavailableNotification } from './userNotificationService.js';

import { prodLogger } from '../utils/prodLogger.js';
/**
 * Convert JSON content to markdown format as fallback
 * Handles cases where AI returns unexpected JSON instead of markdown
 * @param {string} content - Content that might be JSON
 * @returns {string} Markdown formatted content
 */
const convertJsonToMarkdown = (content) => {
  try {
    // Try to parse as JSON
    const jsonData = JSON.parse(content);

    // Handle different JSON structures that might be returned
    if (jsonData.chapterTitle && jsonData.sections) {
      // Handle chapter-like JSON structure
      let markdown = `# ${jsonData.chapterTitle}\n\n`;

      if (Array.isArray(jsonData.sections)) {
        jsonData.sections.forEach((section) => {
          if (typeof section === "string") {
            markdown += `${section}\n\n`;
          } else if (section.title && section.content) {
            markdown += `## ${section.title}\n\n${section.content}\n\n`;
          }
        });
      }

      return markdown;
    }

    // Handle simple content object
    if (jsonData.content) {
      return jsonData.content;
    }

    // Handle document structure with title and content arrays (common academic paper format)
    if (jsonData.title && Array.isArray(jsonData.introduction)) {
      let markdown = `# ${jsonData.title}\n\n`;

      // Process introduction array
      if (jsonData.introduction) {
        jsonData.introduction.forEach((item) => {
          if (item.type === "paragraph" && item.content) {
            markdown += `${item.content}\n\n`;
          } else if (item.type === "heading" && item.content) {
            const level = item.level || 2;
            const hashes = "#".repeat(level);
            markdown += `${hashes} ${item.content}\n\n`;
          }
        });
      }

      return markdown;
    }

    // Handle array of content items (common AI response format)
    if (Array.isArray(jsonData)) {
      return jsonData
        .map((item) => {
          if (typeof item === "string") return item;
          if (item.type === "paragraph" && item.content) return item.content;
          if (item.type === "heading" && item.content) {
            const level = item.level || 2;
            const hashes = "#".repeat(level);
            return `${hashes} ${item.content}`;
          }
          if (item.content) return item.content;
          if (item.text) return item.text;
          return JSON.stringify(item);
        })
        .join("\n\n");
    }

    // Handle document structure with title and content arrays
    if (jsonData.title && Array.isArray(jsonData.introduction)) {
      let markdown = `# ${jsonData.title}\n\n`;

      // Process introduction array
      if (jsonData.introduction) {
        jsonData.introduction.forEach((item) => {
          if (item.type === "paragraph" && item.content) {
            markdown += `${item.content}\n\n`;
          } else if (item.type === "heading" && item.content) {
            const level = item.level || 2;
            const hashes = "#".repeat(level);
            markdown += `${hashes} ${item.content}\n\n`;
          }
        });
      }

      return markdown;
    }

    // Handle nested content structures
    if (typeof jsonData === "object") {
      let markdown = "";

      // Check for title
      if (jsonData.title) {
        markdown += `# ${jsonData.title}\n\n`;
      }

      // Process each property that might contain content
      Object.entries(jsonData).forEach(([key, value]) => {
        if (key === "title") return; // Already handled

        if (Array.isArray(value)) {
          // Handle arrays of content items
          value.forEach((item) => {
            if (typeof item === "string") {
              markdown += `${item}\n\n`;
            } else if (item.type === "paragraph" && item.content) {
              markdown += `${item.content}\n\n`;
            } else if (item.type === "heading" && item.content) {
              const level = item.level || 2;
              const hashes = "#".repeat(level);
              markdown += `${hashes} ${item.content}\n\n`;
            } else if (item.content) {
              markdown += `${item.content}\n\n`;
            } else if (item.text) {
              markdown += `${item.text}\n\n`;
            }
          });
        } else if (typeof value === "string") {
          // Handle string values
          markdown += `${value}\n\n`;
        }
      });

      return markdown || "No content available";
    }

    // Handle nested content structures (fallback for complex JSON)
    if (typeof jsonData === "object") {
      let markdown = "";

      // Check for title
      if (jsonData.title) {
        markdown += `# ${jsonData.title}\n\n`;
      }

      // Process each property that might contain content
      Object.entries(jsonData).forEach(([key, value]) => {
        if (key === "title") return; // Already handled

        if (Array.isArray(value)) {
          // Handle arrays of content items
          value.forEach((item) => {
            if (typeof item === "string") {
              markdown += `${item}\n\n`;
            } else if (item.type === "paragraph" && item.content) {
              markdown += `${item.content}\n\n`;
            } else if (item.type === "heading" && item.content) {
              const level = item.level || 2;
              const hashes = "#".repeat(level);
              markdown += `${hashes} ${item.content}\n\n`;
            } else if (item.content) {
              markdown += `${item.content}\n\n`;
            } else if (item.text) {
              markdown += `${item.text}\n\n`;
            }
          });
        } else if (typeof value === "string") {
          // Handle string values
          markdown += `${value}\n\n`;
        }
      });

      return markdown || "No content available";
    }

    // If it's a valid JSON but unknown structure, return as formatted text
    return Object.entries(jsonData)
      .map(([key, value]) => `**${key}:** ${value}`)
      .join("\n\n");
  } catch (error) {
    // Not valid JSON, return original content
    return content;
  }
};

/**
 * Process AI response content to ensure it's in markdown format
 * @param {string} content - Raw AI response content
 * @returns {string} Processed markdown content
 */
const processAIContent = (content) => {
  if (!content) return "";

  // Remove any JSON code block markers if present
  const cleanContent = content
    .replace(/^```json\s*/, "")
    .replace(/\s*```$/, "");

  // Check if content looks like JSON (starts with { or [)
  const trimmedContent = cleanContent.trim();
  if (trimmedContent.startsWith("{") || trimmedContent.startsWith("[")) {
    return convertJsonToMarkdown(trimmedContent);
  }

  // Content is already in markdown format
  return cleanContent;
};

// Environment variables for AI API keys
const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;
const ANTHROPIC_API_KEY = import.meta.env.VITE_ANTHROPIC_API_KEY;

// Create a logger for the AI service
const aiLogger = errorMonitor.createContextLogger("AIService");

// Initialize Gemini AI
let genAI = null;
let model = null;

// Lazy initialization - will be called when services are first used
let isInitialized = false;
const ensureInitialized = () => {
  if (isInitialized) return;

  if (GEMINI_API_KEY && GEMINI_API_KEY !== 'your-gemini-api-key-here') {
    try {
      aiLogger.info("Initializing Gemini AI");
      genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
      model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
      aiLogger.info("Gemini AI initialized successfully");
      try {
        updateServiceStatus('gemini', true);
      } catch (statusError) {
        // Ignore service status errors during initialization
        prodLogger.warn('Could not update service status:', statusError.message);
      }
    } catch (error) {
      aiLogger.error(error, {
        action: "initialize_gemini",
        message: "Failed to initialize Gemini AI",
      });
      try {
        updateServiceStatus('gemini', false, { error: error.message });
      } catch (statusError) {
        // Ignore service status errors during initialization
        prodLogger.warn('Could not update service status:', statusError.message);
      }
    }
  } else {
    aiLogger.warn("Gemini API key not configured - AI features will not be available", {
      source: "initialization",
    });
    try {
      updateServiceStatus('gemini', false, { reason: 'API key not configured' });
    } catch (statusError) {
      // Ignore service status errors during initialization
      prodLogger.warn('Could not update service status:', statusError.message);
    }
  }

  isInitialized = true;
};

/**
 * Generate relevant sub-niches based on a main topic using Gemini AI
 * @param {string} topic - The main topic entered by the user
 * @param {string} language - The target language for content
 * @param {Object} documentData - Document configuration for type-specific prompts (optional)
 * @returns {Promise<Array>} Array of sub-niche objects
 */
export const generateSubNiches = async (topic, language = "english", documentData = null) => {
  ensureInitialized();
  try {
    // Use Gemini AI if available
    if (model && GEMINI_API_KEY) {
      let prompt;

      // Use document type-specific prompt if documentData is provided
      if (documentData) {
        // Enhanced subtype-aware prompt generation
        const documentType = documentData.documentPurpose?.primaryType || 'ebook';
        const subType = documentData.documentPurpose?.subType;

        if (documentType === 'academic' && subType) {
          prompt = createAcademicSubNichePrompt(topic, language, subType);
        } else if (documentType === 'business' && subType) {
          prompt = createBusinessSubNichePrompt(topic, language, subType);
        } else {
          prompt = createDocumentTypePrompt(documentData, "sub-topics");
        }
      } else {
        // Fallback to generic prompt for backward compatibility
        prompt = `You are an expert content strategist and niche specialist. Generate 8 highly relevant and specific sub-niches for the topic "${topic}".

Requirements:
- Each sub-niche should be specific and actionable
- Focus on areas that would make excellent content topics
- Consider different skill levels and approaches
- Make them appealing to content creators and audiences
- Ensure they are distinct from each other

Return ONLY a valid JSON array with objects containing:
- id: kebab-case identifier (e.g., "wellness-coaching")
- name: Clear, descriptive name (e.g., "Wellness and Health Coaching")
- description: Brief explanation of what this sub-niche covers (max 60 characters)

Topic: ${topic}
Language: ${language}

JSON Response:`;
      }

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Extract JSON from the response
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const subNiches = JSON.parse(jsonMatch[0]);

        // Validate the structure
        if (Array.isArray(subNiches) && subNiches.length > 0) {
          return subNiches.map((niche) => ({
            id:
              niche.id ||
              niche.name
                ?.toLowerCase()
                .replace(/\s+/g, "-")
                .replace(/[^a-z0-9-]/g, ""),
            name: niche.name || "Untitled Sub-niche",
            description: niche.description || "No description available",
          }));
        }
      }

      // If parsing fails, throw an error
      const parseError = new Error("Failed to parse AI response for sub-niches generation");
      aiLogger.error(parseError, {
        action: 'generate_sub_niches',
        topic: topic.substring(0, 50),
        response: text.substring(0, 200)
      });
      handleApiError(parseError, { service: 'Gemini AI - Sub-niches Generation' });
    }

    // Service not available
    const configError = new Error("Gemini AI is not configured. Please set up your API key to generate sub-niches.");
    aiLogger.error(configError, {
      action: 'generate_sub_niches',
      topic: topic.substring(0, 50),
      reason: 'service_not_configured'
    });
    handleApiError(configError, { service: 'Gemini AI - Sub-niches Generation' });
  } catch (error) {
    aiLogger.error(error, {
      action: 'generate_sub_niches',
      topic: topic.substring(0, 50),
      message: 'Failed to generate sub-niches with Gemini'
    });

    // Update service status and handle error
    updateServiceStatus('gemini', false, { lastError: error.message });
    handleApiError(error, { service: 'Gemini AI - Sub-niches Generation' });
  }
};

/**
 * Generate document titles based on topic, audience, and sub-niches using Gemini AI
 * Enhanced to handle imported content from URLs
 * @param {Object} params - Parameters for title generation
 * @returns {Promise<Array>} Array of title objects
 */
export const generateTitles = async ({
  topic,
  audience,
  subNiches = [],
  language = "english",
  documentData = null,
}) => {
  ensureInitialized();
  try {
    // Use Gemini AI if available
    if (model && GEMINI_API_KEY) {
      let prompt;

      // Check if we have imported content to enhance title generation
      const hasImportedContent =
        documentData?.documentPurpose?.importedContent?.extractedContent;

      // Use document type-specific prompt if documentData is provided
      if (documentData) {
        prompt = createDocumentTypePrompt(documentData, "title");

        // Enhance prompt with imported content context if available
        if (hasImportedContent) {
          const importedContent = documentData.documentPurpose.importedContent;
          prompt += `\n\nIMPORTED CONTENT CONTEXT:
Original Title: "${importedContent.originalTitle}"
Content Preview: "${importedContent.extractedContent.substring(0, 500)}..."
Word Count: ${importedContent.wordCount}

Please generate titles that improve upon or provide alternatives to the original title,
taking into account the actual content and making them more engaging and specific to the target audience.`;
        }
      } else {
        // Fallback to generic title prompt for backward compatibility
        const subNicheText =
          subNiches.length > 0
            ? `\nSelected sub-niches: ${subNiches
                .map((s) => s.name || s)
                .join(", ")}`
            : "";

        prompt = `You are an expert copywriter and content strategist. Generate 8 compelling document titles for the following specifications:

Topic: ${topic}
Target Audience: ${audience}${subNicheText}
Language: ${language}

Requirements:
- Create titles that are engaging and click-worthy
- Vary the styles: some descriptive, some catchy, some professional
- Make them specific to the target audience
- Include power words that drive engagement
- Ensure they clearly communicate value
- Length should be 40-80 characters for optimal readability

Return ONLY a valid JSON array with objects containing:
- id: unique identifier (e.g., "title-1")
- text: the actual title text
- style: one of "descriptive", "catchy", "professional", "inspirational", "academic"

JSON Response:`;
      }

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Extract JSON from the response
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const titles = JSON.parse(jsonMatch[0]);

        // Validate the structure
        if (Array.isArray(titles) && titles.length > 0) {
          return titles.map((title, index) => ({
            id: title.id || `title-${index + 1}`,
            text: title.text || title.title || "Untitled Document",
            style: title.style || "professional",
          }));
        }
      }

      // If parsing fails, throw an error
      const parseError = new Error("Failed to parse AI response for title generation");
      aiLogger.error(parseError, {
        action: 'generate_titles',
        topic: topic.substring(0, 50),
        response: text.substring(0, 200)
      });
      handleApiError(parseError, { service: 'Gemini AI - Title Generation' });
    }

    // Service not available
    const configError = new Error("Gemini AI is not configured. Please set up your API key to generate titles.");
    aiLogger.error(configError, {
      action: 'generate_titles',
      topic: topic.substring(0, 50),
      reason: 'service_not_configured'
    });
    handleApiError(configError, { service: 'Gemini AI - Title Generation' });
  } catch (error) {
    aiLogger.error(error, {
      action: 'generate_titles',
      topic: topic.substring(0, 50),
      message: 'Failed to generate titles with Gemini'
    });

    // Update service status and handle error
    updateServiceStatus('gemini', false, { lastError: error.message });
    handleApiError(error, { service: 'Gemini AI - Title Generation' });
  }
};

/**
 * Generate document outline based on all collected parameters using Gemini AI
 * Enhanced to incorporate imported content structure
 * @param {Object} documentData - Complete document configuration
 * @returns {Promise<Object>} Document outline object
 */
export const generateDocumentOutline = async (documentData) => {
  ensureInitialized();
  try {
    // Use Gemini AI if available
    if (model && GEMINI_API_KEY) {
      // Use new document type-specific prompt system
      let prompt = createDocumentTypePrompt(documentData, "outline");

      // Check if we have imported content to enhance outline generation
      const hasImportedContent =
        documentData?.documentPurpose?.importedContent?.extractedContent;

      if (hasImportedContent) {
        const importedContent = documentData.documentPurpose.importedContent;
        prompt += `\n\nIMPORTED CONTENT TO ENHANCE:
Original Title: "${importedContent.originalTitle}"
Source: ${importedContent.sourceUrl}
Content Length: ${importedContent.wordCount} words

Content to Structure and Improve:
"${importedContent.extractedContent}"

Please create an outline that:
1. Improves upon the structure of the imported content
2. Fills in any gaps or missing sections
3. Reorganizes content for better flow and readability
4. Adds relevant sections that would enhance the document
5. Maintains the core message and value of the original content
6. Adapts the structure to the target audience and document type`;
      }

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const outline = JSON.parse(jsonMatch[0]);

        // Validate the structure
        if (outline.chapters && Array.isArray(outline.chapters)) {
          return {
            title: outline.title || title,
            chapters: outline.chapters.map((chapter, index) => ({
              number: chapter.number || index + 1,
              title: chapter.title || `Chapter ${index + 1}`,
              sections: Array.isArray(chapter.sections) ? chapter.sections : [],
            })),
          };
        }
      }

      // If parsing fails, throw an error
      const parseError = new Error("Failed to parse AI response for document outline generation");
      aiLogger.error(parseError, {
        action: 'generate_outline',
        topic: documentData.topicAndNiche?.mainTopic?.substring(0, 50),
        response: text.substring(0, 200)
      });
      handleApiError(parseError, { service: 'Gemini AI - Outline Generation' });
    }

    // Service not available
    const configError = new Error("Gemini AI is not configured. Please set up your API key to generate document outlines.");
    aiLogger.error(configError, {
      action: 'generate_outline',
      topic: documentData.topicAndNiche?.mainTopic?.substring(0, 50),
      reason: 'service_not_configured'
    });
    handleApiError(configError, { service: 'Gemini AI - Outline Generation' });
  } catch (error) {
    aiLogger.error(error, {
      action: 'generate_outline',
      topic: documentData.topicAndNiche?.mainTopic?.substring(0, 50),
      message: 'Failed to generate outline with Gemini'
    });

    // Update service status and handle error
    updateServiceStatus('gemini', false, { lastError: error.message });
    handleApiError(error, { service: 'Gemini AI - Outline Generation' });
  }
};

/**
 * Generate full document content using Gemini AI with chapter-by-chapter structure
 * Enhanced to improve and expand imported content
 * @param {Object} documentData - Complete document configuration
 * @returns {Promise<Object>} Generated document content with structured chapters
 */
export const generateDocumentContent = async (documentData) => {
  ensureInitialized();
  const startTime = performance.now();
  const documentId = documentData?.id || "unknown";

  aiLogger.info("Starting document content generation", {
    documentId,
    documentType: documentData.documentPurpose?.primaryType || "unknown",
    hasOutline: !!documentData.documentOutline?.generatedOutline,
    hasImportedContent:
      !!documentData.documentPurpose?.importedContent?.extractedContent,
    timestamp: new Date().toISOString(),
  });

  try {
    // Use Gemini AI if available
    if (model && GEMINI_API_KEY) {
      aiLogger.debug("Using Gemini AI for content generation", {
        documentId,
        modelName: "gemini-1.5-flash",
        apiKeyPresent: !!GEMINI_API_KEY,
      });

      const topic = documentData.topicAndNiche?.mainTopic || "General Topic";
      const audience =
        documentData.audienceAnalysis?.primaryAudience || "General Audience";

      // Check if we have imported content to enhance
      const hasImportedContent =
        documentData?.documentPurpose?.importedContent?.extractedContent;
      let contentEnhancementContext = "";

      if (hasImportedContent) {
        const importedContent = documentData.documentPurpose.importedContent;
        aiLogger.debug("Processing imported content for enhancement", {
          documentId,
          sourceUrl: importedContent.sourceUrl,
          originalTitle: importedContent.originalTitle,
          wordCount: importedContent.wordCount,
        });

        contentEnhancementContext = `\n\nIMPORTED CONTENT ENHANCEMENT:
This document is based on imported content from: ${importedContent.sourceUrl}
Original Title: "${importedContent.originalTitle}"
Original Length: ${importedContent.wordCount} words

ORIGINAL CONTENT TO ENHANCE:
"${importedContent.extractedContent}"

ENHANCEMENT GUIDELINES:
- Use the imported content as foundation but significantly expand and improve it
- Maintain core insights while adding substantial new value
- Restructure according to the outline for better flow
- Add examples, case studies, and practical applications
- Improve clarity, engagement, and actionability
- Ensure final content is 2-3x more comprehensive than original`;
      }

      const title =
        documentData.titleSelection?.selectedTitle || `Guide to ${topic}`;
      const subNiches = documentData.topicAndNiche?.subNiches || [];
      const customSubNiche = documentData.topicAndNiche?.customSubNiche;
      const tone = documentData.toneAndVoice?.toneOfVoice || "informative";
      const documentType = documentData.documentPurpose?.primaryType || "ebook";
      const subType = documentData.documentPurpose?.subType;
      const outline = documentData.documentOutline?.generatedOutline;

      if (!outline?.chapters) {
        aiLogger.error(new Error("Missing document outline"), {
          documentId,
          action: "content_generation",
          issue: "missing_outline",
        });
        throw new Error("Document outline is required for content generation");
      }

      // Combine regular sub-niches with custom sub-niche
      const allSubNiches = [
        ...subNiches,
        ...(customSubNiche ? [customSubNiche] : []),
      ];

      const subNicheText =
        allSubNiches.length > 0
          ? `\nFocus areas: ${allSubNiches.map((s) => s.name || s).join(", ")}`
          : "";

      // Generate content chapter by chapter for better structure
      const chapters = [];
      const chapterCount = outline.chapters.length;

      aiLogger.info("Beginning chapter-by-chapter generation", {
        documentId,
        chapterCount,
        documentType,
        tone,
      });

      for (let i = 0; i < outline.chapters.length; i++) {
        const chapterOutline = outline.chapters[i];
        aiLogger.debug(`Generating chapter ${i + 1} of ${chapterCount}`, {
          documentId,
          chapterNumber: chapterOutline.number,
          chapterTitle: chapterOutline.title,
          sectionCount: chapterOutline.sections?.length || 0,
        });

        const chapterStartTime = performance.now();

        try {
          const chapterContent = await generateChapterContent({
            chapterOutline,
            documentData,
            topic,
            audience,
            tone,
            subNiches: allSubNiches,
            contentEnhancementContext: hasImportedContent
              ? contentEnhancementContext
              : null,
          });

          const chapterDuration = performance.now() - chapterStartTime;

          aiLogger.debug(`Chapter ${i + 1} generated successfully`, {
            documentId,
            chapterNumber: chapterOutline.number,
            wordCount: chapterContent.wordCount,
            durationMs: Math.round(chapterDuration),
          });

          chapters.push(chapterContent);
        } catch (chapterError) {
          aiLogger.error(chapterError, {
            documentId,
            action: "chapter_generation",
            chapterNumber: chapterOutline.number,
            chapterTitle: chapterOutline.title,
          });

          // Continue with other chapters despite error
          chapters.push({
            id: `chapter-${chapterOutline.number}`,
            number: chapterOutline.number,
            title: chapterOutline.title,
            content: `[Error generating content for this chapter: ${chapterError.message}]`,
            sections: chapterOutline.sections || [],
            wordCount: 0,
            estimatedReadingTime: 0,
            error: true,
          });
        }
      }

      // Generate introduction and conclusion
      aiLogger.debug("Generating introduction", { documentId });
      const introduction = await generateIntroduction(documentData);

      aiLogger.debug("Generating conclusion", { documentId });
      const conclusion = await generateConclusion(documentData);

      // Calculate metrics
      const totalWordCount =
        chapters.reduce((total, chapter) => total + chapter.wordCount, 0) +
        introduction.wordCount +
        conclusion.wordCount;
      const estimatedReadingTime = Math.ceil(totalWordCount / 200);
      const totalDuration = performance.now() - startTime;

      aiLogger.info("Document content generation completed successfully", {
        documentId,
        totalWordCount,
        chapterCount: chapters.length,
        estimatedReadingTime: `${estimatedReadingTime} minutes`,
        durationMs: Math.round(totalDuration),
        errorChapters: chapters.filter((ch) => ch.error).length,
      });

      return {
        success: true,
        content: {
          title: title,
          introduction: introduction,
          chapters: chapters,
          conclusion: conclusion,
          wordCount: totalWordCount,
          estimatedReadingTime: `${estimatedReadingTime} minutes`,
          metadata: {
            topic,
            audience,
            tone,
            documentType,
            generatedAt: new Date().toISOString(),
          },
        },
      };
    }

    // Service not available
    const configError = new Error("Gemini AI is not configured. Please set up your API key to generate document content.");
    aiLogger.error(configError, {
      documentId,
      action: "generate_content",
      reason: "service_not_configured",
    });
    handleApiError(configError, { service: 'Gemini AI - Content Generation' });
  } catch (error) {
    const duration = performance.now() - startTime;

    aiLogger.error(error, {
      documentId,
      action: "document_generation",
      durationMs: Math.round(duration),
      errorType: error.name,
      errorMessage: error.message,
    });

    // Update service status and handle error
    updateServiceStatus('gemini', false, { lastError: error.message });
    handleApiError(error, { service: 'Gemini AI - Content Generation' });
  }
};

/**
 * Generate content for a specific chapter
 * @param {Object} params - Chapter generation parameters
 * @returns {Promise<Object>} Generated chapter content
 */
const generateChapterContent = async ({
  chapterOutline,
  documentData,
  topic,
  audience,
  tone,
  subNiches,
  contentEnhancementContext = null,
}) => {
  try {
    if (!model || !GEMINI_API_KEY) {
      const error = new Error('Gemini AI is not configured. Cannot generate chapter content.');
      aiLogger.error(error, {
        action: 'generate_chapter',
        chapterTitle: chapterOutline.title,
        reason: 'service_not_configured'
      });
      handleApiError(error, { service: 'Gemini AI - Chapter Generation' });
    }

    const sectionsText = chapterOutline.sections?.join("\n- ") || "";

    // Use document type-specific chapter prompt
    let prompt = createDocumentTypePrompt(
      documentData,
      "chapter",
      chapterOutline
    );

    // Add content enhancement context if available
    if (contentEnhancementContext) {
      prompt += contentEnhancementContext;
      prompt += `\n\nFor this specific chapter "${chapterOutline.title}", please:
1. Look for relevant content in the imported material
2. Expand and improve upon any related sections
3. Add new insights and examples where appropriate
4. Ensure the chapter flows well with the overall document structure`;
    }

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const rawContent = response.text();

    // Process content to ensure markdown format
    const content = processAIContent(rawContent);

    const wordCount = content.split(/\s+/).length;

    return {
      id: `chapter-${chapterOutline.number}`,
      number: chapterOutline.number,
      title: chapterOutline.title,
      content: content,
      sections: chapterOutline.sections || [],
      wordCount: wordCount,
      estimatedReadingTime: Math.ceil(wordCount / 200),
    };
  } catch (error) {
    aiLogger.error(error, {
      action: 'generate_chapter',
      chapterNumber: chapterOutline.number,
      chapterTitle: chapterOutline.title,
      message: 'Failed to generate chapter content'
    });
    handleApiError(error, { service: 'Gemini AI - Chapter Generation' });
  }
};

/**
 * Generate document introduction
 * @param {Object} documentData - Document configuration
 * @returns {Promise<Object>} Generated introduction
 */
const generateIntroduction = async (documentData) => {
  try {
    if (!model || !GEMINI_API_KEY) {
      const error = new Error('Gemini AI is not configured. Cannot generate introduction.');
      aiLogger.error(error, {
        action: 'generate_introduction',
        topic: documentData.topicAndNiche?.mainTopic?.substring(0, 50),
        reason: 'service_not_configured'
      });
      handleApiError(error, { service: 'Gemini AI - Introduction Generation' });
    }

    const topic = documentData.topicAndNiche?.mainTopic;
    const audience = documentData.audienceAnalysis?.primaryAudience;
    const tone = documentData.toneAndVoice?.toneOfVoice;
    const title = documentData.titleSelection?.selectedTitle;

    // Use document type-specific introduction prompt
    const prompt = createDocumentTypePrompt(documentData, "introduction");

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const rawContent = response.text();

    // Process content to ensure markdown format
    const content = processAIContent(rawContent);

    const wordCount = content.split(/\s+/).length;

    return {
      content: content,
      wordCount: wordCount,
    };
  } catch (error) {
    aiLogger.error(error, {
      action: 'generate_introduction',
      topic: documentData.topicAndNiche?.mainTopic?.substring(0, 50),
      message: 'Failed to generate introduction'
    });
    handleApiError(error, { service: 'Gemini AI - Introduction Generation' });
  }
};

/**
 * Generate document conclusion
 * @param {Object} documentData - Document configuration
 * @returns {Promise<Object>} Generated conclusion
 */
const generateConclusion = async (documentData) => {
  try {
    if (!model || !GEMINI_API_KEY) {
      const error = new Error('Gemini AI is not configured. Cannot generate conclusion.');
      aiLogger.error(error, {
        action: 'generate_conclusion',
        topic: documentData.topicAndNiche?.mainTopic?.substring(0, 50),
        reason: 'service_not_configured'
      });
      handleApiError(error, { service: 'Gemini AI - Conclusion Generation' });
    }

    const topic = documentData.topicAndNiche?.mainTopic;
    const audience = documentData.audienceAnalysis?.primaryAudience;
    const tone = documentData.toneAndVoice?.toneOfVoice;

    const prompt = `Write a compelling conclusion for this document:

Topic: ${topic}
Audience: ${audience}
Tone: ${tone}

Requirements:
- 200-400 words
- Summarize key takeaways
- Provide actionable next steps
- End with motivation/inspiration
- Use ${tone} tone
- Leave ${audience} feeling empowered

Generate the conclusion:`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const rawContent = response.text();

    // Process content to ensure markdown format
    const content = processAIContent(rawContent);

    const wordCount = content.split(/\s+/).length;

    return {
      content: content,
      wordCount: wordCount,
    };
  } catch (error) {
    aiLogger.error(error, {
      action: 'generate_conclusion',
      topic: documentData.topicAndNiche?.mainTopic?.substring(0, 50),
      message: 'Failed to generate conclusion'
    });
    handleApiError(error, { service: 'Gemini AI - Conclusion Generation' });
  }
};







// Utility function to check if AI services are configured
export const isAIConfigured = () => {
  return !!(GEMINI_API_KEY || OPENAI_API_KEY || ANTHROPIC_API_KEY);
};

// Get available AI providers
export const getAvailableProviders = () => {
  const providers = [];
  if (GEMINI_API_KEY) providers.push("gemini");
  if (OPENAI_API_KEY) providers.push("openai");
  if (ANTHROPIC_API_KEY) providers.push("anthropic");
  return providers;
};

// Check if Gemini is specifically configured and working
export const isGeminiConfigured = () => {
  return !!(GEMINI_API_KEY && model);
};

// Test Gemini connection
export const testGeminiConnection = async () => {
  try {
    if (!model || !GEMINI_API_KEY) {
      return { success: false, error: "Gemini not configured" };
    }

    const result = await model.generateContent(
      'Test connection. Respond with "OK".'
    );
    const response = await result.response;
    const text = response.text();

    return {
      success: true,
      message: "Gemini connection successful",
      response: text,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Create academic subtype-specific sub-niche prompts
 * @param {string} topic - Main topic
 * @param {string} language - Target language
 * @param {string} subType - Academic subtype
 * @returns {string} Subtype-specific prompt
 */
const createAcademicSubNichePrompt = (topic, language, subType) => {
  const subtypeContexts = {
    'essay': {
      focus: 'analytical angles and argumentative perspectives',
      examples: 'comparative analysis, critical evaluation, theoretical application',
      approach: 'Focus on different analytical approaches and argumentative angles'
    },
    'research-paper': {
      focus: 'research methodologies and empirical approaches',
      examples: 'quantitative studies, qualitative research, mixed methods',
      approach: 'Focus on different research methodologies and empirical angles'
    },
    'thesis': {
      focus: 'comprehensive research areas and theoretical frameworks',
      examples: 'theoretical development, empirical validation, literature synthesis',
      approach: 'Focus on comprehensive research areas suitable for extended study'
    },
    'dissertation': {
      focus: 'original research contributions and theoretical innovations',
      examples: 'novel theoretical frameworks, methodological innovations, interdisciplinary approaches',
      approach: 'Focus on original research areas with significant academic contribution potential'
    },
    'assignment': {
      focus: 'specific learning objectives and course-relevant applications',
      examples: 'case applications, theoretical examples, practical implementations',
      approach: 'Focus on course-relevant applications and learning objectives'
    }
  };

  const context = subtypeContexts[subType] || subtypeContexts['research-paper'];

  return `You are an expert academic researcher specializing in ${topic}. Generate 8 highly relevant academic sub-niches for a ${subType} on "${topic}".

ACADEMIC ${subType.toUpperCase()} CONTEXT:
- Document Type: Academic ${subType}
- Topic: ${topic}
- Academic Focus: ${context.focus}
- ${context.approach}

ACADEMIC SUB-NICHE REQUIREMENTS:
- Each sub-niche should represent a distinct academic angle or research focus
- Suitable for ${subType} format and scope
- Consider different theoretical frameworks and methodological approaches
- Ensure academic rigor and scholarly relevance
- Examples: ${context.examples}
- Make them specific enough for focused academic writing
- Ensure they are distinct from each other

Return ONLY a valid JSON array with objects containing:
- id: kebab-case identifier (e.g., "theoretical-framework-analysis")
- name: Clear, academic name (e.g., "Theoretical Framework Analysis")
- description: Brief academic description (max 80 characters)

Topic: ${topic}
Language: ${language}

JSON Response:`;
};

/**
 * Create business subtype-specific sub-niche prompts
 * @param {string} topic - Main topic
 * @param {string} language - Target language
 * @param {string} subType - Business subtype
 * @returns {string} Subtype-specific prompt
 */
const createBusinessSubNichePrompt = (topic, language, subType) => {
  const subtypeContexts = {
    'proposal': {
      focus: 'strategic opportunities and value propositions',
      examples: 'ROI analysis, competitive advantages, implementation strategies',
      approach: 'Focus on persuasive business opportunities and value creation'
    },
    'report': {
      focus: 'analytical insights and performance metrics',
      examples: 'market analysis, performance evaluation, trend assessment',
      approach: 'Focus on data-driven insights and analytical perspectives'
    },
    'business-plan': {
      focus: 'strategic planning and market opportunities',
      examples: 'market segmentation, revenue models, growth strategies',
      approach: 'Focus on comprehensive business planning and strategic development'
    },
    'executive-summary': {
      focus: 'high-level strategic insights and key decisions',
      examples: 'strategic recommendations, executive decisions, key findings',
      approach: 'Focus on executive-level insights and strategic recommendations'
    }
  };

  const context = subtypeContexts[subType] || subtypeContexts['report'];

  return `You are an expert business strategist and consultant specializing in ${topic}. Generate 8 highly relevant business sub-niches for a ${subType} on "${topic}".

BUSINESS ${subType.toUpperCase()} CONTEXT:
- Document Type: Business ${subType}
- Topic: ${topic}
- Business Focus: ${context.focus}
- ${context.approach}

BUSINESS SUB-NICHE REQUIREMENTS:
- Each sub-niche should represent a distinct business angle or strategic focus
- Suitable for ${subType} format and business context
- Consider different business perspectives and strategic approaches
- Ensure business relevance and practical application
- Examples: ${context.examples}
- Make them specific enough for focused business writing
- Ensure they are distinct from each other

Return ONLY a valid JSON array with objects containing:
- id: kebab-case identifier (e.g., "market-penetration-strategy")
- name: Clear, business name (e.g., "Market Penetration Strategy")
- description: Brief business description (max 80 characters)

Topic: ${topic}
Language: ${language}

JSON Response:`;
};

// ================================
// DocGenerate AI Functions
// ================================

/**
 * Safely log errors to the error monitor if available
 */
const safeLogError = (operation, error, severity, metadata = {}) => {
  try {
    if (errorMonitor && typeof errorMonitor.captureError === "function") {
      errorMonitor.captureError(error, { operation, ...metadata }, severity);
    } else {
      prodLogger.error(`DocGenerate ${operation} error:`, error, metadata);
    }
  } catch (logError) {
    prodLogger.error(`Failed to log DocGenerate error:`, logError);
    prodLogger.error(`Original error:`, error);
  }
};

/**
 * DocGenerate specific error class for better error handling
 */
class DocGenerateError extends Error {
  constructor(message, code = "DOCGENERATE_ERROR", details = null) {
    super(message);
    this.name = "DocGenerateError";
    this.code = code;
    this.details = details;
  }
}

/**
 * Create context-aware prompt for DocGenerate operations
 */
const createDocGeneratePrompt = (action, text, context = {}) => {
  const { nodeType = "paragraph", documentContext = "" } = context;

  const baseInstructions = {
    rewrite: `Rewrite the following text to improve clarity, flow, and readability while maintaining the original meaning and key information. Keep the same general length and tone.`,
    "fix-grammar": `Fix any spelling, grammar, and punctuation errors in the following text. Only make necessary corrections and preserve the original style and tone.`,
    reduce: `Reduce the following text by approximately 25-30% while preserving the most important information and maintaining readability.`,
    expand: `Expand the following text by adding relevant details, examples, or explanations while maintaining the original tone and style. Add approximately 50% more content.`,
  };

  let prompt = baseInstructions[action] || baseInstructions.rewrite;

  // Add context about the content type
  if (nodeType === "heading") {
    prompt += ` This is a heading, so keep it concise and impactful.`;
  } else if (nodeType === "listItem") {
    prompt += ` This is a list item, so keep it focused and scannable.`;
  } else if (nodeType === "blockquote") {
    prompt += ` This is a quote, so maintain its authoritative or reflective tone.`;
  } else if (nodeType === "codeBlock") {
    prompt += ` This appears to be code-related content, so preserve technical accuracy.`;
  }

  // Add document context if available
  if (documentContext) {
    prompt += ` Context: This text is part of a ${documentContext} document.`;
  }

  prompt += `\n\nText to ${action}:\n"${text}"\n\nProvide only the ${
    action === "fix-grammar"
      ? "corrected"
      : action === "reduce"
      ? "reduced"
      : action === "expand"
      ? "expanded"
      : "rewritten"
  } text without any explanations, formatting, or additional content:`;

  return prompt;
};

/**
 * Validate text content before processing
 */
const validateDocGenerateText = (text) => {
  if (!text || typeof text !== "string") {
    return { valid: false, error: "Text must be a non-empty string" };
  }

  const trimmedText = text.trim();
  if (!trimmedText) {
    return { valid: false, error: "Text cannot be empty or only whitespace" };
  }

  if (trimmedText.length < 3) {
    return { valid: false, error: "Text must be at least 3 characters long" };
  }

  if (trimmedText.length > 5000) {
    return {
      valid: false,
      error: "Text is too long (maximum 5,000 characters)",
    };
  }

  return { valid: true, text: trimmedText };
};

/**
 * Generic method to call AI for DocGenerate operations
 */
const callDocGenerateAI = async (prompt, options = {}) => {
  try {
    // Check if Gemini is configured
    if (!isGeminiConfigured()) {
      throw new DocGenerateError(
        "AI service not configured. Please configure Google Gemini API key.",
        "MISSING_API_KEY"
      );
    }

    const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    const generationConfig = {
      temperature: options.temperature || 0.7,
      topP: 0.95,
      topK: 64,
      maxOutputTokens: options.maxTokens || 1000,
    };

    const result = await model.generateContent({
      contents: [{ role: "user", parts: [{ text: prompt }] }],
      generationConfig,
    });

    if (!result.response) {
      throw new DocGenerateError(
        "No response received from AI service",
        "NO_RESPONSE"
      );
    }

    const text = result.response.text();
    if (!text || !text.trim()) {
      throw new DocGenerateError(
        "Empty response received from AI service",
        "EMPTY_RESPONSE"
      );
    }

    return text.trim();
  } catch (error) {
    prodLogger.error("DocGenerate AI call failed:", error);

    if (error instanceof DocGenerateError) {
      throw error;
    }

    // Handle Gemini-specific errors
    if (error.message?.includes("API key")) {
      throw new DocGenerateError(
        "Invalid or missing API key. Please check your Gemini API configuration.",
        "INVALID_API_KEY",
        error
      );
    }

    if (error.message?.includes("quota") || error.message?.includes("limit")) {
      throw new DocGenerateError(
        "API quota exceeded. Please try again later.",
        "QUOTA_EXCEEDED",
        error
      );
    }

    throw new DocGenerateError(
      `AI request failed: ${error.message}`,
      "AI_REQUEST_FAILED",
      error
    );
  }
};

/**
 * Rewrite content to improve clarity and readability
 */
export const rewriteContent = async (text, context = {}) => {
  const validation = validateDocGenerateText(text);
  if (!validation.valid) {
    throw new DocGenerateError(validation.error, "INVALID_TEXT");
  }

  try {
    const prompt = createDocGeneratePrompt("rewrite", validation.text, context);
    const result = await callDocGenerateAI(prompt, {
      temperature: 0.7,
      maxTokens: Math.max(200, Math.ceil(validation.text.length * 1.5)),
    });

    return result;
  } catch (error) {
    safeLogError("rewrite_content", error, ErrorSeverity.HIGH, {
      textLength: text.length,
      context,
    });
    throw error;
  }
};

/**
 * Fix grammar and spelling errors
 */
export const fixGrammar = async (text, context = {}) => {
  const validation = validateDocGenerateText(text);
  if (!validation.valid) {
    throw new DocGenerateError(validation.error, "INVALID_TEXT");
  }

  try {
    const prompt = createDocGeneratePrompt(
      "fix-grammar",
      validation.text,
      context
    );
    const result = await callDocGenerateAI(prompt, {
      temperature: 0.3, // Lower temperature for more conservative corrections
      maxTokens: Math.max(200, Math.ceil(validation.text.length * 1.1)),
    });

    return result;
  } catch (error) {
    safeLogError("fix_grammar", error, ErrorSeverity.MEDIUM, {
      textLength: text.length,
      context,
    });
    throw error;
  }
};

/**
 * Reduce content length while preserving key information
 */
export const reduceContent = async (text, context = {}) => {
  const validation = validateDocGenerateText(text);
  if (!validation.valid) {
    throw new DocGenerateError(validation.error, "INVALID_TEXT");
  }

  try {
    const prompt = createDocGeneratePrompt("reduce", validation.text, context);
    const result = await callDocGenerateAI(prompt, {
      temperature: 0.6,
      maxTokens: Math.max(100, Math.ceil(validation.text.length * 0.8)),
    });

    return result;
  } catch (error) {
    safeLogError("reduce_content", error, ErrorSeverity.HIGH, {
      textLength: text.length,
      context,
    });
    throw error;
  }
};

/**
 * Expand content with additional details and examples
 */
export const expandContent = async (text, context = {}) => {
  const validation = validateDocGenerateText(text);
  if (!validation.valid) {
    throw new DocGenerateError(validation.error, "INVALID_TEXT");
  }

  try {
    const prompt = createDocGeneratePrompt("expand", validation.text, context);
    const result = await callDocGenerateAI(prompt, {
      temperature: 0.8,
      maxTokens: Math.max(300, Math.ceil(validation.text.length * 2)),
    });

    return result;
  } catch (error) {
    safeLogError("expand_content", error, ErrorSeverity.HIGH, {
      textLength: text.length,
      context,
    });
    throw error;
  }
};

/**
 * Get available tone options for tone adjustment
 */
export const getToneOptions = () => {
  return [
    {
      value: "professional",
      label: "Professional",
      description: "Formal and business-appropriate",
    },
    {
      value: "casual",
      label: "Casual",
      description: "Friendly and conversational",
    },
    {
      value: "academic",
      label: "Academic",
      description: "Scholarly and precise",
    },
    {
      value: "creative",
      label: "Creative",
      description: "Engaging and imaginative",
    },
    {
      value: "technical",
      label: "Technical",
      description: "Clear and precise for technical content",
    },
    {
      value: "persuasive",
      label: "Persuasive",
      description: "Compelling and convincing",
    },
    {
      value: "concise",
      label: "Concise",
      description: "Brief and to the point",
    },
    {
      value: "empathetic",
      label: "Empathetic",
      description: "Understanding and supportive",
    },
  ];
};

/**
 * Export DocGenerateError for use in components
 */
export { DocGenerateError };
