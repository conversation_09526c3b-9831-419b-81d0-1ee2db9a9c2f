/**
 * Export Service - Handles document export functionality
 * Supports PDF, DOCX, and HTML formats
 */

import { hasExistingChapterHeading } from "../utils/contentProcessing.js";
import imageOverlayService from "./imageOverlayService.js";
import { incrementTemplateUsage } from "./templateService.js";
import coverPreviewService from "./coverPreviewService.js";

import { prodLogger } from "../utils/prodLogger.js";
/**
 * Retrieve text overlay customizations from session storage
 * @param {string} templateId - Template ID to get customizations for
 * @returns {Object} Customizations object or empty object if none found
 */
const getTextOverlayCustomizations = (templateId) => {
  if (!templateId) return {};

  try {
    const storageKey = `text-overlay-customizations-${templateId}`;
    const stored = sessionStorage.getItem(storageKey);
    if (stored) {
      const customizations = JSON.parse(stored);
      prodLogger.debug("📝 Retrieved text overlay customizations for export", {
        templateId,
        customizationCount: Object.keys(customizations).length,
      });
      return customizations;
    }
  } catch (error) {
    prodLogger.warn("Failed to retrieve text overlay customizations:", error);
  }

  return {};
};

// Utility function to clean editor HTML content for export
const cleanEditorHTMLForExport = (editorHTML) => {
  if (!editorHTML) return "";

  let cleanHTML = editorHTML;

  // Remove image suggestion cards
  cleanHTML = cleanHTML.replace(
    /<div[^>]*data-type="image-suggestion-card"[^>]*>[\s\S]*?<\/div>/gi,
    ""
  );

  // CRITICAL: Handle problematic image sources that can cause DOCX export to hang
  // BUT preserve AI-generated and uploaded images which should be exported
  cleanHTML = cleanHTML.replace(
    /<img([^>]+)src="data:[^"]*"([^>]*)>/gi,
    (fullMatch) => {
      // Check if this is an AI-generated image that should be preserved
      const isAIGenerated = /data-ai-generated\s*=\s*["']true["']/i.test(
        fullMatch
      );

      // Check if this is an uploaded image that should be preserved
      const isUploaded = /data-uploaded\s*=\s*["']true["']/i.test(fullMatch);

      if (isAIGenerated) {
        // Preserve AI-generated images for export
        prodLogger.debug("✅ Preserving AI-generated image for export");
        return fullMatch;
      } else if (isUploaded) {
        // Preserve uploaded images for export
        prodLogger.debug("✅ Preserving uploaded image for export");
        return fullMatch;
      } else {
        // Replace truly problematic base64 images with placeholder
        prodLogger.debug(
          "🚫 Replacing problematic base64 image with placeholder"
        );
        return "<p><em>[Image: Base64 data - not exported]</em></p>";
      }
    }
  );

  // Remove images with empty or invalid URLs
  cleanHTML = cleanHTML.replace(
    /<img[^>]+src=""[^>]*>/gi,
    "<p><em>[Image: No source URL provided]</em></p>"
  );

  // Remove images with obviously invalid URLs that would cause timeouts
  cleanHTML = cleanHTML.replace(
    /<img([^>]+)src="(blob:|javascript:|#)[^"]*"([^>]*)>/gi,
    "<p><em>[Image: Invalid URL format - not exported]</em></p>"
  );

  // Remove any empty paragraphs that might be left
  cleanHTML = cleanHTML.replace(/<p>\s*<\/p>/g, "");

  // Remove any wrapper divs that might interfere with export
  cleanHTML = cleanHTML.replace(
    /<div[^>]*class="[^"]*image-suggestion-card[^"]*"[^>]*>[\s\S]*?<\/div>/gi,
    ""
  );

  // FIXED: Remove excessive <br> tags that cause spacing issues
  // Remove multiple consecutive <br> tags (more than 2)
  cleanHTML = cleanHTML.replace(/(<br\s*\/?>){3,}/gi, "<br><br>");

  // Remove <br> tags immediately after heading closing tags
  cleanHTML = cleanHTML.replace(/(<\/h[1-6]>)\s*(<br\s*\/?>)+/gi, "$1");

  // Remove <br> tags immediately before heading opening tags
  cleanHTML = cleanHTML.replace(/(<br\s*\/?>)+\s*(<h[1-6][^>]*>)/gi, "$2");

  // Remove <br> tags at the beginning of paragraphs
  cleanHTML = cleanHTML.replace(/(<p[^>]*>)\s*(<br\s*\/?>)+/gi, "$1");

  // Remove <br> tags at the end of paragraphs (before closing </p>)
  cleanHTML = cleanHTML.replace(/(<br\s*\/?>)+\s*(<\/p>)/gi, "$2");

  return cleanHTML.trim();
};

// Utility function to extract images from block-based content
const extractImagesFromContent = (content) => {
  if (!content) return [];

  const images = [];
  const imageRegex = /!\[(.*?)\]\((.*?)\)/g;
  let match;

  while ((match = imageRegex.exec(content)) !== null) {
    const [, alt, src] = match;
    images.push({
      src: src,
      alt: alt || "Image",
      description: alt || "Image",
    });
  }

  return images;
};

// Utility function to convert markdown to HTML with image handling
const markdownToHtml = (markdown) => {
  if (!markdown) return "";

  return (
    markdown
      // Images - convert to HTML img tags
      .replace(
        /!\[(.*?)\]\((.*?)\)/gim,
        '<img src="$2" alt="$1" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin: 20px 0;" />'
      )
      // Headers
      .replace(/^### (.*$)/gim, "<h3>$1</h3>")
      .replace(/^## (.*$)/gim, "<h2>$1</h2>")
      .replace(/^# (.*$)/gim, "<h1>$1</h1>")
      // Bold
      .replace(/\*\*(.*)\*\*/gim, "<strong>$1</strong>")
      // Italic
      .replace(/\*(.*)\*/gim, "<em>$1</em>")
      // Lists
      .replace(/^\* (.*$)/gim, "<li>$1</li>")
      .replace(/(<li>.*<\/li>)/gims, "<ul>$1</ul>")
      // FIXED: Remove excessive line breaks after headings and convert remaining newlines properly
      // Remove newlines immediately after closing heading tags
      .replace(/(<\/h[1-6]>)\s*\n+/gim, "$1")
      // Remove newlines immediately after closing list tags
      .replace(/(<\/ul>|<\/ol>)\s*\n+/gim, "$1")
      // Convert double newlines to paragraph breaks (preserve paragraph structure)
      .replace(/\n\s*\n/gim, "</p><p>")
      // Convert remaining single newlines to line breaks (within paragraphs)
      .replace(/\n/gim, "<br>")
      // Wrap content in paragraphs if not already wrapped
      .replace(/^(?!<[h1-6]|<ul|<ol|<img|<p)/gim, "<p>")
      .replace(/(?<!>)$/gim, "</p>")
      // Clean up any empty paragraphs or malformed paragraph tags
      .replace(/<p>\s*<\/p>/gim, "")
      .replace(/<p>(<h[1-6])/gim, "$1")
      .replace(/(<\/h[1-6]>)<\/p>/gim, "$1")
      .replace(/<p>(<ul|<ol)/gim, "$1")
      .replace(/(<\/ul>|<\/ol>)<\/p>/gim, "$1")
      .replace(/<p>(<img)/gim, "$1")
      .replace(/(<img[^>]*>)<\/p>/gim, "$1")
  );
};

// Helper function to generate template image HTML using image overlay service
const generateTemplateImageHTML = async (template, documentData) => {
  try {
    prodLogger.debug("🎨 Generating template image for export:", template.name);

    // Validate template structure
    if (!template.background_image_url || !template.text_overlays) {
      throw new Error(
        "Invalid template structure: missing background_image_url or text_overlays"
      );
    }

    // Check for text overlay customizations
    const textOverlayCustomizations = getTextOverlayCustomizations(template.id);
    const hasCustomizations = Object.keys(textOverlayCustomizations).length > 0;

    let canvas;
    if (hasCustomizations) {
      prodLogger.debug(
        "🎨 Applying text overlay customizations to legacy template image",
        {
          templateId: template.id,
          customizationCount: Object.keys(textOverlayCustomizations).length,
        }
      );

      // Render template with customizations
      canvas = await imageOverlayService.renderTemplateWithCustomizations(
        template,
        documentData,
        textOverlayCustomizations
      );
    } else {
      // Render template without customizations
      canvas = await imageOverlayService.renderTemplate(template, documentData);
    }

    const imageDataUrl = imageOverlayService.exportAsImage(canvas, "png", 0.95); // Higher quality for export

    prodLogger.debug("✅ Template image generated successfully for export");

    // FULL-BLEED: Return HTML with full-page cover styling
    return `<div class="template-cover" style="
      page-break-after: always;
      width: 100%;
      height: 100vh;
      margin: 0;
      padding: 0;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    ">
      <img src="${imageDataUrl}" alt="Document Cover" style="
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
        margin: 0;
        padding: 0;
      " />
    </div>`;
  } catch (error) {
    prodLogger.error("❌ Error generating template image for export:", error);

    // Enhanced fallback with clean white styling for Skip Template mode
    const fallbackHTML = `<div class="template-cover-fallback" style="text-align: center; margin-bottom: 40px; padding: 60px 40px; background: white; color: #333; border: 2px solid #e5e7eb; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
      <h1 class="title" style="font-size: 2.5em; margin-bottom: 20px; font-weight: bold; color: #333;">${
        documentData.title || "Untitled Document"
      }</h1>
      ${
        documentData.author
          ? `<div class="author" style="font-size: 1.3em; margin-bottom: 15px; opacity: 0.9; font-style: italic;">by ${documentData.author}</div>`
          : ""
      }
      ${
        documentData.description
          ? `<div class="description" style="font-size: 1.1em; line-height: 1.6; opacity: 0.8; max-width: 500px; margin: 0 auto;">${documentData.description}</div>`
          : ""
      }
      <div style="margin-top: 30px; font-size: 0.8em; opacity: 0.6;">Template rendering unavailable - using fallback design</div>
    </div>`;

    return fallbackHTML;
  }
};

/**
 * Generate enhanced cover HTML for export with consistent styling
 * Uses the same cover preview service to ensure consistency between preview and export
 * Supports both template-based and custom cover images
 * @param {Object} template - Template configuration (can be null for custom covers)
 * @param {Object} documentData - Document metadata
 * @returns {Promise<string>} HTML string with enhanced cover
 */
const generateEnhancedCoverHTML = async (template, documentData) => {
  try {
    // Check if custom cover image should be used
    const customCoverConfig = documentData?.contentDetails?.customCoverImage;
    const useCustomCover =
      customCoverConfig?.enabled && customCoverConfig?.imageUrl;

    prodLogger.debug("🎨 Generating enhanced cover for export", {
      templateId: template?.id,
      templateName: template?.name,
      documentTitle: documentData.title,
      useCustomCover,
      customImageUrl: useCustomCover ? customCoverConfig.imageUrl : null,
    });

    let coverPreview;

    if (useCustomCover) {
      // Generate custom cover preview
      coverPreview = await coverPreviewService.generateCustomCoverPreview(
        customCoverConfig,
        documentData,
        {
          quality: 0.95, // Higher quality for export
          format: "png",
        }
      );
    } else {
      // Use template-based cover with potential customizations
      const textOverlayCustomizations = getTextOverlayCustomizations(
        template?.id
      );
      const hasCustomizations =
        Object.keys(textOverlayCustomizations).length > 0;

      if (hasCustomizations) {
        prodLogger.debug(
          "🎨 Applying text overlay customizations to export cover",
          {
            templateId: template.id,
            customizationCount: Object.keys(textOverlayCustomizations).length,
          }
        );

        // Generate cover with customizations
        coverPreview =
          await coverPreviewService.generateCoverPreviewWithCustomizations(
            template,
            documentData,
            textOverlayCustomizations,
            {
              quality: 0.95, // Higher quality for export
              format: "png",
            }
          );
      } else {
        // Generate standard cover without customizations
        coverPreview = await coverPreviewService.generateCoverPreview(
          template,
          documentData,
          {
            quality: 0.95, // Higher quality for export
            format: "png",
          }
        );
      }
    }

    prodLogger.debug("✅ Enhanced cover generated successfully for export");

    // FULL-BLEED: Return the cover HTML with full-page styling
    return `<div class="enhanced-template-cover" style="
      page-break-after: always;
      width: 100%;
      height: 100vh;
      margin: 0;
      padding: 0;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    ">
      <img src="${coverPreview.coverImageData}" alt="Document Cover" style="
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
        margin: 0;
        padding: 0;
      " />
    </div>`;
  } catch (error) {
    prodLogger.error("❌ Error generating enhanced cover for export:", error);

    // Fallback strategy
    const customCoverConfig = documentData?.contentDetails?.customCoverImage;
    if (customCoverConfig?.enabled && customCoverConfig?.fallbackTemplate) {
      prodLogger.debug("🔄 Attempting fallback to template-based cover");
      // Could fetch fallback template here, but for now use simple fallback
    }

    // Fallback to original template generation if available
    if (template) {
      return await generateTemplateImageHTML(template, documentData);
    }

    // Final fallback - simple text-based cover
    return generateSimpleCoverHTML(documentData);
  }
};

/**
 * Generate simple text-based cover HTML as final fallback
 * @param {Object} documentData - Document metadata
 * @returns {string} Simple cover HTML
 */
const generateSimpleCoverHTML = (documentData) => {
  const title = documentData?.title || "Untitled Document";
  const author = documentData?.author || "";
  const description = documentData?.description || "";

  return `<div class="simple-cover" style="
    page-break-after: always;
    width: 100%;
    height: 100vh;
    margin: 0;
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: white;
    color: #333;
    box-sizing: border-box;
    overflow: hidden;
  ">
    <h1 style="font-size: 3rem; margin-bottom: 1rem; font-weight: bold; color: #333;">${title}</h1>
    ${
      author
        ? `<div style="font-size: 1.5rem; margin-bottom: 2rem; color: #666;">by ${author}</div>`
        : ""
    }
    ${
      description
        ? `<div style="font-size: 1.2rem; max-width: 600px; line-height: 1.6; color: #666;">${description}</div>`
        : ""
    }
  </div>`;
};

// Generate HTML content from document data with optional template support
const generateHtmlContent = async (
  documentData,
  generatedContent,
  options = {}
) => {
  const { title, author, description } = documentData;
  const { selectedTemplate = null } = options;

  // CRITICAL: Prioritize editorHTML (contains user-inserted images) over original chapters
  const useEditorHTML =
    generatedContent.editorHTML && generatedContent.editorHTML.trim();
  const chapters = generatedContent.chapters || [];

  // Generate title page content (async operation) - supports both templates and custom covers
  const customCoverConfig = documentData?.contentDetails?.customCoverImage;
  const hasCustomCover =
    customCoverConfig?.enabled && customCoverConfig?.imageUrl;

  let titlePageContent;
  if (hasCustomCover || selectedTemplate) {
    // Use enhanced cover generation for both custom and template covers
    titlePageContent = await generateEnhancedCoverHTML(
      selectedTemplate,
      documentData
    );
  } else {
    // Use styled simple cover for skip template exports
    prodLogger.debug(
      "⚡ Using simple cover for template-less export (Skip Template mode)"
    );
    titlePageContent = generateSimpleCoverHTML(documentData);
  }

  // FIXED: Ensure title page content is never empty to prevent blank page rendering
  if (!titlePageContent || titlePageContent.trim() === "") {
    prodLogger.warn("⚠️ Empty title page content detected, using fallback");
    titlePageContent = `<h1 class="title">${title || "Untitled Document"}</h1>`;
  }

  let htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title || "Document"}</title>
    <meta name="description" content="${
      description || "Generated by RapidDoc AI"
    }">
    <meta name="author" content="${author || "RapidDoc AI"}">
    <style>
        body {
            font-family: 'Georgia', serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
            background: white;
        }

        /* FULL-BLEED: Ensure no backgrounds show through on cover pages */
        html {
            background: white;
            margin: 0;
            padding: 0;
        }
        .title-page {
            text-align: center;
            page-break-after: always;
            width: 100%;
            height: 100vh;
            margin: 0;
            padding: 0;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .title {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #000000;
        }
        .author {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        .description {
            font-style: italic;
            color: #95a5a6;
            max-width: 600px;
            margin: 0 auto;
        }
        .chapter {
            margin-bottom: 40px;
            page-break-before: always;
        }
        .chapter-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #000000;
            padding-bottom: 10px;
            letter-spacing: -0.02em; /* Tighten letter spacing for better readability */
        }
        .chapter-content {
            text-align: justify;
        }
        .chapter-image {
            margin: 20px 0;
            text-align: center;
            page-break-inside: avoid;
        }
        .chapter-image img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .image-credit {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
            font-style: italic;
        }
        h1, h2, h3 {
            color: #000000;
            margin-top: 30px;
            margin-bottom: 15px;
            letter-spacing: -0.02em; /* Tighten letter spacing for better readability */
        }
        p {
            margin-bottom: 15px;
        }
        @media print {
            /* FULL-BLEED: Different page setup for cover vs content pages */
            @page {
                size: A4;
                margin: 0; /* No margin for full-bleed cover */
                /* Remove browser-generated headers and footers */
                @top-left { content: ""; }
                @top-center { content: ""; }
                @top-right { content: ""; }
                @bottom-left { content: ""; }
                @bottom-center { content: ""; }
                @bottom-right { content: ""; }
            }

            /* FULL-BLEED: Separate page setup for content pages */
            @page content {
                size: A4;
                margin: 0.5in; /* Reduced margins for better space utilization */
            }

            /* FIXED: Remove conflicting margin/padding that causes blank pages */
            html, body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0 !important;
                padding: 0 !important;
                height: auto !important;
                background: white !important;
            }

            body {
                font-size: 14pt; /* Increased from 12pt for better readability */
                line-height: 1.5;
                max-width: none !important; /* Allow full page width for container positioning */
                margin: 0 !important; /* Remove auto margins - centering handled by content containers */
                padding: 0 !important; /* Remove padding - margins handled by @page rule */

                /* Ensure proper text rendering for narrower content */
                text-align: left; /* Maintain left alignment for readability */
                word-wrap: break-word; /* Handle long words gracefully */
                overflow-wrap: break-word; /* Modern word wrapping */
            }

            /* FULL-BLEED: Cover page takes full page without margins */
            .title-page {
                margin: 0 !important;
                padding: 0 !important;
                page-break-after: always;
                width: 100vw !important;
                height: 100vh !important;
                background: white !important;
            }

            /* FULL-BLEED: Cover images fill entire page */
            .template-cover,
            .enhanced-template-cover {
                margin: 0 !important;
                padding: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background: white !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .template-cover img,
            .enhanced-template-cover img {
                width: 100vw !important;
                height: 100vh !important;
                object-fit: cover !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* Content pages use optimized margins for better space utilization */
            .chapter-content {
                page: content;
                margin: 0; /* Remove redundant margin - page margins are handled by @page content rule */
                width: 100%; /* Full container width */

                /* READABILITY IMPROVEMENT: Constrain content width for better readability */
                max-width: 8.2in; /* Optimal reading width (~65% of A4 content area) */
                margin-left: auto;
                margin-right: auto;
                padding: 0 0.25in; /* Small padding for content breathing room */
            }

            .chapter {
                page-break-before: always;
                width: 100%; /* Full container width */

                /* READABILITY IMPROVEMENT: Constrain content width for better readability */
                max-width: 8.2in; /* Optimal reading width (~65% of A4 content area) */
                margin-left: auto;
                margin-right: auto;
                padding: 0 0.25in; /* Small padding for content breathing room */
            }

            /* Constrain text elements for optimal readability while allowing full width for special elements */
            p, h1, h2, h3, h4, h5, h6, ul, ol, blockquote {
                width: 100%;
                box-sizing: border-box;
                /* Text elements inherit the container's max-width constraint */
            }

            /* Allow tables and images to use more space if needed */
            table, img, pre, code {
                width: 100%;
                box-sizing: border-box;
                max-width: 100%; /* Can expand to full container width */
            }

            /* Enhanced image handling for narrower content */
            .chapter-image, img {
                page-break-inside: avoid;
                max-width: 100%;
                height: auto;
                margin: 0.5rem auto; /* Center images with some spacing */
                display: block;
            }

            /* Improve table presentation in narrower layout */
            table {
                margin: 0.5rem auto;
                border-collapse: collapse;
                font-size: 0.9em; /* Slightly smaller for better fit */
            }

            /* Code blocks with better formatting for narrow content */
            pre, code {
                font-size: 0.85em;
                overflow-wrap: break-word;
                word-wrap: break-word;
            }
        }
    </style>
</head>
<body>
    <div class="title-page">
        ${titlePageContent}
    </div>
`;

  // CRITICAL: Use editor HTML content if available (contains user-inserted images)
  if (useEditorHTML) {
    // Clean up editor HTML for export
    const cleanedEditorContent = cleanEditorHTMLForExport(
      generatedContent.editorHTML
    );

    // Apply chapter styling to existing headings
    let styledContent = cleanedEditorContent.replace(
      /<h([123])([^>]*)>/gi,
      '<h$1 class="chapter-title"$2>'
    );

    htmlContent += `<div class="chapter-content">${styledContent}</div>`;
  } else {
    prodLogger.debug(
      "📄 Export: Using original chapter content (fallback - no user edits)"
    );
    // Fallback: Use original chapters (legacy content without user edits)
    chapters.forEach((chapter, index) => {
      const chapterNumber = chapter.number || index + 1;
      const chapterTitle = chapter.title || "Untitled Chapter";
      const chapterContent = chapter.content || "";

      // Check if chapter content already contains a chapter heading
      const hasHeading = hasExistingChapterHeading(
        chapterContent,
        chapterNumber,
        chapterTitle
      );

      htmlContent += `<div class="chapter">`;

      // Only add programmatic chapter heading if not already present in content
      if (!hasHeading) {
        htmlContent += `<h2 class="chapter-title">Chapter ${chapterNumber}: ${chapterTitle}</h2>`;
      }

      htmlContent += `
          <div class="chapter-content">
              ${markdownToHtml(chapterContent)}
          </div>
      </div>
  `;
    });
  }

  htmlContent += `
</body>
</html>`;

  return htmlContent;
};

// Create and download file
const downloadFile = (content, filename, mimeType) => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Export as HTML
export const exportAsHtml = async (
  documentData,
  generatedContent,
  options = {}
) => {
  try {
    const { selectedTemplate = null } = options;
    const htmlContent = await generateHtmlContent(
      documentData,
      generatedContent,
      { selectedTemplate }
    );
    const filename = `${documentData.title || "document"}.html`;
    downloadFile(htmlContent, filename, "text/html");
    return { success: true, message: "HTML export completed successfully" };
  } catch (error) {
    prodLogger.error("HTML export failed:", error);
    return { success: false, error: error.message };
  }
};

// Export as PDF (using browser's print functionality)
export const exportAsPdf = async (
  documentData,
  generatedContent,
  options = {}
) => {
  try {
    const { selectedTemplate = null } = options;

    // Track template usage if template is selected
    if (selectedTemplate) {
      try {
        await incrementTemplateUsage(selectedTemplate.id);
        prodLogger.debug(
          `📊 Template usage incremented for: ${selectedTemplate.name}`
        );
      } catch (error) {
        prodLogger.warn("Failed to increment template usage:", error);
      }
    }

    // Create a new window with the HTML content
    const htmlContent = await generateHtmlContent(
      documentData,
      generatedContent,
      { selectedTemplate }
    );

    // Create a blob URL to avoid "about:blank" in the address bar
    const blob = new Blob([htmlContent], { type: "text/html" });
    const blobUrl = URL.createObjectURL(blob);

    // Open the blob URL instead of about:blank
    const printWindow = window.open(blobUrl, "_blank");

    // Fallback: if blob URL doesn't work, use the traditional method with improved title
    if (!printWindow || printWindow.closed) {
      const fallbackWindow = window.open("", "_blank");
      fallbackWindow.document.write(htmlContent);
      fallbackWindow.document.close();

      // Set a proper document title to replace "about:blank"
      fallbackWindow.document.title = documentData.title || "Document";

      // FIXED: Wait for content to load with proper timing
      fallbackWindow.onload = () => {
        setTimeout(() => {
          if (fallbackWindow.document.readyState === "complete") {
            fallbackWindow.print();
            fallbackWindow.close();
            // Clean up blob URL
            URL.revokeObjectURL(blobUrl);
          } else {
            setTimeout(() => {
              fallbackWindow.print();
              fallbackWindow.close();
              URL.revokeObjectURL(blobUrl);
            }, 500);
          }
        }, 1000); // Increased delay
      };
    } else {
      // FIXED: Wait for content to load with longer delay to prevent blank page issues
      printWindow.onload = () => {
        // Ensure all images and content are fully loaded
        setTimeout(() => {
          // Additional check to ensure document is ready
          if (printWindow.document.readyState === "complete") {
            printWindow.print();
            printWindow.close();
            // Clean up blob URL
            URL.revokeObjectURL(blobUrl);
          } else {
            // Wait a bit more if document isn't ready
            setTimeout(() => {
              printWindow.print();
              printWindow.close();
              URL.revokeObjectURL(blobUrl);
            }, 500);
          }
        }, 1000); // Increased delay from 500ms to 1000ms
      };
    }

    return {
      success: true,
      message:
        "PDF export initiated. Please use your browser's print dialog to save as PDF.",
    };
  } catch (error) {
    prodLogger.error("PDF export failed:", error);
    return { success: false, error: error.message };
  }
};

// Helper function to extract and format images for RTF content
const extractRtfImages = (content) => {
  if (!content) return "";

  const images = extractImagesFromContent(content);
  if (images.length === 0) return "";

  // RTF doesn't support external images easily, so we'll add image references as text
  return images
    .map(
      (image) =>
        `\\par\\par{\\i [Image: ${image.description || "Image"}]}\\par\\par`
    )
    .join("");
};

// Export as DOCX with embedded images using proper DOCX generation
export const exportAsDocx = async (
  documentData,
  generatedContent,
  editorInstance = null,
  options = {}
) => {
  try {
    // Import the DOCX generation service
    const { generateDocxWithImages, downloadDocxFile } = await import(
      "./docxGenerationService.js"
    );

    // CRITICAL: Extract content from the document, prioritizing editorHTML
    let content = "";
    let contentType = "markdown"; // default
    let contentSource = "chapters"; // Track content source to prevent duplicate headings

    // Priority 1: Editor instance (live editing session)
    if (editorInstance && typeof editorInstance.getHTML === "function") {
      prodLogger.debug("📄 DOCX Export: Using live editor instance content");
      const rawEditorContent = editorInstance.getHTML();
      content = cleanEditorHTMLForExport(rawEditorContent);
      contentType = "html";
      contentSource = "editor";
    }
    // Priority 2: Saved editor HTML (contains user-inserted images)
    else if (
      generatedContent.editorHTML &&
      generatedContent.editorHTML.trim()
    ) {
      prodLogger.debug(
        "📄 DOCX Export: Using saved editor HTML content (includes user-inserted images)"
      );
      const rawEditorHTML = generatedContent.editorHTML;
      content = cleanEditorHTMLForExport(rawEditorHTML);
      contentType = "html";
      contentSource = "editor";
    }
    // Priority 3: Fallback to original chapters (no user edits)
    else {
      prodLogger.debug(
        "📄 DOCX Export: Using original chapter content (fallback - no user edits)"
      );
      const chapters = generatedContent.chapters || [];

      // Import the hasExistingChapterHeading function
      const { hasExistingChapterHeading } = await import(
        "../utils/contentProcessing.js"
      );

      content = chapters
        .map((chapter, index) => {
          const chapterNumber = chapter.number || index + 1;
          const chapterTitle = chapter.title || "Untitled Chapter";
          const chapterContent = chapter.content || "";

          // Check if chapter content already contains a chapter heading to prevent duplicates
          const hasHeading = hasExistingChapterHeading(
            chapterContent,
            chapterNumber,
            chapterTitle
          );

          if (hasHeading) {
            // Content already has heading, use it directly
            return chapterContent;
          } else {
            // Content doesn't have heading, add it
            const programmaticTitle = `# Chapter ${chapterNumber}: ${chapterTitle}`;
            return `${programmaticTitle}\n\n${chapterContent}`;
          }
        })
        .join("\n\n---\n\n");
      contentType = "markdown";
      contentSource = "chapters";
    }

    // Generate DOCX with embedded images
    prodLogger.debug("📄 DOCX Export: Starting generation...");

    const result = await generateDocxWithImages(
      documentData,
      content,
      contentType,
      {
        // Pass selected template for cover generation
        selectedTemplate: options.selectedTemplate,
        // Pass content source for potential future use
        contentSource: contentSource,
        // Optimized settings for faster export
        imageProcessing: {
          concurrency: 2, // Reduce concurrent downloads to avoid bottlenecks
          maxRetries: 2, // Reduce retries for faster failure handling
          timeout: 8000, // Reduce timeout per image to 8 seconds
          skipOnError: true, // Skip problematic images rather than failing
          logProgress: false, // Disable verbose logging for performance
        },
      }
    );

    if (result.success) {
      // Download the generated DOCX file
      const filename = `${documentData.title || "document"}.docx`;
      downloadDocxFile(result.blob, filename);

      // Create detailed success message with image statistics
      let message = "DOCX export completed successfully with embedded images";
      if (result.imageStats && result.imageStats.totalImages > 0) {
        const { totalImages, successfulImages, failedImages } =
          result.imageStats;
        message += `. Images: ${successfulImages}/${totalImages} embedded successfully`;
        if (failedImages > 0) {
          message += `, ${failedImages} failed to download`;
        }
      }

      return {
        success: true,
        message: message,
        imageStats: result.imageStats,
        userMessage: result.userMessage || message,
      };
    } else {
      return {
        success: false,
        error: result.error || "DOCX generation failed",
        userMessage: result.userMessage || "Failed to generate DOCX file",
      };
    }
  } catch (error) {
    prodLogger.error("DOCX export failed:", error);
    return {
      success: false,
      error: error.message,
      userMessage:
        "An unexpected error occurred during DOCX export. Please try again.",
    };
  }
};

// Main export function
export const exportDocument = async (
  format,
  documentData,
  generatedContent,
  options = {}
) => {
  if (!documentData || !generatedContent) {
    return {
      success: false,
      error: "Missing document data or content",
      userMessage: "Unable to export: missing document data or content",
    };
  }

  try {
    const { editorInstance } = options;

    switch (format.toLowerCase()) {
      case "pdf":
        return await exportAsPdf(documentData, generatedContent, options);
      case "html":
        return await exportAsHtml(documentData, generatedContent, options);
      case "docx":
      case "word":
        return await exportAsDocx(
          documentData,
          generatedContent,
          editorInstance,
          options
        );
      default:
        return {
          success: false,
          error: `Unsupported export format: ${format}`,
          userMessage: `Export format "${format}" is not supported`,
        };
    }
  } catch (error) {
    prodLogger.error("Export failed:", error);
    return {
      success: false,
      error: error.message,
      userMessage: "An error occurred during export. Please try again.",
    };
  }
};

// Get document statistics
export const getDocumentStatistics = (generatedContent) => {
  if (!generatedContent || !generatedContent.chapters) {
    return { pages: 0, chapters: 0, words: 0, readTime: 0 };
  }

  const chapters = generatedContent.chapters.length;
  const words = generatedContent.chapters.reduce(
    (total, chapter) => total + (chapter.wordCount || 0),
    0
  );
  const pages = Math.ceil(words / 250); // Approximate 250 words per page
  const readTime = Math.ceil(words / 200); // Approximate 200 words per minute

  return { pages, chapters, words, readTime };
};

/**
 * Export template as standalone image (PNG/JPG)
 * @param {Object} template - Template configuration
 * @param {Object} documentData - Document metadata
 * @param {Object} options - Export options
 * @returns {Promise<Object>} Export result
 */
export const exportTemplateAsImage = async (
  template,
  documentData,
  options = {}
) => {
  try {
    const { format = "png", quality = 0.95, filename = null } = options;

    prodLogger.debug(
      `📸 Exporting template as ${format.toUpperCase()}:`,
      template.name
    );

    // Validate template
    if (!template.background_image_url || !template.text_overlays) {
      throw new Error("Invalid template structure");
    }

    // Check for text overlay customizations
    const textOverlayCustomizations = getTextOverlayCustomizations(template.id);
    const hasCustomizations = Object.keys(textOverlayCustomizations).length > 0;

    let canvas;
    if (hasCustomizations) {
      prodLogger.debug(
        "🎨 Applying text overlay customizations to template image export",
        {
          templateId: template.id,
          customizationCount: Object.keys(textOverlayCustomizations).length,
        }
      );

      // Render template with customizations
      canvas = await imageOverlayService.renderTemplateWithCustomizations(
        template,
        documentData,
        textOverlayCustomizations
      );
    } else {
      // Render template without customizations
      canvas = await imageOverlayService.renderTemplate(template, documentData);
    }

    const imageDataUrl = imageOverlayService.exportAsImage(
      canvas,
      format,
      quality
    );

    // Generate filename
    const exportFilename =
      filename || `${documentData.title || "document"}-cover.${format}`;

    // Download the image
    downloadFile(imageDataUrl, exportFilename, `image/${format}`);

    prodLogger.debug(
      `✅ Template exported successfully as ${format.toUpperCase()}`
    );

    return {
      success: true,
      message: `Template exported as ${format.toUpperCase()} successfully`,
      filename: exportFilename,
    };
  } catch (error) {
    prodLogger.error("Template image export failed:", error);
    return {
      success: false,
      error: error.message,
      userMessage: `Failed to export template as image: ${error.message}`,
    };
  }
};

/**
 * Export template with multiple format options
 * @param {Object} template - Template configuration
 * @param {Object} documentData - Document metadata
 * @param {Array} formats - Array of formats to export ['png', 'jpg', 'pdf']
 * @returns {Promise<Object>} Export results
 */
export const exportTemplateMultiFormat = async (
  template,
  documentData,
  formats = ["png"]
) => {
  try {
    prodLogger.debug(`📦 Exporting template in multiple formats:`, formats);

    const results = [];

    for (const format of formats) {
      try {
        if (format === "pdf") {
          // For PDF, use the existing PDF export with template
          const result = await exportAsPdf(
            documentData,
            { chapters: [] },
            { selectedTemplate: template }
          );
          results.push({
            format,
            success: result.success,
            error: result.error,
          });
        } else {
          // For image formats
          const result = await exportTemplateAsImage(template, documentData, {
            format,
            filename: `${documentData.title || "document"}-cover.${format}`,
          });
          results.push({
            format,
            success: result.success,
            error: result.error,
          });
        }
      } catch (error) {
        prodLogger.error(`Failed to export ${format}:`, error);
        results.push({ format, success: false, error: error.message });
      }
    }

    const successCount = results.filter((r) => r.success).length;
    const totalCount = results.length;

    prodLogger.debug(
      `✅ Multi-format export complete: ${successCount}/${totalCount} successful`
    );

    return {
      success: successCount > 0,
      results,
      message: `Exported ${successCount} of ${totalCount} formats successfully`,
    };
  } catch (error) {
    prodLogger.error("Multi-format export failed:", error);
    return {
      success: false,
      error: error.message,
      results: [],
    };
  }
};
