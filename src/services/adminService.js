/**
 * Admin Authorization Service
 * Handles admin role verification, management, and security operations
 */

import { supabase } from '../lib/supabase';
import { prodLogger } from '../utils/prodLogger';
import { sessionManager } from '../utils/sessionManager';

class AdminService {
  constructor() {
    this.adminCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Check if current user is an admin
   * @returns {Promise<boolean>}
   */
  async isCurrentUserAdmin() {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return false;

      return await this.isUserAdmin(user.id);
    } catch (error) {
      prodLogger.error('Error checking current user admin status:', error);
      return false;
    }
  }

  /**
   * Check if a specific user is an admin
   * @param {string} userId - User ID to check
   * @returns {Promise<boolean>}
   */
  async isUserAdmin(userId) {
    if (!userId) return false;

    try {
      // Check cache first
      const cacheKey = `admin_${userId}`;
      const cached = this.adminCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.isAdmin;
      }

      // Query database
      const { data, error } = await supabase.rpc('is_user_admin', {
        user_id: userId
      });

      if (error) {
        prodLogger.error('Error checking admin status:', error);
        return false;
      }

      // Cache result
      this.adminCache.set(cacheKey, {
        isAdmin: data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      prodLogger.error('Error in isUserAdmin:', error);
      return false;
    }
  }

  /**
   * Check if user has specific admin role
   * @param {string} userId - User ID to check
   * @param {string} requiredRole - Required role ('moderator', 'admin', 'super_admin')
   * @returns {Promise<boolean>}
   */
  async userHasAdminRole(userId, requiredRole) {
    if (!userId || !requiredRole) return false;

    try {
      const { data, error } = await supabase.rpc('user_has_admin_role', {
        user_id: userId,
        required_role: requiredRole
      });

      if (error) {
        prodLogger.error('Error checking admin role:', error);
        return false;
      }

      return data;
    } catch (error) {
      prodLogger.error('Error in userHasAdminRole:', error);
      return false;
    }
  }

  /**
   * Get current user's admin information
   * @returns {Promise<Object|null>}
   */
  async getCurrentUserAdminInfo() {
    try {
      const { data, error } = await supabase.rpc('get_current_user_admin_info');

      if (error) {
        prodLogger.error('Error getting admin info:', error);
        return null;
      }

      return data?.[0] || null;
    } catch (error) {
      prodLogger.error('Error in getCurrentUserAdminInfo:', error);
      return null;
    }
  }

  /**
   * Log admin activity
   * @param {string} action - Action performed
   * @param {string} resourceType - Type of resource affected
   * @param {string} resourceId - ID of resource affected
   * @param {Object} details - Additional details
   * @returns {Promise<string|null>} Activity log ID
   */
  async logAdminActivity(action, resourceType = null, resourceId = null, details = null) {
    try {
      // Get session info for additional context
      const sessionInfo = sessionManager.getSessionInfo();
      const userAgent = navigator.userAgent;

      const { data, error } = await supabase.rpc('log_admin_activity', {
        p_action: action,
        p_resource_type: resourceType,
        p_resource_id: resourceId,
        p_details: details ? JSON.stringify(details) : null,
        p_ip_address: sessionInfo.ipAddress || null,
        p_user_agent: userAgent,
        p_session_id: sessionInfo.sessionId || null
      });

      if (error) {
        prodLogger.error('Error logging admin activity:', error);
        return null;
      }

      return data;
    } catch (error) {
      prodLogger.error('Error in logAdminActivity:', error);
      return null;
    }
  }

  /**
   * Grant admin role to user (super_admin only)
   * @param {string} targetUserId - User to grant role to
   * @param {string} role - Role to grant
   * @returns {Promise<boolean>}
   */
  async grantAdminRole(targetUserId, role) {
    try {
      const { data, error } = await supabase.rpc('grant_admin_role', {
        target_user_id: targetUserId,
        role_to_grant: role
      });

      if (error) {
        prodLogger.error('Error granting admin role:', error);
        throw new Error(error.message);
      }

      // Clear cache for target user
      this.adminCache.delete(`admin_${targetUserId}`);

      return data;
    } catch (error) {
      prodLogger.error('Error in grantAdminRole:', error);
      throw error;
    }
  }

  /**
   * Revoke admin role from user (super_admin only)
   * @param {string} targetUserId - User to revoke role from
   * @returns {Promise<boolean>}
   */
  async revokeAdminRole(targetUserId) {
    try {
      const { data, error } = await supabase.rpc('revoke_admin_role', {
        target_user_id: targetUserId
      });

      if (error) {
        prodLogger.error('Error revoking admin role:', error);
        throw new Error(error.message);
      }

      // Clear cache for target user
      this.adminCache.delete(`admin_${targetUserId}`);

      return data;
    } catch (error) {
      prodLogger.error('Error in revokeAdminRole:', error);
      throw error;
    }
  }

  /**
   * Get admin activity logs
   * @param {Object} options - Query options
   * @returns {Promise<Array>}
   */
  async getAdminActivityLogs(options = {}) {
    try {
      const {
        limit = 50,
        offset = 0,
        adminUserId = null,
        action = null,
        startDate = null,
        endDate = null
      } = options;

      let query = supabase
        .from('admin_activity_log')
        .select(`
          *,
          admin_user:admin_user_id(full_name, email)
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (adminUserId) {
        query = query.eq('admin_user_id', adminUserId);
      }

      if (action) {
        query = query.eq('action', action);
      }

      if (startDate) {
        query = query.gte('created_at', startDate);
      }

      if (endDate) {
        query = query.lte('created_at', endDate);
      }

      const { data, error } = await query;

      if (error) {
        prodLogger.error('Error fetching admin activity logs:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      prodLogger.error('Error in getAdminActivityLogs:', error);
      return [];
    }
  }

  /**
   * Validate admin session and check for suspicious activity
   * @returns {Promise<Object>}
   */
  async validateAdminSession() {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { valid: false, reason: 'No user session' };
      }

      // Check if user is still admin
      const isAdmin = await this.isUserAdmin(user.id);
      if (!isAdmin) {
        return { valid: false, reason: 'User is no longer admin' };
      }

      // Check for suspicious activity
      const suspiciousActivity = await this.checkSuspiciousActivity(user.id);
      if (suspiciousActivity.detected) {
        return { 
          valid: false, 
          reason: 'Suspicious activity detected',
          details: suspiciousActivity.details
        };
      }

      // Check session freshness (admin sessions should be refreshed more frequently)
      const sessionInfo = sessionManager.getSessionInfo();
      const sessionAge = Date.now() - sessionInfo.startTime;
      const maxAdminSessionAge = 2 * 60 * 60 * 1000; // 2 hours

      if (sessionAge > maxAdminSessionAge) {
        return { 
          valid: false, 
          reason: 'Admin session expired',
          requiresReauth: true
        };
      }

      return { valid: true };
    } catch (error) {
      prodLogger.error('Error validating admin session:', error);
      return { valid: false, reason: 'Session validation error' };
    }
  }

  /**
   * Check for suspicious admin activity
   * @param {string} userId - User ID to check
   * @returns {Promise<Object>}
   */
  async checkSuspiciousActivity(userId) {
    try {
      // Get recent admin activities
      const recentLogs = await this.getAdminActivityLogs({
        adminUserId: userId,
        limit: 20,
        startDate: new Date(Date.now() - 60 * 60 * 1000).toISOString() // Last hour
      });

      const suspiciousPatterns = [];

      // Check for rapid successive actions
      if (recentLogs.length > 15) {
        suspiciousPatterns.push('High frequency of admin actions');
      }

      // Check for unusual IP changes
      const uniqueIPs = new Set(recentLogs.map(log => log.ip_address).filter(Boolean));
      if (uniqueIPs.size > 3) {
        suspiciousPatterns.push('Multiple IP addresses in short time');
      }

      // Check for privilege escalation attempts
      const privilegeActions = recentLogs.filter(log => 
        log.action.includes('grant_admin_role') || log.action.includes('revoke_admin_role')
      );
      if (privilegeActions.length > 2) {
        suspiciousPatterns.push('Multiple privilege changes');
      }

      return {
        detected: suspiciousPatterns.length > 0,
        details: suspiciousPatterns
      };
    } catch (error) {
      prodLogger.error('Error checking suspicious activity:', error);
      return { detected: false, details: [] };
    }
  }

  /**
   * Clear admin cache
   */
  clearCache() {
    this.adminCache.clear();
  }

  /**
   * Get admin statistics
   * @returns {Promise<Object>}
   */
  async getAdminStats() {
    try {
      // Get total admin count
      const { count: adminCount } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true })
        .eq('is_admin', true);

      // Get admin activity count for last 24 hours
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      const { count: recentActivityCount } = await supabase
        .from('admin_activity_log')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', yesterday);

      // Get admin role distribution
      const { data: roleDistribution } = await supabase
        .from('user_profiles')
        .select('admin_role')
        .eq('is_admin', true);

      const roles = roleDistribution?.reduce((acc, item) => {
        acc[item.admin_role] = (acc[item.admin_role] || 0) + 1;
        return acc;
      }, {}) || {};

      return {
        totalAdmins: adminCount || 0,
        recentActivity: recentActivityCount || 0,
        roleDistribution: roles
      };
    } catch (error) {
      prodLogger.error('Error getting admin stats:', error);
      return {
        totalAdmins: 0,
        recentActivity: 0,
        roleDistribution: {}
      };
    }
  }
}

// Create singleton instance
const adminService = new AdminService();

export default adminService;
