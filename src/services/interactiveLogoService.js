/**
 * Interactive Logo Service
 * Handles interactive logo editing functionality for templates
 * Manages logo overlays, selections, and real-time customizations
 */

import { supabase } from "../lib/supabase.js";
import { getUserLogos, incrementLogoUsage } from "./logoService.js";
import { prodLogger } from "../utils/prodLogger.js";

/**
 * Auto-populate template logos from user's default logo
 * @param {string} userId - User ID
 * @param {Object} template - Template object with logo overlays
 * @returns {Promise<Object>} Logo selections for template overlays
 */
export const autoPopulateTemplateLogos = async (userId, template) => {
  try {
    prodLogger.debug('🎨 Auto-populating template logos', {
      userId,
      templateId: template?.id,
      templateName: template?.name
    });

    // Get user's default logo
    const { data: userProfile } = await supabase
      .from('user_profiles')
      .select('default_logo_id, logo_preferences')
      .eq('id', userId)
      .single();

    if (!userProfile?.default_logo_id) {
      prodLogger.debug('ℹ️ No default logo found for user:', userId);
      return {};
    }

    // Get logo overlays from template
    const logoOverlays = template?.logo_overlays?.overlays || [];
    if (logoOverlays.length === 0) {
      prodLogger.debug('ℹ️ Template has no logo overlays:', template?.id);
      return {};
    }

    // Apply default logo to all logo overlays
    const autoPopulated = {};
    for (const overlay of logoOverlays) {
      autoPopulated[overlay.id] = userProfile.default_logo_id;
    }

    prodLogger.debug('✅ Template logos auto-populated', {
      templateId: template.id,
      overlayCount: logoOverlays.length,
      defaultLogoId: userProfile.default_logo_id
    });

    return autoPopulated;
  } catch (error) {
    prodLogger.error('❌ Error auto-populating template logos:', error);
    return {};
  }
};

/**
 * Prepare logos for template with interactive overlays
 * @param {string} userId - User ID
 * @param {Object} template - Template object
 * @param {Object} logoSelections - User's logo selections per overlay
 * @returns {Promise<Object>} Prepared logo data for template rendering
 */
export const prepareLogosForTemplate = async (userId, template, logoSelections = {}) => {
  try {
    prodLogger.debug('🎨 Preparing logos for template', {
      userId,
      templateId: template?.id,
      logoSelections
    });

    const logoOverlays = template?.logo_overlays?.overlays || [];
    if (logoOverlays.length === 0) {
      return {};
    }

    const preparedLogos = {};

    for (const overlay of logoOverlays) {
      const selectedLogoId = logoSelections[overlay.id];
      
      if (selectedLogoId) {
        // Get logo data from database
        const { data: logoData, error } = await supabase
          .from('user_logos')
          .select('*')
          .eq('id', selectedLogoId)
          .eq('user_id', userId)
          .eq('is_active', true)
          .single();

        if (error) {
          prodLogger.warn('⚠️ Failed to fetch logo for overlay:', {
            overlayId: overlay.id,
            logoId: selectedLogoId,
            error
          });
          continue;
        }

        // Download logo data from storage
        const { data: logoBuffer, error: downloadError } = await supabase.storage
          .from('user-logos')
          .download(logoData.storage_path);

        if (downloadError) {
          prodLogger.error('❌ Failed to download logo data:', downloadError);
          continue;
        }

        // Convert blob to array buffer
        const arrayBuffer = await logoBuffer.arrayBuffer();

        // Calculate optimal dimensions based on overlay constraints
        const dimensions = calculateLogoSize(logoData, overlay);

        // Increment usage count (async, don't wait)
        incrementLogoUsage(logoData.id).catch(error => {
          prodLogger.warn('⚠️ Failed to increment logo usage:', error);
        });

        preparedLogos[overlay.id] = {
          id: logoData.id,
          name: logoData.name,
          description: logoData.description,
          data: arrayBuffer,
          originalWidth: logoData.width,
          originalHeight: logoData.height,
          dimensions,
          overlay: overlay,
          metadata: {
            fileSize: logoData.file_size,
            fileType: logoData.file_type,
            aspectRatio: logoData.aspect_ratio
          }
        };
      }
    }

    prodLogger.debug('✅ Logos prepared for template', {
      templateId: template.id,
      preparedCount: Object.keys(preparedLogos).length
    });

    return preparedLogos;
  } catch (error) {
    prodLogger.error('❌ Error preparing logos for template:', error);
    return {};
  }
};

/**
 * Calculate optimal logo size based on overlay constraints
 * @param {Object} logoData - Logo data from database
 * @param {Object} overlay - Logo overlay configuration
 * @returns {Object} Calculated dimensions {width, height}
 */
const calculateLogoSize = (logoData, overlay) => {
  const constraints = overlay.constraints || {};
  const overlayPosition = overlay.position || {};
  
  // Start with overlay default dimensions
  let width = overlayPosition.width || logoData.width;
  let height = overlayPosition.height || logoData.height;
  
  // Apply constraints
  if (constraints.max_width && width > constraints.max_width) {
    width = constraints.max_width;
  }
  if (constraints.min_width && width < constraints.min_width) {
    width = constraints.min_width;
  }
  if (constraints.max_height && height > constraints.max_height) {
    height = constraints.max_height;
  }
  if (constraints.min_height && height < constraints.min_height) {
    height = constraints.min_height;
  }
  
  // Maintain aspect ratio if required
  if (constraints.maintain_aspect_ratio !== false) {
    const aspectRatio = logoData.width / logoData.height;
    
    // Adjust height to maintain aspect ratio
    const calculatedHeight = width / aspectRatio;
    if (calculatedHeight <= height) {
      height = calculatedHeight;
    } else {
      // Adjust width instead
      width = height * aspectRatio;
    }
  }
  
  return {
    width: Math.round(width),
    height: Math.round(height)
  };
};

/**
 * Save user's logo selections for a template
 * @param {string} userId - User ID
 * @param {string} templateId - Template ID
 * @param {Object} logoSelections - Logo selections per overlay
 * @param {Object} customizations - Logo customizations per overlay
 * @returns {Promise<boolean>} Success status
 */
export const saveTemplateLogoSelections = async (userId, templateId, logoSelections, customizations = {}) => {
  try {
    prodLogger.debug('💾 Saving template logo selections', {
      userId,
      templateId,
      logoSelections,
      customizations
    });

    // Prepare data for bulk upsert
    const selectionsData = Object.entries(logoSelections).map(([overlayId, logoId]) => ({
      user_id: userId,
      template_id: templateId,
      logo_overlay_id: overlayId,
      selected_logo_id: logoId,
      customizations: customizations[overlayId] || {}
    }));

    if (selectionsData.length === 0) {
      return true;
    }

    // Upsert logo selections
    const { error } = await supabase
      .from('template_logo_selections')
      .upsert(selectionsData, {
        onConflict: 'user_id,template_id,logo_overlay_id'
      });

    if (error) {
      prodLogger.error('❌ Failed to save template logo selections:', error);
      return false;
    }

    prodLogger.debug('✅ Template logo selections saved successfully');
    return true;
  } catch (error) {
    prodLogger.error('❌ Error saving template logo selections:', error);
    return false;
  }
};

/**
 * Load user's saved logo selections for a template
 * @param {string} userId - User ID
 * @param {string} templateId - Template ID
 * @returns {Promise<Object>} Saved logo selections and customizations
 */
export const loadTemplateLogoSelections = async (userId, templateId) => {
  try {
    prodLogger.debug('📂 Loading template logo selections', {
      userId,
      templateId
    });

    const { data: selections, error } = await supabase
      .from('template_logo_selections')
      .select('*')
      .eq('user_id', userId)
      .eq('template_id', templateId);

    if (error) {
      prodLogger.error('❌ Failed to load template logo selections:', error);
      return { logoSelections: {}, customizations: {} };
    }

    const logoSelections = {};
    const customizations = {};

    selections.forEach(selection => {
      if (selection.selected_logo_id) {
        logoSelections[selection.logo_overlay_id] = selection.selected_logo_id;
      }
      if (selection.customizations && Object.keys(selection.customizations).length > 0) {
        customizations[selection.logo_overlay_id] = selection.customizations;
      }
    });

    prodLogger.debug('✅ Template logo selections loaded', {
      templateId,
      selectionsCount: Object.keys(logoSelections).length,
      customizationsCount: Object.keys(customizations).length
    });

    return { logoSelections, customizations };
  } catch (error) {
    prodLogger.error('❌ Error loading template logo selections:', error);
    return { logoSelections: {}, customizations: {} };
  }
};

/**
 * Migrate logo selections when switching templates
 * @param {Object} fromTemplate - Source template
 * @param {Object} toTemplate - Target template
 * @param {Object} currentSelections - Current logo selections
 * @returns {Object} Migrated logo selections
 */
export const migrateLogoSelections = (fromTemplate, toTemplate, currentSelections) => {
  try {
    prodLogger.debug('🔄 Migrating logo selections between templates', {
      fromTemplateId: fromTemplate?.id,
      toTemplateId: toTemplate?.id,
      currentSelections
    });

    const fromOverlays = fromTemplate?.logo_overlays?.overlays || [];
    const toOverlays = toTemplate?.logo_overlays?.overlays || [];

    if (fromOverlays.length === 0 || toOverlays.length === 0) {
      return {};
    }

    // Simple migration mapping based on overlay IDs and types
    const migrationMap = {
      'primary_logo': ['primary_logo', 'company_logo', 'brand_logo', 'main_logo'],
      'company_logo': ['company_logo', 'primary_logo', 'brand_logo'],
      'institution_logo': ['institution_logo', 'academic_logo', 'org_logo', 'university_logo'],
      'brand_logo': ['brand_logo', 'primary_logo', 'company_logo']
    };

    const migratedSelections = {};

    // Try to map each current selection to the new template
    Object.entries(currentSelections).forEach(([fromOverlayId, logoId]) => {
      const possibleTargets = migrationMap[fromOverlayId] || [fromOverlayId];
      
      // Find the first matching overlay in the target template
      for (const targetId of possibleTargets) {
        const targetOverlay = toOverlays.find(overlay => overlay.id === targetId);
        if (targetOverlay) {
          migratedSelections[targetId] = logoId;
          break;
        }
      }
    });

    prodLogger.debug('✅ Logo selections migrated', {
      fromTemplateId: fromTemplate.id,
      toTemplateId: toTemplate.id,
      migratedCount: Object.keys(migratedSelections).length
    });

    return migratedSelections;
  } catch (error) {
    prodLogger.error('❌ Error migrating logo selections:', error);
    return {};
  }
};

/**
 * Get logo overlay at specific canvas position
 * @param {Array} logoOverlays - Array of logo overlays
 * @param {Object} logoCustomizations - Current logo customizations
 * @param {number} x - X coordinate
 * @param {number} y - Y coordinate
 * @returns {string|null} Logo overlay ID or null
 */
export const getLogoOverlayAtPosition = (logoOverlays, logoCustomizations, x, y) => {
  if (!logoOverlays || logoOverlays.length === 0) return null;

  // Check overlays from top to bottom (reverse order for proper layering)
  for (let i = logoOverlays.length - 1; i >= 0; i--) {
    const overlay = logoOverlays[i];
    const customization = logoCustomizations[overlay.id] || {};
    const position = customization.position || overlay.position;

    if (!position) continue;

    const overlayX = position.x;
    const overlayY = position.y;
    const overlayWidth = position.width;
    const overlayHeight = position.height;

    if (x >= overlayX && x <= overlayX + overlayWidth &&
        y >= overlayY && y <= overlayY + overlayHeight) {
      return overlay.id;
    }
  }

  return null;
};

/**
 * Validate logo overlay constraints
 * @param {Object} overlay - Logo overlay configuration
 * @param {Object} newPosition - New position to validate
 * @param {Object} templateDimensions - Template dimensions
 * @returns {Object} Validation result with corrected position
 */
export const validateLogoPosition = (overlay, newPosition, templateDimensions) => {
  const constraints = overlay.constraints || {};
  const correctedPosition = { ...newPosition };
  const warnings = [];

  // Ensure logo stays within template bounds
  if (correctedPosition.x < 0) {
    correctedPosition.x = 0;
    warnings.push('Logo moved to stay within template bounds');
  }
  if (correctedPosition.y < 0) {
    correctedPosition.y = 0;
    warnings.push('Logo moved to stay within template bounds');
  }
  if (correctedPosition.x + correctedPosition.width > templateDimensions.width) {
    correctedPosition.x = templateDimensions.width - correctedPosition.width;
    warnings.push('Logo moved to stay within template bounds');
  }
  if (correctedPosition.y + correctedPosition.height > templateDimensions.height) {
    correctedPosition.y = templateDimensions.height - correctedPosition.height;
    warnings.push('Logo moved to stay within template bounds');
  }

  // Apply size constraints
  if (constraints.min_width && correctedPosition.width < constraints.min_width) {
    correctedPosition.width = constraints.min_width;
    warnings.push(`Logo width adjusted to minimum: ${constraints.min_width}px`);
  }
  if (constraints.max_width && correctedPosition.width > constraints.max_width) {
    correctedPosition.width = constraints.max_width;
    warnings.push(`Logo width adjusted to maximum: ${constraints.max_width}px`);
  }
  if (constraints.min_height && correctedPosition.height < constraints.min_height) {
    correctedPosition.height = constraints.min_height;
    warnings.push(`Logo height adjusted to minimum: ${constraints.min_height}px`);
  }
  if (constraints.max_height && correctedPosition.height > constraints.max_height) {
    correctedPosition.height = constraints.max_height;
    warnings.push(`Logo height adjusted to maximum: ${constraints.max_height}px`);
  }

  return {
    position: correctedPosition,
    warnings,
    isValid: warnings.length === 0
  };
};
