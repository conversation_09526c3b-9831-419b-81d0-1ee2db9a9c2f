import { supabase } from '../lib/supabase';
import { prodLogger } from '../utils/prodLogger';

/**
 * Usage Tracking Service
 * Handles real-time usage tracking, quota management, and tier-specific limit enforcement
 */
class UsageTrackingService {
  constructor() {
    this.cache = new Map(); // Cache for user limits and usage
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes cache
  }

  /**
   * Get user's current usage and limits
   */
  async getUserUsage(userId) {
    try {
      const cacheKey = `usage_${userId}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data;
      }

      const { data, error } = await supabase
        .from('user_profiles')
        .select(`
          subscription_tier,
          subscription_status,
          documents_created,
          documents_limit,
          ai_generations_used,
          ai_generations_limit,
          ai_image_generations_used,
          ai_image_generations_limit,
          storage_used_mb,
          storage_limit_mb,
          last_usage_reset,
          current_period_end
        `)
        .eq('id', userId)
        .single();

      if (error) {
        prodLogger.error('Failed to get user usage:', error);
        throw error;
      }

      const usage = {
        ...data,
        usage_percentages: {
          documents: this.calculateUsagePercentage(data.documents_created, data.documents_limit),
          ai_generations: this.calculateUsagePercentage(data.ai_generations_used, data.ai_generations_limit),
          ai_images: this.calculateUsagePercentage(data.ai_image_generations_used, data.ai_image_generations_limit),
          storage: this.calculateUsagePercentage(data.storage_used_mb, data.storage_limit_mb),
        },
        limits_exceeded: {
          documents: this.isLimitExceeded(data.documents_created, data.documents_limit),
          ai_generations: this.isLimitExceeded(data.ai_generations_used, data.ai_generations_limit),
          ai_images: this.isLimitExceeded(data.ai_image_generations_used, data.ai_image_generations_limit),
          storage: this.isLimitExceeded(data.storage_used_mb, data.storage_limit_mb),
        }
      };

      // Cache the result
      this.cache.set(cacheKey, {
        data: usage,
        timestamp: Date.now()
      });

      return usage;
    } catch (error) {
      prodLogger.error('Error getting user usage:', error);
      throw error;
    }
  }

  /**
   * Check if user can perform an action based on their limits
   */
  async canPerformAction(userId, actionType, amount = 1) {
    try {
      const usage = await this.getUserUsage(userId);
      
      switch (actionType) {
        case 'create_document':
          return !this.isLimitExceeded(usage.documents_created + amount, usage.documents_limit);
        
        case 'ai_generation':
          return !this.isLimitExceeded(usage.ai_generations_used + amount, usage.ai_generations_limit);
        
        case 'ai_image_generation':
          return !this.isLimitExceeded(usage.ai_image_generations_used + amount, usage.ai_image_generations_limit);
        
        case 'storage_upload':
          return !this.isLimitExceeded(usage.storage_used_mb + amount, usage.storage_limit_mb);
        
        default:
          return true;
      }
    } catch (error) {
      prodLogger.error('Error checking action permission:', error);
      return false; // Fail safe - deny action if we can't check
    }
  }

  /**
   * Track usage for a specific action
   */
  async trackUsage(userId, usageType, amount = 1, metadata = {}) {
    try {
      // Check if user can perform this action
      const canPerform = await this.canPerformAction(userId, usageType, amount);
      
      if (!canPerform) {
        throw new Error(`Usage limit exceeded for ${usageType}`);
      }

      // Track the usage using the database function
      const { error } = await supabase.rpc('track_usage', {
        user_uuid: userId,
        usage_type_param: this.mapUsageType(usageType),
        amount_param: amount,
        metadata_param: metadata
      });

      if (error) {
        prodLogger.error('Failed to track usage:', error);
        throw error;
      }

      // Invalidate cache for this user
      this.cache.delete(`usage_${userId}`);

      prodLogger.info(`Usage tracked: ${usageType} (${amount}) for user ${userId}`);
      return true;
    } catch (error) {
      prodLogger.error('Error tracking usage:', error);
      throw error;
    }
  }

  /**
   * Get usage analytics for a user
   */
  async getUserUsageAnalytics(userId, startDate, endDate) {
    try {
      const { data, error } = await supabase
        .from('usage_tracking')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate)
        .lte('created_at', endDate)
        .order('created_at', { ascending: false });

      if (error) {
        prodLogger.error('Failed to get usage analytics:', error);
        throw error;
      }

      // Group by usage type and calculate totals
      const analytics = data.reduce((acc, record) => {
        const type = record.usage_type;
        if (!acc[type]) {
          acc[type] = {
            total_amount: 0,
            total_records: 0,
            daily_breakdown: {}
          };
        }
        
        acc[type].total_amount += record.amount;
        acc[type].total_records += 1;
        
        const date = new Date(record.created_at).toISOString().split('T')[0];
        if (!acc[type].daily_breakdown[date]) {
          acc[type].daily_breakdown[date] = 0;
        }
        acc[type].daily_breakdown[date] += record.amount;
        
        return acc;
      }, {});

      return analytics;
    } catch (error) {
      prodLogger.error('Error getting usage analytics:', error);
      throw error;
    }
  }

  /**
   * Reset usage counters (typically called at billing period start)
   */
  async resetUsageCounters(userId) {
    try {
      const { error } = await supabase.rpc('reset_usage_counters', {
        user_uuid: userId
      });

      if (error) {
        prodLogger.error('Failed to reset usage counters:', error);
        throw error;
      }

      // Invalidate cache
      this.cache.delete(`usage_${userId}`);

      prodLogger.info(`Usage counters reset for user ${userId}`);
      return true;
    } catch (error) {
      prodLogger.error('Error resetting usage counters:', error);
      throw error;
    }
  }

  /**
   * Get tier-specific feature access
   */
  async getFeatureAccess(userId) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('subscription_tier')
        .eq('id', userId)
        .single();

      if (error) {
        prodLogger.error('Failed to get user tier:', error);
        throw error;
      }

      return this.getTierFeatures(data.subscription_tier);
    } catch (error) {
      prodLogger.error('Error getting feature access:', error);
      throw error;
    }
  }

  /**
   * Helper: Calculate usage percentage
   */
  calculateUsagePercentage(used, limit) {
    if (limit === -1) return 0; // Unlimited
    if (limit === 0) return 100;
    return Math.min(Math.round((used / limit) * 100), 100);
  }

  /**
   * Helper: Check if limit is exceeded
   */
  isLimitExceeded(used, limit) {
    if (limit === -1) return false; // Unlimited
    return used >= limit;
  }

  /**
   * Helper: Map frontend usage types to database types
   */
  mapUsageType(usageType) {
    const mapping = {
      'create_document': 'document_creation',
      'ai_generation': 'ai_generation',
      'ai_image_generation': 'ai_image_generation',
      'storage_upload': 'storage_upload'
    };
    return mapping[usageType] || usageType;
  }

  /**
   * Helper: Get tier-specific features
   */
  getTierFeatures(tier) {
    const features = {
      free: {
        custom_templates: false,
        priority_processing: false,
        remove_watermark: false,
        advanced_analytics: false,
        api_access: false,
        ai_model_selection: false,
        phone_support: false,
      },
      basic: {
        custom_templates: false,
        priority_processing: false,
        remove_watermark: false,
        advanced_analytics: false,
        api_access: false,
        ai_model_selection: false,
        phone_support: false,
      },
      standard: {
        custom_templates: true,
        priority_processing: true,
        remove_watermark: true,
        advanced_analytics: true,
        api_access: false,
        ai_model_selection: false,
        phone_support: false,
      },
      pro: {
        custom_templates: true,
        priority_processing: true,
        remove_watermark: true,
        advanced_analytics: true,
        api_access: true,
        ai_model_selection: true,
        phone_support: true,
      }
    };

    return features[tier] || features.free;
  }

  /**
   * Clear cache (useful for testing or manual cache invalidation)
   */
  clearCache() {
    this.cache.clear();
  }
}

// Create singleton instance
const usageTrackingService = new UsageTrackingService();

export default usageTrackingService;
