import { loadStripe } from "@stripe/stripe-js";
import { supabase } from "../lib/supabase";
import { prodLogger } from "../utils/prodLogger";

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

// Stripe configuration
const STRIPE_CONFIG = {
  PRICE_IDS: {
    basic: {
      monthly: import.meta.env.VITE_STRIPE_BASIC_MONTHLY_PRICE_ID,
      yearly: import.meta.env.VITE_STRIPE_BASIC_YEARLY_PRICE_ID,
    },
    standard: {
      monthly: import.meta.env.VITE_STRIPE_STANDARD_MONTHLY_PRICE_ID,
      yearly: import.meta.env.VITE_STRIPE_STANDARD_YEARLY_PRICE_ID,
    },
    pro: {
      monthly: import.meta.env.VITE_STRIPE_PRO_MONTHLY_PRICE_ID,
      yearly: import.meta.env.VITE_STRIPE_PRO_YEARLY_PRICE_ID,
    },
  },
  CUSTOMER_PORTAL_URL: import.meta.env.VITE_STRIPE_CUSTOMER_PORTAL_URL,
};

/**
 * Stripe Service for handling subscription payments and management
 */
class StripeService {
  constructor() {
    this.stripe = null;
    this.initialized = false;
  }

  /**
   * Initialize Stripe instance
   */
  async initialize() {
    if (this.initialized) return;

    try {
      this.stripe = await stripePromise;
      this.initialized = true;
      prodLogger.info("Stripe service initialized successfully");
    } catch (error) {
      prodLogger.error("Failed to initialize Stripe:", error);
      throw new Error("Stripe initialization failed");
    }
  }

  /**
   * Create a checkout session for subscription
   */
  async createCheckoutSession(
    tier,
    billingPeriod = "monthly",
    userId,
    userEmail
  ) {
    await this.initialize();

    let priceId; // Declare priceId outside try block for proper scope

    try {
      priceId = STRIPE_CONFIG.PRICE_IDS[tier]?.[billingPeriod];

      // Debug logging
      prodLogger.info("Stripe checkout session creation started:", {
        tier,
        billingPeriod,
        priceId,
        userId,
        userEmail,
        availablePriceIds: STRIPE_CONFIG.PRICE_IDS,
      });

      if (!priceId) {
        throw new Error(
          `Invalid tier or billing period: ${tier}/${billingPeriod}. Available tiers: ${Object.keys(
            STRIPE_CONFIG.PRICE_IDS
          ).join(", ")}`
        );
      }

      // Get current session to ensure we have valid auth
      const { data: sessionData, error: sessionError } =
        await supabase.auth.getSession();

      if (sessionError || !sessionData.session) {
        throw new Error("User not authenticated. Please log in again.");
      }

      // Use direct fetch instead of supabase.functions.invoke due to client issues
      const functionUrl = `${
        import.meta.env.VITE_SUPABASE_URL
      }/functions/v1/create-checkout-session`;

      const response = await fetch(functionUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${sessionData.session.access_token}`,
          apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
        },
        body: JSON.stringify({
          priceId,
          userId,
          userEmail,
          tier,
          billingPeriod,
          successUrl: `${window.location.origin}/account-settings?session_id={CHECKOUT_SESSION_ID}`,
          cancelUrl: `${window.location.origin}/pricing`,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { error: errorText };
        }
        throw new Error(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      const data = await response.json();

      if (!data || !data.sessionId) {
        throw new Error("Invalid response from checkout session creation");
      }

      prodLogger.info("Checkout session created successfully:", {
        sessionId: data.sessionId,
        tier,
        billingPeriod,
      });

      return data;
    } catch (error) {
      prodLogger.error("Checkout session creation failed:", {
        error: error.message,
        stack: error.stack,
        priceId,
        tier,
        billingPeriod,
      });
      throw error;
    }
  }

  /**
   * Redirect to Stripe Checkout
   */
  async redirectToCheckout(sessionId) {
    await this.initialize();

    try {
      const { error } = await this.stripe.redirectToCheckout({
        sessionId,
      });

      if (error) {
        prodLogger.error("Stripe redirect failed:", error);
        throw error;
      }
    } catch (error) {
      prodLogger.error("Failed to redirect to checkout:", error);
      throw error;
    }
  }

  /**
   * Create subscription checkout flow
   */
  async subscribeToTier(tier, billingPeriod, userId, userEmail) {
    try {
      const session = await this.createCheckoutSession(
        tier,
        billingPeriod,
        userId,
        userEmail
      );
      await this.redirectToCheckout(session.sessionId);
    } catch (error) {
      prodLogger.error("Subscription flow failed:", error);
      throw error;
    }
  }

  /**
   * Create customer portal session
   */
  async createCustomerPortalSession(userId) {
    try {
      // Get auth session for authorization
      const { data: sessionData, error: sessionError } =
        await supabase.auth.getSession();

      if (sessionError || !sessionData.session) {
        throw new Error("User not authenticated. Please log in again.");
      }

      // Use direct fetch instead of supabase.functions.invoke due to client issues
      const functionUrl = `${
        import.meta.env.VITE_SUPABASE_URL
      }/functions/v1/create-portal-session`;

      const response = await fetch(functionUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${sessionData.session.access_token}`,
          apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { error: errorText };
        }
        throw new Error(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      const data = await response.json();

      if (!data || !data.url) {
        throw new Error("Invalid response from portal session creation");
      }

      prodLogger.info("Customer portal session created successfully");
      return data;
    } catch (error) {
      prodLogger.error("Portal session creation failed:", error);
      throw error;
    }
  }

  /**
   * Redirect to customer portal
   */
  async redirectToCustomerPortal(userId) {
    try {
      const session = await this.createCustomerPortalSession(userId);
      window.location.href = session.url;
    } catch (error) {
      prodLogger.error("Failed to redirect to customer portal:", error);
      throw error;
    }
  }

  /**
   * Get subscription status
   */
  async getSubscriptionStatus(userId) {
    try {
      // Get subscription status directly from database instead of non-existent edge function
      const { data, error } = await supabase
        .from("user_subscriptions")
        .select("*")
        .eq("user_id", userId)
        .single();

      if (error && error.code !== "PGRST116") {
        // PGRST116 = no rows returned
        prodLogger.error("Failed to get subscription status:", error);
        throw error;
      }

      return data || null;
    } catch (error) {
      prodLogger.error("Subscription status fetch failed:", error);
      throw error;
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(userId) {
    try {
      // Update subscription status in database instead of using non-existent edge function
      const { data, error } = await supabase
        .from("user_subscriptions")
        .update({
          subscription_status: "cancelled",
          cancel_at_period_end: true,
          cancelled_at: new Date().toISOString(),
        })
        .eq("user_id", userId)
        .select()
        .single();

      if (error) {
        prodLogger.error("Failed to cancel subscription:", error);
        throw error;
      }

      return data;
    } catch (error) {
      prodLogger.error("Subscription cancellation failed:", error);
      throw error;
    }
  }

  /**
   * Resume subscription
   */
  async resumeSubscription(userId) {
    try {
      // Update subscription status in database instead of using non-existent edge function
      const { data, error } = await supabase
        .from("user_subscriptions")
        .update({
          subscription_status: "active",
          cancel_at_period_end: false,
          cancelled_at: null,
        })
        .eq("user_id", userId)
        .select()
        .single();

      if (error) {
        prodLogger.error("Failed to resume subscription:", error);
        throw error;
      }

      return data;
    } catch (error) {
      prodLogger.error("Subscription resume failed:", error);
      throw error;
    }
  }

  /**
   * Get pricing information
   */
  getPricingInfo() {
    return {
      basic: {
        monthly: { price: 9, priceId: STRIPE_CONFIG.PRICE_IDS.basic.monthly },
        yearly: {
          price: 84,
          priceId: STRIPE_CONFIG.PRICE_IDS.basic.yearly,
          savings: 24,
        },
      },
      standard: {
        monthly: {
          price: 19,
          priceId: STRIPE_CONFIG.PRICE_IDS.standard.monthly,
        },
        yearly: {
          price: 180,
          priceId: STRIPE_CONFIG.PRICE_IDS.standard.yearly,
          savings: 48,
        },
      },
      pro: {
        monthly: { price: 39, priceId: STRIPE_CONFIG.PRICE_IDS.pro.monthly },
        yearly: {
          price: 348,
          priceId: STRIPE_CONFIG.PRICE_IDS.pro.yearly,
          savings: 120,
        },
      },
    };
  }

  /**
   * Validate webhook signature (for use in Edge Functions)
   */
  static validateWebhookSignature(payload, signature, secret) {
    // This would be used in the Edge Function
    // Implementation depends on the webhook handling setup
    return true; // Placeholder
  }
}

// Create singleton instance
const stripeService = new StripeService();

export default stripeService;
export { STRIPE_CONFIG };
