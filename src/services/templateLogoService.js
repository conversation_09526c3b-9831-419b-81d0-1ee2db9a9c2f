/**
 * Template Logo Service
 * Handles template-specific logo integration, positioning, and optimization
 * Integrates with the cover preview workflow for real-time logo customization
 */

import { supabase } from "../lib/supabase.js";
import { getUserLogos, incrementLogoUsage } from "./logoService.js";
import { prodLogger } from "../utils/prodLogger.js";

// Template-specific logo configuration
const TEMPLATE_LOGO_CONFIG = {
  // Business templates - professional, prominent branding
  business: {
    defaultPosition: "top-right",
    defaultSize: "medium",
    defaultOpacity: 1.0,
    allowedPositions: ["top-left", "top-right", "top-center"],
    sizeMultipliers: { small: 0.6, medium: 0.8, large: 1.0 },
    maxDimensions: { width: 120, height: 80 }
  },
  
  // Academic templates - subtle, institutional branding
  academic: {
    defaultPosition: "top-center",
    defaultSize: "small",
    defaultOpacity: 0.8,
    allowedPositions: ["top-center", "top-left", "top-right"],
    sizeMultipliers: { small: 0.4, medium: 0.6, large: 0.8 },
    maxDimensions: { width: 80, height: 60 }
  },
  
  // Creative/Marketing templates - flexible, impactful branding
  creative: {
    defaultPosition: "template-dependent", // Varies by template design
    defaultSize: "large",
    defaultOpacity: 1.0,
    allowedPositions: ["top-left", "top-right", "top-center", "bottom-left", "bottom-right"],
    sizeMultipliers: { small: 0.7, medium: 0.9, large: 1.2 },
    maxDimensions: { width: 150, height: 100 }
  },
  
  // Default fallback for unknown template types
  default: {
    defaultPosition: "top-right",
    defaultSize: "medium",
    defaultOpacity: 1.0,
    allowedPositions: ["top-left", "top-right", "top-center"],
    sizeMultipliers: { small: 0.5, medium: 0.75, large: 1.0 },
    maxDimensions: { width: 100, height: 70 }
  }
};

/**
 * Get template-specific logo configuration
 * @param {Object} template - Template object with category/type information
 * @returns {Object} Template-specific logo configuration
 */
export const getTemplateLogoConfig = (template) => {
  if (!template) return TEMPLATE_LOGO_CONFIG.default;
  
  // Determine template type from category or other properties
  const templateType = determineTemplateType(template);
  return TEMPLATE_LOGO_CONFIG[templateType] || TEMPLATE_LOGO_CONFIG.default;
};

/**
 * Determine template type from template properties
 * @param {Object} template - Template object
 * @returns {string} Template type (business, academic, creative, default)
 */
const determineTemplateType = (template) => {
  const category = template.category?.toLowerCase() || '';
  const name = template.name?.toLowerCase() || '';
  
  // Business template indicators
  if (category.includes('business') || category.includes('professional') ||
      name.includes('business') || name.includes('corporate') || name.includes('professional')) {
    return 'business';
  }
  
  // Academic template indicators
  if (category.includes('academic') || category.includes('education') ||
      name.includes('academic') || name.includes('research') || name.includes('thesis')) {
    return 'academic';
  }
  
  // Creative/Marketing template indicators
  if (category.includes('creative') || category.includes('marketing') ||
      name.includes('creative') || name.includes('modern') || name.includes('artistic')) {
    return 'creative';
  }
  
  return 'default';
};

/**
 * Calculate optimal logo dimensions for template
 * @param {Object} logo - Logo object with width/height
 * @param {Object} template - Template object
 * @param {string} size - Size setting (small, medium, large)
 * @returns {Object} Calculated dimensions {width, height}
 */
export const calculateLogoSize = (logo, template, size = 'medium') => {
  const config = getTemplateLogoConfig(template);
  const multiplier = config.sizeMultipliers[size] || config.sizeMultipliers.medium;
  const maxDimensions = config.maxDimensions;
  
  // Calculate dimensions maintaining aspect ratio
  let width = logo.width || maxDimensions.width;
  let height = logo.height || maxDimensions.height;
  
  // Apply size multiplier
  width *= multiplier;
  height *= multiplier;
  
  // Ensure within maximum dimensions
  if (width > maxDimensions.width || height > maxDimensions.height) {
    const aspectRatio = width / height;
    
    if (width > height) {
      width = maxDimensions.width;
      height = width / aspectRatio;
    } else {
      height = maxDimensions.height;
      width = height * aspectRatio;
    }
  }
  
  return {
    width: Math.round(width),
    height: Math.round(height)
  };
};

/**
 * Get optimal logo position for template
 * @param {Object} template - Template object
 * @param {string} preferredPosition - User's preferred position (optional)
 * @returns {string} Optimal logo position
 */
export const getOptimalLogoPosition = (template, preferredPosition = null) => {
  const config = getTemplateLogoConfig(template);
  
  // Use preferred position if valid for this template type
  if (preferredPosition && config.allowedPositions.includes(preferredPosition)) {
    return preferredPosition;
  }
  
  // Handle template-dependent positioning for creative templates
  if (config.defaultPosition === 'template-dependent') {
    return analyzeTemplateLayout(template);
  }
  
  return config.defaultPosition;
};

/**
 * Analyze template layout to determine optimal logo position
 * @param {Object} template - Template object
 * @returns {string} Recommended logo position
 */
const analyzeTemplateLayout = (template) => {
  // This would analyze template design elements to find optimal logo placement
  // For now, return a sensible default based on template properties
  
  const name = template.name?.toLowerCase() || '';
  
  if (name.includes('minimal') || name.includes('clean')) {
    return 'top-right';
  }
  
  if (name.includes('bold') || name.includes('impact')) {
    return 'top-center';
  }
  
  return 'top-right'; // Safe default
};

/**
 * Prepare logo data for template integration
 * @param {string} userId - User ID
 * @param {Object} template - Selected template
 * @param {string} logoId - Specific logo ID (optional)
 * @param {Object} customSettings - Custom logo settings (optional)
 * @returns {Promise<Object>} Logo data prepared for template integration
 */
export const prepareLogoForTemplate = async (userId, template, logoId = null, customSettings = {}) => {
  try {
    prodLogger.debug('🎨 Preparing logo for template integration', {
      userId,
      templateId: template?.id,
      templateName: template?.name,
      logoId,
      customSettings
    });

    // Get logo data
    let logoData = null;
    
    if (logoId) {
      // Get specific logo
      const { data: logo, error } = await supabase
        .from('user_logos')
        .select('*')
        .eq('id', logoId)
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (error) {
        prodLogger.warn('⚠️ Failed to fetch specific logo:', error);
      } else {
        logoData = logo;
      }
    }

    if (!logoData) {
      // Get user's default logo
      const { data: defaultLogo, error } = await supabase
        .from('user_logos')
        .select('*')
        .eq('user_id', userId)
        .eq('is_default', true)
        .eq('is_active', true)
        .single();

      if (error || !defaultLogo) {
        prodLogger.debug('ℹ️ No logo available for user:', userId);
        return null;
      }

      logoData = defaultLogo;
    }

    // Get template-specific configuration
    const templateConfig = getTemplateLogoConfig(template);
    
    // Calculate optimal settings
    const position = customSettings.position || getOptimalLogoPosition(template, customSettings.position);
    const size = customSettings.size || templateConfig.defaultSize;
    const opacity = customSettings.opacity !== undefined ? customSettings.opacity : templateConfig.defaultOpacity;
    
    // Calculate dimensions
    const dimensions = calculateLogoSize(logoData, template, size);
    
    // Download logo data from storage
    const { data: logoBuffer, error: downloadError } = await supabase.storage
      .from('user-logos')
      .download(logoData.storage_path);

    if (downloadError) {
      prodLogger.error('❌ Failed to download logo data:', downloadError);
      return null;
    }

    // Convert blob to array buffer
    const arrayBuffer = await logoBuffer.arrayBuffer();

    // Increment usage count (async, don't wait)
    incrementLogoUsage(logoData.id).catch(error => {
      prodLogger.warn('⚠️ Failed to increment logo usage:', error);
    });

    const result = {
      id: logoData.id,
      name: logoData.name,
      description: logoData.description,
      data: arrayBuffer,
      originalWidth: logoData.width,
      originalHeight: logoData.height,
      settings: {
        position,
        size,
        opacity,
        dimensions,
        templateType: determineTemplateType(template)
      },
      templateConfig,
      metadata: {
        fileSize: logoData.file_size,
        fileType: logoData.file_type,
        aspectRatio: logoData.aspect_ratio
      }
    };

    prodLogger.debug('✅ Logo prepared successfully for template integration', {
      logoId: logoData.id,
      logoName: logoData.name,
      position,
      size,
      dimensions
    });

    return result;
  } catch (error) {
    prodLogger.error('❌ Error preparing logo for template:', error);
    return null;
  }
};

/**
 * Validate logo compatibility with template
 * @param {Object} logo - Logo data
 * @param {Object} template - Template object
 * @returns {Object} Validation result with warnings and recommendations
 */
export const validateLogoTemplateCompatibility = (logo, template) => {
  const config = getTemplateLogoConfig(template);
  const warnings = [];
  const recommendations = [];
  
  // Check aspect ratio compatibility
  const aspectRatio = logo.originalWidth / logo.originalHeight;
  
  if (config.defaultPosition.includes('center') && aspectRatio > 3) {
    warnings.push('Logo is very wide for center positioning on this template');
    recommendations.push('Consider using left or right positioning');
  }
  
  if ((config.defaultPosition.includes('left') || config.defaultPosition.includes('right')) && aspectRatio < 0.5) {
    warnings.push('Logo is very tall for side positioning on this template');
    recommendations.push('Consider using center positioning');
  }
  
  // Check size compatibility
  const templateType = determineTemplateType(template);
  if (templateType === 'academic' && logo.originalWidth > 200) {
    warnings.push('Logo may be too prominent for academic template style');
    recommendations.push('Consider using a smaller size setting');
  }
  
  return {
    compatible: true,
    warnings,
    recommendations,
    suggestedSettings: {
      position: config.defaultPosition,
      size: config.defaultSize,
      opacity: config.defaultOpacity
    }
  };
};

/**
 * Get logo positioning options for template
 * @param {Object} template - Template object
 * @returns {Array} Available positioning options with labels
 */
export const getLogoPositionOptions = (template) => {
  const config = getTemplateLogoConfig(template);
  
  const positionLabels = {
    'top-left': { label: 'Top Left', icon: 'CornerUpLeft' },
    'top-center': { label: 'Top Center', icon: 'ArrowUp' },
    'top-right': { label: 'Top Right', icon: 'CornerUpRight' },
    'bottom-left': { label: 'Bottom Left', icon: 'CornerDownLeft' },
    'bottom-right': { label: 'Bottom Right', icon: 'CornerDownRight' }
  };
  
  return config.allowedPositions.map(position => ({
    value: position,
    ...positionLabels[position],
    isDefault: position === config.defaultPosition
  }));
};

export { TEMPLATE_LOGO_CONFIG };
