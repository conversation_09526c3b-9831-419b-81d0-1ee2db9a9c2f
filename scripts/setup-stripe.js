#!/usr/bin/env node

/**
 * Stripe Setup Script
 * Creates products and prices in Stripe for RapidDoc AI subscription tiers
 * Run with: node scripts/setup-stripe.js
 */

import Strip<PERSON> from 'stripe';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

const SUBSCRIPTION_PLANS = [
  {
    name: 'RapidDoc AI - Basic Plan',
    tier: 'basic',
    description: 'Perfect for students, educators, and light users who need essential AI document creation',
    features: [
      '10 complete documents per month',
      '50 AI editing requests per month', 
      '10 AI-generated images per month',
      '500MB cloud storage',
      'PDF and DOCX export',
      'Email support (48h response)',
    ],
    prices: {
      monthly: 900, // $9.00 in cents
      yearly: 8400,  // $84.00 in cents (22% off)
    },
  },
  {
    name: 'RapidDoc AI - Standard Plan',
    tier: 'standard',
    description: 'Ideal for content creators, business professionals, and entrepreneurs',
    features: [
      '50 complete documents per month',
      '200 AI editing requests per month',
      '50 AI-generated images per month',
      '2GB cloud storage',
      'PDF, DOCX, HTML export',
      'Priority processing',
      'Custom templates',
      'Priority email support (24h response)',
    ],
    prices: {
      monthly: 1900, // $19.00 in cents
      yearly: 18000, // $180.00 in cents (21% off)
    },
  },
  {
    name: 'RapidDoc AI - Pro Plan',
    tier: 'pro',
    description: 'For power users, agencies, and organizations with high-volume needs',
    features: [
      'Unlimited complete documents',
      'Unlimited AI editing requests',
      'Unlimited AI-generated images',
      '10GB cloud storage',
      'All export formats with custom styling',
      'AI model selection (Claude, ChatGPT, Gemini)',
      'Advanced templates',
      'Priority support (2h response) + phone support',
      'Custom training and onboarding',
    ],
    prices: {
      monthly: 3900, // $39.00 in cents
      yearly: 34800, // $348.00 in cents (26% off)
    },
  },
];

async function createProducts() {
  console.log('🚀 Setting up Stripe products and prices for RapidDoc AI...\n');

  const results = {};

  for (const plan of SUBSCRIPTION_PLANS) {
    console.log(`📦 Creating product: ${plan.name}`);
    
    try {
      // Create product
      const product = await stripe.products.create({
        name: plan.name,
        description: plan.description,
        metadata: {
          tier: plan.tier,
          features: JSON.stringify(plan.features),
        },
      });

      console.log(`✅ Product created: ${product.id}`);

      // Create monthly price
      const monthlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.prices.monthly,
        currency: 'usd',
        recurring: {
          interval: 'month',
          trial_period_days: 7, // 7-day free trial
        },
        metadata: {
          tier: plan.tier,
          billing_period: 'monthly',
        },
      });

      console.log(`💰 Monthly price created: ${monthlyPrice.id} ($${plan.prices.monthly / 100}/month)`);

      // Create yearly price
      const yearlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.prices.yearly,
        currency: 'usd',
        recurring: {
          interval: 'year',
          trial_period_days: 7, // 7-day free trial
        },
        metadata: {
          tier: plan.tier,
          billing_period: 'yearly',
        },
      });

      console.log(`💰 Yearly price created: ${yearlyPrice.id} ($${plan.prices.yearly / 100}/year)`);

      results[plan.tier] = {
        productId: product.id,
        monthlyPriceId: monthlyPrice.id,
        yearlyPriceId: yearlyPrice.id,
      };

      console.log('');
    } catch (error) {
      console.error(`❌ Error creating product ${plan.name}:`, error.message);
    }
  }

  return results;
}

async function setupCustomerPortal(products) {
  console.log('🏪 Setting up Stripe Customer Portal...');

  try {
    const configuration = await stripe.billingPortal.configurations.create({
      business_profile: {
        headline: 'RapidDoc AI - Manage Your Subscription',
        privacy_policy_url: 'https://rapiddoc-ai.com/privacy',
        terms_of_service_url: 'https://rapiddoc-ai.com/terms',
      },
      features: {
        payment_method_update: { enabled: true },
        subscription_cancel: {
          enabled: true,
          mode: 'at_period_end',
          proration_behavior: 'none',
        },
        subscription_pause: { enabled: false },
        subscription_update: {
          enabled: true,
          default_allowed_updates: ['price'],
          proration_behavior: 'create_prorations',
          products: Object.values(products).map(p => ({
            product: p.productId,
            prices: [p.monthlyPriceId, p.yearlyPriceId]
          })),
        },
        invoice_history: { enabled: true },
      },
    });

    console.log(`✅ Customer Portal configured: ${configuration.id}`);
    return configuration.id;
  } catch (error) {
    console.error('❌ Error setting up Customer Portal:', error.message);
  }
}

async function main() {
  try {
    // Validate Stripe key
    if (!process.env.STRIPE_SECRET_KEY) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }

    // Create products and prices
    const products = await createProducts();

    // Setup customer portal
    const portalConfigId = await setupCustomerPortal(products);

    // Display environment variables to add
    console.log('\n🔧 Add these environment variables to your .env file:\n');
    
    Object.entries(products).forEach(([tier, data]) => {
      const tierUpper = tier.toUpperCase();
      console.log(`VITE_STRIPE_${tierUpper}_MONTHLY_PRICE_ID=${data.monthlyPriceId}`);
      console.log(`VITE_STRIPE_${tierUpper}_YEARLY_PRICE_ID=${data.yearlyPriceId}`);
    });
    
    if (portalConfigId) {
      console.log(`VITE_STRIPE_CUSTOMER_PORTAL_CONFIG_ID=${portalConfigId}`);
    }

    console.log('\n✨ Stripe setup completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Add the environment variables above to your .env file');
    console.log('2. Configure your webhook endpoint in Stripe Dashboard');
    console.log('3. Add the webhook signing secret to your environment variables');
    console.log('4. Deploy your Supabase Edge Functions');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
main();
