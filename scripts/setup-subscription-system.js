#!/usr/bin/env node

/**
 * Complete Subscription System Setup Script
 * Sets up database, Stripe products, and environment configuration
 * Run with: node scripts/setup-subscription-system.js
 */

import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

const SUBSCRIPTION_PLANS = [
  {
    name: 'RapidDoc AI - Basic Plan',
    tier: 'basic',
    description: 'Perfect for students, educators, and light users who need essential AI document creation',
    prices: {
      monthly: 900, // $9.00 in cents
      yearly: 8400,  // $84.00 in cents
    },
    limits: {
      documents: 10,
      ai_generations: 50,
      ai_images: 10,
      storage_mb: 500,
      max_file_mb: 5,
    },
    features: {
      custom_templates: false,
      priority_processing: false,
      remove_watermark: false,
      advanced_analytics: false,
      api_access: false,
      ai_model_selection: false,
      phone_support: false,
    }
  },
  {
    name: 'RapidDoc AI - Standard Plan',
    tier: 'standard',
    description: 'Ideal for content creators, business professionals, and entrepreneurs',
    prices: {
      monthly: 1900, // $19.00 in cents
      yearly: 18000, // $180.00 in cents
    },
    limits: {
      documents: 50,
      ai_generations: 200,
      ai_images: 50,
      storage_mb: 2048,
      max_file_mb: 25,
    },
    features: {
      custom_templates: true,
      priority_processing: true,
      remove_watermark: true,
      advanced_analytics: true,
      api_access: false,
      ai_model_selection: false,
      phone_support: false,
    }
  },
  {
    name: 'RapidDoc AI - Pro Plan',
    tier: 'pro',
    description: 'For power users, agencies, and organizations with high-volume needs',
    prices: {
      monthly: 3900, // $39.00 in cents
      yearly: 34800, // $348.00 in cents
    },
    limits: {
      documents: -1, // Unlimited
      ai_generations: -1, // Unlimited
      ai_images: -1, // Unlimited
      storage_mb: 10240,
      max_file_mb: 100,
    },
    features: {
      custom_templates: true,
      priority_processing: true,
      remove_watermark: true,
      advanced_analytics: true,
      api_access: true,
      ai_model_selection: true,
      phone_support: true,
    }
  },
];

async function runDatabaseMigration() {
  console.log('🗄️  Running database migration...');
  
  try {
    const migrationSQL = fs.readFileSync(
      path.join(process.cwd(), 'database/migrations/001_subscription_system.sql'),
      'utf8'
    );

    const { error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });

    if (error) {
      throw error;
    }

    console.log('✅ Database migration completed successfully');
  } catch (error) {
    console.error('❌ Database migration failed:', error.message);
    throw error;
  }
}

async function createStripeProducts() {
  console.log('🛍️  Creating Stripe products and prices...');

  const results = {};

  for (const plan of SUBSCRIPTION_PLANS) {
    console.log(`📦 Creating product: ${plan.name}`);
    
    try {
      // Create product
      const product = await stripe.products.create({
        name: plan.name,
        description: plan.description,
        metadata: {
          tier: plan.tier,
        },
      });

      // Create monthly price
      const monthlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.prices.monthly,
        currency: 'usd',
        recurring: {
          interval: 'month',
          trial_period_days: 7,
        },
        metadata: {
          tier: plan.tier,
          billing_period: 'monthly',
        },
      });

      // Create yearly price
      const yearlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.prices.yearly,
        currency: 'usd',
        recurring: {
          interval: 'year',
          trial_period_days: 7,
        },
        metadata: {
          tier: plan.tier,
          billing_period: 'yearly',
        },
      });

      results[plan.tier] = {
        productId: product.id,
        monthlyPriceId: monthlyPrice.id,
        yearlyPriceId: yearlyPrice.id,
      };

      console.log(`✅ Created ${plan.tier} plan with prices`);
    } catch (error) {
      console.error(`❌ Error creating product ${plan.name}:`, error.message);
      throw error;
    }
  }

  return results;
}

async function updateSubscriptionPlans(stripeResults) {
  console.log('📊 Updating subscription plans in database...');

  try {
    for (const plan of SUBSCRIPTION_PLANS) {
      const stripeData = stripeResults[plan.tier];
      
      const { error } = await supabase
        .from('subscription_plans')
        .upsert({
          name: plan.name,
          tier: plan.tier,
          price_monthly: plan.prices.monthly,
          price_yearly: plan.prices.yearly,
          stripe_price_id_monthly: stripeData.monthlyPriceId,
          stripe_price_id_yearly: stripeData.yearlyPriceId,
          stripe_product_id: stripeData.productId,
          documents_limit: plan.limits.documents,
          ai_generations_limit: plan.limits.ai_generations,
          ai_image_generations_limit: plan.limits.ai_images,
          storage_limit_mb: plan.limits.storage_mb,
          max_file_upload_mb: plan.limits.max_file_mb,
          ...plan.features,
        });

      if (error) {
        throw error;
      }
    }

    console.log('✅ Subscription plans updated in database');
  } catch (error) {
    console.error('❌ Failed to update subscription plans:', error.message);
    throw error;
  }
}

async function setupCustomerPortal() {
  console.log('🏪 Setting up Stripe Customer Portal...');
  
  try {
    const configuration = await stripe.billingPortal.configurations.create({
      business_profile: {
        headline: 'RapidDoc AI - Manage Your Subscription',
        privacy_policy_url: 'https://rapiddoc-ai.com/privacy',
        terms_of_service_url: 'https://rapiddoc-ai.com/terms',
      },
      features: {
        payment_method_update: { enabled: true },
        subscription_cancel: { 
          enabled: true,
          mode: 'at_period_end',
          proration_behavior: 'none',
        },
        subscription_pause: { enabled: false },
        subscription_update: {
          enabled: true,
          default_allowed_updates: ['price'],
          proration_behavior: 'create_prorations',
        },
        invoice_history: { enabled: true },
      },
    });

    console.log(`✅ Customer Portal configured: ${configuration.id}`);
    return configuration.id;
  } catch (error) {
    console.error('❌ Error setting up Customer Portal:', error.message);
    throw error;
  }
}

function generateEnvironmentVariables(stripeResults, portalConfigId) {
  console.log('\n🔧 Environment variables to add to your .env file:\n');
  
  Object.entries(stripeResults).forEach(([tier, data]) => {
    const tierUpper = tier.toUpperCase();
    console.log(`VITE_STRIPE_${tierUpper}_MONTHLY_PRICE_ID=${data.monthlyPriceId}`);
    console.log(`VITE_STRIPE_${tierUpper}_YEARLY_PRICE_ID=${data.yearlyPriceId}`);
  });
  
  if (portalConfigId) {
    console.log(`VITE_STRIPE_CUSTOMER_PORTAL_CONFIG_ID=${portalConfigId}`);
  }

  console.log('\n📝 Next steps:');
  console.log('1. Add the environment variables above to your .env file');
  console.log('2. Configure your webhook endpoint in Stripe Dashboard');
  console.log('3. Add the webhook signing secret to STRIPE_WEBHOOK_SECRET');
  console.log('4. Deploy your Supabase Edge Functions');
  console.log('5. Test the subscription flow');
}

async function main() {
  try {
    console.log('🚀 Setting up RapidDoc AI Subscription System...\n');

    // Validate environment variables
    if (!process.env.VITE_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('Supabase environment variables are required');
    }

    if (!process.env.STRIPE_SECRET_KEY) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }

    // Run setup steps
    await runDatabaseMigration();
    const stripeResults = await createStripeProducts();
    await updateSubscriptionPlans(stripeResults);
    const portalConfigId = await setupCustomerPortal();

    // Generate environment variables
    generateEnvironmentVariables(stripeResults, portalConfigId);

    console.log('\n✨ Subscription system setup completed successfully!');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
main();
