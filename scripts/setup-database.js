import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Supabase configuration
const supabaseUrl = 'https://mlfojzeyywdxlpbgbtsv.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || 'your-service-key-here'

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function setupDatabase() {
  try {
    console.log('🚀 Setting up RapidDoc AI database schema...')
    
    // Read the schema file
    const schemaPath = path.join(__dirname, '../database/schema.sql')
    const schema = fs.readFileSync(schemaPath, 'utf8')
    
    // Split the schema into individual statements
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`)
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';'
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`)
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        if (error) {
          console.warn(`⚠️  Warning on statement ${i + 1}:`, error.message)
        }
      } catch (err) {
        console.warn(`⚠️  Warning on statement ${i + 1}:`, err.message)
      }
    }
    
    console.log('✅ Database schema setup completed!')
    
    // Test the setup by checking if tables exist
    console.log('🔍 Verifying table creation...')
    
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['user_profiles', 'user_sessions', 'login_history'])
    
    if (tablesError) {
      console.error('❌ Error checking tables:', tablesError)
    } else {
      console.log('📊 Created tables:', tables.map(t => t.table_name))
    }
    
  } catch (error) {
    console.error('❌ Error setting up database:', error)
    process.exit(1)
  }
}

// Alternative method using direct SQL execution
async function setupDatabaseDirect() {
  try {
    console.log('🚀 Setting up RapidDoc AI database schema (direct method)...')
    
    // Create user_profiles table
    const { error: profilesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.user_profiles (
          id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
          email TEXT UNIQUE NOT NULL,
          full_name TEXT,
          avatar_url TEXT,
          user_type TEXT CHECK (user_type IN ('student', 'educator', 'researcher', 'business', 'entrepreneur', 'content_creator')),
          bio TEXT,
          organization TEXT,
          location TEXT,
          website TEXT,
          phone TEXT,
          timezone TEXT DEFAULT 'UTC',
          language TEXT DEFAULT 'en',
          subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'enterprise')),
          subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'expired', 'trial')),
          subscription_expires_at TIMESTAMPTZ,
          trial_ends_at TIMESTAMPTZ,
          documents_created INTEGER DEFAULT 0,
          documents_limit INTEGER DEFAULT 10,
          ai_generations_used INTEGER DEFAULT 0,
          ai_generations_limit INTEGER DEFAULT 50,
          storage_used_mb INTEGER DEFAULT 0,
          storage_limit_mb INTEGER DEFAULT 100,
          theme TEXT DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
          notifications_email BOOLEAN DEFAULT true,
          notifications_push BOOLEAN DEFAULT true,
          notifications_marketing BOOLEAN DEFAULT false,
          auto_save BOOLEAN DEFAULT true,
          default_document_format TEXT DEFAULT 'pdf' CHECK (default_document_format IN ('pdf', 'docx', 'txt', 'html')),
          two_factor_enabled BOOLEAN DEFAULT false,
          last_login_at TIMESTAMPTZ,
          last_login_ip INET,
          login_count INTEGER DEFAULT 0,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$')
        );
      `
    })
    
    if (profilesError) {
      console.log('ℹ️  user_profiles table may already exist:', profilesError.message)
    } else {
      console.log('✅ user_profiles table created successfully')
    }
    
    console.log('✅ Database setup completed!')
    
  } catch (error) {
    console.error('❌ Error setting up database:', error)
  }
}

// Run the setup
if (process.argv.includes('--direct')) {
  setupDatabaseDirect()
} else {
  setupDatabase()
}
