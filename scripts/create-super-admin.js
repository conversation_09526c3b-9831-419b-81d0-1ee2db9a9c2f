#!/usr/bin/env node

/**
 * Create Super Admin Script
 * Creates the first super admin user for the admin system
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Please ensure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file');
  process.exit(1);
}

// Create admin client with service role key
const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createSuperAdmin(email) {
  if (!email) {
    console.error('❌ Please provide an email address');
    console.error('Usage: node scripts/create-super-admin.js <email>');
    process.exit(1);
  }

  try {
    console.log(`🔧 Creating super admin for ${email}...`);

    // First, check if the user exists
    const { data: existingUser, error: checkError } = await supabase
      .from('user_profiles')
      .select('id, email, full_name, is_admin, admin_role')
      .eq('email', email)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('❌ Error checking user:', checkError.message);
      process.exit(1);
    }

    if (!existingUser) {
      console.error(`❌ No user found with email: ${email}`);
      console.error('Please make sure the user has signed up first.');
      console.error('');
      console.error('Steps to create a user:');
      console.error('1. Go to your application and sign up with this email');
      console.error('2. Or create the user manually in Supabase Auth');
      process.exit(1);
    }

    // Check if user is already an admin
    if (existingUser.is_admin) {
      console.log(`ℹ️  User ${email} is already an admin with role: ${existingUser.admin_role}`);
      
      if (existingUser.admin_role === 'super_admin') {
        console.log('✅ User is already a super admin. No changes needed.');
        return;
      } else {
        console.log('🔄 Upgrading user to super_admin...');
      }
    }

    // Update user to super admin
    const { data, error } = await supabase
      .from('user_profiles')
      .update({
        is_admin: true,
        admin_role: 'super_admin',
        admin_granted_at: new Date().toISOString()
      })
      .eq('email', email)
      .select();

    if (error) {
      console.error('❌ Failed to create super admin:', error.message);
      process.exit(1);
    }

    if (!data || data.length === 0) {
      console.error(`❌ Failed to update user: ${email}`);
      process.exit(1);
    }

    console.log(`✅ Successfully created super admin for ${email}`);
    console.log('');
    console.log('📋 Admin Details:');
    console.log(`   Email: ${data[0].email}`);
    console.log(`   Name: ${data[0].full_name || 'Not set'}`);
    console.log(`   Role: ${data[0].admin_role}`);
    console.log(`   Granted: ${new Date(data[0].admin_granted_at).toLocaleString()}`);
    console.log('');
    console.log('🎉 The user can now access admin routes:');
    console.log('   - /admin (Admin Dashboard)');
    console.log('   - /admin/templates (Template Manager)');
    console.log('');
    console.log('💡 Next steps:');
    console.log('1. Have the user sign in to your application');
    console.log('2. They can now access admin functionality');
    console.log('3. They can grant admin roles to other users from the admin dashboard');

  } catch (error) {
    console.error('❌ Error creating super admin:', error.message);
    process.exit(1);
  }
}

// Get email from command line arguments
const email = process.argv[2];

if (!email) {
  console.log('🔧 Create Super Admin');
  console.log('');
  console.log('This script creates the first super admin user for your admin system.');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/create-super-admin.js <email>');
  console.log('');
  console.log('Example:');
  console.log('  node scripts/create-super-admin.js <EMAIL>');
  console.log('');
  console.log('Requirements:');
  console.log('- The user must already exist (signed up through your app)');
  console.log('- SUPABASE_SERVICE_ROLE_KEY must be set in your .env file');
  process.exit(1);
}

createSuperAdmin(email);
