#!/usr/bin/env node

/**
 * Admin System Deployment Script
 * Deploys the admin role system to Supabase
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env') });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Please ensure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file');
  process.exit(1);
}

// Create admin client with service role key
const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function deployAdminSystem() {
  console.log('🚀 Starting Admin System Deployment...\n');

  try {
    // Step 1: Read and execute the migration SQL
    console.log('📄 Reading admin role migration...');
    const migrationPath = path.join(__dirname, '../database/admin-role-migration.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('🔧 Executing admin role migration...');
    console.log('📋 Please execute the following SQL in your Supabase SQL Editor:');
    console.log('=' .repeat(80));
    console.log(migrationSQL);
    console.log('=' .repeat(80));
    console.log('');
    console.log('⚠️  Manual execution required because exec_sql function is not available.');
    console.log('');
    console.log('Steps to complete deployment:');
    console.log('1. Copy the SQL above');
    console.log('2. Go to your Supabase Dashboard > SQL Editor');
    console.log('3. Paste and execute the SQL');
    console.log('4. Run this script again with --verify flag to verify deployment');
    console.log('');

    // Check if user wants to verify existing deployment
    if (process.argv.includes('--verify')) {
      console.log('🔍 Verifying existing deployment...');
    } else {
      console.log('💡 Run with --verify flag after executing SQL: node scripts/deploy-admin-system.js --verify');
      return;
    }

    console.log('✅ Admin role migration completed successfully!\n');

    // Step 2: Verify the deployment
    console.log('🔍 Verifying deployment...');
    
    // Check if admin functions exist
    const functions = [
      'is_user_admin',
      'user_has_admin_role',
      'get_current_user_admin_info',
      'log_admin_activity',
      'grant_admin_role',
      'revoke_admin_role'
    ];

    for (const funcName of functions) {
      try {
        // Try to call the function with test parameters
        const { error } = await supabase.rpc(funcName, 
          funcName === 'is_user_admin' ? { user_id: '00000000-0000-0000-0000-000000000000' } :
          funcName === 'user_has_admin_role' ? { user_id: '00000000-0000-0000-0000-000000000000', required_role: 'admin' } :
          funcName === 'get_current_user_admin_info' ? {} :
          {}
        );
        
        if (error && !error.message.includes('permission denied') && !error.message.includes('not found')) {
          console.warn(`   ⚠️  Function ${funcName} may have issues:`, error.message);
        } else {
          console.log(`   ✅ Function ${funcName} is available`);
        }
      } catch (err) {
        console.log(`   ✅ Function ${funcName} exists (test call failed as expected)`);
      }
    }

    // Check if tables exist by trying to query them
    try {
      const { error: profilesError } = await supabase
        .from('user_profiles')
        .select('is_admin')
        .limit(1);

      if (!profilesError) {
        console.log('   ✅ user_profiles table with admin fields exists');
      } else if (profilesError.message.includes('column "is_admin" does not exist')) {
        console.log('   ❌ user_profiles table missing admin fields');
      } else {
        console.log('   ✅ user_profiles table exists');
      }
    } catch (err) {
      console.log('   ❌ user_profiles table not accessible');
    }

    try {
      const { error: activityError } = await supabase
        .from('admin_activity_log')
        .select('id')
        .limit(1);

      if (!activityError) {
        console.log('   ✅ admin_activity_log table exists');
      } else {
        console.log('   ❌ admin_activity_log table does not exist');
      }
    } catch (err) {
      console.log('   ❌ admin_activity_log table not accessible');
    }

    console.log('\n✅ Deployment verification completed!\n');

    // Step 3: Provide next steps
    console.log('📋 Next Steps:');
    console.log('1. Create your first super admin user by running:');
    console.log('   npm run create-super-admin <admin-email>');
    console.log('');
    console.log('2. Or manually execute this SQL in Supabase SQL Editor:');
    console.log(`   UPDATE public.user_profiles 
   SET is_admin = true, admin_role = 'super_admin', admin_granted_at = NOW()
   WHERE email = '<EMAIL>';`);
    console.log('');
    console.log('3. Test the admin routes:');
    console.log('   - /admin (Admin Dashboard)');
    console.log('   - /admin/templates (Template Manager)');
    console.log('');
    console.log('🎉 Admin system deployment completed successfully!');

  } catch (error) {
    console.error('\n❌ Deployment failed:', error.message);
    console.error('\nPlease check your Supabase configuration and try again.');
    process.exit(1);
  }
}

// Helper function to create super admin
async function createSuperAdmin(email) {
  if (!email) {
    console.error('❌ Please provide an email address');
    console.error('Usage: npm run create-super-admin <email>');
    process.exit(1);
  }

  try {
    console.log(`🔧 Creating super admin for ${email}...`);

    const { data, error } = await supabase
      .from('user_profiles')
      .update({
        is_admin: true,
        admin_role: 'super_admin',
        admin_granted_at: new Date().toISOString()
      })
      .eq('email', email)
      .select();

    if (error) {
      console.error('❌ Failed to create super admin:', error.message);
      process.exit(1);
    }

    if (!data || data.length === 0) {
      console.error(`❌ No user found with email: ${email}`);
      console.error('Please make sure the user has signed up first.');
      process.exit(1);
    }

    console.log(`✅ Successfully created super admin for ${email}`);
    console.log('The user can now access admin routes.');

  } catch (error) {
    console.error('❌ Error creating super admin:', error.message);
    process.exit(1);
  }
}

// Check command line arguments
const command = process.argv[2];
const email = process.argv[3];

if (command === 'create-super-admin') {
  createSuperAdmin(email);
} else {
  deployAdminSystem();
}
