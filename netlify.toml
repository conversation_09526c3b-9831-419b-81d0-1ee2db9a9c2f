[build]
  command = "npm run build"
  publish = "build"

[dev]
  command = "npm run dev"
  port = 4028
  
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"
    X-Content-Type-Options = "nosniff"
    X-Frame-Options = "DENY"
    Referrer-Policy = "no-referrer-when-downgrade"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(), payment=()"
    X-XSS-Protection = "0"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https://images.unsplash.com https://source.unsplash.com; font-src 'self' https://fonts.gstatic.com data:; connect-src 'self' https://*.supabase.co https://api.openai.com https://generativelanguage.googleapis.com https://api.replicate.com https://api.unsplash.com https://www.google-analytics.com; worker-src 'self' blob:; frame-ancestors 'none'; frame-src 'self'; object-src 'none'; base-uri 'self'"

# Long-cache immutable assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Service worker should not be aggressively cached to allow updates
[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "no-cache"
