-- Database Verification Script for Logo Upload Implementation
-- Run these queries in Supabase SQL Editor to verify setup

-- 1. Check if user_logos table exists
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'user_logos' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Check if logo_overlays column was added to cover_templates
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'cover_templates' AND column_name = 'logo_overlays';

-- 3. Check storage bucket
SELECT id, name, public 
FROM storage.buckets 
WHERE id = 'user-logos';

-- 4. Check RLS policies for user_logos
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename = 'user_logos';

-- 5. Check if trigger functions exist
SELECT routine_name, routine_type
FROM information_schema.routines
WHERE routine_name IN (
  'update_user_logos_updated_at',
  'ensure_single_default_logo',
  'update_logo_storage_usage',
  'increment_logo_usage'
);

-- 6. Test basic user_logos table access (should return empty result, not error)
SELECT COUNT(*) as logo_count FROM user_logos;

-- 7. Check user_profiles has logo-related columns
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
AND column_name IN ('default_logo_id', 'logo_storage_used_mb');
