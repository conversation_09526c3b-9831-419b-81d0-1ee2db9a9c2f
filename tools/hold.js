#!/usr/bin/env node

/**
 * Simplified Template Creator for debugging
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { createClient } from "@supabase/supabase-js";
import sizeOf from "image-size";
import dotenv from "dotenv";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, "../.env") });

console.log("🚀 Simple Template Creator Starting...");

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY
);

// Default text overlay configuration
const DEFAULT_TEXT_OVERLAYS = {
  overlays: [
    {
      id: "title",
      type: "text",
      placeholder: "{{title}}",
      position: { x: 50, y: 200, width: 400, height: 100 },
      styling: {
        fontSize: 40,
        fontFamily: "Arial",
        fontWeight: "bold",
        color: "#000000",
        textAlign: "center",
        lineHeight: 1.2,
        maxLines: 2,
        overflow: "ellipsis",
        verticalAlign: "center",
      },
    },
    {
      id: "author",
      type: "text",
      placeholder: "With {{author}}",
      position: { x: 50, y: 320, width: 400, height: 50 },
      styling: {
        fontSize: 22,
        fontFamily: "Arial",
        fontWeight: "normal",
        color: "#000",
        textAlign: "center",
        lineHeight: 1.2,
        maxLines: 1,
        overflow: "ellipsis",
        verticalAlign: "center",
      },
    },
    {
      id: "description",
      type: "text",
      placeholder: "{{description}}",
      position: { x: 50, y: 370, width: 400, height: 80 },
      styling: {
        fontSize: 18,
        fontFamily: "Arial",
        fontWeight: "normal",
        color: "#000",
        textAlign: "center",
        lineHeight: 1.2,
        maxLines: 3,
        overflow: "ellipsis",
        verticalAlign: "center",
      },
    },
  ],
};

async function createSimpleTemplate() {
  try {
    console.log("📋 Creating template with hardcoded values...");

    const imagePath = "./cover/health-food.png";
    const name = "healthy eating template";
    const category = "health";
    const description = "health";
    const tags = ["health", "food"];

    // Check if image exists
    console.log("📁 Checking image file...");
    if (!fs.existsSync(imagePath)) {
      throw new Error(`Image file not found: ${imagePath}`);
    }
    console.log("✅ Image file found");

    // Get image dimensions
    console.log("📏 Getting image dimensions...");
    const buffer = fs.readFileSync(imagePath);
    const dimensions = sizeOf(buffer);
    console.log(
      `✅ Image dimensions: ${dimensions.width}x${dimensions.height}`
    );

    // Generate template ID
    const templateId = `health-${Date.now().toString(36)}`;
    console.log(`🆔 Generated template ID: ${templateId}`);

    // Upload image to storage
    console.log("📤 Uploading image to Supabase storage...");
    const fileName = `${templateId}.png`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from("template-backgrounds")
      .upload(fileName, buffer, {
        contentType: "image/png",
        upsert: true,
      });

    if (uploadError) {
      console.error("❌ Upload error:", uploadError);
      throw uploadError;
    }

    console.log("✅ Image uploaded successfully");

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from("template-backgrounds").getPublicUrl(fileName);

    console.log(`🔗 Public URL: ${publicUrl}`);

    // Create template data
    const templateData = {
      id: templateId,
      name,
      description,
      category,
      tags,
      background_image_url: publicUrl,
      background_image_width: dimensions.width,
      background_image_height: dimensions.height,
      text_overlays: DEFAULT_TEXT_OVERLAYS,
      supported_formats: ["pdf", "png", "jpg"],
      is_premium: true,
      status: "active",
    };

    console.log("💾 Inserting template into database...");

    // Insert into database
    const { data: createdTemplate, error: dbError } = await supabase
      .from("cover_templates")
      .insert([templateData])
      .select()
      .single();

    if (dbError) {
      console.error("❌ Database error:", dbError);
      throw dbError;
    }

    console.log("\n🎉 Template created successfully!");
    console.log(`   ID: ${createdTemplate.id}`);
    console.log(`   Name: ${createdTemplate.name}`);
    console.log(`   Category: ${createdTemplate.category}`);
    console.log(`   Background: ${createdTemplate.background_image_url}`);

    return createdTemplate;
  } catch (error) {
    console.error("\n❌ Failed to create template:", error.message);
    console.error("Full error:", error);
    process.exit(1);
  }
}

// Run the function
createSimpleTemplate();
