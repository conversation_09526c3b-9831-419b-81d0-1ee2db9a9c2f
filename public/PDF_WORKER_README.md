# PDF.js Worker File

This file is a local copy of the PDF.js worker to avoid CORS issues with CDN loading.

**Why this file is needed:**
- Eliminates dependency on external CDN
- Prevents CORS/network errors 
- Improves PDF processing reliability
- Faster loading (local vs remote)

**Maintenance:**
- File is automatically copied from node_modules during development
- Should be updated when pdfjs-dist package is updated
- Size: ~1MB (acceptable for core functionality)

**Technical Details:**
- Original: node_modules/pdfjs-dist/build/pdf.worker.min.mjs
- Copied as: public/pdf.worker.min.js (for browser compatibility)
