// Debug Component Test
// Add this temporarily to your ProfileSection.jsx to test BrandingSection loading

import React from 'react';

// Test component to verify BrandingSection can load
const BrandingSectionTest = () => {
  console.log('🧪 BrandingSectionTest: Component is rendering');
  
  try {
    // Test imports
    console.log('🧪 Testing imports...');
    
    // Test auth context
    const { user } = useAuth();
    console.log('🧪 Auth context:', { userId: user?.id, userEmail: user?.email });
    
    return (
      <div className="p-4 border border-blue-500 rounded bg-blue-50">
        <h3 className="text-blue-800 font-bold">🧪 BrandingSection Debug Test</h3>
        <p className="text-blue-600">If you see this, the component is loading successfully!</p>
        <p className="text-sm text-blue-500">User ID: {user?.id || 'Not found'}</p>
      </div>
    );
  } catch (error) {
    console.error('🧪 BrandingSectionTest Error:', error);
    return (
      <div className="p-4 border border-red-500 rounded bg-red-50">
        <h3 className="text-red-800 font-bold">❌ BrandingSection Error</h3>
        <p className="text-red-600">Error: {error.message}</p>
      </div>
    );
  }
};

// Replace <BrandingSection /> with <BrandingSectionTest /> temporarily
// in ProfileSection.jsx to test basic loading
