import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Starting create-checkout-session function...')

    // Check environment variables
    const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY')
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    console.log('Environment check:', {
      hasStripeKey: !!stripeSecretKey,
      hasSupabaseUrl: !!supabaseUrl,
      hasServiceKey: !!supabaseServiceKey
    })

    if (!stripeSecretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is not set')
    }

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Supabase environment variables are not set')
    }

    // Initialize Stripe
    const stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2023-10-16',
    })

    // Initialize Supabase client
    const supabaseClient = createClient(supabaseUrl, supabaseServiceKey)

    // Parse request body with error handling
    let requestBody
    try {
      requestBody = await req.json()
    } catch (error) {
      console.error('Failed to parse request body:', error)
      return new Response(
        JSON.stringify({ error: 'Invalid JSON in request body' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const { priceId, userId, userEmail, tier, billingPeriod, successUrl, cancelUrl } = requestBody

    console.log('Request parameters:', {
      priceId,
      userId,
      userEmail,
      tier,
      billingPeriod,
      hasSuccessUrl: !!successUrl,
      hasCancelUrl: !!cancelUrl
    })

    // Validate required parameters
    if (!priceId || !userId || !userEmail) {
      console.error('Missing required parameters:', { priceId: !!priceId, userId: !!userId, userEmail: !!userEmail })
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: priceId, userId, and userEmail are required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get or create Stripe customer
    let customer
    console.log('Fetching user profile for userId:', userId)

    const { data: profile, error: profileError } = await supabaseClient
      .from('user_profiles')
      .select('stripe_customer_id, full_name')
      .eq('id', userId)
      .single()

    if (profileError) {
      console.error('Error fetching user profile:', profileError)
      return new Response(
        JSON.stringify({ error: 'User profile not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('User profile found:', { hasStripeCustomerId: !!profile?.stripe_customer_id, fullName: profile?.full_name })

    if (profile?.stripe_customer_id) {
      try {
        // Retrieve existing customer
        console.log('Retrieving existing Stripe customer:', profile.stripe_customer_id)
        customer = await stripe.customers.retrieve(profile.stripe_customer_id)
        console.log('Existing customer retrieved successfully')
      } catch (stripeError) {
        console.error('Error retrieving Stripe customer:', stripeError)
        // If customer doesn't exist in Stripe, create a new one
        customer = await stripe.customers.create({
          email: userEmail,
          name: profile?.full_name || userEmail.split('@')[0],
          metadata: {
            userId: userId,
          },
        })
        console.log('New customer created after retrieval failed:', customer.id)

        // Update user profile with new Stripe customer ID
        await supabaseClient
          .from('user_profiles')
          .update({ stripe_customer_id: customer.id })
          .eq('id', userId)
      }
    } else {
      // Create new customer
      console.log('Creating new Stripe customer for user:', userId)
      customer = await stripe.customers.create({
        email: userEmail,
        name: profile?.full_name || userEmail.split('@')[0],
        metadata: {
          userId: userId,
        },
      })
      console.log('New customer created:', customer.id)

      // Update user profile with Stripe customer ID
      const { error: updateError } = await supabaseClient
        .from('user_profiles')
        .update({ stripe_customer_id: customer.id })
        .eq('id', userId)

      if (updateError) {
        console.error('Error updating user profile with Stripe customer ID:', updateError)
        // Continue anyway, as the customer was created successfully
      }
    }

    // Create checkout session
    console.log('Creating checkout session with parameters:', {
      customerId: customer.id,
      priceId,
      tier,
      billingPeriod,
      hasSuccessUrl: !!successUrl,
      hasCancelUrl: !!cancelUrl
    })

    try {
      const session = await stripe.checkout.sessions.create({
        customer: customer.id,
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: successUrl || `${Deno.env.get('SUPABASE_URL')}/account-settings?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: cancelUrl || `${Deno.env.get('SUPABASE_URL')}/pricing`,
        metadata: {
          userId: userId,
          tier: tier,
          billingPeriod: billingPeriod,
        },
        subscription_data: {
          metadata: {
            userId: userId,
            tier: tier,
            billingPeriod: billingPeriod,
          },
          trial_period_days: 7, // 7-day free trial
        },
        allow_promotion_codes: true,
      })

      console.log('Checkout session created successfully:', session.id)

      return new Response(
        JSON.stringify({ sessionId: session.id, url: session.url }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    } catch (stripeSessionError) {
      console.error('Stripe checkout session creation failed:', stripeSessionError)
      return new Response(
        JSON.stringify({
          error: 'Failed to create checkout session',
          details: stripeSessionError.message
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

  } catch (error) {
    console.error('Error in create-checkout-session function:', error)
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
