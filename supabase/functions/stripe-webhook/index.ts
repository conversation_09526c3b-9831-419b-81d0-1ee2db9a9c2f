import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

const cryptoProvider = Stripe.createSubtleCryptoProvider()

serve(async (req) => {
  const signature = req.headers.get('Stripe-Signature')
  const body = await req.text()
  
  let event: Stripe.Event

  try {
    event = await stripe.webhooks.constructEventAsync(
      body,
      signature!,
      Deno.env.get('STRIPE_WEBHOOK_SECRET')!,
      undefined,
      cryptoProvider
    )
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message)
    return new Response(`Webhook Error: ${err.message}`, { status: 400 })
  }

  console.log('Processing webhook event:', event.type)

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break
      
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription)
        break
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break
      
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
        break
      
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice)
        break
      
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return new Response(
      JSON.stringify({ error: 'Webhook processing failed' }),
      { status: 500 }
    )
  }
})

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  const userId = session.metadata?.userId
  if (!userId) {
    console.error('No userId in session metadata')
    return
  }

  const tier = session.metadata?.tier
  const subscriptionId = session.subscription as string
  const customerId = session.customer as string

  // Update user profile with subscription info
  await supabaseClient.rpc('update_subscription_tier', {
    user_uuid: userId,
    new_tier: tier,
    new_status: 'trial',
    stripe_customer_id_param: customerId,
    stripe_subscription_id_param: subscriptionId
  })

  console.log(`Checkout completed for user ${userId}, tier: ${tier}, customer: ${customerId}`)
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  const userId = subscription.metadata?.userId
  if (!userId) {
    console.error('No userId in subscription metadata')
    return
  }

  const tier = subscription.metadata?.tier
  const status = subscription.status === 'trialing' ? 'trial' : 'active'

  // Use the update_subscription_tier function to ensure tier is properly set
  if (tier) {
    await supabaseClient.rpc('update_subscription_tier', {
      user_uuid: userId,
      new_tier: tier,
      new_status: status,
      stripe_subscription_id_param: subscription.id
    })
  } else {
    // Fallback: update only status and dates if no tier in metadata
    await supabaseClient
      .from('user_profiles')
      .update({
        subscription_status: status,
        current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
        current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
        trial_ends_at: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
        stripe_subscription_id: subscription.id,
      })
      .eq('id', userId)
  }

  console.log(`Subscription created for user ${userId}, tier: ${tier}`)
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  const userId = subscription.metadata?.userId
  if (!userId) {
    console.error('No userId in subscription metadata')
    return
  }

  let status = 'active'
  if (subscription.status === 'trialing') status = 'trial'
  else if (subscription.status === 'past_due') status = 'past_due'
  else if (subscription.status === 'canceled') status = 'cancelled'
  else if (subscription.status === 'incomplete') status = 'incomplete'

  const tier = subscription.metadata?.tier

  // If tier is available in metadata, use the full update function
  if (tier) {
    await supabaseClient.rpc('update_subscription_tier', {
      user_uuid: userId,
      new_tier: tier,
      new_status: status,
      stripe_subscription_id_param: subscription.id
    })
  } else {
    // Otherwise, just update status and period information
    await supabaseClient
      .from('user_profiles')
      .update({
        subscription_status: status,
        current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
        current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
        cancel_at_period_end: subscription.cancel_at_period_end,
        stripe_subscription_id: subscription.id,
      })
      .eq('id', userId)
  }

  console.log(`Subscription updated for user ${userId}, status: ${status}, tier: ${tier || 'unchanged'}`)
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  const userId = subscription.metadata?.userId
  if (!userId) {
    console.error('No userId in subscription metadata')
    return
  }

  await supabaseClient.rpc('update_subscription_tier', {
    user_uuid: userId,
    new_tier: 'free',
    new_status: 'expired'
  })

  console.log(`Subscription deleted for user ${userId}`)
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  const customerId = invoice.customer as string
  
  // Get user by customer ID
  const { data: profile } = await supabaseClient
    .from('user_profiles')
    .select('id')
    .eq('stripe_customer_id', customerId)
    .single()

  if (!profile) {
    console.error('No user found for customer:', customerId)
    return
  }

  // Record billing history
  await supabaseClient
    .from('billing_history')
    .insert({
      user_id: profile.id,
      stripe_invoice_id: invoice.id,
      stripe_payment_intent_id: invoice.payment_intent as string,
      amount_paid: invoice.amount_paid,
      currency: invoice.currency,
      status: 'paid',
      billing_reason: invoice.billing_reason,
      invoice_pdf_url: invoice.invoice_pdf,
      period_start: new Date(invoice.period_start * 1000).toISOString(),
      period_end: new Date(invoice.period_end * 1000).toISOString(),
    })

  // Reset usage counters for new billing period
  await supabaseClient.rpc('reset_usage_counters', {
    user_uuid: profile.id
  })

  console.log(`Payment succeeded for user ${profile.id}`)
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  const customerId = invoice.customer as string
  
  const { data: profile } = await supabaseClient
    .from('user_profiles')
    .select('id')
    .eq('stripe_customer_id', customerId)
    .single()

  if (!profile) {
    console.error('No user found for customer:', customerId)
    return
  }

  // Update subscription status to past_due
  await supabaseClient
    .from('user_profiles')
    .update({ subscription_status: 'past_due' })
    .eq('id', profile.id)

  console.log(`Payment failed for user ${profile.id}`)
}
