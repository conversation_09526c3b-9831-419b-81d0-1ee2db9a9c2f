export default {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '^components/(.*)$': '<rootDir>/src/components/$1',
    '^pages/(.*)$': '<rootDir>/src/pages/$1',
    '^utils/(.*)$': '<rootDir>/src/utils/$1',
  },
  transform: {
    '^.+\\.(js|jsx)$': 'babel-jest',
  },
  transformIgnorePatterns: [
    'node_modules/(?!(unified|remark-parse|remark-gfm|remark-rehype|rehype-stringify|rehype-parse|rehype-remark|remark-stringify|micromark|mdast-util-from-markdown|mdast-util-to-markdown|hast-util-to-mdast|mdast-util-to-hast|unist-util-visit|unist-util-is|unist-util-stringify-position|bail|trough|vfile|vfile-message|zwitch|longest-streak|markdown-table|mdast-util-find-and-replace|escape-string-regexp|mdast-util-to-string|unist-util-position|ccount|mdast-util-compact|trim-lines|mdast-util-definitions|unist-util-generated|mdast-util-heading-style|mdast-util-phrasing|mdast-util-list-item-indent|repeat-string|mdast-util-math|mdast-util-frontmatter|mdast-util-directive|mdast-util-gfm|mdast-util-gfm-table|mdast-util-gfm-task-list-item|mdast-util-gfm-strikethrough|mdast-util-gfm-footnote|mdast-util-gfm-autolink-literal|hast-util-from-parse5|hast-util-parse-selector|hast-util-raw|hast-util-to-parse5|hast-util-whitespace|hastscript|html-void-elements|property-information|space-separated-tokens|comma-separated-tokens|web-namespaces)/)'
  ],
  moduleFileExtensions: ['js', 'jsx', 'json'],
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx}'
  ],
  collectCoverageFrom: [
    'src/**/*.{js,jsx}',
    '!src/index.js',
    '!src/setupTests.js',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
};
