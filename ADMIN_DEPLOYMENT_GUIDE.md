# Admin System Deployment Guide

## Quick Start

Follow these steps to deploy the admin security system:

### Step 1: Deploy Database Schema

**Option A: Using Supabase SQL Editor (Recommended)**
1. Copy the contents of `database/deploy-admin-system.sql`
2. Go to your Supabase Dashboard → SQL Editor
3. Paste and execute the SQL
4. You should see success messages in the output

**Option B: Using the deployment script**
```bash
npm run admin:deploy
```
This will show you the SQL to execute manually in Supabase.

### Step 2: Create Your First Super Admin

```bash
npm run admin:create-super <<EMAIL>>
```

**Requirements:**
- The user must already exist (signed up through your app)
- You need `SUPABASE_SERVICE_ROLE_KEY` in your `.env` file

### Step 3: Test Admin Access

1. Sign in to your application with the super admin email
2. Navigate to `/admin` - you should see the Admin Dashboard
3. Navigate to `/admin/templates` - you should see the Template Manager

## Environment Variables Required

Make sure these are set in your `.env` file:

```env
VITE_SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

## Admin Role Hierarchy

- **Super Admin**: Can manage other admins, full system access
- **Admin**: Can manage templates and content, view activity logs
- **Moderator**: Basic admin functions, read-only access to logs

## Available Routes

- `/admin` - Admin Dashboard (requires `moderator` role or higher)
- `/admin/templates` - Template Manager (requires `admin` role or higher)

## Troubleshooting

### "No user found with email"
The user must sign up through your application first before being granted admin privileges.

### "Permission denied" errors
Make sure you're using the `SUPABASE_SERVICE_ROLE_KEY`, not the anon key.

### "Function does not exist" errors
The database migration wasn't executed properly. Re-run the SQL in Supabase SQL Editor.

### Admin routes show "Access Denied"
1. Check if the user has admin privileges: `SELECT is_admin, admin_role FROM user_profiles WHERE email = 'your-email';`
2. Verify the admin functions exist: `SELECT * FROM pg_proc WHERE proname LIKE '%admin%';`

## Manual Admin Creation (Alternative)

If the script doesn't work, you can manually create a super admin:

```sql
UPDATE public.user_profiles 
SET 
    is_admin = true, 
    admin_role = 'super_admin', 
    admin_granted_at = NOW()
WHERE email = '<EMAIL>';
```

## Verification Commands

Check if deployment was successful:

```bash
npm run admin:verify
```

Or manually verify in Supabase SQL Editor:

```sql
-- Check if admin columns exist
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'user_profiles' AND column_name LIKE '%admin%';

-- Check if admin functions exist
SELECT proname FROM pg_proc WHERE proname LIKE '%admin%';

-- Check if admin activity log table exists
SELECT * FROM admin_activity_log LIMIT 1;
```

## Security Features Included

✅ **Multi-layer Authentication**: User auth + admin role + session validation  
✅ **Role-based Access Control**: Hierarchical admin roles with proper permissions  
✅ **Activity Monitoring**: All admin actions are logged with full context  
✅ **Rate Limiting**: Protection against abuse and automated attacks  
✅ **Session Security**: Enhanced monitoring and automatic expiration  
✅ **Suspicious Activity Detection**: Real-time monitoring for unusual patterns  
✅ **Database Security**: Row Level Security and privilege escalation prevention  

## Next Steps

After deployment:

1. **Test the system** - Verify all admin routes work correctly
2. **Create additional admins** - Use the admin dashboard to grant roles to other users
3. **Monitor activity** - Check the activity logs regularly for security
4. **Review permissions** - Ensure users have appropriate role levels

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Verify your environment variables are correct
3. Ensure the database migration completed successfully
4. Review the admin activity logs for error details

For technical support, refer to the detailed documentation in `docs/ADMIN_SECURITY_IMPLEMENTATION.md`.
