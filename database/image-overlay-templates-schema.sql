-- Image Overlay Template System - Clean Database Schema
-- Complete replacement of JSON-based templates with Image Overlay Templates

-- Drop existing cover_templates table if it exists (clean slate)
DROP TABLE IF EXISTS public.cover_templates CASCADE;

-- Create the new cover_templates table for Image Overlay Templates ONLY
CREATE TABLE public.cover_templates (
    id TEXT PRIMARY KEY,

    -- Template metadata
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    tags TEXT[] DEFAULT '{}',

    -- Image Overlay Template Properties (REQUIRED)
    background_image_url TEXT NOT NULL, -- URL to background image in Supabase Storage
    background_image_width INTEGER NOT NULL, -- Original image width
    background_image_height INTEGER NOT NULL, -- Original image height
    text_overlays JSONB NOT NULL, -- Text overlay configurations

    -- Visual assets
    thumbnail_url TEXT, -- Generated thumbnail of the template
    preview_url TEXT, -- Generated preview with sample text

    -- Template properties
    supported_formats TEXT[] DEFAULT '{"pdf", "png", "jpg"}',

    -- Usage and popularity
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.0,

    -- Status and availability
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
    is_premium BOOLEAN DEFAULT false,

    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Text Overlay Configuration Schema (stored in text_overlays JSONB field)
-- Example structure:
-- {
--   "overlays": [
--     {
--       "id": "title",
--       "type": "text",
--       "placeholder": "{{title}}",
--       "position": {
--         "x": 100,
--         "y": 200,
--         "width": 400,
--         "height": 60
--       },
--       "styling": {
--         "fontSize": 32,
--         "fontFamily": "Arial",
--         "fontWeight": "bold",
--         "color": "#000000",
--         "textAlign": "center",
--         "lineHeight": 1.2,
--         "maxLines": 2,
--         "overflow": "ellipsis"
--       }
--     },
--     {
--       "id": "author",
--       "type": "text", 
--       "placeholder": "by {{author}}",
--       "position": {
--         "x": 100,
--         "y": 280,
--         "width": 400,
--         "height": 30
--       },
--       "styling": {
--         "fontSize": 18,
--         "fontFamily": "Arial",
--         "fontWeight": "normal",
--         "color": "#666666",
--         "textAlign": "center"
--       }
--     },
--     {
--       "id": "description",
--       "type": "text",
--       "placeholder": "{{description}}",
--       "position": {
--         "x": 100,
--         "y": 350,
--         "width": 400,
--         "height": 100
--       },
--       "styling": {
--         "fontSize": 14,
--         "fontFamily": "Arial",
--         "fontWeight": "normal",
--         "color": "#333333",
--         "textAlign": "left",
--         "lineHeight": 1.4,
--         "maxLines": 5,
--         "overflow": "ellipsis"
--       }
--     }
--   ]
-- }

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_cover_templates_category ON public.cover_templates(category);
CREATE INDEX IF NOT EXISTS idx_cover_templates_status ON public.cover_templates(status);
CREATE INDEX IF NOT EXISTS idx_cover_templates_tags ON public.cover_templates USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_cover_templates_usage ON public.cover_templates(usage_count DESC);

-- Create storage bucket for template background images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('template-backgrounds', 'template-backgrounds', true)
ON CONFLICT (id) DO NOTHING;

-- Set up storage policies for template background images
CREATE POLICY "Public read access for template backgrounds" ON storage.objects
FOR SELECT USING (bucket_id = 'template-backgrounds');

CREATE POLICY "Authenticated users can upload template backgrounds" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'template-backgrounds' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update template backgrounds" ON storage.objects
FOR UPDATE USING (bucket_id = 'template-backgrounds' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete template backgrounds" ON storage.objects
FOR DELETE USING (bucket_id = 'template-backgrounds' AND auth.role() = 'authenticated');

-- Enable RLS on cover_templates table
ALTER TABLE public.cover_templates ENABLE ROW LEVEL SECURITY;

-- Create policies for cover_templates
CREATE POLICY "Public read access for active templates" ON public.cover_templates
FOR SELECT USING (status = 'active');

CREATE POLICY "Authenticated users can manage templates" ON public.cover_templates
FOR ALL USING (auth.role() = 'authenticated');

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_cover_templates_updated_at 
    BEFORE UPDATE ON public.cover_templates 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
