-- Admin System Deployment SQL
-- Execute this entire script in your Supabase SQL Editor

-- Step 1: Add admin role fields to user_profiles table
DO $$ 
BEGIN
    -- Add admin columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'is_admin') THEN
        ALTER TABLE public.user_profiles ADD COLUMN is_admin BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'admin_role') THEN
        ALTER TABLE public.user_profiles ADD COLUMN admin_role TEXT CHECK (admin_role IN ('super_admin', 'admin', 'moderator'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'admin_granted_at') THEN
        ALTER TABLE public.user_profiles ADD COLUMN admin_granted_at TIMESTAMPTZ;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'admin_granted_by') THEN
        ALTER TABLE public.user_profiles ADD COLUMN admin_granted_by UUID REFERENCES public.user_profiles(id);
    END IF;
END $$;

-- Step 2: Create admin activity logging table
CREATE TABLE IF NOT EXISTS public.admin_activity_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    resource_type TEXT,
    resource_id TEXT,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 3: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_is_admin ON public.user_profiles(is_admin) WHERE is_admin = true;
CREATE INDEX IF NOT EXISTS idx_user_profiles_admin_role ON public.user_profiles(admin_role) WHERE admin_role IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_admin_user_id ON public.admin_activity_log(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_created_at ON public.admin_activity_log(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_action ON public.admin_activity_log(action);

-- Step 4: Create admin authorization functions

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION public.is_user_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.user_profiles
        WHERE id = user_id AND is_admin = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has specific admin role
CREATE OR REPLACE FUNCTION public.user_has_admin_role(user_id UUID, required_role TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    role_hierarchy INTEGER;
    required_hierarchy INTEGER;
BEGIN
    -- Get user's admin role
    SELECT admin_role INTO user_role
    FROM public.user_profiles
    WHERE id = user_id AND is_admin = true;
    
    -- If user is not admin or has no role, return false
    IF user_role IS NULL THEN
        RETURN false;
    END IF;
    
    -- Define role hierarchy (higher number = more permissions)
    role_hierarchy := CASE user_role
        WHEN 'super_admin' THEN 3
        WHEN 'admin' THEN 2
        WHEN 'moderator' THEN 1
        ELSE 0
    END;
    
    required_hierarchy := CASE required_role
        WHEN 'super_admin' THEN 3
        WHEN 'admin' THEN 2
        WHEN 'moderator' THEN 1
        ELSE 0
    END;
    
    -- User has required role if their hierarchy level is >= required level
    RETURN role_hierarchy >= required_hierarchy;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's admin info
CREATE OR REPLACE FUNCTION public.get_current_user_admin_info()
RETURNS TABLE(
    is_admin BOOLEAN,
    admin_role TEXT,
    admin_granted_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        up.is_admin,
        up.admin_role,
        up.admin_granted_at
    FROM public.user_profiles up
    WHERE up.id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log admin activity
CREATE OR REPLACE FUNCTION public.log_admin_activity(
    p_action TEXT,
    p_resource_type TEXT DEFAULT NULL,
    p_resource_id TEXT DEFAULT NULL,
    p_details JSONB DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_session_id TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    activity_id UUID;
    current_user_id UUID;
BEGIN
    -- Get current user ID
    current_user_id := auth.uid();
    
    -- Verify user is admin before logging
    IF NOT public.is_user_admin(current_user_id) THEN
        RAISE EXCEPTION 'Access denied: User is not an admin';
    END IF;
    
    -- Insert activity log
    INSERT INTO public.admin_activity_log (
        admin_user_id,
        action,
        resource_type,
        resource_id,
        details,
        ip_address,
        user_agent,
        session_id
    ) VALUES (
        current_user_id,
        p_action,
        p_resource_type,
        p_resource_id,
        p_details,
        p_ip_address,
        p_user_agent,
        p_session_id
    ) RETURNING id INTO activity_id;
    
    RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to grant admin role (only super_admin can grant roles)
CREATE OR REPLACE FUNCTION public.grant_admin_role(
    target_user_id UUID,
    role_to_grant TEXT,
    granted_by_user_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    granting_user_id UUID;
BEGIN
    -- Use provided user ID or current user
    granting_user_id := COALESCE(granted_by_user_id, auth.uid());
    
    -- Verify granting user is super_admin
    IF NOT public.user_has_admin_role(granting_user_id, 'super_admin') THEN
        RAISE EXCEPTION 'Access denied: Only super_admin can grant admin roles';
    END IF;
    
    -- Validate role
    IF role_to_grant NOT IN ('super_admin', 'admin', 'moderator') THEN
        RAISE EXCEPTION 'Invalid admin role: %', role_to_grant;
    END IF;
    
    -- Update user profile
    UPDATE public.user_profiles
    SET 
        is_admin = true,
        admin_role = role_to_grant,
        admin_granted_at = NOW(),
        admin_granted_by = granting_user_id,
        updated_at = NOW()
    WHERE id = target_user_id;
    
    -- Log the action
    PERFORM public.log_admin_activity(
        'grant_admin_role',
        'user',
        target_user_id::TEXT,
        jsonb_build_object(
            'role_granted', role_to_grant,
            'granted_by', granting_user_id
        )
    );
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to revoke admin role
CREATE OR REPLACE FUNCTION public.revoke_admin_role(
    target_user_id UUID,
    revoked_by_user_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    revoking_user_id UUID;
    target_role TEXT;
BEGIN
    -- Use provided user ID or current user
    revoking_user_id := COALESCE(revoked_by_user_id, auth.uid());
    
    -- Get target user's current role
    SELECT admin_role INTO target_role
    FROM public.user_profiles
    WHERE id = target_user_id;
    
    -- Verify revoking user is super_admin
    IF NOT public.user_has_admin_role(revoking_user_id, 'super_admin') THEN
        RAISE EXCEPTION 'Access denied: Only super_admin can revoke admin roles';
    END IF;
    
    -- Prevent self-revocation of super_admin
    IF revoking_user_id = target_user_id AND target_role = 'super_admin' THEN
        RAISE EXCEPTION 'Cannot revoke your own super_admin role';
    END IF;
    
    -- Update user profile
    UPDATE public.user_profiles
    SET 
        is_admin = false,
        admin_role = NULL,
        admin_granted_at = NULL,
        admin_granted_by = NULL,
        updated_at = NOW()
    WHERE id = target_user_id;
    
    -- Log the action
    PERFORM public.log_admin_activity(
        'revoke_admin_role',
        'user',
        target_user_id::TEXT,
        jsonb_build_object(
            'role_revoked', target_role,
            'revoked_by', revoking_user_id
        )
    );
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 5: Enable RLS on admin_activity_log
ALTER TABLE public.admin_activity_log ENABLE ROW LEVEL SECURITY;

-- Step 6: Create RLS policies for admin_activity_log
-- Only admins can view admin activity logs
DROP POLICY IF EXISTS "Admins can view admin activity logs" ON public.admin_activity_log;
CREATE POLICY "Admins can view admin activity logs" ON public.admin_activity_log
    FOR SELECT USING (public.is_user_admin(auth.uid()));

-- Only the system can insert admin activity logs (through functions)
DROP POLICY IF EXISTS "System can insert admin activity logs" ON public.admin_activity_log;
CREATE POLICY "System can insert admin activity logs" ON public.admin_activity_log
    FOR INSERT WITH CHECK (admin_user_id = auth.uid() AND public.is_user_admin(auth.uid()));

-- Step 7: Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.is_user_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.user_has_admin_role(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_current_user_admin_info() TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_admin_activity(TEXT, TEXT, TEXT, JSONB, INET, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.grant_admin_role(UUID, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.revoke_admin_role(UUID, UUID) TO authenticated;

-- Step 8: Success message
DO $$ 
BEGIN
    RAISE NOTICE '✅ Admin system deployment completed successfully!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 Next Steps:';
    RAISE NOTICE '1. Create your first super admin by running:';
    RAISE NOTICE '   UPDATE public.user_profiles SET is_admin = true, admin_role = ''super_admin'', admin_granted_at = NOW() WHERE email = ''<EMAIL>'';';
    RAISE NOTICE '';
    RAISE NOTICE '2. Test the admin routes:';
    RAISE NOTICE '   - /admin (Admin Dashboard)';
    RAISE NOTICE '   - /admin/templates (Template Manager)';
END $$;
