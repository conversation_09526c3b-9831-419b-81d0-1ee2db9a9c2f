-- RapidDoc AI - Database Triggers and Functions for Projects
-- Automated functions for maintaining data consistency and audit trails

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to log project activities
CREATE OR REPLACE FUNCTION public.log_project_activity()
RETURNS TRIGGER AS $$
DECLARE
    activity_type_val TEXT;
    activity_desc TEXT;
    project_id_val UUID;
BEGIN
    -- Determine activity type based on operation
    IF TG_OP = 'INSERT' THEN
        activity_type_val := 'created';
        activity_desc := 'Project created';
        project_id_val := NEW.id;
    ELSIF TG_OP = 'UPDATE' THEN
        activity_type_val := 'updated';
        activity_desc := 'Project updated';
        project_id_val := NEW.id;
    ELSIF TG_OP = 'DELETE' THEN
        activity_type_val := 'deleted';
        activity_desc := 'Project deleted';
        project_id_val := OLD.id;
    END IF;

    -- Insert activity log
    INSERT INTO public.project_activities (
        project_id,
        user_id,
        activity_type,
        activity_description,
        activity_data
    ) VALUES (
        project_id_val,
        auth.user_id(),
        activity_type_val,
        activity_desc,
        CASE 
            WHEN TG_OP = 'UPDATE' THEN 
                jsonb_build_object(
                    'old_status', OLD.status,
                    'new_status', NEW.status,
                    'old_progress', OLD.progress,
                    'new_progress', NEW.progress
                )
            ELSE NULL
        END
    );

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update project statistics
CREATE OR REPLACE FUNCTION public.update_project_stats()
RETURNS TRIGGER AS $$
DECLARE
    doc_word_count INTEGER;
    doc_chapter_count INTEGER;
BEGIN
    -- Calculate word count and chapter count from document content
    IF NEW.generated_content IS NOT NULL THEN
        -- Extract word count from generated content
        doc_word_count := COALESCE(
            (NEW.generated_content->>'wordCount')::INTEGER,
            (NEW.generated_content->'introduction'->>'wordCount')::INTEGER + 
            COALESCE(
                (SELECT SUM((chapter->>'wordCount')::INTEGER) 
                 FROM jsonb_array_elements(NEW.generated_content->'chapters') AS chapter),
                0
            ) +
            COALESCE((NEW.generated_content->'conclusion'->>'wordCount')::INTEGER, 0),
            0
        );
        
        -- Count chapters
        doc_chapter_count := COALESCE(
            jsonb_array_length(NEW.generated_content->'chapters'),
            0
        );
        
        -- Update project statistics
        UPDATE public.projects 
        SET 
            word_count = doc_word_count,
            chapter_count = doc_chapter_count,
            updated_at = NOW()
        WHERE id = NEW.project_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user document count
CREATE OR REPLACE FUNCTION public.update_user_document_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Increment document count
        UPDATE public.user_profiles 
        SET documents_created = documents_created + 1
        WHERE id = NEW.user_id;
    ELSIF TG_OP = 'DELETE' THEN
        -- Decrement document count
        UPDATE public.user_profiles 
        SET documents_created = GREATEST(documents_created - 1, 0)
        WHERE id = OLD.user_id;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle project soft delete
CREATE OR REPLACE FUNCTION public.soft_delete_project()
RETURNS TRIGGER AS $$
BEGIN
    -- Instead of hard delete, set deleted_at timestamp
    UPDATE public.projects 
    SET deleted_at = NOW()
    WHERE id = OLD.id;
    
    -- Prevent the actual delete
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to ensure only one current document version per project
CREATE OR REPLACE FUNCTION public.ensure_single_current_version()
RETURNS TRIGGER AS $$
BEGIN
    -- If this is being set as current version, unset others
    IF NEW.is_current_version = true THEN
        UPDATE public.documents 
        SET is_current_version = false
        WHERE project_id = NEW.project_id 
        AND id != NEW.id 
        AND is_current_version = true;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update share view count
CREATE OR REPLACE FUNCTION public.increment_share_views()
RETURNS TRIGGER AS $$
BEGIN
    NEW.current_views = COALESCE(OLD.current_views, 0) + 1;
    NEW.last_accessed_at = NOW();
    
    -- Check if max views exceeded
    IF NEW.max_views IS NOT NULL AND NEW.current_views > NEW.max_views THEN
        NEW.is_active = false;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for updated_at columns
CREATE TRIGGER update_projects_updated_at
    BEFORE UPDATE ON public.projects
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_documents_updated_at
    BEFORE UPDATE ON public.documents
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_collaborators_updated_at
    BEFORE UPDATE ON public.project_collaborators
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Create triggers for activity logging
CREATE TRIGGER log_project_activity_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.projects
    FOR EACH ROW
    EXECUTE FUNCTION public.log_project_activity();

-- Create trigger for project statistics
CREATE TRIGGER update_project_stats_trigger
    AFTER INSERT OR UPDATE ON public.documents
    FOR EACH ROW
    EXECUTE FUNCTION public.update_project_stats();

-- Create trigger for user document count
CREATE TRIGGER update_user_document_count_trigger
    AFTER INSERT OR DELETE ON public.projects
    FOR EACH ROW
    EXECUTE FUNCTION public.update_user_document_count();

-- Create trigger for soft delete
CREATE TRIGGER soft_delete_project_trigger
    BEFORE DELETE ON public.projects
    FOR EACH ROW
    EXECUTE FUNCTION public.soft_delete_project();

-- Create trigger for document versioning
CREATE TRIGGER ensure_single_current_version_trigger
    BEFORE INSERT OR UPDATE ON public.documents
    FOR EACH ROW
    EXECUTE FUNCTION public.ensure_single_current_version();

-- Create trigger for share view tracking
CREATE TRIGGER increment_share_views_trigger
    BEFORE UPDATE ON public.project_shares
    FOR EACH ROW
    WHEN (OLD.current_views IS DISTINCT FROM NEW.current_views)
    EXECUTE FUNCTION public.increment_share_views();
