-- RapidDoc AI Database Schema
-- This file contains the database schema for user management and authentication

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret-here';

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    user_type TEXT CHECK (user_type IN ('student', 'educator', 'researcher', 'business', 'entrepreneur', 'content_creator')),
    bio TEXT,
    organization TEXT,
    location TEXT,
    website TEXT,
    phone TEXT,
    timezone TEXT DEFAULT 'UTC',
    language TEXT DEFAULT 'en',
    
    -- Subscription and billing
    subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'basic', 'standard', 'pro')),
    subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'expired', 'trial', 'past_due', 'incomplete')),
    subscription_expires_at TIMESTAMPTZ,
    trial_ends_at TIMESTAMPTZ,
    stripe_customer_id TEXT UNIQUE,
    stripe_subscription_id TEXT UNIQUE,
    current_period_start TIMESTAMPTZ,
    current_period_end TIMESTAMPTZ,
    cancel_at_period_end BOOLEAN DEFAULT false,

    -- Billing address
    billing_address TEXT,
    billing_city TEXT,
    billing_state TEXT,
    billing_country TEXT,
    billing_postal_code TEXT,

    -- Usage tracking (current billing period)
    documents_created INTEGER DEFAULT 0,
    documents_limit INTEGER DEFAULT 10,
    ai_generations_used INTEGER DEFAULT 0,
    ai_generations_limit INTEGER DEFAULT 50,
    ai_image_generations_used INTEGER DEFAULT 0,
    ai_image_generations_limit INTEGER DEFAULT 5,
    storage_used_mb INTEGER DEFAULT 0,
    storage_limit_mb INTEGER DEFAULT 500,

    -- Usage reset tracking
    last_usage_reset TIMESTAMPTZ DEFAULT NOW(),
    
    -- Preferences (MVP - Essential only)
    theme TEXT DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    notifications_email BOOLEAN DEFAULT true,
    auto_save BOOLEAN DEFAULT true,
    
    -- Security
    two_factor_enabled BOOLEAN DEFAULT false,
    last_login_at TIMESTAMPTZ,
    last_login_ip INET,
    login_count INTEGER DEFAULT 0,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON public.user_profiles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create user_sessions table for session management
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    session_token TEXT UNIQUE NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    location TEXT,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_accessed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create login_history table for security tracking
CREATE TABLE IF NOT EXISTS public.login_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    login_method TEXT DEFAULT 'email' CHECK (login_method IN ('email', 'google', 'github')),
    ip_address INET,
    user_agent TEXT,
    location TEXT,
    device_info JSONB,
    status TEXT DEFAULT 'success' CHECK (status IN ('success', 'failed', 'blocked')),
    failure_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create subscription_plans table for tier configuration
CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    tier TEXT NOT NULL CHECK (tier IN ('basic', 'standard', 'pro')),
    price_monthly INTEGER NOT NULL, -- Price in cents
    price_yearly INTEGER NOT NULL, -- Price in cents
    stripe_price_id_monthly TEXT,
    stripe_price_id_yearly TEXT,
    stripe_product_id TEXT,

    -- Feature limits
    documents_limit INTEGER NOT NULL,
    ai_generations_limit INTEGER NOT NULL, -- -1 for unlimited
    ai_image_generations_limit INTEGER NOT NULL, -- -1 for unlimited
    storage_limit_mb INTEGER NOT NULL,
    max_file_upload_mb INTEGER NOT NULL,

    -- Feature flags
    custom_templates BOOLEAN DEFAULT false,
    priority_processing BOOLEAN DEFAULT false,
    remove_watermark BOOLEAN DEFAULT false,
    advanced_analytics BOOLEAN DEFAULT false,
    api_access BOOLEAN DEFAULT false,
    ai_model_selection BOOLEAN DEFAULT false,
    phone_support BOOLEAN DEFAULT false,

    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create subscription_history table for tracking changes
CREATE TABLE IF NOT EXISTS public.subscription_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    old_tier TEXT,
    new_tier TEXT NOT NULL,
    old_status TEXT,
    new_status TEXT NOT NULL,
    change_reason TEXT, -- 'upgrade', 'downgrade', 'trial_start', 'trial_end', 'payment_failed', etc.
    stripe_event_id TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create usage_tracking table for detailed usage analytics
CREATE TABLE IF NOT EXISTS public.usage_tracking (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    usage_type TEXT NOT NULL CHECK (usage_type IN ('document_creation', 'ai_generation', 'ai_image_generation', 'storage_upload')),
    amount INTEGER DEFAULT 1, -- Amount used (e.g., 1 document, 50MB storage)
    metadata JSONB, -- Additional context (document type, AI model used, etc.)
    billing_period_start TIMESTAMPTZ NOT NULL,
    billing_period_end TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create billing_history table for invoice tracking
CREATE TABLE IF NOT EXISTS public.billing_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    stripe_invoice_id TEXT UNIQUE,
    stripe_payment_intent_id TEXT,
    amount_paid INTEGER, -- Amount in cents
    currency TEXT DEFAULT 'usd',
    status TEXT CHECK (status IN ('paid', 'open', 'void', 'uncollectible')),
    billing_reason TEXT, -- 'subscription_cycle', 'subscription_create', etc.
    invoice_pdf_url TEXT,
    period_start TIMESTAMPTZ,
    period_end TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription_tier ON public.user_profiles(subscription_tier);
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription_status ON public.user_profiles(subscription_status);
CREATE INDEX IF NOT EXISTS idx_user_profiles_stripe_customer_id ON public.user_profiles(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_created_at ON public.user_profiles(created_at);
CREATE INDEX IF NOT EXISTS idx_login_history_user_id ON public.login_history(user_id);
CREATE INDEX IF NOT EXISTS idx_login_history_created_at ON public.login_history(created_at);

-- Indexes for new subscription tables
CREATE INDEX IF NOT EXISTS idx_subscription_plans_tier ON public.subscription_plans(tier);
CREATE INDEX IF NOT EXISTS idx_subscription_history_user_id ON public.subscription_history(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_history_created_at ON public.subscription_history(created_at);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_id ON public.usage_tracking(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_usage_type ON public.usage_tracking(usage_type);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_billing_period ON public.usage_tracking(billing_period_start, billing_period_end);
CREATE INDEX IF NOT EXISTS idx_billing_history_user_id ON public.billing_history(user_id);
CREATE INDEX IF NOT EXISTS idx_billing_history_stripe_invoice_id ON public.billing_history(stripe_invoice_id);

-- Row Level Security Policies
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.login_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.billing_history ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see and edit their own profile
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Policy: Users can only see their own sessions
CREATE POLICY "Users can view own sessions" ON public.user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own sessions" ON public.user_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sessions" ON public.user_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can only see their own login history
CREATE POLICY "Users can view own login history" ON public.login_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own login history" ON public.login_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policies for subscription_history
CREATE POLICY "Users can view own subscription history" ON public.subscription_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage subscription history" ON public.subscription_history
    FOR ALL USING (auth.role() = 'service_role');

-- Policies for usage_tracking
CREATE POLICY "Users can view own usage tracking" ON public.usage_tracking
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage usage tracking" ON public.usage_tracking
    FOR ALL USING (auth.role() = 'service_role');

-- Policies for billing_history
CREATE POLICY "Users can view own billing history" ON public.billing_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage billing history" ON public.billing_history
    FOR ALL USING (auth.role() = 'service_role');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription ON public.user_profiles(subscription_tier, subscription_status);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON public.user_sessions(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_login_history_user_id ON public.login_history(user_id);
CREATE INDEX IF NOT EXISTS idx_login_history_created_at ON public.login_history(created_at);

-- Function to create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile on user signup
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update login statistics
CREATE OR REPLACE FUNCTION public.update_login_stats(user_uuid UUID, ip_addr INET)
RETURNS VOID AS $$
BEGIN
    UPDATE public.user_profiles
    SET
        last_login_at = NOW(),
        last_login_ip = ip_addr,
        login_count = login_count + 1
    WHERE id = user_uuid;

    INSERT INTO public.login_history (user_id, ip_address, status)
    VALUES (user_uuid, ip_addr, 'success');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment usage counters
CREATE OR REPLACE FUNCTION public.increment_usage_counter(user_id UUID, counter_type TEXT)
RETURNS VOID AS $$
BEGIN
    CASE counter_type
        WHEN 'documents_created' THEN
            UPDATE public.user_profiles
            SET documents_created = documents_created + 1
            WHERE id = user_id;
        WHEN 'ai_generations_used' THEN
            UPDATE public.user_profiles
            SET ai_generations_used = ai_generations_used + 1
            WHERE id = user_id;
        WHEN 'storage_used_mb' THEN
            -- This would need a specific amount parameter
            NULL;
        ELSE
            RAISE EXCEPTION 'Invalid counter type: %', counter_type;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user sessions
CREATE OR REPLACE FUNCTION public.get_user_sessions(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    session_token TEXT,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    location TEXT,
    is_active BOOLEAN,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    last_accessed_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.id,
        s.session_token,
        s.device_info,
        s.ip_address,
        s.user_agent,
        s.location,
        s.is_active,
        s.expires_at,
        s.created_at,
        s.last_accessed_at
    FROM public.user_sessions s
    WHERE s.user_id = user_uuid
    AND s.is_active = true
    ORDER BY s.last_accessed_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user login history
CREATE OR REPLACE FUNCTION public.get_user_login_history(user_uuid UUID, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    login_method TEXT,
    ip_address INET,
    user_agent TEXT,
    location TEXT,
    device_info JSONB,
    status TEXT,
    failure_reason TEXT,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        lh.id,
        lh.login_method,
        lh.ip_address,
        lh.user_agent,
        lh.location,
        lh.device_info,
        lh.status,
        lh.failure_reason,
        lh.created_at
    FROM public.login_history lh
    WHERE lh.user_id = user_uuid
    ORDER BY lh.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update subscription tier and reset usage
CREATE OR REPLACE FUNCTION public.update_subscription_tier(
    user_uuid UUID,
    new_tier TEXT,
    new_status TEXT DEFAULT 'active',
    stripe_customer_id_param TEXT DEFAULT NULL,
    stripe_subscription_id_param TEXT DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    old_tier TEXT;
    old_status TEXT;
    plan_limits RECORD;
BEGIN
    -- Get current subscription info
    SELECT subscription_tier, subscription_status INTO old_tier, old_status
    FROM public.user_profiles WHERE id = user_uuid;

    -- Get plan limits for new tier
    SELECT * INTO plan_limits FROM public.subscription_plans WHERE tier = new_tier AND is_active = true;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Invalid subscription tier: %', new_tier;
    END IF;

    -- Update user profile with new tier and limits
    UPDATE public.user_profiles SET
        subscription_tier = new_tier,
        subscription_status = new_status,
        stripe_customer_id = COALESCE(stripe_customer_id_param, stripe_customer_id),
        stripe_subscription_id = COALESCE(stripe_subscription_id_param, stripe_subscription_id),
        documents_limit = plan_limits.documents_limit,
        ai_generations_limit = plan_limits.ai_generations_limit,
        ai_image_generations_limit = plan_limits.ai_image_generations_limit,
        storage_limit_mb = plan_limits.storage_limit_mb,
        updated_at = NOW()
    WHERE id = user_uuid;

    -- Log subscription change
    INSERT INTO public.subscription_history (user_id, old_tier, new_tier, old_status, new_status, change_reason)
    VALUES (user_uuid, old_tier, new_tier, old_status, new_status, 'tier_change');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reset usage counters for billing period
CREATE OR REPLACE FUNCTION public.reset_usage_counters(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE public.user_profiles SET
        documents_created = 0,
        ai_generations_used = 0,
        ai_image_generations_used = 0,
        last_usage_reset = NOW()
    WHERE id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to track usage with billing period context
CREATE OR REPLACE FUNCTION public.track_usage(
    user_uuid UUID,
    usage_type_param TEXT,
    amount_param INTEGER DEFAULT 1,
    metadata_param JSONB DEFAULT '{}'::JSONB
)
RETURNS VOID AS $$
DECLARE
    billing_start TIMESTAMPTZ;
    billing_end TIMESTAMPTZ;
BEGIN
    -- Get current billing period from user profile
    SELECT current_period_start, current_period_end INTO billing_start, billing_end
    FROM public.user_profiles WHERE id = user_uuid;

    -- If no billing period set, use current month
    IF billing_start IS NULL THEN
        billing_start := date_trunc('month', NOW());
        billing_end := billing_start + INTERVAL '1 month';
    END IF;

    -- Insert usage tracking record
    INSERT INTO public.usage_tracking (
        user_id, usage_type, amount, metadata,
        billing_period_start, billing_period_end
    ) VALUES (
        user_uuid, usage_type_param, amount_param, metadata_param,
        billing_start, billing_end
    );

    -- Update user profile counters
    CASE usage_type_param
        WHEN 'document_creation' THEN
            UPDATE public.user_profiles SET documents_created = documents_created + amount_param WHERE id = user_uuid;
        WHEN 'ai_generation' THEN
            UPDATE public.user_profiles SET ai_generations_used = ai_generations_used + amount_param WHERE id = user_uuid;
        WHEN 'ai_image_generation' THEN
            UPDATE public.user_profiles SET ai_image_generations_used = ai_image_generations_used + amount_param WHERE id = user_uuid;
        WHEN 'storage_upload' THEN
            UPDATE public.user_profiles SET storage_used_mb = storage_used_mb + amount_param WHERE id = user_uuid;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert default subscription plans
INSERT INTO public.subscription_plans (
    name, tier, price_monthly, price_yearly,
    documents_limit, ai_generations_limit, ai_image_generations_limit,
    storage_limit_mb, max_file_upload_mb,
    custom_templates, priority_processing, remove_watermark,
    advanced_analytics, api_access, ai_model_selection, phone_support
) VALUES
    ('Basic Plan', 'basic', 900, 8400, 10, 50, 10, 500, 5, false, false, false, false, false, false, false),
    ('Standard Plan', 'standard', 1900, 18000, 50, 200, 50, 2048, 25, true, true, true, true, false, false, false),
    ('Pro Plan', 'pro', 3900, 34800, -1, -1, -1, 10240, 100, true, true, true, true, true, true, true)
ON CONFLICT (name) DO UPDATE SET
    price_monthly = EXCLUDED.price_monthly,
    price_yearly = EXCLUDED.price_yearly,
    documents_limit = EXCLUDED.documents_limit,
    ai_generations_limit = EXCLUDED.ai_generations_limit,
    ai_image_generations_limit = EXCLUDED.ai_image_generations_limit,
    storage_limit_mb = EXCLUDED.storage_limit_mb,
    max_file_upload_mb = EXCLUDED.max_file_upload_mb,
    custom_templates = EXCLUDED.custom_templates,
    priority_processing = EXCLUDED.priority_processing,
    remove_watermark = EXCLUDED.remove_watermark,
    advanced_analytics = EXCLUDED.advanced_analytics,
    api_access = EXCLUDED.api_access,
    ai_model_selection = EXCLUDED.ai_model_selection,
    phone_support = EXCLUDED.phone_support,
    updated_at = NOW();
