-- Add Custom Fonts Support to Cover Templates
-- This migration adds custom font metadata storage to the cover_templates table

-- Add custom_fonts column to store custom font metadata
ALTER TABLE public.cover_templates
ADD COLUMN IF NOT EXISTS custom_fonts JSONB DEFAULT '{"fonts": []}';

-- Add comment explaining the custom_fonts structure
COMMENT ON COLUMN public.cover_templates.custom_fonts IS 'Custom font metadata including URLs and configurations';

-- Custom fonts structure:
-- {
--   "fonts": [
--     {
--       "name": "Roboto Slab",
--       "category": "Custom",
--       "fallback": "\"Roboto Slab\", serif",
--       "googleFont": true,
--       "customUrl": "https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400;700&display=swap",
--       "weights": [300, 400, 700],
--       "addedAt": "2024-01-15T10:30:00Z"
--     }
--   ]
-- }

-- Create index for efficient custom font queries
CREATE INDEX IF NOT EXISTS idx_cover_templates_custom_fonts 
ON public.cover_templates USING GIN (custom_fonts);

-- Update existing templates to have empty custom_fonts structure
UPDATE public.cover_templates 
SET custom_fonts = '{"fonts": []}'::jsonb 
WHERE custom_fonts IS NULL;

-- Verify the migration
SELECT 
    'Custom fonts column added successfully' as status,
    COUNT(*) as templates_updated
FROM public.cover_templates 
WHERE custom_fonts IS NOT NULL;

-- Show sample of updated structure
SELECT 
    id,
    name,
    jsonb_array_length(text_overlays->'overlays') as text_overlays_count,
    jsonb_array_length(custom_fonts->'fonts') as custom_fonts_count
FROM public.cover_templates 
LIMIT 3;
