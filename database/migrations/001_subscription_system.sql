-- Migration: Add Subscription System Support
-- This migration updates the existing database to support the 3-tier subscription system

-- 1. Update user_profiles table with new subscription fields
ALTER TABLE public.user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_subscription_tier_check;

ALTER TABLE public.user_profiles 
ADD CONSTRAINT user_profiles_subscription_tier_check 
CHECK (subscription_tier IN ('free', 'basic', 'standard', 'pro'));

ALTER TABLE public.user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_subscription_status_check;

ALTER TABLE public.user_profiles 
ADD CONSTRAINT user_profiles_subscription_status_check 
CHECK (subscription_status IN ('active', 'cancelled', 'expired', 'trial', 'past_due', 'incomplete'));

-- Add new columns if they don't exist
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS current_period_start TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS current_period_end TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS cancel_at_period_end BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS ai_image_generations_used INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS ai_image_generations_limit INTEGER DEFAULT 5,
ADD COLUMN IF NOT EXISTS last_usage_reset TIMESTAMPTZ DEFAULT NOW();

-- Update storage limit for existing free users to match new Basic tier
UPDATE public.user_profiles 
SET storage_limit_mb = 500, ai_image_generations_limit = 5
WHERE subscription_tier = 'free';

-- 2. Create subscription_plans table
CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    tier TEXT NOT NULL CHECK (tier IN ('basic', 'standard', 'pro')),
    price_monthly INTEGER NOT NULL, -- Price in cents
    price_yearly INTEGER NOT NULL, -- Price in cents
    stripe_price_id_monthly TEXT,
    stripe_price_id_yearly TEXT,
    stripe_product_id TEXT,
    
    -- Feature limits
    documents_limit INTEGER NOT NULL,
    ai_generations_limit INTEGER NOT NULL, -- -1 for unlimited
    ai_image_generations_limit INTEGER NOT NULL, -- -1 for unlimited
    storage_limit_mb INTEGER NOT NULL,
    max_file_upload_mb INTEGER NOT NULL,
    
    -- Feature flags
    custom_templates BOOLEAN DEFAULT false,
    priority_processing BOOLEAN DEFAULT false,
    remove_watermark BOOLEAN DEFAULT false,
    advanced_analytics BOOLEAN DEFAULT false,
    api_access BOOLEAN DEFAULT false,
    ai_model_selection BOOLEAN DEFAULT false,
    phone_support BOOLEAN DEFAULT false,
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Create subscription_history table
CREATE TABLE IF NOT EXISTS public.subscription_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    old_tier TEXT,
    new_tier TEXT NOT NULL,
    old_status TEXT,
    new_status TEXT NOT NULL,
    change_reason TEXT,
    stripe_event_id TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Create usage_tracking table
CREATE TABLE IF NOT EXISTS public.usage_tracking (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    usage_type TEXT NOT NULL CHECK (usage_type IN ('document_creation', 'ai_generation', 'ai_image_generation', 'storage_upload')),
    amount INTEGER DEFAULT 1,
    metadata JSONB,
    billing_period_start TIMESTAMPTZ NOT NULL,
    billing_period_end TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. Create billing_history table
CREATE TABLE IF NOT EXISTS public.billing_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    stripe_invoice_id TEXT UNIQUE,
    stripe_payment_intent_id TEXT,
    amount_paid INTEGER,
    currency TEXT DEFAULT 'usd',
    status TEXT CHECK (status IN ('paid', 'open', 'void', 'uncollectible')),
    billing_reason TEXT,
    invoice_pdf_url TEXT,
    period_start TIMESTAMPTZ,
    period_end TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. Create indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_stripe_customer_id ON public.user_profiles(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_tier ON public.subscription_plans(tier);
CREATE INDEX IF NOT EXISTS idx_subscription_history_user_id ON public.subscription_history(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_history_created_at ON public.subscription_history(created_at);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_id ON public.usage_tracking(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_usage_type ON public.usage_tracking(usage_type);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_billing_period ON public.usage_tracking(billing_period_start, billing_period_end);
CREATE INDEX IF NOT EXISTS idx_billing_history_user_id ON public.billing_history(user_id);
CREATE INDEX IF NOT EXISTS idx_billing_history_stripe_invoice_id ON public.billing_history(stripe_invoice_id);

-- 7. Enable RLS for new tables
ALTER TABLE public.subscription_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.billing_history ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policies
CREATE POLICY "Users can view own subscription history" ON public.subscription_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage subscription history" ON public.subscription_history
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Users can view own usage tracking" ON public.usage_tracking
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage usage tracking" ON public.usage_tracking
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Users can view own billing history" ON public.billing_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage billing history" ON public.billing_history
    FOR ALL USING (auth.role() = 'service_role');

-- 9. Insert default subscription plans
INSERT INTO public.subscription_plans (
    name, tier, price_monthly, price_yearly,
    documents_limit, ai_generations_limit, ai_image_generations_limit, 
    storage_limit_mb, max_file_upload_mb,
    custom_templates, priority_processing, remove_watermark, 
    advanced_analytics, api_access, ai_model_selection, phone_support
) VALUES 
    ('Basic Plan', 'basic', 900, 8400, 10, 50, 10, 500, 5, false, false, false, false, false, false, false),
    ('Standard Plan', 'standard', 1900, 18000, 50, 200, 50, 2048, 25, true, true, true, true, false, false, false),
    ('Pro Plan', 'pro', 3900, 34800, -1, -1, -1, 10240, 100, true, true, true, true, true, true, true)
ON CONFLICT (name) DO UPDATE SET
    price_monthly = EXCLUDED.price_monthly,
    price_yearly = EXCLUDED.price_yearly,
    documents_limit = EXCLUDED.documents_limit,
    ai_generations_limit = EXCLUDED.ai_generations_limit,
    ai_image_generations_limit = EXCLUDED.ai_image_generations_limit,
    storage_limit_mb = EXCLUDED.storage_limit_mb,
    max_file_upload_mb = EXCLUDED.max_file_upload_mb,
    updated_at = NOW();
