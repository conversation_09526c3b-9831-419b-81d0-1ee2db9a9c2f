-- Migration: MVP Preferences - Essential fields only
-- Date: 2025-08-01
-- Description: Streamlined preferences for MVP launch - only essential user preferences

-- Note: This migration is now minimal for MVP
-- Removed non-essential fields: language, default_tone, default_privacy, date_format, number_format
-- Language removed as it's non-functional without i18n setup (post-MVP feature)
-- Other fields will be handled per-document or via browser locale detection

-- Create indexes for better query performance on essential preference fields
CREATE INDEX IF NOT EXISTS idx_user_profiles_timezone ON public.user_profiles(timezone);

-- Verify existing essential fields
SELECT
    COUNT(*) as total_users,
    COUNT(timezone) as users_with_timezone,
    COUNT(notifications_email) as users_with_email_notifications,
    COUNT(auto_save) as users_with_auto_save
FROM public.user_profiles;
