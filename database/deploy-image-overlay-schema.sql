-- Image Overlay Template System - Clean Database Schema
-- Complete replacement of JSON-based templates with Image Overlay Templates
-- Run this script in your Supabase SQL Editor

-- Drop existing cover_templates table if it exists (clean slate)
DROP TABLE IF EXISTS public.cover_templates CASCADE;

-- Create the new cover_templates table for Image Overlay Templates ONLY
CREATE TABLE public.cover_templates (
    id TEXT PRIMARY KEY,
    
    -- Template metadata
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    tags TEXT[] DEFAULT '{}',
    
    -- Image Overlay Template Properties (REQUIRED)
    background_image_url TEXT NOT NULL, -- URL to background image in Supabase Storage
    background_image_width INTEGER NOT NULL, -- Original image width
    background_image_height INTEGER NOT NULL, -- Original image height
    text_overlays JSONB NOT NULL, -- Text overlay configurations
    
    -- Visual assets
    thumbnail_url TEXT, -- Generated thumbnail of the template
    preview_url TEXT, -- Generated preview with sample text
    
    -- Template properties
    supported_formats TEXT[] DEFAULT '{"pdf", "png", "jpg"}',
    
    -- Usage and popularity
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.0,
    
    -- Status and availability
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
    is_premium BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_cover_templates_category ON public.cover_templates(category);
CREATE INDEX IF NOT EXISTS idx_cover_templates_status ON public.cover_templates(status);
CREATE INDEX IF NOT EXISTS idx_cover_templates_tags ON public.cover_templates USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_cover_templates_usage ON public.cover_templates(usage_count DESC);

-- Create storage bucket for template background images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('template-backgrounds', 'template-backgrounds', true)
ON CONFLICT (id) DO NOTHING;

-- Set up storage policies for template background images
CREATE POLICY "Public read access for template backgrounds" ON storage.objects
FOR SELECT USING (bucket_id = 'template-backgrounds');

CREATE POLICY "Authenticated users can upload template backgrounds" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'template-backgrounds' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update template backgrounds" ON storage.objects
FOR UPDATE USING (bucket_id = 'template-backgrounds' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete template backgrounds" ON storage.objects
FOR DELETE USING (bucket_id = 'template-backgrounds' AND auth.role() = 'authenticated');

-- Enable RLS on cover_templates table
ALTER TABLE public.cover_templates ENABLE ROW LEVEL SECURITY;

-- Create policies for cover_templates
CREATE POLICY "Public read access for active templates" ON public.cover_templates
FOR SELECT USING (status = 'active');

CREATE POLICY "Authenticated users can manage templates" ON public.cover_templates
FOR ALL USING (auth.role() = 'authenticated');

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_cover_templates_updated_at 
    BEFORE UPDATE ON public.cover_templates 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert a sample template for testing
INSERT INTO public.cover_templates (
    id,
    name,
    description,
    category,
    tags,
    background_image_url,
    background_image_width,
    background_image_height,
    text_overlays,
    status
) VALUES (
    'sample-modern-business-001',
    'Modern Business Template',
    'Clean, professional design perfect for business documents and reports',
    'Business',
    ARRAY['modern', 'professional', 'business', 'clean'],
    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjgwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6IzM0OThkYjtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMmMzZTUwO3N0b3Atb3BhY2l0eToxIiAvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNncmFkaWVudCkiLz4KICA8dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0IiBmaWxsPSJyZ2JhKDI1NSwyNTUsMjU1LDAuMykiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJjZW50cmFsIj5TYW1wbGUgQmFja2dyb3VuZDwvdGV4dD4KPC9zdmc+',
    600,
    800,
    '{
        "overlays": [
            {
                "id": "title",
                "type": "text",
                "placeholder": "{{title}}",
                "position": {
                    "x": 50,
                    "y": 200,
                    "width": 500,
                    "height": 80
                },
                "styling": {
                    "fontSize": 36,
                    "fontFamily": "Arial",
                    "fontWeight": "bold",
                    "color": "#FFFFFF",
                    "textAlign": "center",
                    "lineHeight": 1.2,
                    "maxLines": 2,
                    "overflow": "ellipsis",
                    "verticalAlign": "center"
                }
            },
            {
                "id": "author",
                "type": "text",
                "placeholder": "by {{author}}",
                "position": {
                    "x": 50,
                    "y": 320,
                    "width": 500,
                    "height": 40
                },
                "styling": {
                    "fontSize": 20,
                    "fontFamily": "Arial",
                    "fontWeight": "normal",
                    "color": "#BDC3C7",
                    "textAlign": "center",
                    "lineHeight": 1.2,
                    "maxLines": 1,
                    "overflow": "ellipsis",
                    "verticalAlign": "center"
                }
            },
            {
                "id": "description",
                "type": "text",
                "placeholder": "{{description}}",
                "position": {
                    "x": 50,
                    "y": 400,
                    "width": 500,
                    "height": 120
                },
                "styling": {
                    "fontSize": 14,
                    "fontFamily": "Arial",
                    "fontWeight": "normal",
                    "color": "#ECF0F1",
                    "textAlign": "center",
                    "lineHeight": 1.4,
                    "maxLines": 6,
                    "overflow": "ellipsis",
                    "verticalAlign": "top"
                }
            }
        ]
    }'::jsonb,
    'active'
);

-- Verify the setup
SELECT 
    'Templates table created successfully' as status,
    COUNT(*) as sample_templates_count
FROM public.cover_templates;

-- Show the sample template
SELECT 
    id,
    name,
    category,
    background_image_width,
    background_image_height,
    jsonb_array_length(text_overlays->'overlays') as overlay_count
FROM public.cover_templates 
WHERE id = 'sample-modern-business-001';
