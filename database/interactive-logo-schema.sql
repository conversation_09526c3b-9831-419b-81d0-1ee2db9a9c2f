-- Simplified Logo System Database Schema
-- Mirrors the text overlay architecture for consistency and simplicity

-- Add logo overlays support to cover_templates table (mirrors text_overlays)
ALTER TABLE public.cover_templates
ADD COLUMN IF NOT EXISTS logo_overlays JSONB DEFAULT '{"overlays": []}';

-- Logo overlay structure mirrors text overlay structure exactly:
-- {
--   "overlays": [
--     {
--       "id": "company_logo",
--       "type": "logo",
--       "placeholder": "{{logo}}",
--       "position": {"x": 450, "y": 50, "width": 100, "height": 60},
--       "styling": {"opacity": 1.0, "borderRadius": 0}
--     }
--   ]
-- }

-- Add logo preferences to user_profiles table for central logo management
ALTER TABLE public.user_profiles
ADD COLUMN IF NOT EXISTS default_logo_id UUID REFERENCES public.user_logos(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS logo_preferences JSONB DEFAULT '{}';

-- Create indexes for performance on user_profiles
CREATE INDEX IF NOT EXISTS idx_user_profiles_default_logo_id ON public.user_profiles(default_logo_id);

-- Create indexes for logo_overlays JSONB queries
CREATE INDEX IF NOT EXISTS idx_cover_templates_logo_overlays ON public.cover_templates USING GIN (logo_overlays);

-- Note: Logo customizations will be handled like text overlay customizations
-- using session storage and the same patterns, so no separate table needed

-- Sample logo overlay configurations for different template types
-- These would be added to existing templates via UPDATE statements

-- Business Template Logo Overlay Example:
-- UPDATE public.cover_templates 
-- SET logo_overlays = '{
--   "overlays": [
--     {
--       "id": "primary_logo",
--       "type": "logo",
--       "position": {"x": 450, "y": 50, "width": 100, "height": 60},
--       "constraints": {
--         "min_width": 50, "max_width": 150,
--         "min_height": 30, "max_height": 100,
--         "maintain_aspect_ratio": true
--       },
--       "styling": {
--         "opacity": 1.0,
--         "blend_mode": "normal",
--         "border_radius": 0
--       }
--     }
--   ]
-- }'
-- WHERE category = 'Business' AND id = 'business-template-001';

-- Academic Template Logo Overlay Example:
-- UPDATE public.cover_templates 
-- SET logo_overlays = '{
--   "overlays": [
--     {
--       "id": "institution_logo",
--       "type": "logo",
--       "position": {"x": 250, "y": 30, "width": 80, "height": 50},
--       "constraints": {
--         "min_width": 40, "max_width": 120,
--         "min_height": 25, "max_height": 75,
--         "maintain_aspect_ratio": true
--       },
--       "styling": {
--         "opacity": 0.8,
--         "blend_mode": "normal",
--         "border_radius": 0
--       }
--     }
--   ]
-- }'
-- WHERE category = 'Academic' AND id = 'academic-template-001';

-- Creative Template Logo Overlay Example (Multiple logos):
-- UPDATE public.cover_templates 
-- SET logo_overlays = '{
--   "overlays": [
--     {
--       "id": "brand_logo",
--       "type": "logo",
--       "position": {"x": 50, "y": 50, "width": 120, "height": 80},
--       "constraints": {
--         "min_width": 60, "max_width": 180,
--         "min_height": 40, "max_height": 120,
--         "maintain_aspect_ratio": true
--       },
--       "styling": {
--         "opacity": 1.0,
--         "blend_mode": "normal",
--         "border_radius": 8
--       }
--     },
--     {
--       "id": "partner_logo",
--       "type": "logo",
--       "position": {"x": 450, "y": 700, "width": 80, "height": 50},
--       "constraints": {
--         "min_width": 40, "max_width": 120,
--         "min_height": 25, "max_height": 75,
--         "maintain_aspect_ratio": true
--       },
--       "styling": {
--         "opacity": 0.7,
--         "blend_mode": "normal",
--         "border_radius": 4
--       }
--     }
--   ]
-- }'
-- WHERE category = 'Creative' AND id = 'creative-template-001';

-- Note: Logo overlays are only added to templates that are explicitly designed with them
-- No automatic migration - templates must be created with logo overlays intentionally

-- Helper functions for logo overlay management

-- Function to get template logo overlays with fallback
CREATE OR REPLACE FUNCTION public.get_template_logo_overlays(template_id TEXT)
RETURNS JSONB AS $$
DECLARE
    overlays JSONB;
BEGIN
    SELECT logo_overlays INTO overlays
    FROM public.cover_templates
    WHERE id = template_id;

    -- Return empty overlays if none found (mirrors text overlay pattern)
    RETURN COALESCE(overlays, '{"overlays": []}'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- Function to check if template has logo overlays
CREATE OR REPLACE FUNCTION public.template_has_logo_overlays(template_id TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    overlay_count INTEGER;
BEGIN
    SELECT jsonb_array_length(logo_overlays->'overlays') INTO overlay_count
    FROM public.cover_templates
    WHERE id = template_id;

    RETURN COALESCE(overlay_count, 0) > 0;
END;
$$ LANGUAGE plpgsql;

-- Function to get default logo overlay configurations for template types
CREATE OR REPLACE FUNCTION public.get_default_logo_overlay_for_category(category TEXT)
RETURNS JSONB AS $$
BEGIN
    CASE category
        WHEN 'Business' THEN
            RETURN jsonb_build_object(
                'id', 'company_logo',
                'type', 'logo',
                'placeholder', '{{logo}}',
                'position', jsonb_build_object('x', 450, 'y', 50, 'width', 100, 'height', 60),
                'styling', jsonb_build_object('opacity', 1.0, 'borderRadius', 0)
            );
        WHEN 'Academic' THEN
            RETURN jsonb_build_object(
                'id', 'institution_logo',
                'type', 'logo',
                'placeholder', '{{logo}}',
                'position', jsonb_build_object('x', 250, 'y', 30, 'width', 80, 'height', 50),
                'styling', jsonb_build_object('opacity', 0.8, 'borderRadius', 0)
            );
        ELSE
            RETURN jsonb_build_object(
                'id', 'brand_logo',
                'type', 'logo',
                'placeholder', '{{logo}}',
                'position', jsonb_build_object('x', 450, 'y', 50, 'width', 100, 'height', 60),
                'styling', jsonb_build_object('opacity', 1.0, 'borderRadius', 0)
            );
    END CASE;
END;
$$ LANGUAGE plpgsql;
