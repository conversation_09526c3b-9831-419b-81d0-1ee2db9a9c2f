-- RapidDoc AI - Row Level Security Policies for Projects
-- Simplified secure access control for projects

-- Enable RLS on tables
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_activities ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user ID
CREATE OR REPLACE FUNCTION auth.user_id() RETURNS UUID AS $$
  SELECT COALESCE(
    auth.jwt() ->> 'sub',
    (auth.jwt() ->> 'user_id')
  )::UUID
$$ LANGUAGE SQL STABLE;

-- Helper function to check if user owns project
CREATE OR REPLACE FUNCTION public.user_owns_project(project_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.projects
    WHERE id = project_id AND user_id = auth.user_id()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- PROJECTS TABLE POLICIES

-- Users can view their own projects
CREATE POLICY "Users can view their own projects" ON public.projects
  FOR SELECT USING (user_id = auth.user_id());

-- Users can create their own projects
CREATE POLICY "Users can create their own projects" ON public.projects
  FOR INSERT WITH CHECK (user_id = auth.user_id());

-- Users can update their own projects
CREATE POLICY "Users can update their own projects" ON public.projects
  FOR UPDATE USING (user_id = auth.user_id());

-- Users can delete their own projects
CREATE POLICY "Users can delete their own projects" ON public.projects
  FOR DELETE USING (user_id = auth.user_id());



-- PROJECT ACTIVITIES TABLE POLICIES

-- Users can view activities for their own projects
CREATE POLICY "Users can view activities for their own projects" ON public.project_activities
  FOR SELECT USING (
    user_id = auth.user_id() OR
    EXISTS (
      SELECT 1 FROM public.projects p
      WHERE p.id = project_id AND p.user_id = auth.user_id()
    )
  );

-- System can create activity logs (no user restriction for INSERT)
CREATE POLICY "System can create activity logs" ON public.project_activities
  FOR INSERT WITH CHECK (true);

-- No updates or deletes allowed on activity logs (audit trail)
-- Activities are immutable for audit purposes
