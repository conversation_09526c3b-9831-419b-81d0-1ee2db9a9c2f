# RapidDoc AI - Subscription System Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing the complete 3-tier subscription system in RapidDoc AI.

## Prerequisites

Before starting the implementation, ensure you have:

1. **Supabase Project**: Active Supabase project with database access
2. **Stripe Account**: Stripe account with API keys
3. **Environment Setup**: Node.js 18+ and npm/yarn installed
4. **Database Access**: Supabase service role key for database operations

## Implementation Steps

### Step 1: Database Setup

1. **Run the database migration**:
   ```bash
   # Apply the subscription system migration
   psql -h your-supabase-host -U postgres -d postgres -f database/migrations/001_subscription_system.sql
   ```

2. **Verify database changes**:
   - Check that new tables are created: `subscription_plans`, `subscription_history`, `usage_tracking`, `billing_history`
   - Verify user_profiles table has new subscription columns
   - Confirm RLS policies are in place

### Step 2: Stripe Configuration

1. **Install dependencies**:
   ```bash
   npm install stripe @stripe/stripe-js
   ```

2. **Set up Stripe products and prices**:
   ```bash
   # Run the Stripe setup script
   node scripts/setup-stripe.js
   ```

3. **Configure environment variables**:
   ```env
   # Add to your .env file
   VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
   STRIPE_SECRET_KEY=sk_test_...
   STRIPE_WEBHOOK_SECRET=whsec_....
   
   # Price IDs (from setup script output)
   VITE_STRIPE_BASIC_MONTHLY_PRICE_ID=price_...
   VITE_STRIPE_BASIC_YEARLY_PRICE_ID=price_...
   VITE_STRIPE_STANDARD_MONTHLY_PRICE_ID=price_...
   VITE_STRIPE_STANDARD_YEARLY_PRICE_ID=price_...
   VITE_STRIPE_PRO_MONTHLY_PRICE_ID=price_...
   VITE_STRIPE_PRO_YEARLY_PRICE_ID=price_...
   ```

### Step 3: Supabase Edge Functions

1. **Deploy Edge Functions**:
   ```bash
   # Deploy the subscription-related Edge Functions
   supabase functions deploy create-checkout-session
   supabase functions deploy stripe-webhook
   supabase functions deploy create-portal-session
   ```

2. **Configure webhook endpoint**:
   - In Stripe Dashboard, add webhook endpoint: `https://your-project.supabase.co/functions/v1/stripe-webhook`
   - Select events: `checkout.session.completed`, `customer.subscription.*`, `invoice.payment_*`
   - Copy webhook signing secret to environment variables

### Step 4: Frontend Integration

1. **Update routing**:
   - Pricing page is already added to Routes.jsx
   - Verify PricingPage component is accessible at `/pricing`

2. **Update account settings**:
   ```jsx
   // In src/pages/account-settings/AccountSettings.jsx
   import SubscriptionSection from './components/SubscriptionSection';
   
   // Replace BillingSection with SubscriptionSection
   <SubscriptionSection />
   ```

3. **Add subscription guards to protected features**:
   ```jsx
   // Example: Protect AI image generation
   import { useSubscriptionAwareAPI } from '../middleware/subscriptionMiddleware';
   
   const { generateAIImage } = useSubscriptionAwareAPI();
   
   const handleGenerateImage = async () => {
     try {
       await generateAIImage(async () => {
         // Your AI image generation logic
         return await aiService.generateImage(prompt);
       });
     } catch (error) {
       // Handle usage limit or feature access errors
       showUpgradePrompt(error.message);
     }
   };
   ```

### Step 5: Usage Tracking Integration

1. **Track document creation**:
   ```jsx
   // In document creation components
   import { subscriptionUtils } from '../middleware/subscriptionMiddleware';
   
   const createDocument = async (documentData) => {
     const canCreate = await subscriptionUtils.canUserPerformAction(
       user.id, 
       'create_document'
     );
     
     if (!canCreate) {
       throw new Error('Document creation limit reached');
     }
     
     const document = await documentService.create(documentData);
     
     // Track usage after successful creation
     await subscriptionUtils.trackUserUsage(
       user.id, 
       'create_document', 
       1, 
       { documentType: documentData.type }
     );
     
     return document;
   };
   ```

2. **Track AI generations**:
   ```jsx
   // In AI service calls
   const generateContent = async (prompt) => {
     await subscriptionUtils.trackUserUsage(
       user.id, 
       'ai_generation', 
       1, 
       { model: 'gpt-4', promptLength: prompt.length }
     );
     
     return await aiService.generate(prompt);
   };
   ```

### Step 6: Feature Protection

1. **Protect premium features**:
   ```jsx
   // Example: Custom templates feature
   import { withSubscriptionGuard } from '../hooks/useSubscriptionGuard';
   
   const CustomTemplateEditor = withSubscriptionGuard(
     ({ template, onSave }) => {
       // Component implementation
     },
     'custom_templates' // Required feature
   );
   ```

2. **Add upgrade prompts**:
   ```jsx
   // In components that hit usage limits
   const { upgradeSuggestions, startSubscription } = useSubscriptionGuard();
   
   useEffect(() => {
     if (upgradeSuggestions.length > 0) {
       showUpgradeModal(upgradeSuggestions[0]);
     }
   }, [upgradeSuggestions]);
   ```

### Step 7: Testing

1. **Test subscription flow**:
   - Visit `/pricing` and start a subscription
   - Verify checkout session creation
   - Test webhook processing
   - Confirm database updates

2. **Test usage tracking**:
   - Create documents and verify usage increments
   - Test usage limit enforcement
   - Verify usage resets on billing cycle

3. **Test feature protection**:
   - Verify premium features are blocked for free users
   - Test upgrade prompts and flows
   - Confirm feature access after subscription

### Step 8: Production Deployment

1. **Environment configuration**:
   ```env
   # Production environment variables
   VITE_STRIPE_PUBLISHABLE_KEY=pk_live_...
   STRIPE_SECRET_KEY=sk_live_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   ```

2. **Security checklist**:
   - [ ] Webhook signatures are verified
   - [ ] RLS policies are enabled
   - [ ] Environment variables are secure
   - [ ] HTTPS is enforced
   - [ ] Error handling is implemented

3. **Monitoring setup**:
   - Configure Stripe webhook monitoring
   - Set up database performance monitoring
   - Implement error tracking and alerting
   - Monitor subscription metrics

## Common Issues & Solutions

### Issue: Webhook verification fails
**Solution**: Ensure webhook secret is correctly set and signature verification is implemented properly.

### Issue: Usage tracking not working
**Solution**: Check that the `track_usage` database function is properly deployed and user permissions are correct.

### Issue: Subscription status not updating
**Solution**: Verify webhook events are being processed and database updates are successful.

### Issue: Feature protection not working
**Solution**: Ensure subscription guard hooks are properly implemented and feature flags are correctly checked.

## Maintenance

### Regular Tasks
- Monitor subscription metrics and conversion rates
- Update pricing based on market analysis
- Review and optimize usage tracking performance
- Update security measures and compliance documentation

### Quarterly Reviews
- Analyze subscription data and user behavior
- Review and update pricing strategy
- Conduct security audits
- Update documentation and guides

## Support

For implementation support:
- Technical issues: Create GitHub issue with detailed description
- Business questions: Contact product team
- Security concerns: Email <EMAIL>

## Next Steps

After successful implementation:
1. Monitor subscription metrics and user feedback
2. Implement A/B testing for pricing optimization
3. Add advanced features like team subscriptions
4. Integrate with analytics and business intelligence tools
5. Expand to additional payment methods and currencies
