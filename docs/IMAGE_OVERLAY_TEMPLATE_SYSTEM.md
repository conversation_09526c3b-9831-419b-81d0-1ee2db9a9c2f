# Image Overlay Template System Documentation

## Overview

The Image Overlay Template System is a complete replacement for the previous PDF-to-JSON template conversion system in DocForge AI. It provides a simpler, more reliable approach to document cover generation using static background images with dynamic text overlays.

## Table of Contents

1. [Architecture](#architecture)
2. [Key Features](#key-features)
3. [Database Schema](#database-schema)
4. [Core Services](#core-services)
5. [Template Creation](#template-creation)
6. [Integration Guide](#integration-guide)
7. [Performance](#performance)
8. [Testing](#testing)
9. [Deployment](#deployment)
10. [Troubleshooting](#troubleshooting)

## Architecture

### System Overview

```mermaid
graph TB
    A[Background Image] --> B[Image Overlay Engine]
    C[Document Metadata] --> B
    D[Text Position Config] --> B
    B --> E[Canvas Rendering]
    E --> F[High-Quality Output]
    F --> G[PDF/PNG Export]
    
    H[Template Management] --> I[Image Upload]
    H --> J[Position Editor]
    I --> K[Supabase Storage]
    J --> L[Position Metadata]
    K --> A
    L --> D
```

### Core Components

- **Image Overlay Service**: Canvas-based text rendering engine
- **Template Service**: Database operations for template management
- **Preview Service**: Real-time preview generation
- **Export Service**: High-quality document export
- **Template Manager**: Admin interface for template creation

## Key Features

### ✅ **Advantages Over Previous System**

- **Simplified Architecture**: No complex JSON-to-React conversion
- **Better Performance**: Direct Canvas rendering vs HTML/CSS layout
- **Higher Quality**: Pixel-perfect text rendering on images
- **Visual Accuracy**: Templates look exactly like original designs
- **Easier Debugging**: Simpler rendering pipeline
- **Better Mobile Support**: Canvas rendering works consistently

### 🎯 **Core Capabilities**

- Dynamic text overlay positioning
- Advanced text styling (fonts, colors, alignment)
- Text overflow handling (ellipsis, word wrapping)
- Real-time preview generation
- High-quality image export (PNG, JPG, PDF)
- Template caching for performance
- Comprehensive error handling

## Database Schema

### Cover Templates Table

```sql
CREATE TABLE public.cover_templates (
    id TEXT PRIMARY KEY,
    
    -- Template metadata
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    tags TEXT[] DEFAULT '{}',
    
    -- Image Overlay Template Properties
    background_image_url TEXT NOT NULL,
    background_image_width INTEGER NOT NULL,
    background_image_height INTEGER NOT NULL,
    text_overlays JSONB NOT NULL,
    
    -- Visual assets
    thumbnail_url TEXT,
    preview_url TEXT,
    
    -- Template properties
    supported_formats TEXT[] DEFAULT '{"pdf", "png", "jpg"}',
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.0,
    status TEXT DEFAULT 'active',
    is_premium BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Text Overlay Configuration

```json
{
  "overlays": [
    {
      "id": "title",
      "type": "text",
      "placeholder": "{{title}}",
      "position": {
        "x": 100,
        "y": 200,
        "width": 400,
        "height": 60
      },
      "styling": {
        "fontSize": 32,
        "fontFamily": "Arial",
        "fontWeight": "bold",
        "color": "#000000",
        "textAlign": "center",
        "lineHeight": 1.2,
        "maxLines": 2,
        "overflow": "ellipsis",
        "verticalAlign": "center"
      }
    }
  ]
}
```

## Core Services

### Image Overlay Service

**Location**: `src/services/imageOverlayService.js`

**Key Methods**:
- `renderTemplate(template, documentData)`: Render complete template
- `generatePreview(template, sampleData)`: Generate preview image
- `exportAsImage(canvas, format, quality)`: Export rendered template
- `loadBackgroundImage(imageUrl)`: Load and cache background images

**Usage Example**:
```javascript
import imageOverlayService from './services/imageOverlayService.js';

// Render template
const canvas = await imageOverlayService.renderTemplate(template, documentData);
const imageData = imageOverlayService.exportAsImage(canvas, 'png', 0.9);
```

### Template Service

**Location**: `src/services/templateService.js`

**Key Methods**:
- `fetchCoverTemplates(options)`: Fetch templates with filtering
- `searchTemplates(searchTerm)`: Search templates by name/description
- `incrementTemplateUsage(templateId)`: Track template usage

### Preview Service

**Location**: `src/services/previewService.js`

**Key Methods**:
- `generatePreviewData(template, content, documentData)`: Generate preview
- `updatePreviewWithNewTemplate(currentPreview, newTemplate, documentData)`: Update preview
- `generateTemplateThumbnail(template, sampleData)`: Generate thumbnail

### Export Service

**Location**: `src/services/exportService.js`

**Key Methods**:
- `exportAsPdf(documentData, content, options)`: Export with template
- `exportTemplateAsImage(template, documentData, options)`: Export template only
- `exportTemplateMultiFormat(template, documentData, formats)`: Multi-format export

## Template Creation

### Using the Command-Line Tool

**Location**: `tools/image-template-creator.js`

**Basic Usage**:
```bash
node tools/image-template-creator.js \
  --image ./background.jpg \
  --name "Modern Business Cover" \
  --category "Business" \
  --layout "standard" \
  --description "Clean, professional design for business documents"
```

**Available Layouts**:
- `standard`: Title, author, description (book-style)
- `minimal`: Title only (clean, modern)
- `professional`: Left-aligned text (business documents)

**Advanced Options**:
```bash
node tools/image-template-creator.js \
  --image ./background.jpg \
  --name "Premium Template" \
  --category "Creative" \
  --tags "modern,artistic,premium" \
  --layout "professional" \
  --premium true
```

### Manual Template Creation

1. **Upload Background Image** to Supabase Storage (`template-backgrounds` bucket)
2. **Configure Text Overlays** using the admin interface
3. **Test Preview** with sample data
4. **Save Template** to database

## Integration Guide

### Frontend Components

**Template Selection**:
```jsx
import TemplateSelectionInterface from './components/TemplateSelectionInterface.jsx';

<TemplateSelectionInterface
  onTemplateSelect={handleTemplateSelect}
  documentData={documentData}
/>
```

**Preview Generation**:
```jsx
import { generatePreviewData } from './services/previewService.js';

const preview = await generatePreviewData(template, content, documentData);
```

**Export with Template**:
```jsx
import { exportAsPdf } from './services/exportService.js';

const result = await exportAsPdf(documentData, content, { 
  selectedTemplate: template 
});
```

### Admin Interface

**Template Manager**:
```jsx
import TemplateManager from './pages/admin/TemplateManager.jsx';

// Admin route
<Route path="/admin/templates" component={TemplateManager} />
```

**Text Overlay Editor**:
```jsx
import TextOverlayEditor from './pages/admin/components/TextOverlayEditor.jsx';

<TextOverlayEditor
  template={template}
  onTemplateChange={handleTemplateChange}
/>
```

## Performance

### Benchmarks

- **Canvas Initialization**: < 1ms per operation
- **Text Measurement**: < 0.1ms per operation  
- **Template Rendering**: < 100ms per template
- **Image Export**: < 1ms per export
- **Memory Usage**: Stable under load

### Optimization Features

- **Image Caching**: Background images cached in memory
- **Canvas Reuse**: Efficient canvas context management
- **Progressive Loading**: Thumbnails load before full images
- **Batch Operations**: Multiple templates processed efficiently

### Performance Monitoring

```javascript
// Monitor rendering performance
const startTime = performance.now();
await imageOverlayService.renderTemplate(template, documentData);
const duration = performance.now() - startTime;
console.log(`Template rendered in ${duration}ms`);
```

## Testing

### Test Suite Structure

```
src/
├── services/__tests__/
│   └── imageOverlayService.test.js     # Unit tests
├── __tests__/
│   ├── integration/
│   │   └── templateWorkflow.test.js    # Integration tests
│   └── performance/
│       └── imageOverlayPerformance.test.js  # Performance tests
└── scripts/
    └── test-image-overlay-system.js    # Test runner
```

### Running Tests

**All Tests**:
```bash
node scripts/test-image-overlay-system.js
```

**Individual Test Suites**:
```bash
# Unit tests
npm test src/services/__tests__/imageOverlayService.test.js

# Integration tests  
npm test src/__tests__/integration/templateWorkflow.test.js

# Performance tests
npm test src/__tests__/performance/imageOverlayPerformance.test.js
```

### Test Coverage

- **Unit Tests**: 95%+ coverage of core service methods
- **Integration Tests**: Complete workflow testing
- **Performance Tests**: Benchmarking and load testing
- **Error Handling**: Comprehensive failure scenario testing

## Deployment

### Prerequisites

1. **Database Schema**: Deploy `database/deploy-image-overlay-schema.sql`
2. **Storage Bucket**: Ensure `template-backgrounds` bucket exists
3. **Environment Variables**: Supabase credentials configured

### Deployment Steps

1. **Deploy Database Schema**:
```sql
-- Run in Supabase SQL Editor
\i database/deploy-image-overlay-schema.sql
```

2. **Verify Storage Setup**:
```bash
# Check bucket exists and is public
curl https://your-project.supabase.co/storage/v1/object/public/template-backgrounds/
```

3. **Deploy Application Code**:
```bash
# Build and deploy
npm run build
npm run deploy
```

4. **Create Initial Templates**:
```bash
# Add sample templates
node tools/image-template-creator.js --image sample1.jpg --name "Business Template" --category "Business"
node tools/image-template-creator.js --image sample2.jpg --name "Academic Template" --category "Academic"
```

5. **Verify Integration**:
- Test template selection interface
- Verify preview generation
- Test export functionality
- Check admin template manager

### Rollback Plan

If issues occur, you can rollback by:

1. **Disable New System**: Set feature flag to use fallback
2. **Restore Database**: Keep old schema as backup
3. **Revert Code**: Git revert to previous version
4. **Monitor**: Check error logs and user feedback

## Troubleshooting

### Common Issues

**Template Not Loading**:
```javascript
// Check image URL accessibility
console.log('Testing image URL:', template.background_image_url);
```

**Canvas Rendering Errors**:
```javascript
// Verify Canvas API support
if (!document.createElement('canvas').getContext) {
  console.error('Canvas not supported');
}
```

**Text Positioning Issues**:
```javascript
// Debug text overlay positions
console.log('Overlay positions:', template.text_overlays.overlays);
```

**Performance Issues**:
```javascript
// Monitor rendering time
const startTime = performance.now();
await imageOverlayService.renderTemplate(template, documentData);
console.log(`Render time: ${performance.now() - startTime}ms`);
```

### Error Handling

The system includes comprehensive error handling:

- **Image Loading Failures**: Automatic fallback to gradient backgrounds
- **Canvas Errors**: Graceful degradation to HTML templates
- **Text Rendering Issues**: Fallback styling and positioning
- **Export Failures**: Alternative export formats

### Debug Mode

Enable debug logging:
```javascript
// Set debug flag
localStorage.setItem('imageOverlay_debug', 'true');

// View detailed logs in console
```

### Support

For issues or questions:
1. Check console logs for error details
2. Verify template structure in database
3. Test with sample data
4. Review performance metrics
5. Contact development team with specific error messages


