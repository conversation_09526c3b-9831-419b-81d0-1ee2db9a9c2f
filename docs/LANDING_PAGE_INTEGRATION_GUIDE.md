# Landing Page to DocForge AI Integration Guide (Option 1: Sign-up First)

## Overview

This guide provides step-by-step instructions for integrating your external HTML/CSS/JS landing page with the DocForge AI subscription system using the "sign-up first, then pay" approach.

## Flow Overview

1. **Landing Page** → User clicks "Get Started" 
2. **Authentication** → User creates account with pre-selected plan
3. **Pricing Page** → Auto-redirects to checkout for selected plan
4. **Stripe Checkout** → User completes payment
5. **Dashboard** → User accesses premium features

## Prerequisites

- Existing DocForge AI codebase with subscription system implemented
- External landing page (HTML/CSS/JS)
- Domain/subdomain for main application
- Completed [Subscription Implementation Guide](./SUBSCRIPTION_IMPLEMENTATION_GUIDE.md)

## Implementation Steps

### Step 1: Update Landing Page HTML

Update your pricing buttons to redirect to the main app with plan parameters:

```html
<!-- Basic Plan Pricing Card -->
<div class="pricing-card basic">
  <h3>Basic Plan</h3>
  <div class="price">$9/month</div>
  <ul class="features">
    <li>5 AI-generated documents per month</li>
    <li>Basic templates</li>
    <li>Standard support</li>
  </ul>
  <a href="https://your-docforge-domain.com/auth?mode=register&plan=basic&billing=monthly" 
     class="btn btn-primary" 
     id="basic-monthly-btn">
     Get Started
  </a>
  <a href="https://your-docforge-domain.com/auth?mode=register&plan=basic&billing=yearly" 
     class="btn btn-secondary" 
     id="basic-yearly-btn">
     Get Started (Yearly)
  </a>
</div>

<!-- Standard Plan Pricing Card -->
<div class="pricing-card standard featured">
  <h3>Standard Plan</h3>
  <div class="price">$19/month</div>
  <ul class="features">
    <li>25 AI-generated documents per month</li>
    <li>Premium templates</li>
    <li>Priority support</li>
    <li>Custom branding</li>
  </ul>
  <a href="https://your-docforge-domain.com/auth?mode=register&plan=standard&billing=monthly" 
     class="btn btn-primary" 
     id="standard-monthly-btn">
     Get Started
  </a>
  <a href="https://your-docforge-domain.com/auth?mode=register&plan=standard&billing=yearly" 
     class="btn btn-secondary" 
     id="standard-yearly-btn">
     Get Started (Yearly)
  </a>
</div>

<!-- Pro Plan Pricing Card -->
<div class="pricing-card pro">
  <h3>Pro Plan</h3>
  <div class="price">$39/month</div>
  <ul class="features">
    <li>Unlimited AI-generated documents</li>
    <li>All templates + custom templates</li>
    <li>24/7 priority support</li>
    <li>Advanced AI features</li>
    <li>Team collaboration</li>
  </ul>
  <a href="https://your-docforge-domain.com/auth?mode=register&plan=pro&billing=monthly" 
     class="btn btn-primary" 
     id="pro-monthly-btn">
     Get Started
  </a>
  <a href="https://your-docforge-domain.com/auth?mode=register&plan=pro&billing=yearly" 
     class="btn btn-secondary" 
     id="pro-yearly-btn">
     Get Started (Yearly)
  </a>
</div>
```

### Step 2: Add JavaScript for Enhanced UX (Optional)

Add tracking and user feedback to your landing page:

```html
<script>
// Track button clicks for analytics
function trackPlanSelection(plan, billing) {
  // Add your analytics tracking here
  if (typeof gtag !== 'undefined') {
    gtag('event', 'plan_selected', {
      'plan_type': plan,
      'billing_cycle': billing,
      'source': 'landing_page'
    });
  }
  
  // Optional: Store in localStorage for later use
  localStorage.setItem('selected_plan', JSON.stringify({
    plan: plan,
    billing: billing,
    timestamp: Date.now()
  }));
}

// Add event listeners to all pricing buttons
document.addEventListener('DOMContentLoaded', function() {
  // Basic plan buttons
  document.getElementById('basic-monthly-btn')?.addEventListener('click', function() {
    trackPlanSelection('basic', 'monthly');
  });
  
  document.getElementById('basic-yearly-btn')?.addEventListener('click', function() {
    trackPlanSelection('basic', 'yearly');
  });
  
  // Standard plan buttons
  document.getElementById('standard-monthly-btn')?.addEventListener('click', function() {
    trackPlanSelection('standard', 'monthly');
  });
  
  document.getElementById('standard-yearly-btn')?.addEventListener('click', function() {
    trackPlanSelection('standard', 'yearly');
  });
  
  // Pro plan buttons
  document.getElementById('pro-monthly-btn')?.addEventListener('click', function() {
    trackPlanSelection('pro', 'monthly');
  });
  
  document.getElementById('pro-yearly-btn')?.addEventListener('click', function() {
    trackPlanSelection('pro', 'yearly');
  });
});

// Optional: Add loading state when user clicks
function addLoadingState(button) {
  button.addEventListener('click', function() {
    this.innerHTML = 'Redirecting...';
    this.disabled = true;
  });
}
</script>
```

### Step 3: Update Authentication Page

Modify your authentication page to handle plan pre-selection:

```jsx
// filepath: src/pages/auth/index.jsx
import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const AuthPage = () => {
  const [authMode, setAuthMode] = useState('register');
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();

  useEffect(() => {
    // Parse URL parameters
    const searchParams = new URLSearchParams(location.search);
    const mode = searchParams.get('mode');
    const preSelectedPlan = searchParams.get('plan');
    const preSelectedBilling = searchParams.get('billing');
    
    // Set authentication mode if specified
    if (mode && ['register', 'login'].includes(mode)) {
      setAuthMode(mode);
    }
    
    // Store pre-selected plan in sessionStorage for later use
    if (preSelectedPlan && preSelectedBilling) {
      sessionStorage.setItem('preSelectedPlan', preSelectedPlan);
      sessionStorage.setItem('preSelectedBilling', preSelectedBilling);
      
      // Optional: Show selected plan in UI
      sessionStorage.setItem('planSelectionSource', 'landing_page');
    }
  }, [location.search]);

  // Redirect authenticated users
  useEffect(() => {
    if (user) {
      const preSelectedPlan = sessionStorage.getItem('preSelectedPlan');
      const preSelectedBilling = sessionStorage.getItem('preSelectedBilling');
      
      if (preSelectedPlan && preSelectedBilling) {
        // Clear from storage and redirect to pricing with auto-checkout
        sessionStorage.removeItem('preSelectedPlan');
        sessionStorage.removeItem('preSelectedBilling');
        sessionStorage.removeItem('planSelectionSource');
        
        navigate(`/pricing?plan=${preSelectedPlan}&billing=${preSelectedBilling}&auto=true`);
      } else {
        navigate('/dashboard');
      }
    }
  }, [user, navigate]);

  // ...existing component code...
  
  return (
    <div className="auth-container">
      {/* Add plan selection indicator */}
      {sessionStorage.getItem('preSelectedPlan') && (
        <div className="plan-selection-banner">
          <p>
            Selected Plan: <strong>{sessionStorage.getItem('preSelectedPlan')}</strong> 
            ({sessionStorage.getItem('preSelectedBilling')})
          </p>
          <small>Complete registration to continue with your selected plan</small>
        </div>
      )}
      
      {/* ...existing auth form code... */}
    </div>
  );
};

export default AuthPage;
```

### Step 4: Update AuthContext for Post-Registration Flow

```jsx
// filepath: src/contexts/AuthContext.jsx
// ...existing code...

const signUp = async (email, password, userData) => {
  try {
    setLoading(true);
    
    // ...existing signup logic...
    
    // Handle post-registration redirect
    const preSelectedPlan = sessionStorage.getItem('preSelectedPlan');
    const preSelectedBilling = sessionStorage.getItem('preSelectedBilling');
    
    if (preSelectedPlan && preSelectedBilling) {
      // Track conversion from landing page
      if (typeof gtag !== 'undefined') {
        gtag('event', 'sign_up_from_landing', {
          'plan_type': preSelectedPlan,
          'billing_cycle': preSelectedBilling
        });
      }
      
      // Don't clear sessionStorage here - let the auth page handle it
      // This ensures the pricing page gets the parameters
      return; // Auth page useEffect will handle the redirect
    } else {
      navigate('/dashboard');
    }
  } catch (error) {
    setError(error.message);
    throw error;
  } finally {
    setLoading(false);
  }
};

// ...existing code...
```

### Step 5: Update Pricing Page for Auto-Checkout

```jsx
// filepath: src/pages/PricingPage.jsx
import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { stripeService } from '../services/stripeService';

const PricingPage = () => {
  const [billingPeriod, setBillingPeriod] = useState('monthly');
  const [loading, setLoading] = useState(false);
  const [autoCheckoutTriggered, setAutoCheckoutTriggered] = useState(false);
  
  const location = useLocation();
  const { user, isAuthenticated } = useAuth();

  // Handle auto-checkout from landing page
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const preSelectedPlan = searchParams.get('plan');
    const preSelectedBilling = searchParams.get('billing');
    const autoCheckout = searchParams.get('auto') === 'true';
    
    // Set billing period if specified
    if (preSelectedBilling && ['monthly', 'yearly'].includes(preSelectedBilling)) {
      setBillingPeriod(preSelectedBilling);
    }
    
    // Auto-trigger checkout if conditions are met
    if (
      preSelectedPlan && 
      autoCheckout && 
      isAuthenticated && 
      user &&
      !autoCheckoutTriggered &&
      !loading
    ) {
      setAutoCheckoutTriggered(true);
      
      // Small delay to ensure UI is ready
      setTimeout(() => {
        handleSubscribe(preSelectedPlan);
      }, 1500);
      
      // Track auto-checkout initiation
      if (typeof gtag !== 'undefined') {
        gtag('event', 'auto_checkout_initiated', {
          'plan_type': preSelectedPlan,
          'billing_cycle': preSelectedBilling
        });
      }
    }
  }, [isAuthenticated, user, loading, autoCheckoutTriggered, location.search]);

  const handleSubscribe = async (plan) => {
    if (!user) {
      // Redirect to auth with plan selection
      const currentParams = new URLSearchParams(location.search);
      const redirectUrl = `/auth?mode=register&plan=${plan}&billing=${billingPeriod}`;
      window.location.href = redirectUrl;
      return;
    }

    try {
      setLoading(true);
      
      // Track checkout initiation
      if (typeof gtag !== 'undefined') {
        gtag('event', 'checkout_initiated', {
          'plan_type': plan,
          'billing_cycle': billingPeriod,
          'user_id': user.id
        });
      }
      
      const { url } = await stripeService.createCheckoutSession({
        planId: plan,
        billingPeriod: billingPeriod,
        userId: user.id,
        userEmail: user.email
      });
      
      if (url) {
        window.location.href = url;
      }
    } catch (error) {
      console.error('Checkout error:', error);
      // Handle error (show toast, etc.)
    } finally {
      setLoading(false);
    }
  };

  // ...existing component render code...
  
  return (
    <div className="pricing-page">
      {/* Show loading state during auto-checkout */}
      {autoCheckoutTriggered && loading && (
        <div className="auto-checkout-loading">
          <h3>Setting up your subscription...</h3>
          <p>Please wait while we redirect you to checkout.</p>
        </div>
      )}
      
      {/* ...existing pricing cards... */}
    </div>
  );
};

export default PricingPage;
```

### Step 6: Add CSS for Enhanced UX

```css
/* Add to your main CSS file */
.plan-selection-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 20px;
  text-align: center;
  border-radius: 8px;
  margin-bottom: 20px;
}

.plan-selection-banner p {
  margin: 0;
  font-size: 14px;
}

.plan-selection-banner small {
  opacity: 0.8;
  font-size: 12px;
}

.auto-checkout-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.auto-checkout-loading h3 {
  color: #333;
  margin-bottom: 10px;
}

.auto-checkout-loading p {
  color: #666;
  font-size: 14px;
}

/* Landing page button enhancements */
.btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}
```

### Step 7: Environment Configuration

Update your environment variables to include your landing page domain:

```env
# Add to .env file
VITE_LANDING_PAGE_URL=https://your-landing-page-domain.com
VITE_APP_DOMAIN=https://your-docforge-domain.com
```

### Step 8: Analytics & Tracking Setup

Add conversion tracking to monitor the funnel:

```javascript
// Add to your main application
const trackConversionFunnel = {
  landingPageView: () => {
    gtag('event', 'landing_page_view', {
      'page_location': window.location.href
    });
  },
  
  planSelected: (plan, billing) => {
    gtag('event', 'plan_selected', {
      'plan_type': plan,
      'billing_cycle': billing,
      'value': getPlanPrice(plan, billing)
    });
  },
  
  registrationStarted: (plan, billing) => {
    gtag('event', 'begin_checkout', {
      'currency': 'USD',
      'value': getPlanPrice(plan, billing),
      'items': [{
        'item_id': `${plan}_${billing}`,
        'item_name': `${plan} Plan (${billing})`,
        'category': 'subscription',
        'quantity': 1,
        'price': getPlanPrice(plan, billing)
      }]
    });
  },
  
  registrationCompleted: (plan, billing, userId) => {
    gtag('event', 'sign_up', {
      'method': 'email',
      'user_id': userId
    });
  },
  
  checkoutCompleted: (plan, billing, userId, subscriptionId) => {
    gtag('event', 'purchase', {
      'transaction_id': subscriptionId,
      'value': getPlanPrice(plan, billing),
      'currency': 'USD',
      'user_id': userId,
      'items': [{
        'item_id': `${plan}_${billing}`,
        'item_name': `${plan} Plan (${billing})`,
        'category': 'subscription',
        'quantity': 1,
        'price': getPlanPrice(plan, billing)
      }]
    });
  }
};
```

### Step 9: Testing Checklist

- [ ] Landing page buttons redirect correctly
- [ ] URL parameters are preserved and parsed
- [ ] Plan selection is stored in sessionStorage
- [ ] Authentication flow works with pre-selected plans
- [ ] Auto-checkout triggers on pricing page
- [ ] Stripe checkout completes successfully
- [ ] Post-checkout redirect to dashboard works
- [ ] Analytics events fire correctly
- [ ] Error handling works for edge cases

### Step 10: Launch Preparation

1. **Domain Setup**: Ensure your landing page and main app domains are properly configured
2. **SSL Certificates**: Verify HTTPS is working on both domains
3. **CORS Configuration**: Update Supabase CORS settings if needed
4. **Webhook Testing**: Test Stripe webhooks with live events
5. **Analytics**: Verify Google Analytics or your analytics platform is tracking events
6. **Error Monitoring**: Set up error tracking (Sentry, LogRocket, etc.)

## Monitoring & Optimization

### Key Metrics to Track
- Landing page → Registration conversion rate
- Registration → Subscription conversion rate
- Plan selection distribution
- Drop-off points in the funnel
- Time from plan selection to completed payment

### A/B Testing Opportunities
- Different CTA button text ("Get Started" vs "Start Free Trial")
- Plan positioning and pricing display
- Social proof and testimonials
- Urgency and scarcity messaging

## Troubleshooting

### Common Issues
1. **Parameters not preserved**: Check URL encoding and sessionStorage implementation
2. **Auto-checkout not triggering**: Verify authentication state and timing
3. **Stripe checkout fails**: Check price IDs and user data
4. **Analytics not tracking**: Verify GTM/GA implementation

### Support Resources
- Stripe Dashboard for payment debugging
- Supabase logs for backend issues
- Browser dev tools for frontend debugging
- Analytics platforms for conversion tracking

## Implementation Summary

✅ **Completed Changes:**

1. **Auth Page Updates** (`/src/pages/auth/index.jsx`)
   - Added URL parameter parsing for plan and billing selection
   - Added sessionStorage management for plan pre-selection
   - Added plan selection banner UI component
   - Updated redirect logic for post-authentication flow

2. **AuthContext Updates** (`/src/contexts/AuthContext.jsx`)
   - Added post-registration flow for pre-selected plans
   - Added analytics tracking for landing page conversions
   - Preserved plan selection through authentication process

3. **Pricing Page Updates** (`/src/pages/PricingPage.jsx`)
   - Added auto-checkout functionality from URL parameters
   - Added loading overlay for auto-checkout process
   - Updated subscription flow to use stripeService directly
   - Added analytics tracking for checkout initiation

4. **CSS Styles** (`/src/styles/landing-page-integration.css`)
   - Plan selection banner styling
   - Auto-checkout loading overlay
   - Enhanced button interactions
   - Dark mode and responsive support

5. **Utilities** (`/src/utils/conversionTracking.js`)
   - Conversion funnel tracking functions
   - Plan selection storage utilities
   - Analytics event helpers

6. **Environment Configuration** (`.env.example`)
   - Added landing page and app domain variables
   - Updated configuration template

### Testing Checklist

Before going live, test the following flow:

1. **Landing Page Integration**
   - [ ] Update landing page buttons with correct URLs
   - [ ] Test plan parameter passing
   - [ ] Verify analytics tracking on landing page

2. **Authentication Flow**
   - [ ] Test registration with pre-selected plan
   - [ ] Verify plan selection banner appears
   - [ ] Test login flow with plan parameters

3. **Auto-Checkout Flow**
   - [ ] Test auto-redirect to Stripe checkout
   - [ ] Verify loading overlay appears
   - [ ] Test different plan/billing combinations

4. **Error Handling**
   - [ ] Test with invalid plan parameters
   - [ ] Test with unauthenticated users
   - [ ] Test Stripe checkout errors

5. **Analytics**
   - [ ] Verify Google Analytics events fire
   - [ ] Test conversion tracking end-to-end
   - [ ] Monitor funnel drop-off points

## Next Steps

After successful implementation:

1. Monitor conversion rates and optimize accordingly
2. Implement email marketing automation for abandoned checkouts
3. Add referral tracking and affiliate programs
4. Consider implementing free trial periods
5. Add team and enterprise pricing tiers