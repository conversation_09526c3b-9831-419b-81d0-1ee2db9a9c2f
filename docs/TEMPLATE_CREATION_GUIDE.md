# Template Creation Guide - Web Interface

## Overview

DocForge AI now supports creating new templates directly through the web interface, eliminating the need for command-line tools. This guide covers the complete template creation workflow using the admin interface.

## Accessing Template Creation

1. Navigate to the Admin Template Manager at `/admin/templates`
2. Click the "Create Template" button in the top-right corner
3. The template creation wizard will open

## Template Creation Workflow

The template creation process consists of 4 steps:

### Step 1: Basic Information

**Required Fields:**
- **Template Name**: Unique name for your template (3-100 characters)
- **Category**: Select from predefined categories (Business, Academic, Creative, etc.)

**Optional Fields:**
- **Description**: Detailed description of the template (up to 500 characters)
- **Tags**: Searchable keywords for the template (up to 10 tags)
- **Layout Type**: Choose from predefined layouts:
  - **Standard**: Title, Author, Description
  - **Minimal**: Title only
  - **Professional**: Title, Author, Subtitle
  - **Custom**: Title, Author

**Advanced Options:**
- **Premium Status**: Mark as premium template
- **Status**: Active, Draft, or Inactive

### Step 2: Image Upload

**Requirements:**
- **File Types**: JPEG, PNG, or WebP
- **File Size**: Maximum 10MB
- **Dimensions**: Minimum 400×300 pixels, Maximum 5000×5000 pixels
- **Recommended**: 1200×1600 pixels (3:4 aspect ratio)

**Upload Process:**
1. Click "Choose File" or drag and drop an image
2. The system will validate the file and show a preview
3. Image dimensions will be automatically detected
4. Any validation errors will be displayed immediately

### Step 3: Text Overlay Configuration

**Features:**
- **Visual Editor**: Interactive overlay positioning on the image preview
- **Multiple Overlays**: Add, remove, and reorder text overlays
- **Real-time Preview**: See changes instantly

**Overlay Properties:**
- **ID**: Unique identifier (title, author, description, etc.)
- **Placeholder**: Template variables ({{title}}, {{author}}, etc.)
- **Position**: X, Y coordinates, width, height
- **Typography**: Font family, size, weight, color
- **Alignment**: Text alignment and vertical positioning

**Available Placeholders:**
- `{{title}}` - Document title
- `{{author}}` - Document author
- `{{description}}` - Document description
- `{{subtitle}}` - Document subtitle

### Step 4: Preview and Finalize

**Preview Features:**
- **Template Summary**: Review all template settings
- **Sample Data Editor**: Edit sample text to test the template
- **Live Preview**: See how the template looks with sample data
- **Final Validation**: Comprehensive validation before creation

## Validation and Error Handling

### Client-Side Validation
- **Real-time validation** as you type
- **Step-by-step validation** prevents progression with errors
- **Comprehensive error messages** with specific guidance

### Server-Side Validation
- **File upload validation** (type, size, dimensions)
- **Template data validation** before database insertion
- **Duplicate name checking**
- **Text overlay structure validation**

### Common Validation Errors

**Template Name:**
- Must be 3-100 characters long
- Cannot contain special characters: `< > : " / \ | ? *`
- Must be unique

**Image Upload:**
- Invalid file type (only JPEG, PNG, WebP allowed)
- File too large (max 10MB)
- Image dimensions too small/large
- Corrupted or unreadable file

**Text Overlays:**
- Missing required properties (ID, position, styling)
- Invalid position values (negative coordinates)
- Invalid font size (must be 1-200)
- Invalid color format (must be hex color)

## Best Practices

### Image Selection
1. **High Resolution**: Use high-quality images for best results
2. **Contrast**: Ensure good contrast for text readability
3. **Composition**: Leave space for text overlays
4. **Aspect Ratio**: 3:4 ratio works best for most document types

### Text Overlay Design
1. **Hierarchy**: Use different font sizes to create visual hierarchy
2. **Readability**: Ensure sufficient contrast between text and background
3. **Positioning**: Leave adequate margins and spacing
4. **Consistency**: Use consistent styling across overlays

### Template Organization
1. **Naming**: Use descriptive, searchable names
2. **Categories**: Choose appropriate categories for discoverability
3. **Tags**: Add relevant tags for better search results
4. **Descriptions**: Write clear, helpful descriptions

## Technical Implementation

### Architecture
- **Frontend**: React-based multi-step form with real-time validation
- **Backend**: Supabase integration for data storage and file upload
- **Validation**: Comprehensive client and server-side validation
- **Storage**: Supabase Storage for background images

### Database Schema
Templates are stored in the `cover_templates` table with the following structure:
```sql
- id (TEXT PRIMARY KEY)
- name (TEXT NOT NULL)
- description (TEXT)
- category (TEXT NOT NULL)
- tags (TEXT[])
- background_image_url (TEXT NOT NULL)
- background_image_width (INTEGER NOT NULL)
- background_image_height (INTEGER NOT NULL)
- text_overlays (JSONB NOT NULL)
- supported_formats (TEXT[])
- is_premium (BOOLEAN DEFAULT false)
- status (TEXT DEFAULT 'active')
- usage_count (INTEGER DEFAULT 0)
- rating (DECIMAL(3,2) DEFAULT 0.0)
- created_at (TIMESTAMPTZ DEFAULT NOW())
- updated_at (TIMESTAMPTZ DEFAULT NOW())
```

### Text Overlay Structure
```json
{
  "overlays": [
    {
      "id": "title",
      "type": "text",
      "placeholder": "{{title}}",
      "position": {
        "x": 50,
        "y": 200,
        "width": 500,
        "height": 80
      },
      "styling": {
        "fontSize": 36,
        "fontFamily": "Arial",
        "fontWeight": "bold",
        "color": "#000000",
        "textAlign": "center",
        "lineHeight": 1.2,
        "maxLines": 2,
        "overflow": "ellipsis",
        "verticalAlign": "center"
      }
    }
  ]
}
```

## Troubleshooting

### Common Issues

**Upload Fails:**
- Check file size (max 10MB)
- Verify file type (JPEG, PNG, WebP only)
- Ensure stable internet connection

**Validation Errors:**
- Review error messages carefully
- Check all required fields are filled
- Verify image meets dimension requirements

**Preview Not Showing:**
- Refresh the page and try again
- Check browser console for JavaScript errors
- Ensure image uploaded successfully

### Getting Help

1. Check the error messages for specific guidance
2. Review this documentation for requirements
3. Contact support if issues persist

## Migration from Command-Line Tools

The web interface provides all functionality previously available through command-line tools:

- **Equivalent Features**: All CLI features are available in the web interface
- **Enhanced Validation**: More comprehensive validation and error handling
- **Better UX**: Visual feedback and real-time preview
- **No Setup Required**: No need to install or configure CLI tools

### CLI vs Web Interface Comparison

| Feature | CLI Tool | Web Interface |
|---------|----------|---------------|
| Template Creation | ✅ | ✅ |
| Image Upload | ✅ | ✅ |
| Text Overlay Config | ✅ | ✅ Enhanced |
| Real-time Preview | ❌ | ✅ |
| Validation | Basic | Comprehensive |
| User Experience | Technical | User-friendly |
| Setup Required | Yes | No |

The web interface is now the recommended method for creating templates, with CLI tools maintained for advanced users and automation scenarios.
