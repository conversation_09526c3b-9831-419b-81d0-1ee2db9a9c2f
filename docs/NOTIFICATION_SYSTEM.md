# DocForge AI Notification System

## Overview

The DocForge AI notification system provides comprehensive user feedback for all application operations. It includes toast notifications, a notification center, and seamless integration with existing services.

## Architecture

### Core Components

1. **NotificationService** (`src/services/notificationService.js`)
   - Centralized notification management
   - Queue management with priority
   - Persistence and state management
   - Event-driven architecture

2. **NotificationContext** (`src/contexts/NotificationContext.jsx`)
   - React context for global state
   - Performance-optimized hooks
   - Convenient notification methods

3. **UI Components** (`src/components/notifications/`)
   - `NotificationCenter` - Bell icon with dropdown
   - `ToastContainer` - Toast notification display
   - `NotificationItem` - Individual notification component
   - `NotificationBadge` - Unread count indicator

4. **Configuration System** (`src/services/notificationConfig.js`)
   - User preferences management
   - Duration and positioning settings
   - Accessibility options

## Usage

### Basic Usage

```jsx
import { useNotifications } from '../hooks/useNotifications';

function MyComponent() {
  const { showSuccess, showError, showWarning, showInfo } = useNotifications();

  const handleSuccess = () => {
    showSuccess('Operation Complete', 'Your action was successful!');
  };

  const handleError = () => {
    showError('Operation Failed', 'Something went wrong.');
  };

  return (
    <div>
      <button onClick={handleSuccess}>Success</button>
      <button onClick={handleError}>Error</button>
    </div>
  );
}
```

### Category-Specific Notifications

```jsx
import { useProjectNotifications, useTemplateNotifications } from '../hooks/useNotifications';

function ProjectManager() {
  const { notifyProjectCreated, notifyProjectError } = useProjectNotifications();
  const { notifyTemplateSaved } = useTemplateNotifications();

  const createProject = async (projectData) => {
    try {
      const result = await projectService.create(projectData);
      notifyProjectCreated(result.title);
    } catch (error) {
      notifyProjectError('creation', error.message);
    }
  };
}
```

### Service Integration

Services automatically show notifications when integrated:

```javascript
// In templateService.js
import notificationService, { createTemplateNotification } from './notificationService.js';
import { NOTIFICATION_TYPES } from './userNotificationService.js';

export const createTemplate = async (templateData) => {
  try {
    // ... template creation logic
    
    const successNotification = createTemplateNotification(
      NOTIFICATION_TYPES.SUCCESS,
      'Template Created',
      `Template "${templateData.name}" has been created successfully.`,
      { duration: 4000 }
    );
    notificationService.add(successNotification);
    
    return { success: true, template };
  } catch (error) {
    const errorNotification = createTemplateNotification(
      NOTIFICATION_TYPES.ERROR,
      'Template Creation Failed',
      error.message,
      { persistent: true }
    );
    notificationService.add(errorNotification);
    
    throw error;
  }
};
```

## Notification Types

- **SUCCESS** - Green, auto-dismiss (4s)
- **INFO** - Blue, auto-dismiss (5s)
- **WARNING** - Yellow, persistent by default (8s)
- **ERROR** - Red, persistent by default
- **LOADING** - Blue with spinner, persistent until manually dismissed
- **PROGRESS** - Blue with progress indicator, persistent

## Categories

- **SYSTEM** - General system notifications
- **PROJECT** - Project-related operations
- **TEMPLATE** - Template operations
- **EXPORT** - Document export status
- **UPLOAD** - File upload results
- **GENERATION** - AI content/image generation
- **FONT** - Font loading operations
- **AUTH** - Authentication operations

## Configuration

### Default Settings

```javascript
{
  enabled: true,
  maxNotifications: 50,
  maxToasts: 3,
  durations: {
    success: 4000,
    info: 5000,
    warning: 8000,
    error: 0, // Persistent
    loading: 0, // Persistent
    progress: 0 // Persistent
  },
  position: {
    vertical: 'top',
    horizontal: 'right',
    offset: { top: 80, right: 16 }
  }
}
```

### Customization

```javascript
import notificationConfig from '../services/notificationConfig.js';

// Update configuration
notificationConfig.updateConfig({
  durations: {
    success: 3000, // Shorter success duration
    info: 4000
  },
  maxToasts: 5 // More simultaneous toasts
});
```

## Accessibility

- **ARIA labels** for screen readers
- **Keyboard navigation** support
- **High contrast** mode detection
- **Reduced motion** preference respect
- **Screen reader announcements** for important notifications

## Integration Points

### Current Integrations

- ✅ **Header** - NotificationCenter replaces old StatusNotification
- ✅ **App.jsx** - NotificationProvider wraps entire app
- ✅ **templateService** - Template creation, upload, and error handling
- ✅ **projectsService** - Project CRUD operations
- 🔄 **aiService** - AI content generation (planned)
- 🔄 **replicateService** - Image generation (planned)
- 🔄 **exportService** - Document export (planned)

### Adding New Integrations

1. Import notification utilities:
```javascript
import notificationService, { createCategoryNotification } from './notificationService.js';
import { NOTIFICATION_TYPES } from './userNotificationService.js';
```

2. Add notifications to success/error paths:
```javascript
// Success
const successNotification = createCategoryNotification(
  NOTIFICATION_TYPES.SUCCESS,
  'Operation Complete',
  'Description of what succeeded'
);
notificationService.add(successNotification);

// Error
const errorNotification = createCategoryNotification(
  NOTIFICATION_TYPES.ERROR,
  'Operation Failed',
  error.message,
  { persistent: true }
);
notificationService.add(errorNotification);
```

## Testing

The notification system can be tested using the debug panel:

```jsx
import NotificationTestPanel from '../components/debug/NotificationTestPanel.jsx';

// Add to any page for testing
<NotificationTestPanel />
```

## Performance Considerations

- **Memoized context values** prevent unnecessary re-renders
- **Event-driven updates** minimize state changes
- **Queue management** limits memory usage
- **Automatic cleanup** removes old notifications
- **Lazy loading** for notification components

## Browser Support

- **Modern browsers** with ES6+ support
- **localStorage** for persistence
- **matchMedia** for accessibility preferences
- **Intersection Observer** for performance (if needed)

## Migration from Old System

### Replacing StatusNotification

Old:
```jsx
import StatusNotification from '../ui/StatusNotification';
<StatusNotification />
```

New:
```jsx
import NotificationCenter from '../notifications/NotificationCenter';
<NotificationCenter />
```

### Replacing ToastNotification

Old:
```jsx
import ToastNotification from '../ToastNotification';
<ToastNotification message="Success!" type="success" onClose={handleClose} />
```

New:
```jsx
import { useNotifications } from '../hooks/useNotifications';
const { showSuccess } = useNotifications();
showSuccess('Success!', 'Operation completed successfully.');
```

## Future Enhancements

- **Push notifications** for important updates
- **Email notifications** for critical errors
- **Notification history** with search
- **Custom notification sounds**
- **Notification grouping** by category
- **Batch operations** for multiple notifications
- **Analytics integration** for notification effectiveness
