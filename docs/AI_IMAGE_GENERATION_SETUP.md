# AI Image Generation Setup Guide

## Overview

RapidDoc AI now supports AI-powered image generation using Replicate's API. This feature allows users to generate custom images directly within the document editor using text prompts.

## API Key Setup

### 1. Get a Replicate API Key

1. Visit [Replicate.com](https://replicate.com)
2. Sign up for an account or log in
3. Navigate to your account settings
4. Generate an API token
5. Copy the token (it starts with `r8_...`)

### 2. Configure Environment Variable

Add your Replicate API key to your `.env` file:

```bash
# AI Image Generation
VITE_REPLICATE_API_KEY=r8_your_actual_api_key_here
```

**Important**: 
- Replace `r8_your_actual_api_key_here` with your actual API key
- Never commit your actual API key to version control
- The key must start with `VITE_` to be accessible in the frontend

### 3. Restart Development Server

After adding the environment variable, restart your development server:

```bash
npm run dev
```

## Features

### Available Models

- **Stable Diffusion**: High-quality general purpose image generation
- **SDXL**: Latest Stable Diffusion XL for enhanced quality  
- **Realistic Vision**: Photorealistic image generation

### Image Styles

- **Photorealistic**: Professional, realistic images
- **Artistic**: Creative, artistic compositions
- **Illustration**: Digital illustration style
- **Professional**: Business-appropriate imagery

### Image Sizes

- **Square**: 512×512 pixels
- **Landscape**: 768×512 pixels  
- **Portrait**: 512×768 pixels
- **Wide**: 1024×512 pixels

## Usage

### Accessing Image Generation

1. **In the Document Editor**: Click the floating menu (⋯) next to any paragraph
2. **Select DocGenerate**: Choose "DocGenerate" from the menu
3. **Choose Generate Image**: Select "Generate Image" from the submenu

### Generation Process

1. **Enter Prompt**: Describe the image you want to generate
2. **Select Style**: Choose from photorealistic, artistic, illustration, or professional
3. **Choose Size**: Select the appropriate aspect ratio
4. **Generate**: Click "Generate Image" to start the process
5. **Preview**: Review the generated image
6. **Insert**: Approve and insert into your document

### Tips for Better Results

- **Be Specific**: Detailed prompts produce better results
- **Include Style Keywords**: Add terms like "professional", "modern", "clean"
- **Specify Context**: Mention the intended use (e.g., "for a business presentation")
- **Avoid Copyrighted Content**: Don't request images of specific brands or copyrighted characters

## Fallback Behavior

### Without API Key

If no Replicate API key is configured:
- The feature will show placeholder/mock images during development
- Users will see helpful error messages with setup instructions
- The existing image import functionality remains fully available

### API Errors

If the Replicate API is unavailable:
- The system will retry failed requests automatically
- Clear error messages guide users to alternative options
- Fallback to existing image import methods

## Cost Considerations

### Replicate Pricing

- Image generation typically costs $0.0023-$0.01 per image
- Costs vary based on model, resolution, and generation steps
- Higher resolution and more steps = higher cost

### Cost Estimation

The system provides cost estimates before generation:
- Shows estimated cost in USD
- Factors in resolution and complexity
- Helps users make informed decisions

## Troubleshooting

### Common Issues

1. **"API Key Not Configured"**
   - Ensure `VITE_REPLICATE_API_KEY` is set in `.env`
   - Restart the development server
   - Check that the key starts with `r8_`

2. **"Generation Failed"**
   - Check your internet connection
   - Verify your Replicate account has sufficient credits
   - Try a simpler prompt

3. **"Request Timeout"**
   - Complex images may take longer to generate
   - Try reducing the number of inference steps
   - Check Replicate service status

### Debug Information

In development mode, check the browser console for detailed logs:
- API request/response information
- Error details and suggestions
- Fallback behavior notifications

## Security Notes

- API keys are only used client-side for direct API communication
- No image data is stored on RapidDoc servers
- Generated images are handled the same as uploaded images
- Follow your organization's policies for AI-generated content

## Testing the Implementation

### Manual Testing Steps

1. **Open Document Editor**: Navigate to any document in edit mode
2. **Access Plus Menu**: Click the "+" button next to any paragraph
3. **Select Generate Image**: Click "Generate Image" from the menu
4. **Enter Prompt**: Type a descriptive prompt (e.g., "A modern office workspace")
5. **Choose Style**: Select from photorealistic, artistic, illustration, or professional
6. **Generate**: Click "Generate Image" button
7. **Review Result**: Preview the generated image
8. **Insert**: Click "Insert Image" to add it to your document

### Expected Behavior

- **With API Key**: Real AI-generated images from Replicate
- **Without API Key**: Placeholder images with demo notice
- **Network Issues**: Automatic fallback to placeholder images
- **Errors**: Clear error messages with retry options

### Troubleshooting Common Issues

1. **"Failed to insert generated image into editor"**
   - Click in the document to ensure editor is focused
   - Try generating again
   - Check browser console for detailed error information

2. **Generation takes too long**
   - Complex prompts may take 30-60 seconds
   - Check your internet connection
   - Verify Replicate service status

3. **Poor image quality**
   - Be more specific in your prompt
   - Try different styles
   - Avoid very short or vague descriptions

## Development Notes

### Architecture Overview

The AI image generation system consists of:

- **ReplicateService**: Core API integration with retry logic and fallbacks
- **AIImageGenerationModal**: Simplified user interface for prompt input
- **AIImageGenerationExtension**: Tiptap extension for editor integration
- **DocumentCanvasMinimal**: Integration with plus menu and state management

### Key Features Implemented

- ✅ Simplified modal interface (prompt + style only)
- ✅ Real-time progress tracking with cancellation
- ✅ Preview and approval workflow
- ✅ Comprehensive error handling with user-friendly messages
- ✅ Automatic fallbacks for development and service issues
- ✅ Integration with existing image handling system
- ✅ Plus menu integration (not DocGenerate menu)
- ✅ Mock image generation for testing without API key

### Performance Optimizations

- Lazy loading of Replicate service
- Efficient state management with minimal re-renders
- Progress tracking without blocking UI
- Automatic cleanup of generation state

## Support

For issues with:
- **RapidDoc Integration**: Check the browser console and GitHub issues
- **Replicate API**: Visit [Replicate's documentation](https://replicate.com/docs)
- **Billing/Credits**: Contact Replicate support directly
- **Feature Requests**: Submit GitHub issues with enhancement label
