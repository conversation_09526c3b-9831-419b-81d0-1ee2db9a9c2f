# Image Overlay Template System - Deployment Guide

## Pre-Deployment Checklist

### ✅ **System Requirements**
- [ ] Node.js 18+ installed
- [ ] Supabase project configured
- [ ] Storage bucket permissions set
- [ ] Environment variables configured
- [ ] Build tools available

### ✅ **Code Verification**
- [ ] All tests passing (`node scripts/test-image-overlay-system.js`)
- [ ] No console errors in development
- [ ] Template selection working
- [ ] Preview generation functional
- [ ] Export functionality tested

### ✅ **Database Preparation**
- [ ] Backup existing `cover_templates` table (if any)
- [ ] Supabase SQL Editor access confirmed
- [ ] Storage bucket `template-backgrounds` ready

## Step-by-Step Deployment

### Step 1: Database Schema Deployment

1. **Open Supabase SQL Editor**
   - Navigate to your Supabase project dashboard
   - Go to SQL Editor

2. **Run Schema Script**
   ```sql
   -- Copy and paste contents of database/deploy-image-overlay-schema.sql
   -- This will:
   -- - Drop existing cover_templates table
   -- - Create new image overlay template table
   -- - Set up storage bucket and policies
   -- - Insert sample template
   ```

3. **Verify Deployment**
   ```sql
   -- Check table exists
   SELECT COUNT(*) FROM public.cover_templates;
   
   -- Check sample template
   SELECT id, name, category FROM public.cover_templates 
   WHERE id = 'sample-modern-business-001';
   
   -- Check storage bucket
   SELECT * FROM storage.buckets WHERE id = 'template-backgrounds';
   ```

### Step 2: Application Code Deployment

1. **Build Application**
   ```bash
   npm run build
   ```

2. **Run Tests**
   ```bash
   node scripts/test-image-overlay-system.js
   ```

3. **Deploy to Production**
   ```bash
   # Your deployment command (e.g., Vercel, Netlify, etc.)
   npm run deploy
   ```

### Step 3: Verify Integration

1. **Test Template Loading**
   - Open DocForge AI application
   - Navigate to template selection
   - Verify sample template appears
   - Check for console errors

2. **Test Preview Generation**
   - Select the sample template
   - Verify preview generates correctly
   - Check image quality and text positioning

3. **Test Export Functionality**
   - Create a test document
   - Select template and export as PDF
   - Verify template appears in exported document

### Step 4: Create Initial Template Library

1. **Prepare Background Images**
   ```bash
   # Organize your template images
   mkdir template-images
   # Add your background images (JPG/PNG)
   ```

2. **Create Templates**
   ```bash
   # Business templates
   node tools/image-template-creator.js \
     --image ./template-images/business-1.jpg \
     --name "Professional Business" \
     --category "Business" \
     --layout "professional"

   node tools/image-template-creator.js \
     --image ./template-images/business-2.jpg \
     --name "Modern Corporate" \
     --category "Business" \
     --layout "standard"

   # Academic templates
   node tools/image-template-creator.js \
     --image ./template-images/academic-1.jpg \
     --name "Research Paper" \
     --category "Academic" \
     --layout "professional"

   # Creative templates
   node tools/image-template-creator.js \
     --image ./template-images/creative-1.jpg \
     --name "Artistic Design" \
     --category "Creative" \
     --layout "minimal"
   ```

3. **Verify Templates**
   ```sql
   -- Check all templates created
   SELECT id, name, category, status FROM public.cover_templates 
   ORDER BY created_at DESC;
   ```

## Post-Deployment Verification

### Functional Testing

1. **Template Selection Interface**
   - [ ] Templates load without errors
   - [ ] Categories filter correctly
   - [ ] Search functionality works
   - [ ] Template previews generate

2. **Preview System**
   - [ ] Real-time preview updates
   - [ ] Text overlays position correctly
   - [ ] Image quality is acceptable
   - [ ] Performance is responsive

3. **Export System**
   - [ ] PDF export includes template
   - [ ] Image export works (PNG/JPG)
   - [ ] Quality meets requirements
   - [ ] File sizes are reasonable

### Performance Testing

1. **Load Testing**
   ```bash
   # Test with multiple concurrent users
   # Monitor response times
   # Check memory usage
   ```

2. **Template Rendering Performance**
   ```javascript
   // Monitor in browser console
   const startTime = performance.now();
   // Perform template operations
   const duration = performance.now() - startTime;
   console.log(`Operation took ${duration}ms`);
   ```

3. **Memory Usage**
   ```javascript
   // Check memory usage in DevTools
   // Look for memory leaks
   // Verify cache clearing works
   ```

## Monitoring and Maintenance

### Error Monitoring

1. **Set Up Error Tracking**
   ```javascript
   // Add to your error monitoring service
   window.addEventListener('error', (event) => {
     if (event.message.includes('imageOverlay')) {
       // Log template system errors
       console.error('Template System Error:', event);
     }
   });
   ```

2. **Monitor Key Metrics**
   - Template loading success rate
   - Preview generation time
   - Export success rate
   - User error reports

### Performance Monitoring

1. **Key Performance Indicators**
   - Template rendering time (< 100ms target)
   - Preview generation time (< 200ms target)
   - Export completion time (< 5s target)
   - Memory usage stability

2. **Monitoring Dashboard**
   ```javascript
   // Track performance metrics
   const metrics = {
     templateRenderTime: [],
     previewGenerationTime: [],
     exportTime: [],
     errorRate: 0
   };
   ```

### Regular Maintenance

1. **Weekly Tasks**
   - [ ] Review error logs
   - [ ] Check template usage statistics
   - [ ] Monitor performance metrics
   - [ ] Verify storage usage

2. **Monthly Tasks**
   - [ ] Analyze user feedback
   - [ ] Review template library
   - [ ] Update documentation
   - [ ] Plan new features

## Rollback Procedures

### Emergency Rollback

If critical issues occur:

1. **Immediate Actions**
   ```bash
   # Revert to previous deployment
   git revert HEAD
   npm run build
   npm run deploy
   ```

2. **Database Rollback** (if needed)
   ```sql
   -- Restore from backup
   -- Or disable new templates
   UPDATE public.cover_templates SET status = 'inactive';
   ```

### Gradual Rollback

For non-critical issues:

1. **Feature Flag Approach**
   ```javascript
   // Add feature flag to disable new system
   const useImageOverlayTemplates = process.env.ENABLE_IMAGE_OVERLAY === 'true';
   ```

2. **Selective Rollback**
   - Disable specific templates
   - Route users to fallback system
   - Monitor and fix issues

## Troubleshooting Common Issues

### Template Not Loading

**Symptoms**: Templates don't appear in selection interface

**Solutions**:
1. Check database connection
2. Verify table exists and has data
3. Check RLS policies
4. Review console errors

### Preview Generation Fails

**Symptoms**: Preview shows error or doesn't load

**Solutions**:
1. Check image URL accessibility
2. Verify Canvas API support
3. Check CORS settings
4. Review text overlay configuration

### Export Issues

**Symptoms**: Export fails or template missing from output

**Solutions**:
1. Check template rendering service
2. Verify image overlay service
3. Review export service integration
4. Check browser compatibility

### Performance Issues

**Symptoms**: Slow template operations

**Solutions**:
1. Check image sizes and optimization
2. Review cache configuration
3. Monitor memory usage
4. Optimize text rendering

## Support and Documentation

### Getting Help

1. **Check Documentation**
   - Review `docs/IMAGE_OVERLAY_TEMPLATE_SYSTEM.md`
   - Check API documentation
   - Review troubleshooting guide

2. **Debug Information**
   ```javascript
   // Enable debug mode
   localStorage.setItem('imageOverlay_debug', 'true');
   
   // Collect debug information
   console.log('Template System Debug Info:', {
     templates: await fetchCoverTemplates(),
     browserSupport: !!document.createElement('canvas').getContext,
     performance: performance.memory
   });
   ```

3. **Contact Support**
   - Provide error messages
   - Include browser/device information
   - Share debug information
   - Describe steps to reproduce

### Documentation Updates

Keep documentation current:
- Update deployment procedures
- Document new features
- Record troubleshooting solutions
- Maintain performance benchmarks

---

## Deployment Success Criteria

✅ **System is successfully deployed when**:
- [ ] Database schema deployed without errors
- [ ] Sample template loads and renders correctly
- [ ] Template selection interface functional
- [ ] Preview generation works
- [ ] Export functionality operational
- [ ] Performance meets benchmarks
- [ ] No critical errors in logs
- [ ] User acceptance testing passed

🎉 **Congratulations! Your Image Overlay Template System is now live!**

For ongoing support and feature development, refer to the main documentation and development team.
